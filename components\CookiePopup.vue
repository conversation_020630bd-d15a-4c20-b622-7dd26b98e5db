<template>
  <div class="cookie-popup">
    <div class="d-flex align-center">
      <div class="cookie-popup-image d-flex align-center justify-center">
        <svg width="20" height="20" viewBox="0 0 20 20">
          <use
            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#cookie`"
          ></use>
        </svg>
      </div>
      <div class="cookie-popup-text" v-html="$t('use_cookies', { link })"></div>
    </div>
    <div class="cookie-popup-button">
      <v-btn
        small
        width="auto"
        color="orange"
        @click="acceptCookieClickHandler"
      >
        {{ $t('i_agree') }}
      </v-btn>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CookiePopup',
  computed: {
    link() {
      return `${process.env.NUXT_ENV_URL}/privacy-policy`
    },
  },
  methods: {
    acceptCookie<PERSON>lickHandler() {
      this.$store.dispatch('setCookieAllowed', 1)
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/vars';

.cookie-popup {
  position: fixed;
  bottom: 30px;
  left: 50%;
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 580px;
  margin: 0 auto;
  padding: 15px;
  font-size: 14px;
  line-height: 1.4;
  background: #ffffff;
  box-shadow: 0 12px 24px -6px rgba(96, 96, 96, 0.15),
    0px 0px 1px rgba(96, 96, 96, 0.1);
  border-radius: 16px;
  transform: translateX(-50%);
  z-index: 101;

  @media only screen and (max-width: $xsm-and-down) {
    flex-direction: column;
    max-width: calc(100% - 20px);
    padding: 10px;
    font-size: 12px;
  }

  &-image {
    flex: 1 0 36px;
    width: 36px;
    height: 36px;
    color: var(--v-orange-base);
    background: linear-gradient(
      126.15deg,
      rgba(128, 182, 34, 0.1) 0%,
      rgba(60, 135, 248, 0.1) 102.93%
    );
    border-radius: 10px;
  }

  &-text {
    flex-basis: auto;
    padding: 0 10px;

    @media only screen and (max-width: $xsm-and-down) {
      padding-right: 0;
    }

    a {
      color: var(--v-orange-base) !important;
      text-decoration: none;
    }
  }

  &-button {
    @media only screen and (max-width: $xsm-and-down) {
      margin-top: 16px;
    }
  }
}
</style>
