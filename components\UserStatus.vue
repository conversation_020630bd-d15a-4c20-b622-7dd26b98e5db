<template>
  <div
    :class="[
      'user-status',
      `user-status--${status}`,
      { 'user-status--large': large },
    ]"
  ></div>
</template>

<script>
export default {
  name: 'UserStatus',
  props: {
    userId: {
      type: Number,
      default: 0,
    },
    large: {
      type: Boolean,
      default: false,
    },
    userStatuses: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    status() {
      let status = 'offline'

      if (
        Object.prototype.hasOwnProperty.call(
          this.userStatuses,
          this.userId?.toString()
        )
      ) {
        status = this.userStatuses[this.userId]
      }

      return status
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.user-status {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  background: #636363;
  z-index: 2;

  &--idle {
    background: linear-gradient(
      122.42deg,
      var(--v-redLight-base) 0%,
      var(--v-orangeLight2-base) 100%
    );
  }

  &--online {
    background: var(--v-success-base);
  }

  &--large {
    width: 25px;
    height: 25px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: 23px;
      height: 23px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      width: 21px;
      height: 21px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      width: 19px;
      height: 19px;
    }
  }
}
</style>
