<template>
  <v-row>
    <v-col class="col-12 px-0">
      <section class="section section-1">
        <div class="section-1__content">
          <h3 class="section-1__label">{{ pageData.title }}</h3>
          <h4 class="section-1__sublabel">{{ pageData.intro }}</h4>
          <div>
            <v-btn
              large
              color="primary"
              to="/teacher-listing/1/motivation,3;sortOption,9"
            >
              {{ $t('lets_get_started') }}
            </v-btn>
          </div>
        </div>
        <img
          class="section1__img"
          :src="require('~/assets/images/education-page/section1/Section1.svg')"
          alt=""
        />
      </section>
      <section class="section-2">
        <div class="row-education">
          <div class="business-label">
            <div class="business-label__point"></div>
            {{ $t('education_page.selection_title') }}
          </div>
          <div class="cards-cont-list">
            <v-container fluid>
              <v-row>
                <v-col
                  v-for="i in 6"
                  :key="i"
                  class="col-12 col-sm-6 col-md-4 cards-cont"
                >
                  <div class="card mb-0">
                    <img
                      class="card-img"
                      :src="
                        require(`~/assets/images/education-page/section2/img${i}.svg`)
                      "
                      alt=""
                    />
                    <div class="card-label text-left">
                      {{ $t(`education_page.col_card_label_${i}`) }}
                    </div>
                    <div class="card-text">
                      {{ $t(`education_page.col_card_text_${i}`) }}
                    </div>
                  </div>
                </v-col>
              </v-row>
            </v-container>
          </div>
          <div class="section-2__bottom mt-1 mt-sm-3">
            <div class="section-2__more">
              <img
                :src="require('~/assets/images/education-page/persent.svg')"
                alt=""
                class="section-2__more-img"
              />
              <div class="mobile-padding-right">
                <div class="section-2__more-label">
                  {{ $t('education_page.col_card_label_7') }}
                </div>
                <div class="section-2__more-text">
                  {{ $t('education_page.col_card_text_7') }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <img
          class="section-2__img"
          :src="require('~/assets/images/education-page/BGS2.png')"
          alt=""
        />
      </section>
      <section class="section-3">
        <div class="row-education">
          <div class="business-label">
            <div class="business-label__point"></div>
            {{ $t('education_page.selection_title_3') }}
          </div>
        </div>

        <testimonials-slider
          v-if="ratings.length > 0"
          :data="ratings"
          :dark="false"
        ></testimonials-slider>

        <div class="row-education">
          <div class="number-wrap">
            <div v-for="i in 5" :key="i" :class="['number', `number-${i}`]">
              <div class="number-helper">
                <div class="number-label">
                  {{ $t(`education_page.gold_text_${i}`) }}
                </div>
                <div class="number-text">
                  {{ $t(`education_page.number_text_${i}`) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <img
          :src="require('~/assets/images/education-page/BGS3.png')"
          alt=""
          class="section-3__img"
        />
      </section>
      <section class="section-4 justify-center">
        <div class="row-education">
          <div class="business-label">
            <div class="business-label__point"></div>
            {{ $t('education_page.selection_title_4') }}
          </div>
        </div>
        <div class="row-education">
          <div class="cards-cont-4">
            <v-container fluid class="py-0">
              <v-row justify="center">
                <v-col
                  v-for="i in 3"
                  :key="i"
                  class="col-12 col-sm-6 col-md-4 col-card"
                >
                  <div class="card">
                    <img
                      class="card-img"
                      :src="
                        require(`~/assets/images/education-page/section4/img${i}.svg`)
                      "
                      alt=""
                    />
                    <div class="card-label text-left">
                      {{ $t(`education_page.selection_4_card_label_${i}`) }}
                    </div>
                    <div class="card-text">
                      {{ $t(`education_page.selection_4_card_text_${i}`) }}
                    </div>
                  </div>
                </v-col>
                <v-col class="col-12 d-flex justify-center mt-2">
                  <v-btn
                    large
                    color="primary"
                    to="/teacher-listing/1/motivation,1;speciality,21"
                  >
                    {{ $t('step_1_title') }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </div>
        </div>
      </section>
      <section class="section-5">
        <div class="row-education">
          <div class="business-label">
            <div class="business-label__point"></div>
            {{ $t('education_page.selection_title_5') }}
          </div>
        </div>
        <div class="row-education">
          <v-row class="flex-lg-row-reverse">
            <v-col class="col-12 col-md-6 col-lg-5 skills-cont">
              <div class="teacher">
                <img
                  :src="
                    require('~/assets/images/education-page/section5/img1.svg')
                  "
                  class="teacher-img"
                  alt=""
                />
                <div class="teacher-content">
                  <div class="teacher-content__label">
                    {{ $t('education_page.verified') }}
                  </div>
                  <div class="teacher-content__text">
                    {{ $t('education_page.verified_describe') }}
                  </div>
                </div>
              </div>
              <div class="teacher">
                <img
                  :src="
                    require('~/assets/images/education-page/section5/img2.svg')
                  "
                  class="teacher-img"
                  alt=""
                />
                <div class="teacher-content">
                  <div class="teacher-content__label">
                    {{ $t('education_page.trusted') }}
                  </div>
                  <div class="teacher-content__text">
                    {{ $t('education_page.feedbacks_describe') }}
                  </div>
                </div>
              </div>
              <div class="teacher">
                <img
                  :src="
                    require('~/assets/images/education-page/section5/img3.svg')
                  "
                  class="teacher-img"
                  alt=""
                />
                <div class="teacher-content">
                  <div class="teacher-content__label">
                    {{ $t('education_page.specialised') }}
                  </div>
                  <div class="teacher-content__text">
                    {{ $t('education_page.specialised_describe') }}
                  </div>
                </div>
              </div>
            </v-col>

            <v-spacer class="d-none d-lg-block"></v-spacer>

            <v-col class="col-12 col-md-6 col-lg-6 px-0 pt-0">
              <teachers-slider :data="teachers" :dark="false"></teachers-slider>
            </v-col>
          </v-row>
        </div>
      </section>
      <section class="section-6">
        <div class="row-education">
          <div class="business-label">
            <div class="business-label__point"></div>
            {{ $t('education_page.selection_title_6') }}
          </div>
        </div>
        <div class="row-education">
          <div class="type">
            <img
              :src="require('~/assets/images/education-page/section6/img1.svg')"
              alt=""
            />
            <div class="type-content">
              <div class="type-content__label">
                {{ $t('education_page.selection_6_card_label_1') }}
              </div>
              <ul class="type-content__list">
                <li>
                  <p>
                    {{ $t('education_page.selection_6_card_text_1') }}
                  </p>
                </li>
                <li>
                  <p>{{ $t('education_page.selection_6_card_text_2') }}</p>
                </li>
                <li>
                  <p>{{ $t('education_page.selection_6_card_text_3') }}</p>
                </li>
                <li>
                  <p>{{ $t('education_page.selection_6_card_text_4') }}</p>
                </li>
              </ul>
            </div>
          </div>
          <div class="type reverse">
            <div class="type-content">
              <div class="type-content__label">
                {{ $t('education_page.selection_6_card_label_2') }}
              </div>
              <ul class="type-content__list">
                <li>
                  <p>{{ $t('education_page.selection_6_card_text_5') }}</p>
                </li>
                <li>
                  <p>{{ $t('education_page.selection_6_card_text_6') }}</p>
                </li>
                <li>
                  <p>{{ $t('education_page.selection_6_card_text_7') }}</p>
                </li>
                <li>
                  <p>{{ $t('education_page.selection_6_card_text_8') }}</p>
                </li>
              </ul>
            </div>
            <img
              :src="require('~/assets/images/education-page/section6/img2.svg')"
              alt=""
            />
          </div>
        </div>

        <img
          :src="require('~/assets/images/education-page/section6/BGL.png')"
          class="section-6__imgl"
          alt=""
        />
        <img
          :src="require('~/assets/images/education-page/section6/BGR.png')"
          class="section-6__imgr"
          alt=""
        />
      </section>
      <section class="section-7">
        <div class="row-education">
          <div class="section-7__label">
            {{ $t('education_page.selection_title_7') }}
            <div class="section-7__label-point"></div>
          </div>
        </div>
        <div class="row-education">
          <div style="width: 100%; padding-right: 21%">
            <img
              :src="
                require('~/assets/images/education-page/section7/image-bottom.svg')
              "
              alt=""
              class="section-7__image"
            />
          </div>

          <div class="form-image">
            <img
              :src="
                require('~/assets/images/education-page/section7/image-mobile.svg')
              "
              alt=""
            />
            <div class="form-image__text-wrap">
              <div class="form-image__text form-image__text-item-1">
                {{ $t('education_page.selection_7_text_1') }}
              </div>
              <div class="form-image__text left form-image__text-item-2">
                {{ $t('education_page.selection_7_text_2') }}
              </div>
              <div class="form-image__text form-image__text-item-3">
                {{ $t('education_page.selection_7_text_3') }}
              </div>
              <div class="form-image__text left form-image__text-item-4">
                {{ $t('education_page.selection_7_text_4') }}
              </div>
            </div>
          </div>

          <div class="section-7__item section-7__item-1 section-7__top-content">
            {{ $t('education_page.selection_7_text_1') }}
          </div>
          <div
            class="section-7__item section-7__item-2 section-7__bottom-content"
          >
            {{ $t('education_page.selection_7_text_2') }}
          </div>
          <div class="section-7__item section-7__item-3 section-7__top-content">
            {{ $t('education_page.selection_7_text_3') }}
          </div>
          <div
            class="section-7__item section-7__item-4 section-7__bottom-content"
          >
            {{ $t('education_page.selection_7_text_4') }}
          </div>

          <div class="d-flex justify-center">
            <v-btn
              large
              color="primary"
              to="/teacher-listing/1/motivation,3;sortOption,9"
            >
              {{ $t('education_page.selection_7_text_button') }}
            </v-btn>
          </div>
        </div>
      </section>
    </v-col>
  </v-row>
</template>

<script>
import TestimonialsSlider from '~/components/landing-page/TestimonialsSlider'
import TeachersSlider from '~/components/landing-page/TeachersSlider'

export default {
  name: 'EducationPage',
  components: { TestimonialsSlider, TeachersSlider },
  async asyncData({ store }) {
    let pageData, teachers, ratings

    await Promise.allSettled([
      store.dispatch('education_page/getPageData'),
      store.dispatch('education_page/getTeachers'),
      store.dispatch('education_page/getTestimonials'),
    ]).then((res) => {
      pageData = res[0]?.value
      teachers = res[1]?.value
      ratings = res[2]?.value
    })

    return { pageData, teachers, ratings }
  },
  data() {
    return {}
  },
  head() {
    return {
      title:
        this.pageData?.seoTitle ??
        'Private Online Language Lessons for Kids & Teenagers',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.pageData?.seoDescription ?? '',
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content:
            this.pageData?.seoTitle ??
            'Private Online Language Lessons for Kids & Teenagers',
        },
        {
          property: 'og:description',
          content: this.pageData?.seoDescription ?? '',
        },
        { hid: 'og:image', property: 'og:image', content: this.previewImage },
        { hid: 'og:image:width', property: 'og:image:width', content: 772 },
        { hid: 'og:image:height', property: 'og:image:height', content: 564 },
        {
          hid: 'og:image:type',
          property: 'og:image:type',
          content: 'svg+xml',
        },
      ],
      bodyAttrs: {
        class: 'education-page',
      },
    }
  },
  computed: {
    previewImage() {
      return (
        process.env.NUXT_ENV_URL +
        require(`~/assets/images/business-page/img1.svg`)
      )
    },
  },
}
</script>

<style scoped lang="scss">
@import '~assets/styles/education-page.scss';
</style>
