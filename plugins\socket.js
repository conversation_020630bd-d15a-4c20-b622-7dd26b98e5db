import Vue from 'vue'
import VueSocketIO from 'vue-socket.io'

// Determine if this is a classroom page and use appropriate WebSocket
const isClassroomPage =
  process.client && window.location.pathname.includes('/classroom')
const socketUrl =
  isClassroomPage && process.env.NUXT_ENV_SOCKET_URL?.includes('langu.io')
    ? 'https://liveserver.langu.io'
    : process.env.NUXT_ENV_SOCKET_URL

const socketPath = isClassroomPage ? '/websocket-classroom/' : '/websocket/'

console.log('🔌 Socket configuration:', {
  isClassroomPage,
  socketUrl,
  socketPath,
  currentPath: process.client ? window.location.pathname : 'server',
})

Vue.use(
  new VueSocketIO({
    debug: process.env.NUXT_ENV_SOCKET_ENVIRONMENT === 'development',
    connection: socketUrl,
    options: {
      path: socketPath,
      withCredentials: true,
      secure: true,
      forceNew: isClassroomPage,
      transports: ['websocket', 'polling'],
      // reconnectionAttempts: 5,
    },
  })
)
