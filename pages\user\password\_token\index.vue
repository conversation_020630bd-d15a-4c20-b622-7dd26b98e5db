<template>
  <div style="min-height: calc(100vh - 300px)"></div>
</template>

<script>
export default {
  name: 'UserPasswordToken',
  beforeMount() {
    const token = this.$route.params.token
    const redirectUrl = this.$route.query.redirectUrl

    // If redirectUrl is present in the query parameters, save it to the store
    if (redirectUrl) {
      this.$store.dispatch('user/setRedirectUrl', redirectUrl)
    }

    this.$store.dispatch('auth/checkPasswordToken', token).finally(() => {
      this.$router.push('/teacher-listing')
    })
  },
}
</script>
