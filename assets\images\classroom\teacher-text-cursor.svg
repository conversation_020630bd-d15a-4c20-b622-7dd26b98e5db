<svg width="47" height="82" viewBox="0 0 47 82" transform="scale(0.5)" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path d="M32.1836 60.0703H27.7891V15.9297H32.1836C32.9933 15.9297 33.6484 15.2745 33.6484 14.4648C33.6484 13.6552 32.9933 13 32.1836 13H20.4648C19.6552 13 19 13.6552 19 14.4648C19 15.2745 19.6552 15.9297 20.4648 15.9297H24.8594V60.0703H20.4648C19.6552 60.0703 19 60.7255 19 61.5352C19 62.3448 19.6552 63 20.4648 63H32.1836C32.9933 63 33.6484 62.3448 33.6484 61.5352C33.6484 60.7255 32.9933 60.0703 32.1836 60.0703Z" fill="#80B723"/>
<path d="M32.1836 60.0703H27.7891V15.9297H32.1836C32.9933 15.9297 33.6484 15.2745 33.6484 14.4648C33.6484 13.6552 32.9933 13 32.1836 13H20.4648C19.6552 13 19 13.6552 19 14.4648C19 15.2745 19.6552 15.9297 20.4648 15.9297H24.8594V60.0703H20.4648C19.6552 60.0703 19 60.7255 19 61.5352C19 62.3448 19.6552 63 20.4648 63H32.1836C32.9933 63 33.6484 62.3448 33.6484 61.5352C33.6484 60.7255 32.9933 60.0703 32.1836 60.0703Z" stroke="#80B723" stroke-width="2"/>
</g>
<defs>
<filter id="filter0_d" x="0" y="0" width="46.6484" height="82" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dx="-3" dy="3"/>
<feGaussianBlur stdDeviation="7.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
