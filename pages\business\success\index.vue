<template>
  <business-page-component></business-page-component>
</template>

<script>
import BusinessPageComponent from '@/components/business-page/BusinessPage'

export default {
  name: 'BusinessSuccessPage',
  components: { BusinessPageComponent },
  middleware: 'businessSuccessPageAllowed',
  async asyncData({ store }) {
    let pageData

    await store.dispatch('business_page/getPageData').then((res) => {
      pageData = res
    })

    return { pageData }
  },
  head() {
    return {
      title:
        this.pageData?.seoTitle ??
        'Trusted Online Language Training for Companies & Teams',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.pageData?.seoDescription ?? '',
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content:
            this.pageData?.seoTitle ??
            'Trusted Online Language Training for Companies & Teams',
        },
        {
          property: 'og:description',
          content: this.pageData?.seoDescription ?? '',
        },
        { hid: 'og:image', property: 'og:image', content: this.previewImage },
        { hid: 'og:image:width', property: 'og:image:width', content: 772 },
        { hid: 'og:image:height', property: 'og:image:height', content: 564 },
        {
          hid: 'og:image:type',
          property: 'og:image:type',
          content: 'svg+xml',
        },
      ],
      bodyAttrs: {
        class: 'business-page',
      },
    }
  },
  beforeMount() {
    this.$cookiz.remove('business_success_page_allowed', {
      domain: process.env.NUXT_ENV_COOKIE_DOMAIN,
      path: '/',
    })
  },
}
</script>
