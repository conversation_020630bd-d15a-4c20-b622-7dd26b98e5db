const isIOS = () => {
  return (
    [
      'iPad Simulator',
      'iPhone Simulator',
      'iPod Simulator',
      'iPad',
      'iPhone',
      'iPod',
    ].includes(window.navigator.platform) ||
    // iPad on iOS 13 detection
    (window.navigator.userAgent.includes('Mac') && 'ontouchend' in document)
  )
}

const isAndroid = () => {
  return !!window.navigator.userAgent.match(/Android/i)
}

const isDevice = () => {
  return isIOS() || isAndroid()
}

const isTouchDevice = () => {
  return (
    'ontouchstart' in window ||
    window.navigator.maxTouchPoints > 0 ||
    window.navigator.msMaxTouchPoints > 0
  )
}

const isSupportedScreenShare = () => {
  return 'getDisplayMedia' in navigator.mediaDevices
}

export { isDevice, isIOS, isAndroid, isTouchDevice, isSupportedScreenShare }
