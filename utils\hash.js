/* eslint-disable unicorn/number-literal-case */
function generateRandomHash(length) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * Utility functions for hashing and encoding/decoding user data for GTM events
 */

/**
 * Reversible hash function that produces a 64-character output
 * This uses a combination of XOR cipher and padding to ensure a 64-character output
 * while still allowing for decoding
 *
 * @param {string} str - The string to hash
 * @param {boolean} decode - Whether to decode (true) or encode (false)
 * @returns {string} The hashed or decoded string
 */
export function hashUserData(str, decode = false) {
  if (!str) return generateRandomHash(64)

  // Use a fixed key for XOR operation (this should be kept consistent)
  const key = 'langu-gtm-key-2023'

  if (decode) {
    // Decode: First validate the string is 64 characters
    if (str.length !== 64) {
      console.error('Invalid hash length for decoding, expected 64 characters')
      return generateRandomHash(64)
    }

    try {
      // Extract the encoded data from the 64-character string
      const encodedPart = str.substring(0, 44) // Base64 encoded part

      // Decode the base64 part
      const decoded = atob(encodedPart)

      // Apply XOR decryption
      let result = ''
      for (let i = 0; i < decoded.length; i++) {
        const charCode = decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        // Ensure we stay within Latin1 range
        // eslint-disable-next-line unicorn/number-literal-case
        result += String.fromCharCode(charCode & 0xff)
      }
      return result
    } catch (e) {
      console.error('Error decoding hashed data:', e)
      return generateRandomHash(64)
    }
  } else {
    // Encode: XOR with key and convert to base64
    try {
      let xorResult = ''
      for (let i = 0; i < str.length; i++) {
        const charCode = str.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        // Ensure we stay within Latin1 range
        xorResult += String.fromCharCode(charCode & 0xff)
      }

      // Convert to base64
      const base64Result = btoa(xorResult)

      // Pad or truncate to ensure we have a 44-character base64 string
      let paddedBase64 = base64Result
      if (paddedBase64.length > 44) {
        paddedBase64 = paddedBase64.substring(0, 44)
      } else {
        while (paddedBase64.length < 44) {
          paddedBase64 += '='
        }
      }

      // Add a padding suffix to reach exactly 64 characters
      const timestamp = Date.now().toString(36)
      const padding = timestamp.padEnd(20, '0')

      // Combine to get exactly 64 characters
      return paddedBase64 + padding
    } catch (e) {
      console.error('Error encoding data:', e)
      return generateRandomHash(64)
    }
  }
}

/**
 * SHA-256 hash function that produces a 64-character hexadecimal string
 * This is a one-way hash function and cannot be decoded
 *
 * @param {string} text - The string to hash
 * @returns {Promise<string>} A promise that resolves to the 64-character hash
 */
export const hashSHA256 = async (text) => {
  if (!text) return ''
  const encoder = new TextEncoder()
  const data = encoder.encode(text)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map((b) => b.toString(16).padStart(2, '0')).join('')
}

/**
 * Test function to verify encoding/decoding works
 *
 * @param {string} str - The string to test
 * @returns {object} Test results including success status and encoded string length
 */
export function testHashFunction(str) {
  const encoded = hashUserData(str)
  const decoded = hashUserData(encoded, true)

  return {
    success: str === decoded,
    encodedLength: encoded.length,
    originalString: str,
    encodedString: encoded,
    decodedString: decoded,
  }
}
