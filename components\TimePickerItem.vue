<template>
  <div
    :id="elId"
    :class="[
      'time-picker-item',
      { active: isActive },
      { selected: item.isSelected },
      { free: item.isAvailable },
      { unavailable: item.isUnavailable },
    ]"
    @mouseover="mouseoverHandler"
    @mouseleave="mouseleaveHandler"
    @click.stop="clickHandler"
  ></div>
</template>

<script>
import { isTouchDevice } from '~/helpers/check_device'

export default {
  name: 'TimePickerItem',
  props: {
    idDefined: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      required: true,
    },
    allowedToSelect: {
      type: Boolean,
      required: true,
    },
    activeItems: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      isTouchDevice: isTouchDevice(),
    }
  },
  computed: {
    timezone() {
      return this.$store.getters['user/timeZone']
    },
    elId() {
      return this.idDefined
        ? `h-${this.$dayjs(this.item.date)
            .add(
              this.$dayjs(this.item.date).tz(this.timezone).utcOffset(),
              'minute'
            )
            .format('HH-mm')}`
        : null
    },
    isActive() {
      return this.item.isAvailable && this.activeItems.includes(this.item)
    },
  },
  methods: {
    clickHandler() {
      if (this.item.isAvailable) {
        this.$emit('click-item', this.item)
      }
    },
    mouseoverHandler() {
      if (
        !this.isTouchDevice &&
        this.item.isAvailable &&
        !this.item.isSelected &&
        this.allowedToSelect
      ) {
        this.$emit('mouseover-item', this.item)
      }
    },
    mouseleaveHandler() {
      if (
        this.item.isAvailable &&
        !this.item.isSelected &&
        this.allowedToSelect
      ) {
        this.$emit('mouseleave-item')
      }
    },
  },
}
</script>

<style scoped lang="scss">
.time-picker-item {
  position: relative;
  height: 32px;
  box-shadow: inset -1px -1px 0px #e0e0e0;

  &.free {
    background-color: var(--v-success-base);
    cursor: pointer;
  }

  &.active {
    background: #fff
      repeating-linear-gradient(
        45deg,
        rgba(251, 176, 59, 0.6),
        rgba(251, 176, 59, 0.6) 7px,
        var(--v-orange-base) 7px,
        var(--v-orange-base) 20px
      );
  }

  &.selected {
    background-color: var(--v-orange-base);
  }

  &.unavailable {
    background-color: #636363;
  }

  &.first-half::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    box-shadow: inset 0px -1px 0px #f7f7f7;
  }
}
</style>
