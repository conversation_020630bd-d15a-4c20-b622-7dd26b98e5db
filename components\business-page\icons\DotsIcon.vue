<template>
  <div>
    <v-img
      :src="require('~/assets/images/business-page/dots.svg')"
      contain
      :width="width"
      :options="{ rootMargin: '20%' }"
    ></v-img>
  </div>
</template>

<script>
export default {
  props: {
    width: {
      type: String,
      default: '180',
    },
  },
}
</script>

<style scoped>
.top-left {
  top: -32px;
  left: -40px;
}

.bottom-right {
  bottom: -40px;
  right: -40px;
}

.top-right {
  top: -32px;
  right: -40px;
}

.bottom-left {
  bottom: -40px;
  left: -40px;
}
</style>
