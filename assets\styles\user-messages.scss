@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.user-messages {
  --sidebar-width: 280px;

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    --sidebar-width: 255px;
  }

  margin-top: 10px;

  &-wrap {
    max-width: 1030px;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      display: flex;
    }

    & > div {
      width: 100%;
    }
  }

  &-title {
    font-size: 24px;
    line-height: 1.333;

    @media only screen and (max-width: $xxs-and-down) {
      font-size: 20px;
    }
  }

  .user-status {
    position: absolute;
    right: 0;
    bottom: 0;
  }

  &-content {
    width: calc(100% - var(--sidebar-width));
    padding-left: 36px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding-left: 20px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      width: 100%;
      padding-left: 0;
    }
  }

  &-sidebar {
    width: var(--sidebar-width);

    &-sticky {
      position: sticky;
      top: 80px;
    }

    .user-messages-tabs-nav {
      overflow: hidden;

      & > div {
        width: calc(100% + 15px);
        height: calc(100vh - 198px);
        padding-right: 15px;
        overflow-y: scroll;

        .nav-item {
          margin-bottom: 28px;

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            margin-bottom: 16px;
          }
        }

        .v-btn {
          padding: 0 10px 0 26px;
          border-radius: 20px;
          font-size: 18px;
          background-color: transparent !important;

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            padding: 0 10px;
            font-size: 16px;
          }

          &__content {
            justify-content: flex-start;
            color: var(--v-greyDark-base);
            text-align: left;
          }

          &::before {
            transition: none !important;
          }

          &.active {
            background: linear-gradient(
                126.15deg,
                rgba(128, 182, 34, 0.18) 0%,
                rgba(60, 135, 248, 0.18) 102.93%
            );

            .v-btn__content {
              color: var(--v-dark-base);
              font-weight: 600 !important;
            }

            &:focus,
            &:hover {
              &::before {
                display: none !important;
              }
            }
          }

          &:focus,
          &:hover {
            &::before {
              background: linear-gradient(
                  126.15deg,
                  rgba(128, 182, 34, 0.18) 0%,
                  rgba(60, 135, 248, 0.18) 102.93%
              );
              opacity: 0.6;
            }
          }
        }
      }
    }

    &-search {
      .v-input {
        border-radius: 8px !important;

        &.v-input--is-focused {
          .v-input__control {
            .v-input__slot {
              &::before {
                border-radius: 8px !important;
              }
            }
          }
        }
      }
    }

    &-show-more-btn {
      margin: 14px 0;

      .v-btn {
        padding: 0 10px 0 26px !important;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding: 0 10px !important;
        }

        @media #{map-get($display-breakpoints, 'sm-and-up')} {
          &__content {
            justify-content: flex-start !important;
          }
        }
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        max-width: 280px;
        margin-left: auto;
        margin-right: auto;
      }
    }
  }

  .nav-item-avatar {
    position: relative;
    filter: drop-shadow(0px 4px 5px rgba(0, 0, 0, 0.2));
    z-index: 2;

    .envelop {
      position: absolute;
      top: -3px;
      left: -14px;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        top: -2px;
        left: -10px;
      }
    }
  }

  .tabs-mobile {
    &.v-expansion-panels {
      border-radius: 0;

      & > .v-expansion-panel {
        margin-bottom: 16px;
        background-color: transparent !important;

        & > .v-expansion-panel-header {
          min-height: 48px;
          padding: 0 15px;

          @media only screen and (max-width: $xxs-and-down) {
            padding: 0 10px;
          }

          & > *:not(.v-expansion-panel-header__icon) {
            flex-grow: 0;
          }

          &--active {
            border-radius: 20px;
            background: linear-gradient(
                126.15deg,
                rgba(128, 182, 34, 0.18) 0%,
                rgba(60, 135, 248, 0.18) 102.93%
            );
          }
        }

        & > .v-expansion-panel-content {
          margin-top: 12px;
          background-color: #fff;
          box-shadow: 0 8px 17px rgba(17, 46, 90, 0.1);
          border-radius: 20px;

          & > .v-expansion-panel-content__wrap {
            padding: 24px 16px 32px;
          }
        }
      }
    }
  }
}
