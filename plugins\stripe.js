// plugins/stripe.client.js
export default async ({ app, route }, inject) => {
  // Only load Stripe if not on the home page
  console.log(route.name, 'route.name')
  if (route.name?.includes('teacher-slug')) {
    const { loadStripe } = await import('@stripe/stripe-js/pure')
    const stripe = await loadStripe(process.env.STRIPE_PUBLISHABLE_KEY)
    // this.$stripe = stripes
    inject('stripe', stripe)
  }
}
