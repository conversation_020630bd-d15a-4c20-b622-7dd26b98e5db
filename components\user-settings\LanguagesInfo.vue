<template>
  <user-setting-template
    v-if="item"
    :title="$t('languages')"
    :custom-valid="valid"
    :submit-func="submitData"
  >
    <div class="mb-4 mb-md-7">
      <v-row>
        <template v-if="isTeacher">
          <v-col class="col-12">
            <div class="input-wrap-title body-1 font-weight-medium mb-2">
              {{ $t('what_is_your_native_language') }}
            </div>
          </v-col>
          <v-col class="col-12 col-sm-10 col-md-6">
            <div class="input-wrap">
              <user-setting-autocomplete
                :items="item.languages"
                :selected-items="item.nativeLanguages"
                attach-id="native-language"
                :placeholder="$t('choose_language')"
                @change="addLanguage($event, 'nativeLanguages')"
              ></user-setting-autocomplete>
            </div>
          </v-col>
          <v-col class="col-12 col-md-6">
            <div
              v-if="item.nativeLanguages && item.nativeLanguages.length"
              class="chips"
            >
              <l-chip
                v-for="nativeLanguage in item.nativeLanguages"
                :key="nativeLanguage.id"
                :label="nativeLanguage.name"
                light
                class="mr-1"
                @click:close="resetLanguage(nativeLanguage, 'nativeLanguages')"
              ></l-chip>
            </div>
          </v-col>
        </template>
        <template v-if="isStudent">
          <v-col class="col-12">
            <div class="input-wrap-title body-1 font-weight-medium mb-2">
              {{ $t('what_language_would_you_like_to_learn_on_langu') }}
            </div>
          </v-col>
          <v-col class="col-12 col-sm-10 col-md-6">
            <div class="input-wrap">
              <user-setting-select
                :value="selectedLanguageToLearn"
                :items="languagesToLearn"
                attach-id="learn-language"
                :hide-details="false"
                :placeholder="$t('choose_language')"
                @change="updateValue($event.id, 'languageToLearn')"
              ></user-setting-select>
            </div>
          </v-col>
        </template>
      </v-row>
    </div>
    <div v-if="isTeacher" class="mb-4 mb-md-7">
      <v-row>
        <v-col class="col-12">
          <div class="input-wrap-title body-1 font-weight-medium mb-2">
            {{ $t('what_languages_would_you_like_to_teach_on_langu') }}
          </div>
        </v-col>
        <v-col class="col-12 col-sm-10 col-md-6">
          <div class="input-wrap">
            <user-setting-autocomplete
              :items="item.languages"
              :selected-items="item.languagesTaught"
              attach-id="teach-language"
              :placeholder="$t('choose_language')"
              @change="addTeachLanguage"
            ></user-setting-autocomplete>
          </div>
        </v-col>
        <v-col class="col-12 col-md-6">
          <div
            v-if="item.languagesTaught && item.languagesTaught.length"
            class="chips"
          >
            <l-chip
              v-for="taughtLanguage in item.languagesTaught"
              :key="taughtLanguage.id"
              :label="taughtLanguage.name"
              light
              class="mr-1"
              @click:close="resetLanguage(taughtLanguage, 'languagesTaught')"
            ></l-chip>
          </div>
        </v-col>
      </v-row>
    </div>
    <div>
      <v-row>
        <template v-if="isTeacher">
          <v-col class="col-12">
            <div class="input-wrap-title body-1 font-weight-medium mb-2">
              {{ $t('what_other_languages_do_you_speak') }}
            </div>
          </v-col>
          <v-col class="col-12 col-sm-10 col-md-6">
            <div class="input-wrap">
              <user-setting-autocomplete
                :items="item.languages"
                :selected-items="item.languagesSpoken"
                attach-id="other-speak-language"
                :placeholder="$t('choose_language')"
                @change="addLanguage($event, 'languagesSpoken')"
              ></user-setting-autocomplete>
            </div>
          </v-col>
          <v-col class="col-12 col-md-6">
            <div
              v-if="item.languagesSpoken && item.languagesSpoken.length"
              class="chips"
            >
              <l-chip
                v-for="spokenLanguage in item.languagesSpoken"
                :key="spokenLanguage.id"
                :label="spokenLanguage.name"
                light
                class="mr-1"
                @click:close="resetLanguage(spokenLanguage, 'languagesSpoken')"
              ></l-chip>
            </div>
          </v-col>
        </template>
        <template v-if="isStudent">
          <v-col class="col-12">
            <div class="input-wrap-title body-1 font-weight-medium mb-2">
              {{ $t('what_is_your_native_language') }}
            </div>
          </v-col>
          <v-col class="col-12 col-sm-10 col-md-6">
            <div class="input-wrap">
              <user-setting-autocomplete
                :items="item.languages"
                :selected-items="item.nativeLanguages"
                attach-id="other-speak-language"
                :placeholder="$t('choose_language')"
                @change="addLanguage($event, 'nativeLanguages')"
              ></user-setting-autocomplete>
            </div>
          </v-col>
          <v-col class="col-12 col-md-6">
            <div
              v-if="item.nativeLanguages && item.nativeLanguages.length"
              class="chips"
            >
              <l-chip
                v-for="nativeLanguage in item.nativeLanguages"
                :key="nativeLanguage.id"
                :label="nativeLanguage.name"
                light
                class="mr-1"
                @click:close="resetLanguage(nativeLanguage, 'nativeLanguages')"
              ></l-chip>
            </div>
          </v-col>
        </template>
      </v-row>
    </div>
  </user-setting-template>
</template>

<script>
import UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'
import UserSettingSelect from '@/components/user-settings/UserSettingSelect'
import UserSettingAutocomplete from '@/components/user-settings/UserSettingAutocomplete'
import LChip from '@/components/LChip'

export default {
  name: 'LanguagesInfo',
  components: {
    UserSettingTemplate,
    UserSettingSelect,
    UserSettingAutocomplete,
    LChip,
  },
  data() {
    return {
      key: 1,
      languagesToLearn: [
        {
          id: null,
          name: this.$t('all_languages'),
        },
      ],
    }
  },
  computed: {
    item() {
      return this.$store.state.settings.languagesItem
    },
    selectedLanguageToLearn() {
      return (
        this.languagesToLearn.find(
          (item) => item.id === this.item.languageToLearn
        ) || {
          id: null,
          name: this.$t('all_languages'),
        }
      )
    },
    isTeacher() {
      return this.$store.getters['user/isTeacher']
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
    valid() {
      return !!(
        this.isStudent ||
        (this.item.nativeLanguages.length && this.item.languagesTaught.length)
      )
    },
  },
  async mounted() {
    await this.$store.dispatch('settings/getLanguages')

    this.languagesToLearn = [...this.languagesToLearn, ...this.item?.languages]
  },
  methods: {
    addTeachLanguage(item) {
      const currentItems = [...this.item.languagesTaught]

      if (currentItems.length < 2) {
        currentItems.push(item)
      } else {
        currentItems.splice(1, 1, item)
      }

      this.$store.commit('settings/SET_LANGUAGES_ITEM', {
        ...this.item,
        languagesTaught: currentItems,
      })
    },
    addLanguage(item, property) {
      const currentItems = [...this.item[property]]

      currentItems.push(item)

      this.$store.commit('settings/SET_LANGUAGES_ITEM', {
        ...this.item,
        [property]: currentItems,
      })
    },
    resetLanguage(item, property) {
      this.$store.commit('settings/SET_LANGUAGES_ITEM', {
        ...this.item,
        [property]: this.item[property].filter((el) => el.id !== item.id),
      })
    },
    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_LANGUAGES_ITEM', {
        [property]: value,
      })
    },
    submitData() {
      this.$store.dispatch('settings/updateLanguages')
    },
  },
}
</script>
