@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.features {
  padding-top: 85px;

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding-top: 60px;
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    &-button {
      text-align: center;

      .v-btn {
        min-width: 285px !important;
      }
    }
  }

  &-item {
    margin-bottom: 38px;

    @media only screen and (max-width: $xsm-and-down) {
      margin-bottom: 15px;
    }

    &-wrap {
      position: relative;
      padding-left: 140px;

      @media #{map-get($display-breakpoints, 'lg-and-down')} {
        padding-left: 130px;
      }

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        padding-left: 100px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        padding-left: 120px;
      }

      @media only screen and (max-width: $xsm-and-down) {
        max-width: 355px;
        margin-left: auto;
        margin-right: auto;
        padding-left: 0;
        padding-top: 86px;
        text-align: center;
      }
    }

    &-image {
      position: absolute;
      left: 40px;
      top: 8px;

      @media #{map-get($display-breakpoints, 'lg-and-down')} {
        left: 30px;
      }

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        left: 0;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
      }

      @media only screen and (max-width: $xsm-and-down) {
        left: 50%;
        top: 0;
        transform: translate(-50%,0);
      }
    }

    &-title {
      max-width: 396px;
      font-size: 24px;
      font-weight: 700;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        left: 0;
        margin-bottom: 4px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 20px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        max-width: 100%;
      }
    }

    &-text {
      max-width: 358px;
      font-size: 16px;
      color: var(--v-grey-base);
      line-height: 1.5;

      @media #{map-get($display-breakpoints, 'xs-only')} {
        max-width: 100%;
      }
    }

    &-i2,
    &-i4 {
      @media #{map-get($display-breakpoints, 'md-and-up')} {
        .features-item-wrap {
          padding-left: 112px;
        }

        .features-item-image {
          left: 0;
        }
      }
    }

    &-i4 {
      @media #{map-get($display-breakpoints, 'xs-only')} {
        margin-bottom: 20px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        margin-bottom: 5px;
      }
    }
  }
}

.how-works,
.about {
  &-item {
    &-title {
      margin-bottom: 8px;
      font-size: 24px;
      font-weight: 700;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        font-size: 22px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        font-size: 20px;
      }

      @media only screen and (max-width: $xsm-and-down) {
        margin-bottom: 4px;
      }
    }

    &-text {
      line-height: 1.55;
      opacity: .5;

      @media only screen and (max-width: $xsm-and-down) {
        font-size: 16px;
      }
    }
  }
}

.find-teacher {
  position: relative;
  margin-top: -135px;
  padding: 196px 0 146px;

  @media #{map-get($display-breakpoints, 'md-and-up')} {
    .section-bg {
      .v-image__image {
        background-size: 100% 100% !important;
      }
    }
  }

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    padding: 96px 0 46px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    margin-top: 0;
  }

  &-images {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      margin-bottom: 25px;
    }

    &-item {
      max-width: 145px;
      margin: 0 15px 15px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        max-width: 125px;
      }

      @media only screen and (max-width: $xsm-and-down) {
        max-width: 106px;
        margin: 0 5px 10px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        max-width: 85px;
      }
    }
  }

  &-content {
    position: relative;
    max-width: 504px;
    margin: 0 auto;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      margin: 100px auto;
    }
  }

  &-title {
    margin-bottom: 8px;
    font-size: 24px;
    font-weight: 700;
    color: var(--v-orangeLight-base);

    @media only screen and (max-width: $xsm-and-down) {
      font-size: 22px;
    }
  }

  &-text {
    color: #fff;
    line-height: 1.77;

    @media only screen and (max-width: $xsm-and-down) {
      font-size: 16px;
    }
  }

  &-button {
    margin-top: 64px;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      margin-top: 54px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      margin-top: 40px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      .v-btn {
        width: 100% !important;
      }
    }
  }
}

.virtual-classroom {
  position: relative;
  padding: 165px 0;
  color: #fff;

  @media #{map-get($display-breakpoints, 'lg-and-down')} {
    padding: 195px 0;
  }

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    padding: 200px 0 195px;

    .section-bg .v-image__image {
      background-position: 30% center !important;
    }
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding: 150px 0 160px;

    .section-bg .v-image__image {
      background-position: 24% center !important;
    }
  }

  @media only screen and (max-width: $xxs-and-down) {
    padding-bottom: 260px;
  }

  .section-head {
    max-width: 550px;
    margin-bottom: 60px;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      margin-bottom: 25px;
    }
  }

  &-image {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 688px;

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      min-height: 580px;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      min-height: auto;
      justify-content: center;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      display: block;
      max-width: 870px;
    }

    .circle {
      position: absolute;
      top: 50%;
      left: -215px;
      width: 100%;
      height: 100%;
      transform: translateY(-50%);

      @media #{map-get($display-breakpoints, 'lg-and-down')} {
        left: -270px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        left: -100px;
      }
    }

    & > div {
      position: relative;
      left: -40px;

      @media #{map-get($display-breakpoints, 'lg-and-down')} {
        left: -130px;
      }

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        left: -50px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        left: auto;
        margin: 0 auto;
        padding-right: 8%;
      }

      .laptop {
        max-width: 802px;

        @media #{map-get($display-breakpoints, 'lg-and-down')} {
          max-width: 706px;
        }

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          max-width: 590px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          max-width: 802px;
        }
      }

      .whiteboard,
      .exercise,
      .stream {
        position: absolute;
        width: 100%;
        transition: transform .3s;

        @media #{map-get($display-breakpoints, 'md-and-up')} {
          &:hover {
            transform: scale(1.12);
          }
        }
      }

      .whiteboard {
        max-width: 476px;
        left: 0;
        top: 25px;

        @media #{map-get($display-breakpoints, 'lg-and-down')} {
          max-width: 420px;
        }

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          max-width: 345px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          width: 55%;
          top: 7%;
        }
      }

      .stream {
        max-width: 185px;
        left: 402px;
        top: 215px;

        @media #{map-get($display-breakpoints, 'lg-and-down')} {
          max-width: 158px;
          left: 368px;
          top: 200px;
        }

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          max-width: 138px;
          left: 328px;
          top: 196px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          width: 22%;
          left: 46%;
          top: 46%;
        }
      }

      .exercise {
        max-width: 81px;
        left: 460px;
        top: 80px;

        @media #{map-get($display-breakpoints, 'lg-and-down')} {
          max-width: 72px;
          left: 405px;
          top: 75px;
        }

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          max-width: 60px;
          left: 55%;
          top: 80px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          left: 50%;
          width: 10%;
          max-width: 81px;
          top: 18%;
        }
      }

      .mobile {
        position: absolute;
        right: -90px;
        bottom: -75px;

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          width: 28%;
          right: -25px;
          bottom: -62px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          right: 0;
          bottom: -15%;
        }
      }
    }
  }

  &-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding-left: 90px;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      padding-left: 0;
    }
  }

  &-title {
    margin-bottom: 24px;
    font-size: 32px;
    font-weight: 600;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      margin-top: 65px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin-top: 125px;
      text-align: center;
    }

    span {
      color: var(--v-orange-base);
      background: linear-gradient(-90deg, var(--v-orange-base), var(--v-green-base));
      background: -moz-linear-gradient(-90deg, var(--v-orange-base), var(--v-green-base));
      background: -webkit-linear-gradient(-90deg, var(--v-orange-base), var(--v-green-base));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  &-text {
    max-width: 378px;
    font-weight: 300;
    line-height: 1.77;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      max-width: 500px;
      margin: 0 auto;
    }

    ul {
      max-width: 326px;
      padding-left: 20px !important;
    }
  }

  &-video-1,
  &-video-2 {
    position: absolute;
  }

  &-video-1 {
    left: 60px;
    top: -135px;

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      top: -165px;
      left: 40px;
      max-width: 320px;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      top: -185px;
      left: 70px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: 320px;
      left: auto;
      top: auto;
      right: 0;
      bottom: -245px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      max-width: 285px;
    }
  }

  &-video-2 {
    left: 40px;
    bottom: -255px;

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      left: 80px;
      bottom: -255px;
      max-width: 420px;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      left: auto;
      right: -15px;
      bottom: -295px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: 240px;
      top: -50px;
      left: 0;
      right: auto;
      bottom: auto;
    }

    @media only screen and (max-width: $xxs-and-down) {
      top: -30px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}

.company {
  padding: 30px 0 190px;

  @media #{map-get($display-breakpoints, 'lg-and-down')} {
    padding-bottom: 75px;
  }

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    padding-bottom: 110px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding: 90px 0 60px;
  }

  @media only screen and (max-width: $xxs-and-down) {
    padding: 90px 0 45px;
  }

  .section-head {
    margin-bottom: 158px;

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      margin-bottom: 106px;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      margin-bottom: 85px;
    }
  }

  &-list {
    display: flex;
    flex-wrap: wrap;
  }

  &-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 25%;
    height: 125px;
    padding: 0 15px;
    opacity: .3;

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      height: 118px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: 50%;
      height: 98px;
      margin-bottom: 30px;
      opacity: .7;
    }

    @media only screen and (max-width: $xsm-and-down) {
      height: 110px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      height: 70px;
    }

    &:hover {
      opacity: 1;
    }

    .v-image {
      max-width: 215px;
      width: auto;
      height: auto;
      max-height: 100%;

      @media #{map-get($display-breakpoints, 'lg-and-down')} {
        max-width: 208px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        max-width: 130px;
      }
    }
  }
}

.start {
  position: relative;
  height: 1px;
  min-height: 748px;
  color: #fff;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    min-height: 660px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    height: auto;
    min-height: auto;
    padding: 80px 0;
    overflow: hidden;

    .section-bg {
      .v-image__image {
        background-position: 45% center !important;
      }
    }
  }

  @media #{map-get($display-breakpoints, 'sm-and-up')} {
    &-title--mobile {
      display: none;
    }
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    &-title--mobile {
      position: relative;
      max-width: 360px;
      margin: 0 auto 48px;
      font-size: 22px;
      line-height: 1.45;
      text-align: center;
    }

    &-text {
      display: none;
    }
  }

  &-image {
    .v-image {
      max-width: 540px !important;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      display: flex;
      justify-content: center;

      .v-image {
        max-width: 392px !important;
      }
    }

    @media only screen and (max-width: $xxs-and-down) {
      justify-content: flex-end;

      .v-image {
        position: relative;
        right: -95px;
        max-width: 392px !important;
      }
    }
  }

  &-content {
    position: relative;
    margin-left: 120px;
    font-size: 32px;
    line-height: 1.3;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      margin-left: 0;
      font-size: 28px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 24px;
    }
  }

  &-button {
    margin-top: 54px;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      display: flex;
      justify-content: center;
    }

    .v-btn {
      @media only screen and (max-width: $xxs-and-down) {
        min-width: 100% !important;
        width: 100% !important;
      }
    }
  }
}
