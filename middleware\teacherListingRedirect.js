export default function ({ redirect, route }) {
  const ids = [51, 52, 53, 54, 56, 58, 59, 60, 61, 63, 64, 65, 66] // array of hidden specialities
  const { params } = route.params
  const paramsObj = {}

  params.split(';').forEach((item) => {
    let arr = item.split(',')

    const property = arr[0]

    arr.splice(0, 1)
    arr = arr.map((item) => +item)

    paramsObj[property] = arr
  })

  if (
    paramsObj.speciality &&
    Array.isArray(paramsObj.speciality) &&
    ids.some((id) => paramsObj.speciality.includes(id))
  ) {
    if (paramsObj.speciality.length === 1) {
      redirect(301, '/teacher-listing')

      return
    }

    paramsObj.speciality = [...paramsObj.speciality].filter(
      (id) => !ids.includes(id)
    )

    const properties = Object.keys(paramsObj)

    let paramsWithoutHiddenSpecialities = ''

    for (let i = 0; i < properties.length; i++) {
      paramsWithoutHiddenSpecialities +=
        properties[i] + ',' + paramsObj[properties[i]].toString()

      if (i < properties.length - 1) {
        paramsWithoutHiddenSpecialities += ';'
      }
    }

    redirect(301, '/teacher-listing/1/' + paramsWithoutHiddenSpecialities)
  }
}
