<svg width="1232" height="330" viewBox="0 0 1232 330" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1223.75 269.974C1074.24 295.337 1090.26 169.858 906.715 104.449C723.168 39.0391 515.593 161.849 452.186 199.226C252.477 316.949 96.4396 139.823 78.4187 74.4139" stroke="#F1C4B0" stroke-width="6" stroke-dasharray="15 15"/>
<g filter="url(#filter0_d)">
<ellipse cx="71.7442" cy="71.5585" rx="66.7442" ry="66.5581" fill="white"/>
</g>
<g clip-path="url(#clip0)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M101.652 51.9667V95.1966C101.652 97.8464 99.4823 100.016 96.8285 100.016H37.8561C35.202 100.016 33.0325 97.8464 33.0325 95.1966V51.9667H101.652Z" fill="url(#paint0_linear)" fill-opacity="0.45"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M38.3236 39.9103H96.3566C99.2675 39.9103 101.652 42.2944 101.652 45.2054V52.7123H33.0325V45.2054C33.0325 42.2944 35.4126 39.9103 38.3236 39.9103V39.9103Z" fill="#2D2D2D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M46.1657 79.8516H60.9933C61.6141 79.8516 62.1181 80.3214 62.1181 80.9002V94.7241C62.1181 95.2976 61.6141 95.7727 60.9933 95.7727H46.1657C45.5504 95.7727 45.0464 95.2976 45.0464 94.7241V80.9002C45.0464 80.3214 45.5502 79.8516 46.1657 79.8516ZM46.1657 57.061H60.9933C61.6141 57.061 62.1181 57.5358 62.1181 58.1093V71.9332C62.1181 72.5118 61.6141 72.9818 60.9933 72.9818H46.1657C45.5504 72.9818 45.0464 72.512 45.0464 71.9332V58.1093C45.0464 57.5356 45.5502 57.061 46.1657 57.061ZM71.8103 57.061H86.6381C87.2535 57.061 87.7627 57.5358 87.7627 58.1093V71.9332C87.7627 72.5118 87.2535 72.9818 86.6381 72.9818H71.8103C71.1898 72.9818 70.6856 72.512 70.6856 71.9332V58.1093C70.6856 57.5356 71.1896 57.061 71.8103 57.061Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M82.7616 107.137C91.0393 107.137 97.788 100.384 97.788 92.1108C97.788 83.8327 91.0393 77.0844 82.7616 77.0844C74.488 77.0844 67.7395 83.8327 67.7395 92.1108C67.7395 100.384 74.488 107.137 82.7616 107.137Z" fill="#2D2D2D"/>
<g clip-path="url(#clip1)">
<path d="M54.9644 66.0609H52.4816C50.0396 66.0609 48.053 68.0476 48.053 70.4895V73.4171H59.393V70.4895C59.393 68.0476 57.4064 66.0609 54.9644 66.0609Z" fill="#2D2D2D"/>
<path d="M53.7236 58.396C51.8353 58.396 50.2991 59.9323 50.2991 61.8206C50.2991 63.7089 51.8353 65.2451 53.7236 65.2451C55.6119 65.2451 57.1482 63.7089 57.1482 61.8205C57.1482 59.9323 55.6119 58.396 53.7236 58.396Z" fill="#2D2D2D"/>
</g>
<g clip-path="url(#clip2)">
<path d="M80.3272 66.0609H77.8444C75.4024 66.0609 73.4158 68.0476 73.4158 70.4895V73.4171H84.7558V70.4895C84.7558 68.0476 82.7692 66.0609 80.3272 66.0609Z" fill="#2D2D2D"/>
<path d="M79.0864 58.396C77.1981 58.396 75.6619 59.9323 75.6619 61.8206C75.6619 63.7089 77.1981 65.2451 79.0864 65.2451C80.9747 65.2451 82.511 63.7089 82.511 61.8205C82.511 59.9323 80.9747 58.396 79.0864 58.396Z" fill="#2D2D2D"/>
</g>
<g clip-path="url(#clip3)">
<path d="M54.9644 88.754H52.4816C50.0396 88.754 48.053 90.7407 48.053 93.1826V96.1102H59.393V93.1826C59.393 90.7407 57.4064 88.754 54.9644 88.754Z" fill="#2D2D2D"/>
<path d="M53.7236 81.0891C51.8353 81.0891 50.2991 82.6254 50.2991 84.5137C50.2991 86.402 51.8353 87.9382 53.7236 87.9382C55.6119 87.9382 57.1482 86.402 57.1482 84.5137C57.1482 82.6254 55.6119 81.0891 53.7236 81.0891Z" fill="#2D2D2D"/>
</g>
<g clip-path="url(#clip4)">
<path d="M91.4538 87.6213L89.9312 86.0987C89.7225 85.8898 89.4686 85.7853 89.1701 85.7853C88.8714 85.7853 88.6175 85.8898 88.4087 86.0987L81.0641 93.4544L77.7725 90.1516C77.5635 89.9426 77.3098 89.8382 77.0113 89.8382C76.7127 89.8382 76.4589 89.9426 76.2499 90.1516L74.7273 91.6742C74.5183 91.8832 74.4138 92.137 74.4138 92.4356C74.4138 92.734 74.5183 92.9881 74.7273 93.197L78.7801 97.2497L80.3028 98.7723C80.5117 98.9814 80.7655 99.0858 81.0641 99.0858C81.3626 99.0858 81.6164 98.9811 81.8254 98.7723L83.3481 97.2497L91.4538 89.144C91.6627 88.935 91.7673 88.6812 91.7673 88.3826C91.7675 88.0842 91.6627 87.8303 91.4538 87.6213Z" fill="white"/>
</g>
</g>
<g filter="url(#filter1_d)">
<ellipse cx="333.382" cy="258.442" rx="66.7442" ry="66.5581" fill="white"/>
</g>
<g clip-path="url(#clip5)">
<path d="M332.715 275.661H294.671V223.523C294.671 221.866 296.014 220.523 297.671 220.523H332.715V255.32V275.661Z" fill="url(#paint1_linear)" fill-opacity="0.45"/>
<path d="M332.715 220.523H367.759C369.416 220.523 370.759 221.866 370.759 223.523V275.661H332.715V220.523Z" fill="url(#paint2_linear)" fill-opacity="0.45"/>
<path d="M352.319 294.382C352.319 293.151 351.309 292.177 350.107 291.911C346.729 291.165 343.859 288.745 342.614 285.428L341.22 281.711H332.715V296.611H350.09C351.321 296.611 352.319 295.613 352.319 294.382V294.382Z" fill="#2D2D2D"/>
<path d="M313.329 294.414C313.329 293.183 314.338 292.209 315.541 291.944C318.918 291.197 321.789 288.778 323.033 285.46L324.427 281.743H332.933V296.644H315.558C314.327 296.644 313.329 295.646 313.329 294.414V294.414Z" fill="#2D2D2D"/>
<path d="M332.959 285.81H304.671C299.148 285.81 294.671 281.333 294.671 275.81V275.009H332.959L335.469 280.66L332.959 285.81Z" fill="#2D2D2D"/>
<path d="M332.715 275.008H370.759V275.81C370.759 281.332 366.282 285.81 360.759 285.81H332.715V275.008Z" fill="#2D2D2D"/>
<path d="M347.128 242.174C347.128 241.07 348.023 240.174 349.128 240.174H349.586C350.69 240.174 351.586 241.07 351.586 242.174V247.847C351.586 248.951 350.69 249.847 349.586 249.847H349.128C348.023 249.847 347.128 248.951 347.128 247.847V242.174Z" fill="white"/>
<path d="M313.005 237.134C311.35 237.801 311.331 240.136 312.974 240.831L323.207 245.158C323.523 245.292 323.868 245.342 324.208 245.304L332.714 244.353L334.022 244.353C335.327 244.353 336.283 243.122 335.959 241.857L332.715 229.187L313.005 237.134Z" fill="#2D2D2D"/>
<path d="M352.459 240.828C354.098 240.13 354.075 237.799 352.423 237.133L332.715 229.187V242.516C332.715 243.555 333.51 244.421 334.545 244.509L341.781 245.124C342.106 245.152 342.433 245.099 342.734 244.971L352.459 240.828Z" fill="#2D2D2D"/>
<path d="M306.377 261.552C305.146 261.552 304.148 262.55 304.148 263.781V263.781C304.148 265.012 305.146 266.01 306.377 266.01H332.715L335.006 264.012L332.715 261.552H306.377Z" fill="white"/>
<path d="M332.715 261.552H358.576C359.807 261.552 360.805 262.55 360.805 263.781V263.781C360.805 265.012 359.807 266.01 358.576 266.01H332.715V261.552Z" fill="white"/>
<path d="M323.574 241.055L323.433 241.114C322.685 241.424 322.198 242.153 322.198 242.962V252.072C322.198 253.177 323.093 254.072 324.198 254.072H332.714L335.29 247.792C335.48 247.33 335.49 246.814 335.318 246.346L333.195 240.538C332.906 239.749 332.155 239.216 331.317 239.266C328.689 239.422 326.078 240.019 323.574 241.055Z" fill="#2D2D2D"/>
<path d="M343.231 242.962C343.231 242.153 342.744 241.424 341.997 241.115L341.855 241.056C339.542 240.098 337.137 239.516 334.712 239.31C333.612 239.216 332.715 240.12 332.715 241.225V254.073H341.231C342.336 254.073 343.231 253.177 343.231 252.073V242.962Z" fill="#2D2D2D"/>
<path d="M308.32 259.983C308.32 258.751 309.318 257.753 310.549 257.753V257.753C311.78 257.753 312.778 258.751 312.778 259.983V267.722C312.778 268.954 311.78 269.952 310.549 269.952V269.952C309.318 269.952 308.32 268.954 308.32 267.722V259.983Z" fill="#2D2D2D"/>
</g>
<g filter="url(#filter2_d)">
<ellipse cx="1159.68" cy="258.442" rx="66.7442" ry="66.5581" fill="white"/>
</g>
<path d="M1159.01 275.055H1120.96V222.917C1120.96 221.26 1122.31 219.917 1123.96 219.917H1159.01V254.713V275.055Z" fill="url(#paint3_linear)" fill-opacity="0.45"/>
<path d="M1159.01 219.917H1194.05C1195.71 219.917 1197.05 221.26 1197.05 222.917V275.055H1159.01V219.917Z" fill="url(#paint4_linear)" fill-opacity="0.45"/>
<path d="M1132.54 262.427C1131.3 262.427 1130.31 263.425 1130.31 264.656V264.656C1130.31 265.887 1131.3 266.885 1132.54 266.885H1158.87L1161.16 264.887L1158.87 262.427H1132.54Z" fill="white"/>
<path d="M1158.87 262.427H1184.73C1185.97 262.427 1186.96 263.425 1186.96 264.656V264.656C1186.96 265.887 1185.97 266.885 1184.73 266.885H1158.87V262.427Z" fill="white"/>
<path d="M1161.18 260.857C1161.18 259.626 1162.18 258.628 1163.41 258.628V258.628C1164.64 258.628 1165.64 259.626 1165.64 260.857V268.597C1165.64 269.828 1164.64 270.827 1163.41 270.827V270.827C1162.18 270.827 1161.18 269.828 1161.18 268.597V260.857Z" fill="#2D2D2D"/>
<path d="M1178.61 293.776C1178.61 292.545 1177.6 291.571 1176.4 291.305C1173.02 290.559 1170.15 288.139 1168.91 284.822L1167.51 281.105H1159.01V296.005H1176.38C1177.61 296.005 1178.61 295.007 1178.61 293.776V293.776Z" fill="#2D2D2D"/>
<path d="M1139.62 293.809C1139.62 292.577 1140.63 291.603 1141.83 291.338C1145.21 290.591 1148.08 288.172 1149.33 284.854L1150.72 281.137H1159.23V296.038H1141.85C1140.62 296.038 1139.62 295.04 1139.62 293.809V293.809Z" fill="#2D2D2D"/>
<path d="M1159.25 285.203H1130.96C1125.44 285.203 1120.96 280.726 1120.96 275.203V274.402H1159.25L1161.76 280.054L1159.25 285.203Z" fill="#2D2D2D"/>
<path d="M1159.01 274.402H1197.05V275.203C1197.05 280.726 1192.57 285.203 1187.05 285.203H1159.01V274.402Z" fill="#2D2D2D"/>
<path d="M1165.01 243.702C1165.01 237.913 1169.84 233.225 1175.77 233.266C1181.65 233.306 1186.42 238.042 1186.37 243.785C1186.34 247.403 1184.43 250.582 1181.55 252.433C1181.41 252.52 1181.33 252.669 1181.33 252.827V256.897C1181.33 257.204 1180.99 257.394 1180.72 257.237L1175.5 254.197C1175.43 254.156 1175.35 254.134 1175.27 254.131C1169.57 253.914 1165.01 249.328 1165.01 243.702V243.702Z" fill="white"/>
<path d="M1160.94 237.902C1160.94 230.888 1155.23 225.207 1148.2 225.256C1141.24 225.306 1135.59 231.044 1135.65 238.003C1135.68 242.386 1137.95 246.239 1141.36 248.481C1141.52 248.586 1141.62 248.767 1141.62 248.958V253.891C1141.62 254.262 1142.02 254.493 1142.34 254.302L1148.52 250.619C1148.6 250.569 1148.7 250.543 1148.79 250.539C1155.55 250.275 1160.94 244.719 1160.94 237.902Z" fill="white"/>
<path d="M1154.64 241.615L1149.64 230.813C1149.4 230.287 1148.87 229.951 1148.29 229.951C1147.71 229.951 1147.19 230.287 1146.94 230.813C1146.94 230.813 1142.29 241.214 1141.95 241.615C1141.6 242.361 1141.93 243.245 1142.67 243.59C1143.42 243.935 1144.3 243.61 1144.65 242.864L1145.63 240.726H1150.95L1151.94 242.864C1152.29 243.619 1153.18 243.928 1153.92 243.59C1154.66 243.245 1154.99 242.361 1154.64 241.615V241.615ZM1147.01 237.752L1148.29 234.979L1149.58 237.752H1147.01Z" fill="#2D2D2D"/>
<path d="M1181.31 239.41H1177.14V238.271C1177.14 237.718 1176.66 237.27 1176.08 237.27C1175.49 237.27 1175.02 237.718 1175.02 238.271V239.41H1171.41C1170.83 239.41 1170.35 239.859 1170.35 240.412C1170.35 240.965 1170.83 241.413 1171.41 241.413H1177.81C1177.24 242.438 1176.42 243.581 1175.23 244.629C1174.75 244.142 1174.32 243.608 1173.95 243.032C1173.64 242.559 1172.99 242.41 1172.49 242.697C1171.99 242.985 1171.83 243.601 1172.14 244.073C1172.55 244.718 1173.03 245.317 1173.56 245.869C1172.85 246.309 1172.09 246.683 1171.29 246.982C1170.75 247.186 1170.48 247.768 1170.7 248.283C1170.92 248.803 1171.54 249.044 1172.07 248.845C1173.18 248.432 1174.21 247.9 1175.16 247.261C1176.41 248.167 1177.81 248.837 1179.34 249.246C1179.88 249.394 1180.48 249.095 1180.65 248.557C1180.81 248.025 1180.48 247.471 1179.92 247.32C1178.81 247.022 1177.78 246.558 1176.85 245.944C1178.5 244.428 1179.54 242.761 1180.16 241.413H1181.31C1181.89 241.413 1182.37 240.965 1182.37 240.412C1182.37 239.859 1181.89 239.41 1181.31 239.41V239.41Z" fill="#2D2D2D"/>
<g filter="url(#filter3_d)">
<ellipse cx="844.642" cy="71.5581" rx="66.7442" ry="66.5581" fill="white"/>
</g>
<g clip-path="url(#clip6)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M874.55 51.7848V95.0148C874.55 97.6645 872.381 99.8342 869.727 99.8342H810.754C808.1 99.8342 805.931 97.6645 805.931 95.0148V51.7848H874.55Z" fill="url(#paint5_linear)" fill-opacity="0.45"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M811.222 39.7284H869.255C872.166 39.7284 874.55 42.1125 874.55 45.0235V52.5304H805.931V45.0235C805.931 42.1125 808.311 39.7284 811.222 39.7284V39.7284Z" fill="#2D2D2D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M813.989 79.1079H825.764C826.257 79.1079 826.657 79.5081 826.657 80.0011V91.7755C826.657 92.2639 826.257 92.6686 825.764 92.6686H813.989C813.501 92.6686 813.101 92.2639 813.101 91.7755V80.0011C813.101 79.5081 813.501 79.1079 813.989 79.1079ZM813.989 59.6963H825.764C826.257 59.6963 826.657 60.1007 826.657 60.5891V72.3635C826.657 72.8563 826.257 73.2567 825.764 73.2567H813.989C813.501 73.2567 813.101 72.8565 813.101 72.3635V60.5891C813.101 60.1005 813.501 59.6963 813.989 59.6963ZM834.353 59.6963H846.128C846.616 59.6963 847.021 60.1007 847.021 60.5891V72.3635C847.021 72.8563 846.616 73.2567 846.128 73.2567H834.353C833.86 73.2567 833.46 72.8565 833.46 72.3635V60.5891C833.46 60.1005 833.86 59.6963 834.353 59.6963ZM854.713 59.6963H866.487C866.98 59.6963 867.38 60.1007 867.38 60.5891V72.3635C867.38 72.8563 866.98 73.2567 866.487 73.2567H854.713C854.224 73.2567 853.82 72.8565 853.82 72.3635V60.5891C853.82 60.1005 854.224 59.6963 854.713 59.6963ZM834.353 79.1079H846.128C846.616 79.1079 847.021 79.5081 847.021 80.0011V91.7755C847.021 92.2639 846.616 92.6686 846.128 92.6686H834.353C833.86 92.6686 833.46 92.2639 833.46 91.7755V80.0011C833.46 79.5081 833.86 79.1079 834.353 79.1079Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M866.993 108.352C875.27 108.352 882.019 101.599 882.019 93.3257C882.019 85.0477 875.27 78.2993 866.993 78.2993C858.719 78.2993 851.97 85.0477 851.97 93.3257C851.97 101.599 858.719 108.352 866.993 108.352Z" fill="#2D2D2D"/>
<path d="M866.348 84.1978C866.348 83.5827 866.845 83.0814 867.46 83.0814C868.079 83.0814 868.576 83.5827 868.576 84.1978V94.3082C868.576 94.6072 868.458 94.8809 868.26 95.0832L861.242 103.34C860.846 103.808 860.147 103.867 859.675 103.47C859.207 103.075 859.148 102.371 859.544 101.903L866.348 93.9037V84.1978H866.348Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d" x="0" y="0.000488281" width="143.488" height="143.116" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="261.637" y="186.884" width="143.488" height="143.116" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter2_d" x="1087.93" y="186.884" width="143.488" height="143.116" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter3_d" x="772.898" y="0" width="143.488" height="143.116" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="70.4196" y1="31.438" x2="70.9319" y2="109.545" gradientUnits="userSpaceOnUse">
<stop stop-color="#AB135C"/>
<stop offset="0.739583" stop-color="#F7AD48"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="317.645" y1="196.965" x2="318.743" y2="286.585" gradientUnits="userSpaceOnUse">
<stop stop-color="#AB135C"/>
<stop offset="0.739583" stop-color="#F7AD48"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="353.443" y1="196.965" x2="354.66" y2="286.582" gradientUnits="userSpaceOnUse">
<stop stop-color="#AB135C"/>
<stop offset="0.739583" stop-color="#F7AD48"/>
</linearGradient>
<linearGradient id="paint3_linear" x1="1143.94" y1="196.359" x2="1145.03" y2="285.979" gradientUnits="userSpaceOnUse">
<stop stop-color="#AB135C"/>
<stop offset="0.739583" stop-color="#F7AD48"/>
</linearGradient>
<linearGradient id="paint4_linear" x1="1179.73" y1="196.359" x2="1180.95" y2="285.976" gradientUnits="userSpaceOnUse">
<stop stop-color="#AB135C"/>
<stop offset="0.739583" stop-color="#F7AD48"/>
</linearGradient>
<linearGradient id="paint5_linear" x1="843.318" y1="31.2561" x2="843.83" y2="109.363" gradientUnits="userSpaceOnUse">
<stop stop-color="#AB135C"/>
<stop offset="0.739583" stop-color="#F7AD48"/>
</linearGradient>
<clipPath id="clip0">
<rect width="76.0884" height="76.0884" fill="white" transform="translate(33.0325 33.0332)"/>
</clipPath>
<clipPath id="clip1">
<rect width="14.6837" height="14.6837" fill="white" transform="translate(46.3813 58.396)"/>
</clipPath>
<clipPath id="clip2">
<rect width="14.6837" height="14.6837" fill="white" transform="translate(71.7441 58.396)"/>
</clipPath>
<clipPath id="clip3">
<rect width="14.6837" height="14.6837" fill="white" transform="translate(46.3813 81.0891)"/>
</clipPath>
<clipPath id="clip4">
<rect width="17.3535" height="17.3535" fill="white" transform="translate(74.4138 83.7588)"/>
</clipPath>
<clipPath id="clip5">
<rect width="76.0884" height="76.0884" fill="white" transform="translate(294.671 220.523)"/>
</clipPath>
<clipPath id="clip6">
<rect width="76.0884" height="76.0884" fill="white" transform="translate(805.931 32.8513)"/>
</clipPath>
</defs>
</svg>
