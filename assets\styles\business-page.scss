@import '~vuetify/src/styles/settings/_variables';
@import '~/assets/styles/vars';

.row:not(.wrapper) {
  margin: -12.5px;
}

.col {
  padding: 12.5px;
}

.container-business {
  max-width: 1160px;
}

.intro {
  position: relative;
  height: calc(100vh - 55px);
  min-height: 680px;
  max-height: 740px;
  padding-bottom: 147px;

  @media #{map-get($display-breakpoints, 'lg-and-up')} {
    max-height: 770px;
  }

  @media only screen and (max-width: $mac-13-and-down) {
    max-height: 670px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    max-height: 770px;
    padding-bottom: 110px;
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    max-height: 550px;
    padding-bottom: 90px;
  }

  &-bg {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 147px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      height: 110px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      height: 90px;
    }

    svg {
      position: absolute;
      width: 1920px;
      height: 147px;
      top: 0;
      left: 50%;
      transform: translateX(-50%);

      @media #{map-get($display-breakpoints, 'lg-and-up')} {
        width: 100%;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        height: 110px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        height: 90px;
      }
    }
  }

  &-helper {
    height: 100%;
    background-color: var(--v-darkLight-base);
    z-index: 2;
  }

  .container, .row, .col, .container-business, .intro-content {
    height: inherit;
  }

  &-content {
    width: 55%;
    max-width: 568px;
  }

  &-title {
    margin-top: 0;
    margin-bottom: 0;
    background: linear-gradient(107deg, #80B622 -62.4%, #71AB53 -16.48%, #66A474 13.63%, #589B9F 53.8%, #4E94BF 88.6%, #3C87F8 139.28%);    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 44px;
    font-weight: 700;
    line-height: 1.125;

    @media only screen and (max-width: $mac-13-and-down) {
      font-size: 36px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin-top: 115px;
      font-size: 38px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-top: 30px;
      font-size: 28px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      font-size: 24px;
    }
  }

  &-text {
    color: white;
    font-size: 18px;
    margin: 20px 0 40px;
    font-weight: 500;
    line-height: 1.4;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 16px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin: 20px 0 0;
      font-size: 16px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      font-size: 14px;
    }
  }

  &-img {
    position: absolute;
    top: 25px;
    right: 0;
    width: 55%;
    max-width: 880px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: 380px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      max-width: 280px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      max-width: 231px;
    }

    &-helper {
      position: absolute;
      top: 0;
      right: 0;
      height: 0;
      width: 100%;
      padding-bottom: 65.93%;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        padding-bottom: 154.54%;
      }
    }

    svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  button {
    max-width: 380px;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-top: -30px;
    }
  }
}

.title-decoration {
  position: relative;
  padding-bottom: 16px;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: calc(50% - 31px);
    width: 62px;
    height: 2px;
    border-radius: 2px;
    background: linear-gradient(106deg, #80B622 -67.76%, #71AB53 -15.97%, #66A474 18%, #589B9F 63.31%, #4E94BF 102.56%, #3C87F8 159.72%);
  }
}

.form-section {
  .form {
    &-wrap {
      position: relative;
      max-width: 510px;
      border-radius: 16px;
      background: rgba(255, 255, 255, 0.96);
      box-shadow: 30px 24px 100px 0 rgba(65, 65, 65, 0.12);

      @media #{map-get($display-breakpoints, 'md-and-up')} {
        margin-top: -195px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 42px;
      }

      .input-title {
        color: var(--v-dark-lighten4);
        letter-spacing: 0.25px;
      }
    }

    &-title {
      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 16px;
      }
    }
  }

  .item {
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: 540px;
      margin-left: auto;
      margin-right: auto;
      text-align: center;
    }

    &-title {
      font-size: 20px;
    }

    &-text {
      color: var(--v-darkLight-lighten3);
      letter-spacing: 0.1px;
    }
  }
}

.companies {
  &-wrap {
    border-radius: 24px;
    background: var(--v-darkLight-base);
  }

  &-title {
    font-size: 36px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 26px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      font-size: 24px;
    }
  }

  &-text {
    max-width: 650px;
    font-size: 24px;
    font-weight: 600;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 20px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      font-size: 18px;
    }
  }

  &-list__item {
    display: flex;
    align-items: center;
  }

  &-user {
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 16px;
    }
  }
}

.offer {
  position: relative;

  &::before,
  &::after {
    content: '';
    position: absolute;
    opacity: 0.8;
  }

  &::before {
    top: -60px;
    right: 0;
    width: 393px;
    height: 619px;
    background-image: url('@/assets/images/business-page/img3.svg');
  }

  &::after {
    width: 842px;
    height: 755px;
    left: 0;
    bottom: 0;
    background-image: url('@/assets/images/business-page/img2.svg');
  }

  &-title {
    font-size: 36px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 26px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      font-size: 24px;
    }
  }

  .item {
    height: 100%;
    min-height: 445px;
    border-radius: 20px;
    background-color: #fff;
    box-shadow: 10px 40px 50px 0 rgba(233, 233, 233, 0.40), -10px -36px 84px 0px rgba(93, 93, 93, 0.02);

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: 335px;
      min-height: 380px;
      margin-left: auto;
      margin-right: auto;
      text-align: center;
    }

    &-icon {
      height: 225px;
    }

    &-title {
      font-size: 24px;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 20px;
      }
    }

    &-text {
      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 14px !important;
      }
    }
  }
}

.for-you {
  &-wrap {
    border-radius: 10px;
    box-shadow: 0 20px 41px 0 rgba(19, 7, 45, 0.04);
    background-color: #fff;
    overflow: hidden;
  }

  &-title {
    margin-bottom: 24px;
    font-size: 32px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin-bottom: 12px;
      font-size: 26px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      font-size: 24px;
    }
  }

  &-subtitle {
    margin-bottom: 12px;
    font-size: 20px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 18px;
    }
  }

  &-text {
    font-size: 14px;
    color: var(--v-dark-lighten2, #262626);
  }

  &-decoration {
    right: 0;
    top: 0;
    width: 25%;
    max-width: 259px;
    height: 10%;
    max-height: 183px;
  }

  &-btn {
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      text-align: center;
    }
  }
}
