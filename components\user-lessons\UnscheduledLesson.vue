<template>
  <lesson-item :item="item" :user-statuses="userStatuses">
    <template #date>
      <div class="date">
        {{ item.availableLessons }} {{ $t('of') }} {{ item.countLessons }}
      </div>
      <div class="remaining" v-html="$t('remaining_lessons')"></div>
    </template>

    <template #lessonInfo>
      <div>
        <span>{{ $t('purchase_date') }}:</span>
        <span class="text--gradient font-weight-bold text-no-wrap">
          {{ $dayjs(item.createdDate).tz(timeZone).format('LL') }}
        </span>
      </div>
      <div v-if="isTeacher">
        <span>{{ $t('price_per_lesson') }}:</span>
        <span class="text--gradient font-weight-bold text-no-wrap">
          {{ currentCurrencySymbol }}{{ getPrice(item.priceForOneLesson) }}
        </span>
      </div>
    </template>

    <template #lessonActions>
      <v-btn
        v-if="isStudent && !userIsDeleted"
        color="primary"
        class="font-weight-medium ml-1 mb-1"
        @click="schedule"
      >
        <svg class="mr-1" width="20" height="20" viewBox="0 0 20 20">
          <use
            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#calendar`"
          ></use>
        </svg>
        {{ $t('schedule') }}
      </v-btn>
    </template>

    <time-picker-dialog
      :is-shown-time-picker-dialog="isShownTimePickerDialog"
      :username="item.teacherUsername"
      :language="item.language"
      :course="item.course"
      :lesson-length="item.lessonLength"
      :count-lessons="item.availableLessons"
      :purchase-id="item.purchaseId"
      :current-time="currentTime"
      @update-current-time="currentTime = $event"
      @close-dialog="closeTimePickerDialog"
    ></time-picker-dialog>
  </lesson-item>
</template>

<script>
import { getPrice } from '~/helpers'

import LessonItem from '~/components/user-lessons/LessonItem'
import TimePickerDialog from '~/components/user-lessons/TimePickerDialog'

export default {
  name: 'UnscheduledLesson',
  components: { LessonItem, TimePickerDialog },
  props: {
    item: {
      type: Object,
      required: true,
    },
    userStatuses: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      getPrice,
      currentTime: this.$dayjs(),
      isShownTimePickerDialog: false,
    }
  },
  computed: {
    isTeacher() {
      return this.$store.getters['user/isTeacher']
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
    userIsDeleted() {
      return this.item.userIsDeleted
    },
    timeZone() {
      return this.$store.getters['user/timeZone']
    },
  },
  methods: {
    closeTimePickerDialog() {
      this.isShownTimePickerDialog = false

      this.$store.commit('teacher_profile/RESET_SELECTED_SLOTS')
    },
    schedule() {
      this.$store
        .dispatch('teacher_profile/getSlots', {
          slug: this.item.teacherUsername,
          date: this.currentTime.day(1),
        })
        .then(() => {
          this.isShownTimePickerDialog = true
        })
    },
  },
}
</script>
