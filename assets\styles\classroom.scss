@import './assets/styles/vars';

html.classroom-page {
  font-size: 1em !important;
  overflow: hidden;
}

.classroom {
  background: #f2f2f2 !important;

  div, image, iframe {
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
    -khtml-user-select: none; /* Konqueror HTML */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none; /* Non-prefixed version, currently supported by Chrome and Opera */
  }
}


.note-editable {
  p {
    -webkit-touch-callout: default ; /* iOS Safari */
    -webkit-user-select: auto; /* Safari */
  }

  a {
    color: blue !important;

    &:hover {
      text-decoration: underline;
    }
  }
}

.note-editable[contenteditable="false"] {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255,255,255,.5);
  }
}

.transparent .konvajs-content > canvas {
  background: transparent !important;
}

.container-fluid {
  padding: 0;
  margin: 0;
}

.columns-wrapper .middle .middle-inner {
  padding: 0;
  margin: 0;
}

.img-classroom-preview {
  position: absolute;
  top: 2px;
  right: 2px;
  border: 1px solid grey;
  background-color: #e4e4e4;
  z-index: 1;
  width: 250px;
}

.moveable-control-box,
.moveable-control-box .moveable-line {
  display: none !important;
}

.cursor-pointer,
.cursor-pointer * {
  cursor: pointer !important;
}

.cursor-auto {
  cursor: auto !important;
}

.selected {
  border-bottom: none;

  &.button-student svg {
    color: var(--v-studentColor-base);
  }

  &.button-teacher svg {
    color: var(--v-teacherColor-base);
  }
}

.hide {
  display: none;
}

.plyr__controls {
  position: fixed !important;
  padding: 4px !important;
  background: #5e5e5e !important;
}

.toolbar-buttons {
  position: relative;
  width: 40px;
  padding-left: 0;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);

  li {
    list-style: none;
  }
}

.toolbar-button-wrapper {
  width: 40px;
  display: flex;
  height: 40px;
  justify-content: center;
  position: relative;
}

.toolbar-button-wrapper-replace, .toolbar-button-wrapper-undo {
  padding: 0;
}

.toolbar-button-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 8px;
  position: relative;
  outline: none;
  border: none;
  background: transparent;
}

.toolbar-button-undo {
  padding: 8px 0;
  margin-bottom: 8px;
  width: 100%;
  border-radius: 6px;
  box-shadow: 0 4px 2px -2px rgba(0, 0, 0, 0.15);
}

.toolbar-button-item[disabled],
.toolbar-button-item:disabled,
.toolbar-button-item[disabled] *,
.toolbar-button-item:disabled * {
  cursor: default !important;
}

.toolbar-button-hand::before,
.toolbar-button-file::before {
  content: '';
  position: absolute;
  left: 2px;
  bottom: 2px;
  border: 2px solid transparent;
  border-bottom: 2px solid black;
  border-left: 2px solid black;
}

.toolbar-button-item[disabled]::before,
.toolbar-button-item:disabled::before {
  opacity: 0.3;
}

.toolbar-button-icon {
  width: auto;
  height: auto;
  max-height: 100%;
  max-width: 100%;
}

.toolbar-buttons-horizontal {
  display: none;
  position: absolute;
  top: 0;
  right: 40px;
  padding-right: 5px;
  border-radius: 6px;
}

.toolbar-button-item-draw-line {
  padding-right: 0;
}

.toolbar-button-item-hand {
  padding-left: 0;
}

.toolbar-button-item-horizontal .toolbar-button-icon {
  height: auto;
  width: auto;
}

.toolbar-buttons-horizontal .toolbar-button-wrapper:first-child .toolbar-button-item-horizontal {
  border-bottom-left-radius: 4px!important;
  border-top-left-radius: 4px!important;
}

.stream-controls .toolbar-button-item svg {
  color: var(--v-darkLight-base);
}

.stream-controls .toolbar-button-item[disabled] svg,
.stream-controls .toolbar-button-item:disabled svg {
  color: #c6c6c6;
}

#toolbar-switch {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.toolbar-button-wrapper:last-child .toolbar-button-item {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border-bottom: none!important;
}

.button-teacher:hover svg {
  color: var(--v-teacherColor-base) !important;
}

.button-student:hover svg {
  color: var(--v-studentColor-base) !important;
}

.toolbar-button-item svg {
  color: var(--v-darkLight-base);
}

.hover-btn-info {
  position: absolute;
  display: none;
  width: auto;
  top: 50%;
  transform: translateY(-50%);
  right: 50px;
  color: #fff;
  background: #444444;
  border-radius: 5px;
  padding: 5px 10px;
  white-space: nowrap;
  font-size: 13px;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: -9px;
    border: 5px solid transparent;
    border-left: 5px solid #444444;
  }
}

.hover-horizontal-button {
  top: auto;
  right: auto;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);

  &::after {
    border-top: 5px solid #444444;
    border-left: 5px solid transparent;
    top: auto;
    right: auto;
    bottom: -9px;
    left: 50%;
    transform: translateX(-50%);
  }
}

.hover-btn-info-horizontal {
  top: -40px;
  right: -60%;
}

.toolbar-button-replace + .hover-btn-info {
  top: 40%;
}

input.video-load-input {
  padding: 5px;
  border-radius: 3px;
  margin-bottom: 15px;
}

.video-buttons-wrap {
  display: flex;
  align-items: center;
}

.video-load-btn {
  background: #5aac44;
  color: #fff;
  border: none;
  outline: none;
  padding: 6px 12px;
  border-radius: 3px;
}

.video-load-cross {
  margin-left: 10px;
  font-size: 30px;
  color: #6b778c;
  vertical-align: middle;
}

.video_annotations {
  width: 2px;
  height: 12px;
  position: absolute;
  top: 4px;
  background: red;
}

#video-window {
  position: absolute;
  width: 100%;
  height: 100%;
  background: black;
}

.local-stream {
  position: absolute;
  top: 0;
  left: 0;
  width: 100px;
  height: 75px;
  background-color: #000;
  z-index: 3;
}

.remote-stream {
  z-index: 2;
  position: absolute;
  width: 100%;
  height: 100%;
}

.remote-screenshare {
  background-color: #000;
}

.hr-flip {
  transform: scaleX(-1);
}

.embed-responsive .embed-responsive-item,
.embed-responsive embed,
.embed-responsive iframe,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  border: 0;
}

.video-window--is-fullscreen .stream-controls {
  bottom: 5px !important;
}

.screenshare-component {
  position: relative;
  height: 100%;
  background-color: #000;

  .user-name {
    position: absolute;
    top: 0;
    right: 0;
    padding: 4px 13px;
    line-height: 1;
    background: #ffffff;
    font-size: 12px;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border: none;
    border-bottom-left-radius: 6px;
    z-index: 5;
  }
}

.student-role .stream-controls {
  bottom: -20px;
}

.stream-controls--screenshare {
  bottom: -20px !important;

  .stream-controls-wrapper {
    padding: 0;
  }

  .hover-btn-info {
    left: 50%;
    right: auto;
    transform: translateX(-50%);
    top: auto;
    bottom: -42px;

    &::after {
      left: 50%;
      transform: translateX(-50%);
      top: -9px;
      right: auto;
      border: 5px solid transparent;
      border-bottom: 5px solid #444444;
    }
  }

  .stream-controls {
    bottom: 15px;
  }
}

.student-role .video-window--is-fullscreen .stream-controls {
  bottom: 38px;
}

.embed-responsive {
  position: static;
}

.video-window--is-fullscreen .embed-responsive-4by3 {
  height: 100%;
  padding-bottom: 0;
}

.video-window-buttons-wrap {
  height: 100%;
  display: inline-flex;
  flex-direction: column;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
}

#screenshare::-webkit-media-controls-fullscreen-button {}
#screenshare::-webkit-media-controls-play-button {
  display: none !important;
}
#screenshare::-webkit-media-controls-timeline {
  display: none !important;
}
#screenshare::-webkit-media-controls-current-time-display {
  display: none !important;
}
#screenshare::-webkit-media-controls-time-remaining-display {
  display: none !important;
}
#screenshare::-webkit-media-controls-mute-button {
  display: none !important;
}
#screenshare::-webkit-media-controls-toggle-closed-captions-button {
  display: none !important;
}
#screenshare::-webkit-media-controls-volume-slider {
  display: none !important;
}
#screenshare::-webkit-media-controls-panel {
  background-image: linear-gradient(transparent, transparent) !important;
}
#screenshare::-webkit-media-controls-panel {
  display: flex !important;
  opacity: 1 !important;
}

.OT_publisher, .OT_subscriber {
  min-width: 100% !important;
  min-height: 100% !important;
}

#video-share-catch {
  z-index: 91;
}

.btn {
  box-shadow: none;
}

.note-btn-group.note-style,
.note-btn-group.note-insert,
.note-btn-group.note-para {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
}

.note-editor {
  height: 100%;
}

.note-btn-group {
  &.note-style {
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
    position: relative;
    z-index: 14;

    .btn:first-child {
      border-bottom-left-radius: 4px;
      border-top-left-radius: 4px;
    }
  }

  &.note-insert {
    position: relative;
    z-index: 12;
  }

  &.note-para .note-btn-group,
  &.note-para .note-btn-group .btn {
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
  }
}

.note-btn {
  padding: 8px 10px;
}

.note-toolbar-wrapper {
  height: auto !important;
}

.panel {
  box-shadow: 0 2px 30px rgba(0, 0, 0, 0.25);
  margin-bottom: 0;
}

.stream-controls-wrapper {
  position: relative;
  display: inline-flex;
  justify-content: center;
  background: #FFFFFF;
  padding: 0 10px;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);
  z-index: 12;

  button {
    &:first-child {
      border-bottom-left-radius: 4px;
      border-top-left-radius: 4px;
    }

    &:last-child {
      border-bottom-right-radius: 4px;
      border-top-right-radius: 4px;
    }
  }
}

#play-pause img{
  margin-top: 5px;
  max-height: 27px;
  max-width: 28px;
}

#play-pause, #mute {
  position: relative;
  height: 100%;
  transition: all ease-in-out 0.5s;
}

#play-pause::after,
#mute::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  display: block;
  width: 2px;
  height: 80%;
  background: #4a4a4a;
  opacity: 0;
  transition: all ease-in-out 0.5s;
}

.popup-load-files {
  position: absolute;
  left: 50%;
  top: 50px;
  max-width: 645px;
  width: calc(100% - 20px);
  border-radius: 8px;
  box-shadow: 0 2px 30px rgba(0, 0, 0, 0.25);
  transform: translateX(-50%);
  overflow: hidden;
  cursor: auto !important;
  z-index: 1000000000 !important;

  & * {
    cursor: auto !important;
  }

  .cursor-pointer,
  .cursor-pointer * {
    cursor: pointer !important;
  }
}

.popup-load-files-header {
  min-height: 52px;
  cursor: auto !important;
  padding: 0 12px 10px;
  background: var(--v-teacherColor-lighten1);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;

  &-buttons {
    display: flex;
    flex-wrap: wrap;
    flex-grow: 1;
    justify-content: flex-end;

    @media only screen and (max-width: $xxs-and-down) {
      justify-content: center;
    }

    & > * {
      margin-top: 10px;
    }
  }
}

.popup-load-files-header-selected-files {
  display: none;
  position: relative;
  min-height: 53px;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  background: var(--v-teacherColor-lighten1);
  padding: 0 46px 10px 12px;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;

  button:not(.popup-load-files-header-cross) {
    margin-top: 10px;
  }
}

.popup-load-files-header-selected-files.active {
  cursor: auto !important;
  display: flex;
}

.popup-load-files-header-cross {
  position: absolute;
  top: 6px;
  right: 10px;
}

.popup-load-files-title {
  margin: 10px 20px 0 0;
  color: var(--v-darkLight-base);
  font-weight: bold;
  font-size: 22px;

  @media only screen and (max-width: $xsm-and-down) {
    font-size: 18px;
  }
}

input[type=file].popup-load-files-btn-upload {
  display: none;
}

label.popup-load-files-label-upload,
.popup-load-files-input {
  padding: 6px 14px;
  margin-right: 14px;
  margin-bottom: 0;
  border: none;
  outline: none;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  font-weight: bold;
  font-size: 13px;
  color: var(--v-darkLight-base);
}

label.popup-load-files-label-upload.popup-load-files-label-upload-laptop {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  box-shadow: none;
}

.popup-load-files .popup-load-files-label-upload {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 142px;
  text-align: center;
  margin-right: 4px;
  padding: 0 16px;
  font-size: 15px;
  border: 1px solid var(--v-teacherColor-base);
  white-space: nowrap;
  border-radius: 24px;
  box-shadow: none;
}

.popup-load-files-input:last-child {
  margin-right: 0;
}

.popup-load-files-input::-webkit-input-placeholder {
  color: var(--v-darkLight-base);
}

.popup-load-files-input::-moz-placeholder {
  color: var(--v-darkLight-base);
}

.popup-load-files-input:-ms-input-placeholder {
  color: var(--v-darkLight-base);
}

.popup-load-files-input:-moz-placeholder {
  color: var(--v-darkLight-base);
}

.popup-load-files-select-wrap,
.popup-load-files-label-search {
  position: relative;
}

.popup-load-files-select {
  position: relative;
  width: 166px;
  margin-right: 0;

  &-options::after  {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    display: block;
    border: 5px solid transparent;
    border-bottom: 5px solid #fff;
  }
}

.popup-load-files-select-wrap {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 90%;
    transform: translateY(-20%);
    display: block;
    border: 5px solid transparent;
    border-top: 5px solid #000;
    z-index: 12;
  }
}

.popup-load-files-select-options {
  position: absolute;
  display: none;
  width: 100%;
  top: 130%;
  left: 0;
  background: #fff;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  z-index: 19;

  &.active {
    display: block;
  }
}

.popup-load-files-select-option {
  position: relative;
  font-size: 16px;
  padding: 6px 0;

  &:last-child {
    margin-bottom: 0;
  }

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -13px;
    display: block;
    width: 6px;
    height: 6px;
    transform: translateY(-20%);
    border-radius: 50%;
    background: #000;
  }
}

.popup-load-files-search-wrap {
  position: relative;
}

.library-add-search-img {
  width: 21px;
  height: 20px;
  background: url('~assets/images/search-icon.svg') no-repeat center;
  background-size: contain;
}

.popup-load-files-search {
  padding-right: 38px;

  &-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 23px;
    border: none;
    background: none;
    outline: none;
  }
}

.popup-load-files-body {
  min-height: 384px;

  @media only screen and (max-width: $xsm-and-down) {
    min-height: 278px;
  }

  @media only screen and (max-width: $xxs-and-down) {
    min-height: 198px;
  }
}

.popup-load-files-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 0 15px 15px;

  @media only screen and (max-width: $xsm-and-down) {
    padding: 0 0 8px 8px;
  }
}

.popup-load-files-item {
  width: calc(16.6667% - 15px);
  max-width: 95px;
  margin-right: 15px;
  display: flex;
  justify-content:center;
  align-items: center;
  flex-direction: column;
  margin-top: 15px;

  @media only screen and (max-width: $xsm-and-down) {
    width: calc(16.6667% - 8px);
    margin-right: 8px;
    margin-top: 8px;
  }

  &-helper {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 100%;

    .popup-load-files-item-img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      background: #C4C4C4;

      canvas {
        max-width: calc(100% - 2px);
        max-height: calc(100% - 2px);
      }
    }
  }

  &--loading {
    .popup-load-files-item-img {
      background: #eee;
    }

    .popup-load-files-item-name {
      opacity: 0.6;
    }
  }
}

.popup-load-files-footer {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 17px 17px 17px;
  flex-direction: column;

  @media only screen and (max-width: $xsm-and-down) {
    padding: 0 8px 17px 8px;
  }

  &-pagination {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .popup-load-files-nav-wrap {
      margin: 0 30px;

      @media only screen and (max-width: $xxs-and-down) {
        margin: 0 15px;
      }
    }

    .popup-load-files-nav-number {
      position: relative;
      display: inline-block;
      padding: 5px;
      font-size: 14px;

      &::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        display: none;
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background: #000;
        transform: translateX(-40%);
      }

      &.active::after {
        display: block;
      }
    }

    .popup-load-files-btn-nav {
      display: flex;
      align-items: center;
      min-width: 75px;
      border: none;
      outline: none;
      background: none;

      @media only screen and (max-width: $xxs-and-down) {
        min-width: 68px;
      }

      span {
        font-size: 14px;

        @media only screen and (max-width: $xxs-and-down) {
          font-size: 12px;
        }
      }
    }

    .popup-load-files-nav-icon-next,
    .popup-load-files-nav-icon-prev {
      width: 20px;
      height: 14px;
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;

      @media only screen and (max-width: $xxs-and-down) {
        width: 12px;
        height: 8px;
      }
    }

    .popup-load-files-nav-icon-next {
      margin-left: 6px;
      background-image: url('~assets/images/classroom/arrow-left.svg');
      transform: rotate(-180deg);
    }

    .popup-load-files-nav-icon-prev {
      margin-right: 6px;
      background-image: url('~assets/images/classroom/arrow-left.svg');
    }

    button:disabled,
    button[disabled] {
      opacity: 0.5;
    }
  }

  &-buttons {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
}

.popup-load-files-item-name {
  width: 100%;

  p {
    font-size: 12px;
    width: inherit;
    margin-bottom: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    @media only screen and (max-width: $xsm-and-down) {
      font-size: 10px;
    }
  }
}

.popup-load-files-item-cancel,
.popup-load-files-item-tick {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 16px;
  height: 16px;
  background: #fff;
  border: 1px solid rgba(0,0,0,0.5);
}

.popup-load-files-item-cancel {
  border-radius: 50%;

  .popup-load-files-tick-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 15px;
    height: 15px;
  }
}

.popup-load-files-item-tick {
  border-radius: 4px;

  img {
    display: block;
  }

  &.active .popup-load-files-tick-icon {
    display: block;
  }
}

.popup-load-files-search:hover,
.popup-load-files-search:focus {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.popup-load-files-tick-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  display: none;
  transform: translate(-50%, -50%);
}

.whiteboard_video_el {
  z-index: 20;
}

.popup-load-files-wrap {
  position: relative;
  background: #fff;
}

.popup-load-files-drop-wrap {
  position: absolute;
  top: 0;
  left: 0;
  height: 100% !important;
  width: 100%;
  background: none;

  &.active {
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 999999;
  }

  .drop-area--wrapper {
    width: 100%;
    max-width: 100%;
    height: 100%;

    img {
      max-width: 65%;
    }
  }
}

.popup-load-files.student {
  .popup-load-files-header,
  .popup-load-files-header-selected-files {
    background: var(--v-studentColor-lighten1);
  }

  .popup-load-files-label-upload {
    border: 1px solid var(--v-studentColor-base);
  }
}

.drop-area--wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 640px;
  height: 65%;
  margin: auto;
  pointer-events: none;

  &__upload-meta {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
  }

  &__dropbox-img {
    width: 100%;
    height: auto;
  }
}

.image-wrap-classroom {
  background: #fff;
  box-shadow: 0 2px 30px rgba(0, 0, 0, 0.25);
}

.video-item-description,
.image-classroom-description {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  background: #5e5e5e;
  color: #fff;
}

.video-item-cross,
.image-classroom-cross {
  padding: 0 10px;
  font-size: 20px;
}

.video-item-name {
  width: 100%;
  padding: 10px 10px;
}

.image-classroom-item-icon {
  width: 100%;
  height: 100%;
}

#player {
  width: 100%;
  height: 100%;
}

#video-item-wrap {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
}

.plyr {
  &__video-embed iframe {
    top: -50%;
    height: 200%;
  }

  &--video.plyr--menu-open {
    overflow: hidden;
  }

  &__video-wrapper {
    position: relative;
  }

  &__controls__item,
  &__controls__item span,
  &__progress,
  &__control,
  &__progress input,
  &__progress svg,
  &__volume input {
    cursor: pointer!important;
  }
}

.canvas-video {
  height: 100%;
  width: 100%;
}

.drag-move {
  cursor: url('~assets/images/classroom/teacher-dragging.svg') 32 24, auto !important;
}

.note-modal .modal-dialog {
  width: 100% !important;
  max-width: 1100px !important;
}

.sn-checkbox-open-in-new-window,
.sn-checkbox-use-protocol {
  display: none !important;
}

/* Cursors */
body.teacher-cursor-pointer,
body.teacher-cursor-pointer * {
  cursor: url('~assets/images/classroom/teacher-pointer.svg') 0 0, auto;
}

body.student-cursor-pointer,
body.student-cursor-pointer * {
  cursor: url('~assets/images/classroom/student-pointer.svg') 0 0, auto;
}

body.teacher-cursor-pointer .container-header-title:hover,
body.teacher-cursor-pointer .panel-heading:hover,
body.teacher-cursor-pointer .tox-editor-header:hover,
body.teacher-grabber-hover-cursor-pointer,
body.teacher-grabber-hover-cursor-pointer *,
body.teacher-role .cursor-before-grab:hover,
body.teacher-role .cursor-before-grab:hover * {
  cursor: url('~assets/images/classroom/teacher-beforeGrab.svg') 21 16, auto;
}

body.student-cursor-pointer:not(.room-is-disabled) .container-header-title:hover,
body.student-cursor-pointer:not(.room-is-disabled) .panel-heading:hover,
body.student-cursor-pointer:not(.room-is-disabled) .tox-editor-header:hover,
body.student-grabber-hover-cursor-pointer,
body.student-grabber-hover-cursor-pointer *,
body.student-role:not(.room-is-disabled) .cursor-before-grab:hover,
body.student-role:not(.room-is-disabled) .cursor-before-grab:hover * {
  cursor: url('~assets/images/classroom/student-beforeGrab.svg') 21 16, auto;
}

body.student-cursor-pointer.dragging * {
  cursor: url('~assets/images/classroom/student-dragging.svg') 21 16, auto !important;
}

body.teacher-cursor-pointer.dragging * {
  cursor: url('~assets/images/classroom/teacher-dragging.svg') 21 16, auto !important;
}

body.teacher-eraser-cursor,
body.teacher-eraser-cursor * {
  cursor: url('~assets/images/classroom/teacher-eraser.svg') 10 20, auto;
}

body.teacher-pencil-cursor,
body.teacher-pencil-cursor * {
  cursor: url('~assets/images/classroom/teacher-pencil.svg') 0 22, auto;
}

body.student-eraser-cursor,
body.student-eraser-cursor * {
  cursor: url('~assets/images/classroom/student-eraser.svg') 10 20, auto;
}

body.student-pencil-cursor,
body.student-pencil-cursor * {
  cursor: url('~assets/images/classroom/student-pencil.svg') 0 22, auto;
}

.summernote-teacher .note-editable:hover,
.summernote-teacher .note-editable:hover * {
  cursor: url('~assets/images/classroom/teacher-text-cursor.svg') 32 32, auto;
}

.summernote-student .note-editable:hover,
.summernote-student .note-editable:hover * {
  cursor: url('~assets/images/classroom/student-text-cursor.svg') 32 32, auto;
}
.teacher .plyr__controls__item:hover,
.summernote-teacher .note-popover:hover,
.summernote-teacher a:hover,
.summernote-teacher a:hover * {
  cursor: url('~assets/images/classroom/teacher-cursor-link.svg') 32 32, auto;
}

.student .plyr__controls__item:hover,
.summernote-student .note-popover:hover,
.summernote-student a:hover,
.summernote-student a:hover * {
  cursor: url('~assets/images/classroom/student-cursor-link.svg') 32 32, auto;
}

/*body.teacher-cursor-hand,*/
/*body.teacher-cursor-hand * {*/
/*    cursor: url('~assets/images/classroom/cursor_hand_teacher.svg') 10 8, auto;*/
/*}*/

body.teacher-cursor-pointer.handle-mr *,
body.teacher-cursor-pointer.handle-ml *,
.vdr.teacher .handle-mr,
.vdr.teacher .handle-ml {
  cursor: url('~assets/images/classroom/cursor-teacher-right.svg') 25 19, auto !important;
}

body.teacher-cursor-pointer.handle-tm *,
body.teacher-cursor-pointer.handle-bm *,
.vdr.teacher .handle-tm,
.vdr.teacher .handle-bm {
  cursor: url('~assets/images/classroom/cursor-teacher-down.svg') 19 25, auto !important;
}

body.teacher-cursor-pointer.handle-tl *,
body.teacher-cursor-pointer.handle-br *,
.vdr.teacher .handle-tl,
.vdr.teacher .handle-br {
  cursor: url('~assets/images/classroom/teacher-arrow.svg') 18 16, auto !important;
}

body.teacher-cursor-pointer.handle-tr *,
body.teacher-cursor-pointer.handle-bl *,
.vdr.teacher .handle-tr,
.vdr.teacher .handle-bl {
  cursor: url('~assets/images/classroom/teacher-arrow-2.svg') 16 18, auto !important;
}

body.student-cursor-pointer.handle-mr *,
body.student-cursor-pointer.handle-ml *,
.vdr.student .handle-mr,
.vdr.student .handle-ml {
  cursor: url('~assets/images/classroom/cursor-student-right.svg') 25 19, auto !important;
}

body.student-cursor-pointer.handle-tm *,
body.student-cursor-pointer.handle-bm *,
.vdr.student .handle-tm,
.vdr.student .handle-bm {
  cursor: url('~assets/images/classroom/cursor-student-down.svg') 19 25, auto !important;
}

body.student-cursor-pointer.handle-tl *,
body.student-cursor-pointer.handle-br *,
.vdr.student .handle-tl,
.vdr.student .handle-br {
  cursor: url('~assets/images/classroom/student-arrow.svg') 18 16, auto !important;
}

body.student-cursor-pointer.handle-tr *,
body.student-cursor-pointer.handle-bl *,
.vdr.student .handle-tr,
.vdr.student .handle-bl {
  cursor: url('~assets/images/classroom/student-arrow-2.svg') 16 18, auto !important;
}

body:not(.is-touch-device) {
  .toolbar-button-item:hover + .hover-btn-info,
  .toolbar-button-item:active + .hover-btn-info {
    display: block;
  }
}
