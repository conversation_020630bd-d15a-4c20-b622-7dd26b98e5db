<template>
  <div style="min-height: calc(100vh - 300px)"></div>
</template>

<script>
import { loadStripe } from '@stripe/stripe-js/pure'
export default {
  name: 'StripeForm',
  async beforeMount() {
    const sessionId = this.$route.params.id
    if (!this.$stripe) {
      this.$stripe = await loadStripe(process.env.STRIPE_PUBLISHABLE_KEY)
    }
    await this.$cookiz.set('thank_you_page_allowed', 1, {
      domain: process.env.NUXT_ENV_COOKIE_DOMAIN,
      path: '/',
    })

    if (this.$stripe) {
      this.$stripe
        .redirectToCheckout({
          sessionId,
        })
        .then(() => {
          console.log('success')
        })
        .catch((e) => {
          console.log('error', e)
        })
    } else {
      console.log('stripe not found')
    }
  },
}
</script>
