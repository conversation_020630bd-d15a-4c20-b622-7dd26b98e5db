<template>
  <div>
    <l-dialog
      :dialog="
        show && !showWiseTransfer && !showPaymentDetails && !showSavedAccounts
      "
      max-width="680"
      custom-class="payout-modal"
      @close-dialog="$emit('close')"
    >
      <v-card class="pa-2" flat>
        <div class="d-flex justify-space-between align-center mb-6">
          <h2 class="text-h6 font-weight-medium">Choose Account Type</h2>
        </div>

        <div class="payout-options d-flex">
          <v-list class="pa-0 flex-1">
            <v-list-item
              v-for="option in leftColumnOptions"
              :key="option.id"
              link
              @click="handleOptionClick(option)"
            >
              <v-list-item-content>
                <v-list-item-title class="primary--text">
                  {{ option.title }}
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </v-list>

          <v-list class="pa-0 flex-1">
            <v-list-item
              v-for="option in rightColumnOptions"
              :key="option.id"
              link
              @click="handleOptionClick(option)"
            >
              <v-list-item-content>
                <v-list-item-title class="primary--text">
                  {{ option.title }}
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </div>

        <div class="mt-6 caption grey--text text-center">
          Please note: Only 1 payout is permitted in any 7-day period.
        </div>
      </v-card>
    </l-dialog>

    <wise-transfer-modal
      :show="showWiseTransfer"
      @close="handleWiseTransferClose"
      @submit="handleWiseTransferSubmit"
    />

    <payment-details-modal
      :show="showPaymentDetails"
      :payment-type="selectedPaymentType"
      @close="handlePaymentDetailsClose"
      @submit="handlePaymentDetailsSubmit"
    />

    <saved-accounts-modal
      :show="showSavedAccounts"
      @close="handleSavedAccountsClose"
      @submit="handleSavedAccountsSubmit"
    />
  </div>
</template>

<script>
import WiseTransferModal from './WiseTransferModal'
import PaymentDetailsModal from './PaymentDetailsModal'
import SavedAccountsModal from './SavedAccountsModal'
import LDialog from '~/components/LDialog'

export default {
  name: 'PayoutModal',
  components: {
    LDialog,
    WiseTransferModal,
    PaymentDetailsModal,
    SavedAccountsModal,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showWiseTransfer: false,
      showPaymentDetails: false,
      showSavedAccounts: false,
      selectedPaymentType: '',
      payoutOptions: [
        { id: 1, title: 'Wise Transfer', route: '/payouts/wise' },
        { id: 2, title: 'Transfer to UK Account', route: '/payouts/uk' },
        { id: 3, title: 'SWIFT Transfer', route: '/payouts/swift' },
        { id: 4, title: 'IBAN Transfer', route: '/payouts/iban' },
        { id: 6, title: 'Transfer to US Account', route: '/payouts/us' },
        // { id: 7, title: 'Saved Accounts', route: '/payouts/saved' },
      ],
    }
  },
  computed: {
    leftColumnOptions() {
      return this.payoutOptions.slice(
        0,
        Math.ceil(this.payoutOptions.length / 2)
      )
    },
    rightColumnOptions() {
      return this.payoutOptions.slice(Math.ceil(this.payoutOptions.length / 2))
    },
  },
  methods: {
    handleOptionClick(option) {
      if (option.id === 1) {
        // Wise transfer
        this.showWiseTransfer = true
      } else if (option.id === 7) {
        // Saved accounts
        this.showSavedAccounts = true
      } else {
        this.selectedPaymentType = option.title
        this.showPaymentDetails = true
      }
    },
    handleWiseTransferClose() {
      this.showWiseTransfer = false
      this.$emit('close')
    },
    handleWiseTransferSubmit(formData) {
      // No need to emit an event, as the WiseTransferModal now handles the API call directly
      this.$emit('close')
    },
    handlePaymentDetailsClose() {
      this.showPaymentDetails = false
      this.$emit('close')
    },
    handlePaymentDetailsSubmit(formData) {
      this.$emit('payment-details-submitted', {
        type: this.selectedPaymentType,
        ...formData,
      })
    },
    handleSavedAccountsClose() {
      this.showSavedAccounts = false
      this.$emit('close')
    },
    handleSavedAccountsSubmit(formData) {
      // No need to emit an event, as the SavedAccountsModal now handles the API call directly
      this.$emit('close')
    },
  },
}
</script>

<style lang="scss" scoped>
$mobile-breakpoint: 768px;

.payout-modal {
  .payout-options {
    gap: 16px;
  }

  .v-list {
    background: transparent;
    overflow: hidden;

    .v-list-item {
      min-height: 44px;
      border-radius: 8px;
      margin-bottom: 8px;

      &:hover {
        background: linear-gradient(
          126.15deg,
          rgba(128, 182, 34, 0.1) 0%,
          rgba(60, 135, 248, 0.1) 102.93%
        );
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  @media screen and (max-width: $mobile-breakpoint) {
    .v-card {
      padding: 16px !important;
    }

    .payout-options {
      flex-direction: column;
      gap: 8px;
    }

    .v-list {
      .v-list-item {
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        padding: 12px 16px;

        &-title {
          font-size: 14px;
        }
      }
    }

    .caption {
      font-size: 12px;
      margin-top: 16px !important;
    }
  }
}

.flex-1 {
  flex: 1;
}
</style>
