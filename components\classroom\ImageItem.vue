<template>
  <classroom-container :asset="file" :child-header-height="40">
    <classroom-container-header :file="file" :title="file.asset.displayName">
      <div ref="childComponent" class="image-wrap-classroom">
        <img :src="imageUrl" alt="" @load="resize" />
      </div>
      <div class="transparent">
        <konva
          v-if="file && width && height"
          :file="file"
          :width="width"
          :height="height"
          :scale="scale"
          :style="{
            marginTop: `-${height}px`,
          }"
        ></konva>
      </div>
    </classroom-container-header>
  </classroom-container>
</template>

<script>
import { defaultWidth } from '~/helpers/constants'
import ClassroomContainer from '~/components/classroom/ClassroomContainer'
import ClassroomContainerHeader from '~/components/classroom/ClassroomContainerHeader'
import Konva from '~/components/classroom/Konva'

export default {
  name: 'ImageItem',
  components: { ClassroomContainer, ClassroomContainerHeader, Konva },
  props: {
    file: {
      type: Object,
      required: true,
    },
  },
  computed: {
    scale() {
      if (this.width) {
        return this.width / defaultWidth
      }

      return 1
    },
    width() {
      return this.file.asset?.width ?? 0
    },
    height() {
      return this.file.asset?.height ?? 0
    },
    zoomIndex() {
      return this.$store.getters['classroom/zoomAsset']?.asset?.zoomIndex ?? 1
    },
    imageUrl() {
      return `${process.env.NUXT_ENV_URL}${this.file.asset.path}`
    },
  },
  methods: {
    resize() {
      if (!this.width) {
        const width =
          this.$refs.childComponent.getBoundingClientRect().width /
          this.zoomIndex
        const height =
          this.$refs.childComponent.getBoundingClientRect().height /
          this.zoomIndex
        const asset = { width, height, originalWidth: defaultWidth }

        this.$store.commit('classroom/moveAsset', {
          id: this.file.id,
          asset,
        })

        this.$store.dispatch('classroom/moveAsset', {
          id: this.file.id,
          lessonId: this.file.lessonId,
          asset,
        })
      }
    },
  },
}
</script>

<style scoped lang="scss">
.image-wrap-classroom img {
  display: block;
  width: 100%;
  height: 100%;
}
</style>
