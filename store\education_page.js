export const actions = {
  getPageData() {
    return this.$axios
      .get(`${process.env.NUXT_ENV_API_URL}/education-page-data`)
      .then((response) => JSON.parse(response.data))
      .then((data) => data)
      .catch((e) => console.log(e))
  },
  getTeachers() {
    return this.$axios
      .get(`${process.env.NUXT_ENV_API_URL}/education-page-teachers`)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        if (Array.isArray(data)) {
          return data
        }

        return []
      })
      .catch((e) => console.log(e))
  },
  getTestimonials() {
    return this.$axios
      .get(`${process.env.NUXT_ENV_API_URL}/education-page-ratings`)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        if (Array.isArray(data)) {
          return data
        }

        return []
      })
      .catch((e) => console.log(e))
  },
}
