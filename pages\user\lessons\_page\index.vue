<template>
  <lessons-page route="/user/lessons" :page="page" :type="type"></lessons-page>
</template>

<script>
import LessonsPage from '~/components/user-lessons/LessonsPage'

export default {
  name: 'UpcomingLessonsPage',
  components: { LessonsPage },
  middleware: 'authenticated',
  async asyncData({ params, store, query }) {
    const page = +params.page
    const type = 'upcoming'
    const searchQuery = query?.search

    await Promise.all([
      store.dispatch('lesson/getUpcomingLessons', {
        page,
        perPage: process.env.NUXT_ENV_PER_PAGE,
        type,
        searchQuery,
      }),
      store.dispatch('lesson/getCalendarItems'),
    ])
    await store.dispatch('user/getUserStatus')

    return { page, type }
  },
  head() {
    return {
      title: this.$t('user_upcoming_lessons_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('user_upcoming_lessons_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('user_upcoming_lessons_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('user_upcoming_lessons_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} user-lessons-page user-upcoming-lessons-page`,
      },
    }
  },
  watchQuery: true,
}
</script>
