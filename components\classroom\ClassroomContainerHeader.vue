<template>
  <div>
    <div
      :class="['container-header', { 'container-header--close': isCloseBtn }]"
      @mouseenter.prevent="mouseenterHandler"
      @mouseleave.prevent="mouseleaveHandler"
    >
      <div class="container-header-title">{{ title }}</div>
      <v-btn
        v-if="isCloseBtn"
        class="container-header-close"
        width="32"
        height="32"
        icon
        color="white"
        @click="close"
      >
        <svg width="24" height="24" viewBox="0 0 21 20">
          <use
            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#close`"
          ></use>
        </svg>
      </v-btn>
    </div>

    <slot></slot>
  </div>
</template>

<script>
import SetTool from '~/mixins/SetTool'

export default {
  name: 'ClassroomContainerHeader',
  mixins: [SetTool],
  props: {
    file: {
      type: Object,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
  },
  computed: {
    isCloseBtn() {
      return (
        this.role === null ||
        this.role === 'teacher' ||
        (this.role === 'student' && this.file.asset?.owner === 'student')
      )
    },
  },
  methods: {
    close() {
      this.$store.dispatch('classroom/deleteAsset', this.file)
    },
    mouseenterHandler() {
      this.$store.commit(
        'classroom/setCursorNameBeforeChange',
        this.$store.state.classroom.userParams.cursor || 'cursor-pointer'
      )
      this.$store.commit(
        'classroom/setToolNameBeforeChange',
        this.$store.state.classroom.userParams.tool || 'pointer'
      )

      this.setTool('pointer', 'cursor-pointer')
    },
    mouseleaveHandler() {
      this.setTool(
        this.$store.state.classroom.toolNameBeforeChange,
        this.$store.state.classroom.cursorNameBeforeChange
      )
    },
  },
}
</script>

<style scoped lang="scss">
.container-header {
  position: relative;
  height: 40px;
  color: #fff;
  background: #5e5e5e;
  line-height: normal;

  &-title {
    padding: 10px;
    font-size: 16px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  &--close .container-header-title {
    width: calc(100% - 40px);
    padding-right: 0;
  }

  &-close {
    position: absolute;
    top: 50%;
    right: 5px;
    transform: translateY(-50%);
    cursor: pointer !important;

    & * {
      cursor: pointer !important;
    }
  }
}
</style>
