<template>
  <div class="payment-item">
    <div class="payment-item-date">
      <div class="date">{{ formatDate(item.date) || '-' }}</div>
      <div class="time">{{ formatTime(item.time) || '-' }}</div>
    </div>
    <div class="payment-item-content">
      <div class="payment-info">
        <slot name="additionalActionsTop"></slot>
        <div class="details">
          <slot name="additionalActionsBottom"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PayoutItem',
  props: {
    item: {
      type: Object,
      required: true,
      default: () => ({
        date: '',
        time: '',
        status: '',
        method: '',
        value: '',
      }),
    },
  },
  computed: {
    userLocale() {
      // Get user's UI language/locale, fallback to browser locale or 'en'
      return this.$store.getters['user/isUserLogged']
        ? this.$store.state.user.item?.uiLanguage || this.$i18n.locale
        : this.$i18n.locale || 'en'
    },
    timeZone() {
      // Get user's timezone, fallback to browser timezone
      return this.$store.getters['user/timeZone']
    },
  },
  methods: {
    formatDate(date) {
      if (!date) return null
      try {
        // Use dayjs with timezone support like the lessons page
        return this.$dayjs(date).tz(this.timeZone).format('DD MMM')
      } catch (e) {
        return null
      }
    },
    formatTime(time) {
      // Format time using user's locale and timezone
      if (!time) return null
      try {
        // If time is already in a good format, we can try to parse it with the date
        // and format it according to user's locale
        if (time && this.item.date) {
          // Combine date and time for proper timezone conversion
          const dateTimeString = `${this.item.date} ${time}`
          const dateTime = this.$dayjs(dateTimeString).tz(this.timeZone)

          // Format time using locale-aware format (LT = localized time)
          return dateTime.format('LT')
        }

        // Fallback: return the original time if we can't parse it
        return time
      } catch (e) {
        // Fallback to original time if there's an error
        return time
      }
    },
  },
}
</script>

<style lang="scss" scoped>
$mobile-breakpoint: 768px;
$gradient-primary: linear-gradient(
  126.15deg,
  rgba(128, 182, 34, 0.74) 0%,
  rgba(60, 135, 248, 0.74) 102.93%
);
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

.payment-item {
  display: flex;
  background: white;
  border-radius: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  box-shadow: $box-shadow;
  height: 94px;

  &-date {
    width: 142px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: $gradient-primary;
    color: white;
    border-radius: 16px;
    box-shadow: 4px 0 8px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;

    .date {
      font-size: 20px;
      font-weight: 600;
      line-height: 1.2;
      margin-bottom: 2px;
    }

    .time {
      font-size: 13px;
      margin-top: 2px;
      line-height: 1;
    }
  }

  &-content {
    flex: 1;
    padding: 16px 24px;

    .payment-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;

      .details {
        display: flex;
        align-items: center;
        gap: 24px;
        font-size: 14px;
      }
    }
  }

  &:hover {
    box-shadow: 0 4px 14px rgba(217, 225, 236, 0.47);
  }
}

@media screen and (max-width: $mobile-breakpoint) {
  .payment-item {
    flex-direction: column;
    margin-bottom: 16px;
    box-shadow: none;
    background: transparent;
    box-shadow: 4px 0 8px rgba(0, 0, 0, 0.1);
    height: auto;

    &-date {
      width: auto;
      min-height: auto;
      box-shadow: 4px 0 8px rgba(0, 0, 0, 0.1);
      padding: 8px 16px;
      flex-direction: row;
      justify-content: flex-start;
      border-radius: 24px;
      margin-bottom: 8px;

      .date {
        margin-right: 8px;
        margin-bottom: 0;
      }

      .time {
        margin-left: 0;
        opacity: 1;
      }
    }

    &-content {
      background: white;
      border-radius: 12px;
      padding: 16px;
      box-shadow: $box-shadow;

      .payment-info {
        height: auto;

        .details {
          flex-direction: column;
          gap: 8px;
          width: 100%;

          ::v-deep .caption {
            width: 100%;

            p {
              font-size: 16px;
              line-height: 18px;
            }

            .gradient-text {
              font-size: 16px;
              font-weight: 500;
            }
          }

          ::v-deep .d-flex {
            width: 100%;

            .caption {
              width: 100%;
              display: flex;
              justify-content: space-between;
              padding: 8px 0;
              border-bottom: 1px solid rgba(0, 0, 0, 0.1);

              &:last-child {
                border-bottom: none;
              }
            }
          }
        }
      }
    }
  }
}
</style>
