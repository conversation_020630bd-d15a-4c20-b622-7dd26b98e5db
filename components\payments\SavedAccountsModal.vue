<template>
  <l-dialog
    :dialog="show"
    max-width="680"
    custom-class="saved-accounts-modal"
    @close-dialog="$emit('close')"
  >
    <v-card class="pa-2" flat>
      <div class="d-flex justify-space-between align-center mb-6">
        <h2 class="text-h6 font-weight-medium">Select Saved Account</h2>
      </div>

      <div v-if="savedAccounts.length > 0">
        <v-select
          v-model="selectedAccount"
          :items="savedAccounts"
          item-text="accountNumber"
          item-value="id"
          label="Select Account"
          outlined
          return-object
          class="mb-4"
        >
          <template #selection="{ item }">
            <div class="d-flex align-center">
              <span>{{ item.accountNumber }}</span>
              <span class="ml-2 grey--text text--darken-1"
                >({{ item.name }})</span
              >
            </div>
          </template>
          <template #item="{ item }">
            <div class="d-flex align-center">
              <span>{{ item.accountNumber }}</span>
              <span class="ml-2 grey--text text--darken-1"
                >({{ item.name }})</span
              >
            </div>
          </template>
        </v-select>

        <div class="d-flex justify-end mt-6">
          <v-btn
            color="primary"
            large
            class="px-12"
            :loading="loading"
            @click="handleSubmit"
          >
            Confirm
          </v-btn>
        </div>
      </div>
      <div v-else class="text-center py-4">
        <p>No saved accounts found. Please use another payout method.</p>
        <v-btn color="primary" class="mt-4" @click="$emit('close')">
          Go Back
        </v-btn>
      </div>
    </v-card>
  </l-dialog>
</template>

<script>
import LDialog from '~/components/LDialog'

export default {
  name: 'SavedAccountsModal',
  components: {
    LDialog,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      selectedAccount: null,
      loading: false,
    }
  },
  computed: {
    savedAccounts() {
      return this.$store.getters['payments/savedBankAccounts']
    },
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.fetchSavedAccounts()
      }
    },
    savedAccounts: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.selectedAccount = newVal[0]
        }
      },
    },
  },
  methods: {
    async fetchSavedAccounts() {
      try {
        await this.$store.dispatch('payments/fetchPayoutFormData')
      } catch (error) {
        this.$store.dispatch(
          'snackbar/error',
          { errorMessage: 'Failed to load saved accounts' },
          { root: true }
        )
      }
    },
    async handleSubmit() {
      if (!this.selectedAccount) {
        this.$store.dispatch(
          'snackbar/error',
          { errorMessage: 'Please select an account' },
          { root: true }
        )
        return
      }

      this.loading = true
      try {
        // Call the new API endpoint with the selected account ID
        const result = await this.$store.dispatch(
          'payments/requestSavedAccountPayout',
          String(this.selectedAccount.id)
        )

        if (result.success) {
          this.$store.dispatch(
            'snackbar/success',
            {
              successMessage:
                result.message || 'Payout request submitted successfully',
            },
            { root: true }
          )
          this.$emit('submit', this.selectedAccount)
          this.$emit('close')
        } else {
          this.$store.dispatch(
            'snackbar/error',
            {
              errorMessage: result.message || 'Failed to submit payout request',
            },
            { root: true }
          )
        }
      } catch (error) {
        this.$store.dispatch(
          'snackbar/error',
          { errorMessage: 'Failed to submit payout request' },
          { root: true }
        )
      } finally {
        this.loading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.saved-accounts-modal {
  .v-card {
    padding: 24px;
  }
}
</style>
