<template>
  <v-row class="wrapper">
    <v-col class="col-12 px-0">
      <section class="intro">
        <div class="intro-helper">
          <v-container fluid>
            <v-row>
              <v-col cols="12">
                <div class="container-business mx-auto">
                  <v-row>
                    <v-col cols="12">
                      <div class="intro-content relative d-flex align-center">
                        <div class="mt-md-12">
                          <h3
                            class="intro-title"
                            v-html="$t('business_page.intro_title')"
                          ></h3>
                          <h4 class="intro-text">
                            {{ $t('business_page.intro_text') }}
                          </h4>
                          <div class="d-none d-sm-block">
                            <v-btn
                              x-large
                              color="primary--education"
                              width="220"
                              @click="scrollToForm"
                            >
                              <span class="font-weight-bold">{{
                                $t('business_page.lets_get_started')
                              }}</span>
                            </v-btn>
                          </div>
                        </div>
                      </div>
                      <div class="intro-img">
                        <div class="intro-img-helper">
                          <BusinessPageIntroImage
                            v-if="$vuetify.breakpoint.mdAndUp"
                          />
                          <BusinessPageIntroMobileImage v-else />
                        </div>
                      </div>
                      <v-btn
                        large
                        class="d-sm-none"
                        color="primary--education"
                        width="100%"
                        @click="scrollToForm"
                      >
                        <span class="font-weight-bold">{{
                          $t('business_page.lets_get_started')
                        }}</span>
                      </v-btn>
                    </v-col>
                  </v-row>
                </div>
              </v-col>
            </v-row>
          </v-container>
        </div>
        <div class="intro-bg">
          <svg
            preserveAspectRatio="none"
            viewBox="0 0 1920 196"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g opacity=".1">
              <path
                d="M1919-862H0v983.943s117.939 52.907 271.192 70.772c153.254 17.865 339.247-41.614 429.776-70.772C965.497 36.741 1162.06 21.625 1250.02 28.496c186.81 14.594 270.52 58.404 417.11 93.447 155.65 37.207 251.87 0 251.87 0V-862z"
                fill="#C4C4C4"
              />
              <path
                d="M1919-862H0v983.943s117.939 52.907 271.192 70.772c153.254 17.865 339.247-41.614 429.776-70.772C965.497 36.741 1162.06 21.625 1250.02 28.496c186.81 14.594 270.52 58.404 417.11 93.447 155.65 37.207 251.87 0 251.87 0V-862z"
                fill="url(#b)"
              />
            </g>
            <g opacity=".2">
              <path
                d="M1-862h1919v984.873s-117.94 52.957-271.19 70.839c-153.26 17.882-339.25-41.653-429.78-70.839C954.503 37.591 757.939 22.46 669.985 29.337c-186.819 14.609-270.526 58.46-417.116 93.536-155.644 37.242-251.869 0-251.869 0V-862z"
                fill="#C4C4C4"
              />
              <path
                d="M1-862h1919v984.873s-117.94 52.957-271.19 70.839c-153.26 17.882-339.25-41.653-429.78-70.839C954.503 37.591 757.939 22.46 669.985 29.337c-186.819 14.609-270.526 58.46-417.116 93.536-155.644 37.242-251.869 0-251.869 0V-862z"
                fill="url(#c)"
              />
            </g>
            <path
              d="M0-862h1919V92.183s-117.94 51.307-271.19 68.632c-153.26 17.324-339.25-40.356-429.78-68.632C953.503 9.558 756.939-5.101 668.985 1.563 482.166 15.714 398.459 58.2 251.869 92.182 96.225 128.264 0 92.183 0 92.183V-862z"
              fill="#2D2D2D"
            />
            <path
              d="M0-862h1919V92.183s-117.94 51.307-271.19 68.632c-153.26 17.324-339.25-40.356-429.78-68.632C953.503 9.558 756.939-5.101 668.985 1.563 482.166 15.714 398.459 58.2 251.869 92.182 96.225 128.264 0 92.183 0 92.183V-862z"
              fill="#2D2D2D"
            />
            <defs>
              <linearGradient
                id="b"
                x1="1919"
                y1="-862"
                x2="678.457"
                y2="781.577"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#80B622" />
                <stop offset="1" stop-color="#3C87F8" />
              </linearGradient>
              <linearGradient
                id="c"
                x1="1"
                y1="-862"
                x2="1243.04"
                y2="782.002"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#80B622" />
                <stop offset="1" stop-color="#3C87F8" />
              </linearGradient>
            </defs>
          </svg>
        </div>
      </section>

      <section class="form-section pb-4 pb-md-0">
        <v-container fluid>
          <v-row>
            <v-col cols="12">
              <div class="container-business mx-auto">
                <v-row>
                  <v-col
                    class="col-12 col-md-6 d-flex flex-column justify-space-between pt-md-8"
                  >
                    <div class="item mb-8 mb-md-0">
                      <v-row>
                        <v-col
                          class="col-12 col-md-10 d-flex flex-column flex-md-row"
                        >
                          <div class="item-icon mb-2 mb-md-0 mr-md-3">
                            <svg
                              width="35"
                              height="35"
                              viewBox="0 0 44 44"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <rect
                                width="44"
                                height="44"
                                rx="8"
                                fill="#3C87F8"
                                fill-opacity="0.12"
                              />
                              <path
                                d="M31.3346 22V28.5C31.3346 30.3856 31.3346 31.3284 30.7488 31.9142C30.1631 32.5 29.2203 32.5 27.3346 32.5H15.5846C13.9738 32.5 12.668 31.1942 12.668 29.5833V29.5833C12.668 27.9725 13.9738 26.6667 15.5846 26.6667H27.3346C29.2203 26.6667 30.1631 26.6667 30.7488 26.0809C31.3346 25.4951 31.3346 24.5523 31.3346 22.6667V15.5C31.3346 13.6144 31.3346 12.6716 30.7488 12.0858C30.1631 11.5 29.2203 11.5 27.3346 11.5H16.668C14.7823 11.5 13.8395 11.5 13.2538 12.0858C12.668 12.6716 12.668 13.6144 12.668 15.5V29.5833"
                                stroke="#3C87F8"
                                stroke-width="2"
                              />
                              <path
                                d="M18.5 19.6673L20.1262 21.2935C20.5168 21.6841 21.1499 21.6841 21.5404 21.2935L25.5 17.334"
                                stroke="#3C87F8"
                                stroke-width="2"
                                stroke-linecap="round"
                              />
                            </svg>
                          </div>
                          <div>
                            <div class="item-title mb-1 font-weight-bold">
                              {{ $t('business_page.form_title_1') }}
                            </div>
                            <div
                              class="item-text body-1"
                              v-html="$t('business_page.form_text_1')"
                            ></div>
                          </div>
                        </v-col>
                      </v-row>
                    </div>
                    <div class="item mb-8 mb-md-0">
                      <v-row>
                        <v-col
                          class="col-12 col-md-10 d-flex flex-column flex-md-row"
                        >
                          <div class="item-icon mb-2 mb-md-0 mr-md-3">
                            <svg
                              width="35"
                              height="35"
                              viewBox="0 0 44 44"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <rect
                                width="44"
                                height="44"
                                rx="8"
                                fill="#2D2D2D"
                                fill-opacity="0.12"
                              />
                              <path
                                d="M12.5 17.3327C12.5 16.2045 12.5021 15.4589 12.5765 14.9056C12.647 14.3811 12.7656 14.1815 12.8905 14.0565C13.0155 13.9316 13.2151 13.813 13.7396 13.7425C14.2929 13.6681 15.0385 13.666 16.1667 13.666H27.8333C28.9615 13.666 29.7071 13.6681 30.2604 13.7425C30.7849 13.813 30.9845 13.9316 31.1095 14.0565C31.2344 14.1815 31.353 14.3811 31.4235 14.9056C31.4979 15.4589 31.5 16.2045 31.5 17.3327V26.8327H12.5V17.3327Z"
                                stroke="#2D2D2D"
                                stroke-width="2"
                              />
                              <path
                                d="M12.2778 26.834C11.2959 26.834 10.5 27.6299 10.5 28.6118C10.5 30.0232 11.6442 31.1673 13.0556 31.1673H30.9444C32.3558 31.1673 33.5 30.0232 33.5 28.6118C33.5 27.6299 32.7041 26.834 31.7222 26.834H12.2778Z"
                                stroke="#2D2D2D"
                                stroke-width="2"
                              />
                            </svg>
                          </div>
                          <div>
                            <div class="item-title mb-1 font-weight-bold">
                              {{ $t('business_page.form_title_2') }}
                            </div>
                            <div
                              class="item-text body-1"
                              v-html="$t('business_page.form_text_2')"
                            ></div>
                          </div>
                        </v-col>
                      </v-row>
                    </div>
                    <div class="item">
                      <v-row>
                        <v-col
                          class="col-12 col-md-10 d-flex flex-column flex-md-row"
                        >
                          <div class="item-icon mb-2 mb-md-0 mr-md-3">
                            <svg
                              width="35"
                              height="35"
                              viewBox="0 0 44 44"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <rect
                                width="44"
                                height="44"
                                rx="8"
                                fill="#80B723"
                                fill-opacity="0.12"
                              />
                              <path
                                fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M22.3551 12.1038C22.0246 12.68 21.6558 13.5903 21.0919 14.9962L21.0171 15.1826C20.7287 15.9017 20.4743 16.536 20.2076 17.0433C19.9188 17.5929 19.5648 18.0971 19.0242 18.4991C18.4835 18.901 17.8988 19.0948 17.2893 19.2131C16.7266 19.3224 16.046 19.3833 15.2742 19.4524L14.9117 19.4848C13.5125 19.6101 12.6197 19.6936 12.028 19.8333C12.0134 19.8367 11.9993 19.8401 11.9856 19.8435C11.9944 19.8544 12.0036 19.8657 12.0132 19.8772C12.4003 20.3461 13.0612 20.9521 14.1002 21.8975L14.81 22.5432C15.2848 22.9751 15.7043 23.3568 16.0294 23.7085C16.3809 24.0887 16.688 24.5016 16.892 25.0233C16.9583 25.193 17.0134 25.3667 17.057 25.5436C17.191 26.0875 17.178 26.602 17.1099 27.1152C17.047 27.59 16.9241 28.1437 16.785 28.7703L16.6808 29.24C16.3998 30.5068 16.2159 31.3443 16.1492 31.936C16.1449 31.9735 16.1414 32.0086 16.1384 32.0413C16.1663 32.024 16.196 32.0051 16.2277 31.9844C16.7265 31.6592 17.3924 31.1191 18.3983 30.2994L18.4453 30.2611C19.0481 29.7699 19.5738 29.3415 20.0427 29.0279C20.5444 28.6924 21.0813 28.4173 21.7245 28.3146C22.16 28.2451 22.6039 28.2451 23.0394 28.3146C23.6826 28.4173 24.2195 28.6924 24.7212 29.0279C25.1901 29.3415 25.7158 29.7698 26.3186 30.2611L26.3656 30.2994C27.3715 31.1191 28.0374 31.6592 28.5362 31.9844C28.5679 32.0051 28.5976 32.024 28.6255 32.0413C28.6225 32.0086 28.619 31.9735 28.6147 31.936C28.548 31.3443 28.3641 30.5068 28.0831 29.24L27.9789 28.7703C27.8398 28.1438 27.7169 27.59 27.654 27.1152C27.5859 26.602 27.5729 26.0875 27.7069 25.5436C27.7505 25.3667 27.8056 25.193 27.8719 25.0233C28.0759 24.5016 28.383 24.0887 28.7345 23.7085C29.0596 23.3568 29.4791 22.9751 29.9539 22.5432L30.6637 21.8975C31.7027 20.9521 32.3636 20.3461 32.7507 19.8772C32.7603 19.8657 32.7695 19.8544 32.7783 19.8435C32.7647 19.8401 32.7505 19.8367 32.7359 19.8333C32.1442 19.6936 31.2514 19.6101 29.8522 19.4848L29.4897 19.4524C28.7179 19.3833 28.0373 19.3224 27.4746 19.2131C26.8651 19.0948 26.2804 18.901 25.7397 18.4991C25.1991 18.0971 24.8451 17.5929 24.5563 17.0433C24.2896 16.536 24.0352 15.9017 23.7468 15.1826L23.672 14.9962C23.1081 13.5903 22.7393 12.68 22.4088 12.1038C22.3996 12.0878 22.3907 12.0724 22.3819 12.0576C22.3732 12.0724 22.3643 12.0878 22.3551 12.1038ZM22.6361 11.7049C22.6361 11.7055 22.6318 11.7097 22.6232 11.7158C22.6318 11.7074 22.6361 11.7044 22.6361 11.7049ZM22.1407 11.7158C22.1321 11.7097 22.1278 11.7055 22.1278 11.7049C22.1278 11.7044 22.1321 11.7074 22.1407 11.7158ZM33.145 19.97C33.1445 19.9702 33.1397 19.9675 33.1319 19.9616C33.1416 19.9669 33.1455 19.9699 33.145 19.97ZM32.9887 19.5357C32.9913 19.5263 32.9936 19.5213 32.994 19.5211C32.9945 19.5209 32.9932 19.5256 32.9887 19.5357ZM11.7699 19.5211C11.7703 19.5213 11.7726 19.5263 11.7752 19.5357C11.7707 19.5256 11.7694 19.5209 11.7699 19.5211ZM11.632 19.9616C11.6242 19.9675 11.6194 19.9702 11.6189 19.97C11.6184 19.9699 11.6223 19.9669 11.632 19.9616ZM19.9436 10.7206C20.3628 9.98968 21.0913 9 22.382 9C23.6726 9 24.4011 9.98968 24.8203 10.7206C25.26 11.4872 25.6998 12.5837 26.2118 13.8606L26.3031 14.0882C26.6226 14.8845 26.8248 15.3841 27.0171 15.7499C27.1944 16.0873 27.3096 16.2021 27.3984 16.2681C27.4873 16.3342 27.6304 16.4114 28.0046 16.4841C28.4102 16.5628 28.9469 16.6126 29.8015 16.6891L30.2073 16.7255C31.4679 16.8383 32.5644 16.9364 33.3745 17.1276C34.1694 17.3152 35.2486 17.7167 35.6402 18.881C36.0317 20.0453 35.4145 21.0173 34.8945 21.6472C34.3646 22.2891 33.5503 23.0298 32.614 23.8815L31.8641 24.5637C31.3383 25.0421 31.0095 25.3429 30.7759 25.5956C30.5597 25.8295 30.4942 25.9509 30.4612 26.0354C30.4391 26.092 30.4207 26.1499 30.4062 26.2088C30.3845 26.2969 30.368 26.4339 30.4099 26.7497C30.4551 27.0908 30.5504 27.5261 30.7044 28.2201L30.815 28.7185C31.0734 29.8829 31.2918 30.8671 31.3772 31.6242C31.454 32.3047 31.5048 33.3337 30.8173 34.0992C30.4603 34.4967 29.9963 34.7829 29.4808 34.9236C28.4883 35.1944 27.5915 34.6872 27.0178 34.3132C26.3796 33.8971 25.5981 33.2602 24.6735 32.5067L24.6095 32.4545C23.946 31.9139 23.5223 31.5705 23.1758 31.3388C22.8499 31.1209 22.6962 31.075 22.6011 31.0599C22.4559 31.0367 22.308 31.0367 22.1628 31.0599C22.0677 31.075 21.914 31.1209 21.5881 31.3388C21.2416 31.5705 20.8179 31.9139 20.1544 32.4545L20.0904 32.5068C19.1658 33.2602 18.3843 33.8971 17.7461 34.3132C17.1724 34.6872 16.2756 35.1944 15.2831 34.9236C14.7676 34.7829 14.3036 34.4967 13.9466 34.0992C13.2591 33.3337 13.3099 32.3047 13.3867 31.6242C13.4721 30.8671 13.6905 29.8829 13.9489 28.7185L14.0595 28.2201C14.2135 27.5261 14.3088 27.0908 14.354 26.7497C14.3959 26.4339 14.3794 26.2969 14.3577 26.2088C14.3432 26.1499 14.3248 26.092 14.3027 26.0354C14.2697 25.9509 14.2042 25.8295 13.988 25.5956C13.7544 25.3429 13.4256 25.0421 12.8998 24.5637L12.2294 23.9538C12.2028 23.9296 12.1763 23.9055 12.1499 23.8815C11.2137 23.0298 10.3993 22.2891 9.86938 21.6472C9.34936 21.0173 8.73217 20.0453 9.12374 18.881C9.51532 17.7167 10.5945 17.3152 11.3894 17.1276C12.1995 16.9364 13.296 16.8383 14.5566 16.7255C14.5922 16.7223 14.6279 16.7191 14.6637 16.7159L14.9624 16.6891C15.817 16.6126 16.3537 16.5628 16.7593 16.4841C17.1335 16.4114 17.2766 16.3342 17.3655 16.2681C17.4543 16.2021 17.5695 16.0873 17.7468 15.7499C17.9391 15.3841 18.1413 14.8845 18.4608 14.0882L18.5117 13.9612C18.5252 13.9276 18.5386 13.894 18.5521 13.8605C19.0641 12.5837 19.5039 11.4872 19.9436 10.7206Z"
                                fill="#80B723"
                              />
                            </svg>
                          </div>
                          <div>
                            <div class="item-title mb-1 font-weight-bold">
                              {{ $t('business_page.form_title_3') }}
                            </div>
                            <div class="item-text body-1">
                              <div
                                v-html="$t('business_page.form_text_3')"
                              ></div>
                              <nuxt-link to="/teacher-listing">{{
                                $t('business_page.browse_our_teachers_here')
                              }}</nuxt-link>
                            </div>
                          </div>
                        </v-col>
                      </v-row>
                    </div>
                  </v-col>
                  <v-col class="col-12 col-md-6">
                    <v-form
                      ref="form"
                      v-model="valid"
                      class="business-form"
                      lazy-validation
                      @submit.prevent="submitFormHandler"
                    >
                      <div class="form pb-md-3">
                        <div
                          class="form-wrap pa-3 mx-auto ml-md-auto z-index-5"
                        >
                          <v-container class="pa-0">
                            <v-row>
                              <v-col cols="12">
                                <div
                                  class="form-title font-weight-bold"
                                  v-html="$t('business_page.form_form_title')"
                                ></div>
                              </v-col>
                              <v-col class="col-12 pb-0">
                                <div class="pb-1 input-title body-2">
                                  {{ $t('business_page.business_form_1') }}
                                </div>
                                <text-input
                                  :value="formData.fullName"
                                  type-class="border-gradient"
                                  height="44"
                                  :rules="[rules.required]"
                                  autocomplete="given-name"
                                  @input="updateValue($event, 'fullName')"
                                ></text-input>
                              </v-col>
                              <v-col class="col-12 py-0">
                                <div class="pb-1 input-title body-2">
                                  {{ $t('business_page.business_form_2') }}
                                </div>
                                <text-input
                                  :value="formData.companyName"
                                  type-class="border-gradient"
                                  height="44"
                                  :rules="[rules.required]"
                                  autocomplete="organization"
                                  @input="updateValue($event, 'companyName')"
                                ></text-input>
                              </v-col>
                              <v-col class="col-12 py-0">
                                <div class="pb-1 input-title body-2">
                                  {{ $t('business_page.business_form_3') }}
                                </div>
                                <text-input
                                  :value="formData.email"
                                  type-class="border-gradient"
                                  height="44"
                                  :rules="[rules.required, rules.email]"
                                  autocomplete="email"
                                  @input="updateValue($event, 'email')"
                                ></text-input>
                              </v-col>
                              <v-col class="col-12 py-0">
                                <div class="pb-1 input-title body-2">
                                  {{ $t('business_page.business_form_4') }}
                                </div>
                                <text-input
                                  :value="formData.phoneNumber"
                                  type-class="border-gradient"
                                  height="44"
                                  autocomplete="tel"
                                  @input="updateValue($event, 'phoneNumber')"
                                ></text-input>
                              </v-col>
                              <v-col class="col-12 py-0">
                                <div class="pb-1 input-title body-2">
                                  {{ $t('business_page.business_form_5') }}
                                </div>
                                <text-input
                                  :value="formData.languagesToLearn"
                                  type-class="border-gradient"
                                  height="44"
                                  @input="
                                    updateValue($event, 'languagesToLearn')
                                  "
                                ></text-input>
                              </v-col>
                              <v-col class="col-12 py-0">
                                <div class="pb-1 input-title body-2">
                                  {{ $t('business_page.business_form_6') }}
                                </div>
                                <text-input
                                  :value="formData.numberOfStudents"
                                  type-class="border-gradient"
                                  height="44"
                                  @input="
                                    updateValue($event, 'numberOfStudents')
                                  "
                                ></text-input>
                              </v-col>
                              <v-col class="col-12 py-0">
                                <div class="pb-1 input-title body-2">
                                  {{ $t('business_page.business_form_7') }}
                                </div>
                                <v-textarea
                                  :value="formData.message"
                                  class="l-textarea"
                                  no-resize
                                  height="100"
                                  :placeholder="
                                    $t('business_page.business_form_8')
                                  "
                                  solo
                                  dense
                                  hide-details
                                  @input="updateValue($event, 'message')"
                                ></v-textarea>
                              </v-col>
                              <v-col class="col-12">
                                <v-btn
                                  type="submit"
                                  x-large
                                  width="100%"
                                  :height="$vuetify.breakpoint.xsOnly ? 44 : 52"
                                  color="primary--education"
                                  :disabled="disabledSendButton"
                                  class="mt-1"
                                >
                                  <span class="font-weight-bold">{{
                                    $t('submit')
                                  }}</span>
                                </v-btn>
                              </v-col>
                              <v-col
                                class="col-12 body-2 darkLight--text text--lighten-3"
                              >
                                <div
                                  class="mt-1"
                                  v-html="$t('business_page.form_form_bottom')"
                                ></div>
                              </v-col>
                            </v-row>
                          </v-container>
                        </div>
                      </div>
                    </v-form>
                  </v-col>
                </v-row>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </section>

      <section class="companies py-4 py-md-8">
        <v-container fluid>
          <v-row>
            <v-col cols="12">
              <div class="container-business mx-auto">
                <v-row class="relative">
                  <dots-icon
                    class="absolute top-right z-index-30"
                    width="132"
                  ></dots-icon>
                  <v-col cols="12">
                    <div
                      class="relative companies-wrap pt-6 pb-4 pb-sm-6 pb-md-8 px-3 px-sm-5 px-md-11 greyLight--text text--lighten-2 z-index-20"
                    >
                      <v-row>
                        <v-col cols="12">
                          <h3
                            class="companies-title title-decoration mb-md-1 text-center font-weight-bold"
                          >
                            {{ $t('business_page.companies_title') }}:
                          </h3>
                        </v-col>
                        <v-col cols="12">
                          <div
                            class="companies-list d-flex flex-wrap justify-space-around mt-md-1"
                          >
                            <div class="companies-list__item pa-2">
                              <v-img
                                :src="
                                  require('~/assets/images/business-page/companies/GfK_logo.svg')
                                "
                                contain
                                class="mb-2 mb-md-4"
                                width="90"
                                :options="{ rootMargin: '20%' }"
                              ></v-img>
                            </div>
                            <div class="companies-list__item pa-2">
                              <v-img
                                :src="
                                  require('~/assets/images/business-page/companies/you_lead.svg')
                                "
                                contain
                                class="mb-2 mb-md-4"
                                width="112"
                                :options="{ rootMargin: '20%' }"
                              ></v-img>
                            </div>
                            <div class="companies-list__item pa-2">
                              <v-img
                                :src="
                                  require('~/assets/images/business-page/companies/merxu.svg')
                                "
                                contain
                                class="mb-2 mb-md-4"
                                width="109"
                                :options="{ rootMargin: '20%' }"
                              ></v-img>
                            </div>
                            <div class="companies-list__item pa-2">
                              <v-img
                                :src="
                                  require('~/assets/images/business-page/companies/gorilla.svg')
                                "
                                contain
                                class="mb-2 mb-md-4"
                                width="73"
                                :options="{ rootMargin: '20%' }"
                              ></v-img>
                            </div>
                            <div class="companies-list__item pa-2">
                              <v-img
                                :src="
                                  require('~/assets/images/business-page/companies/columbus.svg')
                                "
                                contain
                                class="mb-2 mb-md-4"
                                width="106"
                                :options="{ rootMargin: '20%' }"
                              ></v-img>
                            </div>
                            <div class="companies-list__item pa-2">
                              <v-img
                                :src="
                                  require('~/assets/images/business-page/companies/pragma_go.svg')
                                "
                                contain
                                class="mb-2 mb-md-4"
                                width="141"
                                :options="{ rootMargin: '20%' }"
                              ></v-img>
                            </div>
                          </div>
                        </v-col>
                        <v-col
                          class="col-12 col-md-8 font-italic font-weight-medium"
                        >
                          <div class="companies-text">
                            {{ $t('business_page.companies_text') }}
                          </div>
                        </v-col>
                        <v-col
                          class="col-12 col-md-4 d-flex justify-md-end align-end"
                        >
                          <div
                            class="d-flex align-end align-md-center mt-2 mt-md-0 pl-md-6"
                          >
                            <v-img
                              :src="
                                require('~/assets/images/business-page/user-avatar.svg')
                              "
                              contain
                              width="48"
                              :options="{ rootMargin: '20%' }"
                            ></v-img>
                            <div class="companies-user pl-2 ml-md-3">
                              {{ $t('business_page.business_partner_since') }}
                            </div>
                          </div>
                        </v-col>
                      </v-row>
                    </div>
                  </v-col>
                  <dots-icon
                    class="absolute bottom-left z-index-10"
                    width="132"
                  ></dots-icon>
                </v-row>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </section>

      <section class="offer py-4 py-md-8">
        <v-container fluid class="relative z-index-10">
          <v-row>
            <v-col cols="12">
              <div class="container-business mx-auto">
                <v-row>
                  <v-col cols="12">
                    <h3
                      class="offer-title title-decoration mb-3 mb-md-8 text-center font-weight-bold"
                    >
                      {{ $t('business_page.offer_title') }}
                    </h3>
                  </v-col>
                </v-row>
                <v-row class="relative">
                  <dots-icon class="absolute top-left" width="132"></dots-icon>
                  <v-col
                    v-for="i in 6"
                    :key="i"
                    class="col-12 col-sm-6 col-md-4 z-index-10"
                  >
                    <div class="item pa-3 pa-md-4">
                      <div class="item-icon d-flex justify-center align-center">
                        <v-img
                          :src="
                            require(`~/assets/images/business-page/offer_icon_${i}.svg`)
                          "
                          contain
                          :height="$vuetify.breakpoint.smAndDown ? 120 : 140"
                          :options="{ rootMargin: '20%' }"
                        ></v-img>
                      </div>
                      <div class="item-title mb-2 font-weight-bold">
                        {{ $t(`business_page.offer_card_label_${i}`) }}
                      </div>
                      <div
                        class="item-text darkLight--text text--lighten-2 body-1"
                        v-html="$t(`business_page.offer_card_text_${i}`)"
                      ></div>
                    </div>
                  </v-col>
                  <dots-icon
                    class="absolute bottom-right"
                    width="132"
                  ></dots-icon>
                </v-row>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </section>

      <section class="for-you pt-4 pb-0 pb-sm-6 pt-md-6 pb-md-10">
        <v-container fluid>
          <v-row>
            <v-col cols="12">
              <div class="container-business mx-auto">
                <div class="for-you-wrap relative pa-4 pb-0 px-md-8 py-md-7">
                  <v-row>
                    <v-col class="col-12 col-md-7 d-flex align-center">
                      <div class="pr-0 mr-md-6">
                        <div class="for-you-title font-weight-bold">
                          {{ $t('business_page.for_you_title') }}
                        </div>
                        <div class="mt-md-2 body-1">
                          {{ $t('business_page.for_you_text') }}
                        </div>
                        <div v-for="i in 3" :key="i" class="mt-4 mt-md-5">
                          <div
                            class="for-you-subtitle text--gradient font-weight-bold"
                          >
                            {{ $t(`business_page.for_you_item_label_${i}`) }}
                          </div>
                          <div class="for-you-text">
                            {{ $t(`business_page.for_you_item_text_${i}`) }}
                          </div>
                        </div>

                        <v-img
                          :src="
                            require('~/assets/images/business-page/for-you.svg')
                          "
                          class="d-md-none mx-auto mt-7"
                          contain
                          width="214"
                          :options="{ rootMargin: '20%' }"
                        ></v-img>

                        <div class="for-you-btn mt-7">
                          <v-btn
                            x-large
                            :width="$vuetify.breakpoint.smAndDown ? 270 : 220"
                            :height="$vuetify.breakpoint.xsOnly ? 44 : 52"
                            color="primary--education"
                            @click="scrollToForm"
                          >
                            <span class="font-weight-bold">{{
                              $t('business_page.lets_talk')
                            }}</span>
                          </v-btn>
                        </div>
                      </div>
                    </v-col>
                    <v-col
                      class="col-12 col-md-5 d-flex flex-wrap justify-center align-center"
                    >
                      <div
                        class="for-you-decoration absolute d-none d-sm-block"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="259"
                          height="183"
                          viewBox="0 0 259 183"
                          fill="none"
                        >
                          <path
                            opacity="0.07"
                            d="M95.2382 -89.0897C61.6838 -51.2095 -27.3398 63.998 9.15811 150.5C25.9243 204.687 150.886 176.375 230.66 161.998C385.924 134.116 496.96 94.4118 494.593 -5.85407C492.227 -106.12 419.636 -248.966 343.677 -228.116C283.275 -211.568 160.74 -163.02 95.2382 -89.0897Z"
                            fill="url(#paint0_linear_20903_3292)"
                          />
                          <defs>
                            <linearGradient
                              id="paint0_linear_20903_3292"
                              x1="-500.084"
                              y1="45.0239"
                              x2="913.708"
                              y2="57.8574"
                              gradientUnits="userSpaceOnUse"
                            >
                              <stop stop-color="#80B622" />
                              <stop offset="0.227673" stop-color="#71AB53" />
                              <stop offset="0.377009" stop-color="#66A474" />
                              <stop offset="0.57618" stop-color="#589B9F" />
                              <stop offset="0.748728" stop-color="#4E94BF" />
                              <stop offset="1" stop-color="#3C87F8" />
                            </linearGradient>
                          </defs>
                        </svg>
                      </div>
                      <dots-icon class="d-none d-md-block"></dots-icon>
                      <v-img
                        :src="
                          require('~/assets/images/business-page/for-you.svg')
                        "
                        class="d-none d-md-block"
                        contain
                        width="443"
                        :options="{ rootMargin: '20%' }"
                      ></v-img>
                    </v-col>
                  </v-row>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </section>
    </v-col>
  </v-row>
</template>

<script>
import BusinessPageIntroImage from '~/components/images/BusinessPageIntroImage.vue'
import TextInput from '~/components/form/TextInput.vue'
import DotsIcon from '~/components/business-page/icons/DotsIcon.vue'
import BusinessPageIntroMobileImage from '~/components/images/BusinessPageIntroMobileImage.vue'

const defaultFormData = () => ({
  fullName: '',
  email: '',
  companyName: '',
  phoneNumber: '',
  numberOfStudents: '',
  languagesToLearn: '',
  message: '',
})

export default {
  name: 'BusinessPageComponent',
  components: {
    BusinessPageIntroMobileImage,
    BusinessPageIntroImage,
    DotsIcon,
    TextInput,
  },
  data() {
    return {
      formData: defaultFormData(),
      valid: true,
      rules: {
        required: (v) => (!!v && v.length > 1) || this.$t('field_required'),
        email: (value) => {
          const pattern = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/

          return pattern.test(value) || this.$t('invalid_email')
        },
      },
      disabledSendButton: false,
    }
  },
  computed: {
    previewImage() {
      return (
        process.env.NUXT_ENV_URL +
        require(`~/assets/images/business-page/img1.svg`)
      )
    },
  },
  beforeDestroy() {
    this.resetForm()
  },
  methods: {
    updateValue(value, property) {
      this.$refs.form.resetValidation()

      this.formData[property] = value
    },
    resetForm() {
      this.formData = defaultFormData()

      this.$refs.form.resetValidation()
    },
    async submitFormHandler() {
      const routeRegex = this.$route.matched[0]?.regex

      await this.$refs.form.validate()

      if (!this.valid) {
        return
      }

      this.disabledSendButton = true

      await this.$store
        .dispatch('business_page/submitForm', this.formData)
        .then(async () => {
          await this.$cookiz.set('business_success_page_allowed', 1, {
            domain: process.env.NUXT_ENV_COOKIE_DOMAIN,
            path: '/',
          })

          this.$store.dispatch('snackbar/success', {
            successMessage: 'form_sent_correctly',
          })
          this.resetForm()

          if (!routeRegex.test('/business/success')) {
            this.$router.push('/business/success')
          } else {
            window.location.reload()
          }
        })
        .catch((e) => {
          this.$store.dispatch('snackbar/error')

          console.info(e)
        })

      this.disabledSendButton = false
    },
    scrollToForm() {
      this.$vuetify.goTo(this.$refs.form, {
        duration: 2000,
        offset: 20,
        easing: 'linear',
      })
    },
  },
}
</script>

<style scoped lang="scss">
@import '~assets/styles/business-page.scss';
</style>

<style lang="scss">
.business-page {
  .form-section {
    .v-text-field--outlined {
      border-radius: 8px;

      .v-input__slot {
        background: var(--white, rgba(242, 242, 242, 0.4)) !important;

        &::before {
          border-radius: inherit !important;
        }
      }

      .v-text-field__details {
        margin-bottom: 0 !important;
      }
    }

    .l-textarea {
      border-radius: 8px !important;

      .v-input__slot {
        background: var(--white, rgba(242, 242, 242, 0.4)) !important;

        &::before {
          border-radius: inherit !important;
        }
      }
    }

    a {
      color: var(--v-darkLight-lighten3) !important;

      &:hover {
        color: var(--v-orange-base) !important;
        transition: color 0.3s;
      }
    }
  }
}
</style>
