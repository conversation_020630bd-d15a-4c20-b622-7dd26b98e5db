<template>
  <text-input
    :key="key"
    ref="priceInput"
    :value="value_"
    type-class="border-gradient"
    height="32"
    hide-details
    placeholder="0.00"
    :rules="[...rules, validatePrice]"
    :prefix="currentCurrencySymbol"
    @keydown="keyCode = $event.code"
    @input="updateValue($event)"
  ></text-input>
</template>

<script>
import TextInput from '@/components/form/TextInput'

const regex = /^\d+\.?\d{0,2}$/

export default {
  name: 'LessonPrice',
  components: { TextInput },
  props: {
    value: {
      type: [String, Number],
      required: true,
    },
    rules: {
      type: Array,
      default: () => [],
    },
    length: {
      type: Number,
      required: true,
      default: 30,
    },
    freeTrial: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      key: 1,
      keyCode: null,
    }
  },
  computed: {
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
    currencyCode() {
      const currencyMap = {
        $: 'USD',
        '€': 'EUR',
        '£': 'GBP',
        zł: 'PLN',
        A$: 'AUD',
        C$: 'CAD',
      }
      return currencyMap[this.currentCurrencySymbol] || 'USD'
    },
    value_() {
      return this.value || null
    },
    minimumPrice() {
      const pricingMap = {
        EUR: { 30: 7, 60: 11, 90: 16, 120: 21 },
        GBP: { 30: 6, 60: 10, 90: 15, 120: 20 },
        PLN: { 30: 30, 60: 50, 90: 70, 120: 85 },
        USD: { 30: 8, 60: 12, 90: 17, 120: 22 },
        AUD: { 30: 12, 60: 20, 90: 28, 120: 36 },
        CAD: { 30: 11, 60: 18, 90: 25, 120: 32 },
      }

      return pricingMap[this.currencyCode]?.[this.length] || 10
    },
    minimumValidation(value) {
      if (Number(length) === 60 || Number(value) > 0) {
        return this.minimumPrice
      } else {
        return 0
      }
    },
  },
  mounted() {
    this.validation(this.value ?? 0, this.freeTrial)
  },
  methods: {
    updateValue(event) {
      let value

      if (
        regex.test(event) ||
        this.keyCode === 'Backspace' ||
        this.keyCode === 'Delete'
      ) {
        value = event
      } else {
        value = this.value

        this.key++
        this.$nextTick(() => {
          this.$refs.priceInput.focus()
        })
      }

      this.keyCode = null

      this.validation(value)
      this.$emit('input', value)
    },
    validation(value, isFreeTrial = false) {
      const minPrice = this.minimumPrice
      this.$emit(
        'validation',
        (function () {
          if (isFreeTrial) {
            return true
          } else if (Number(length) === 60 && Number(value) < minPrice) {
            return false
          } else if (Number(value) > 0 && Number(value) < minPrice) {
            return false
          }
          return true
        })()
      )
    },
    validatePrice(value) {
      const minPrice = this.minimumPrice
      if (this.$props.freeTrial) {
        return true
      } else if (Number(length) === 60 && Number(value) < minPrice) {
        return `Error: Minimum price is ${minPrice}`
      } else if (Number(value) > 0 && Number(value) < minPrice) {
        return `Error: Minimum price is ${minPrice}`
      }
      return true
    },
  },
}
</script>
