<template>
  <div :class="['text-input', `text-input--${typeClass}`]">
    <v-text-field
      ref="input"
      :value="value"
      :type="type"
      :height="height"
      outlined
      dense
      color="dark"
      :autocomplete="autocomplete"
      :disabled="disabled"
      :hide-details="hideDetails"
      :append-icon="appendIcon"
      :placeholder="placeholder"
      :counter="counter"
      :prefix="prefix"
      :rules="rules"
      :required="required"
      :name="name"
      v-on="$listeners"
    >
      <template v-if="$slots.append" #append>
        <slot name="append"></slot>
      </template>
    </v-text-field>
  </div>
</template>

<script>
export default {
  name: 'TextInput',
  props: {
    // eslint-disable-next-line vue/require-prop-types
    value: {
      required: true,
    },
    type: {
      type: String,
      default: 'text',
    },
    height: {
      type: [String, Number],
      default: '50',
    },
    typeClass: {
      type: String,
      default: 'outlined',
    },
    placeholder: {
      type: String,
      default: '',
    },
    appendIcon: {
      type: String,
      default: '',
    },
    hideDetails: {
      type: Boolean,
      default: false,
    },
    required: {
      type: Boolean,
      default: false,
    },
    name: {
      type: String,
      default: null,
    },
    prefix: {
      type: String,
      default: null,
    },
    counter: {
      type: String,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    rules: {
      type: Array,
      default: () => [],
    },
    autocomplete: {
      type: String,
      default: 'off',
    },
  },
  methods: {
    focus() {
      this.$refs.input.focus()
    },
  },
}
</script>

<style lang="scss">
.text-input {
  .v-input {
    &.v-text-field--outlined fieldset {
      border: 1px solid #bebebe !important;
    }
  }

  &--border-gradient {
    .v-input {
      &.v-input--is-focused {
        .v-input__control {
          .v-input__slot {
            position: relative;

            fieldset {
              border-color: transparent !important;
            }

            &::before {
              display: block !important;
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              border-style: none;
              border-radius: 24px;
              padding: 1px;
              background: linear-gradient(
                126.15deg,
                var(--v-success-base) 0%,
                var(--v-primary-base) 102.93%
              );
              -webkit-mask: linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
              -webkit-mask-composite: destination-out;
              mask-composite: exclude;
              transform: none;
            }
          }
        }
      }
    }
  }

  &--outlined {
    .theme--light.v-text-field--outlined:not(.v-input--is-focused):not(.v-input--has-state):not(.v-input--is-disabled)
      > .v-input__control
      > .v-input__slot:hover
      fieldset,
    .v-text-field--outlined.v-input--is-focused fieldset,
    .v-text-field--outlined.v-input--has-state fieldset {
      border: 1px solid var(--v-orange-base) !important;
    }
  }
}
</style>
