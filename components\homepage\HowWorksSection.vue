<template>
  <section class="how-works">
    <v-container class="py-0">
      <v-row>
        <v-col class="col-12 py-0">
          <div class="section-head section-head--decorated">
            <h3
              class="section-head-title"
              style="color: #262626; -webkit-text-fill-color: #262626"
            >
              {{ $t('home_page.details_section_title') }}
            </h3>
          </div>
        </v-col>
      </v-row>
    </v-container>
    <div class="how-works-content">
      <div class="section-bg">
        <v-img
          :src="require('~/assets/images/homepage/details-bg.png')"
          :options="{ rootMargin: '50%' }"
        ></v-img>
      </div>
      <v-container fill-height class="py-0">
        <v-row>
          <v-col class="col-xl-10 offset-xl-1 py-0">
            <v-row>
              <v-col class="col-12 col-md-4 offset-md-4 py-0">
                <div class="how-works-item how-works-item-i1">
                  <div class="how-works-item-bg d-none d-md-block">
                    <v-img
                      :src="require('~/assets/images/homepage/arrow-1.svg')"
                      :options="{ rootMargin: '50%' }"
                      contain
                    ></v-img>
                  </div>
                  <div class="how-works-item-bg d-md-none">
                    <v-img
                      :src="require('~/assets/images/homepage/arrow-1-1.svg')"
                      :options="{ rootMargin: '50%' }"
                      contain
                    ></v-img>
                  </div>
                  <div class="how-works-item-title">
                    {{ $t('home_page.details_1_title') }}
                  </div>
                  <div class="how-works-item-text">
                    {{ $t('home_page.details_1_text') }}
                  </div>
                </div>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="col-12 col-md-4 py-0">
                <div class="how-works-item how-works-item-i2">
                  <div class="how-works-item-bg d-none d-md-block">
                    <v-img
                      :src="require('~/assets/images/homepage/arrow-2.svg')"
                      :options="{ rootMargin: '50%' }"
                      contain
                    ></v-img>
                  </div>
                  <div class="how-works-item-bg d-md-none">
                    <v-img
                      :src="require('~/assets/images/homepage/arrow-2-1.svg')"
                      :options="{ rootMargin: '50%' }"
                      contain
                    ></v-img>
                  </div>
                  <div class="how-works-item-title">
                    {{ $t('home_page.details_2_title') }}
                  </div>
                  <div class="how-works-item-text">
                    {{ $t('home_page.details_2_text') }}
                  </div>
                </div>
              </v-col>
              <v-col class="col-12 col-md-4 offset-md-4 py-0">
                <div class="how-works-item how-works-item-i3">
                  <div class="how-works-item-bg d-none d-md-block">
                    <v-img
                      :src="require('~/assets/images/homepage/arrow-3.svg')"
                      :options="{ rootMargin: '50%' }"
                      contain
                    ></v-img>
                  </div>
                  <div class="how-works-item-bg d-md-none">
                    <v-img
                      :src="require('~/assets/images/homepage/arrow-3-1.svg')"
                      :options="{ rootMargin: '50%' }"
                      contain
                    ></v-img>
                  </div>
                  <div class="how-works-item-title">
                    {{ $t('home_page.details_3_title') }}
                  </div>
                  <div class="how-works-item-text">
                    {{ $t('home_page.details_3_text') }}
                  </div>
                </div>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="col-12 col-md-4 offset-md-4 py-0">
                <div class="how-works-item how-works-item-i4">
                  <div class="how-works-item-title">
                    {{ $t('home_page.details_4_title') }}
                  </div>
                  <div class="how-works-item-text">
                    {{ $t('home_page.details_4_text') }}
                  </div>
                </div>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </section>
</template>

<script>
export default {
  name: 'HowWorksSection',
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.how-works {
  position: relative;
  padding-top: 105px;

  @media #{map-get($display-breakpoints, 'xs-only')} {
    padding-top: 80px;
  }

  .section-head {
    margin-bottom: 90px;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-bottom: 60px;
    }
  }

  &-content {
    position: relative;
    color: #fff;

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      height: 1px;
      min-height: 730px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding: 146px 0 92px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      padding: 180px 0 65px;
    }

    &::after {
      content: '';
      position: absolute;
      top: -230px;
      right: 0;
      width: 295px;
      height: 648px;
      background-image: url('~assets/images/homepage/details-circle-bg.svg');
      background-size: contain;
      background-position: right center;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        top: -115px;
        width: 200px;
        height: 448px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        top: 0;
        width: 110px;
        height: 221px;
      }

      @media only screen and (max-width: $xsm-and-down) {
        top: -50px;
      }
    }
  }

  &-item {
    position: relative;
    margin-bottom: 75px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: 80%;
      margin-bottom: 0;
      padding-left: 30px;
      padding-bottom: 110px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      max-width: 100%;
      padding-left: 25px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      padding-bottom: 150px;
    }

    &-bg {
      position: absolute;
    }

    &-title {
      position: relative;

      &::before {
        position: absolute;
        top: -8px;
        left: -28px;
        font-size: 82px;
        font-weight: 700;
        line-height: 0.8;
        color: var(--v-orange-base);
        opacity: 0.2;
      }
    }

    &-i1 {
      .how-works-item-bg {
        left: -265px;
        top: 20px;
        width: 210px;
        height: 170px;

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          left: -225px;
          top: 52px;
          width: 180px;
          height: 155px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          left: 205px;
          top: 60px;
          width: 169px;
          height: 142px;
        }

        @media only screen and (max-width: $xsm-and-down) {
          left: 180px;
          top: 65px;
        }

        @media only screen and (max-width: $xxs-and-down) {
          left: 125px;
        }

        @media only screen and (max-width: $xxs-and-down) {
          height: 150px;
          top: auto;
          bottom: 0;
        }
      }

      .details-item-title::before {
        content: '1';
      }
    }

    &-i2 {
      .how-works-item-bg {
        right: -350px;
        top: 8px;
        width: 280px;
        height: 80px;

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          right: -292px;
          top: 22px;
          width: 238px;
          height: 66px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          left: 70px;
          top: 65px;
          width: 167px;
          height: 190px;
        }

        @media only screen and (max-width: $xsm-and-down) {
          left: 85px;
          top: 70px;
        }

        @media only screen and (max-width: $xxs-and-down) {
          height: 150px;
          top: auto;
          bottom: 0;
        }
      }

      .details-item-title::before {
        content: '2';
      }
    }

    &-i3 {
      .how-works-item-bg {
        left: -20px;
        top: calc(100% + 22px);
        width: 178px;
        height: 135px;

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          left: 0;
          width: 148px;
          height: 115px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          left: auto;
          right: 5px;
          top: 75px;
          width: 174px;
          height: 140px;
        }

        @media only screen and (max-width: $xxs-and-down) {
          height: 150px;
          top: auto;
          bottom: 0;
        }
      }

      .details-item-title::before {
        content: '3';
      }
    }

    &-i4 {
      .details-item-title::before {
        content: '4';
      }
    }
  }
}
</style>
