<template>
  <v-form @submit.prevent="submit">
    <text-input
      :value="value"
      :class="['search-input', { 'search-input--small': small }]"
      type-class="border-gradient"
      hide-details
      :disabled="disabled"
      :placeholder="$t(placeholder)"
      @input="$emit('input', $event)"
    >
      <template #append>
        <div style="margin-top: 6px; cursor: pointer" @click="submit">
          <v-img :src="require('~/assets/images/search-icon.svg')"></v-img>
        </div>
      </template>
    </text-input>
  </v-form>
</template>

<script>
import TextInput from '~/components/form/TextInput'

export default {
  name: 'SearchInput',
  components: { TextInput },
  props: {
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      required: true,
    },
    small: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    submit() {
      this.$emit('submit')
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.search-input {
  .v-input {
    background-color: #fff;
    border-radius: 50px !important;
    box-shadow: 0 4px 14px rgba(217, 225, 236, 0.47);

    @media #{map-get($display-breakpoints, 'xs-only')} {
      border-radius: 10px !important;
    }

    input::placeholder {
      color: var(--v-greyLight-darken2);
    }

    .v-input__control {
      & > .v-input__slot {
        height: 56px !important;

        @media #{map-get($display-breakpoints, 'xs-only')} {
          height: 40px !important;
        }
      }
    }

    .v-input__append-inner {
      margin-top: 9px !important;

      @media #{map-get($display-breakpoints, 'xs-only')} {
        margin-top: 6px !important;
      }

      .v-image {
        width: 26px !important;
        height: 26px !important;

        @media #{map-get($display-breakpoints, 'xs-only')} {
          width: 20px !important;
          height: 20px !important;
        }
      }
    }

    &.v-input--is-focused {
      .v-input__control {
        & > .v-input__slot {
          &::before {
            border-radius: 16px !important;
          }
        }
      }
    }

    &.v-input.v-text-field--outlined fieldset {
      border-color: transparent !important;
      box-shadow: 0 4px 14px rgba(217, 225, 236, 0.47);
    }
  }

  &--inner-border {
    .v-input {
      .v-input__control {
        & > .v-input__slot {
          padding-top: 5px !important;
          padding-left: 5px !important;
          padding-bottom: 5px !important;

          & > .v-text-field__slot {
            position: relative;
            padding: 0 16px;
            background-color: transparent !important;

            @media only screen and (max-width: $mac-13-and-down) {
              padding: 0 12px;
            }

            @media #{map-get($display-breakpoints, 'xs-only')} {
              padding: 0 10px;
            }

            &::before {
              display: none !important;
              content: '';
              position: absolute;
              top: -1px;
              left: -1px;
              width: calc(100% + 2px);
              height: calc(100% + 2px);
              border-style: none;
              border-radius: 15px;
              padding: 1px;
              background: linear-gradient(
                126.15deg,
                var(--v-success-base) 0%,
                var(--v-primary-base) 102.93%
              );
              -webkit-mask: linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
              -webkit-mask-composite: destination-out;
              mask-composite: exclude;
              transform: none;

              @media #{map-get($display-breakpoints, 'xs-only')} {
                border-radius: 9px;
              }
            }

            input {
              position: relative;
              z-index: 2;
            }
          }
        }
      }

      .v-input__append-inner {
        margin-top: 4px !important;
        padding-left: 15px;

        @media #{map-get($display-breakpoints, 'xs-only')} {
          margin-top: 0 !important;
        }
      }

      &.v-input--is-focused {
        .v-input__control {
          & > .v-input__slot {
            &::before {
              display: none !important;
            }

            & > .v-text-field__slot {
              &::before {
                display: block !important;
              }
            }
          }
        }
      }
    }
  }

  &--small {
    .v-input {
      .v-input__control {
        & > .v-input__slot {
          height: 44px !important;
        }
      }

      .v-input__append-inner {
        margin-top: 6px !important;

        .v-image {
          width: 20px !important;
          height: 20px !important;
        }
      }
    }
  }
}
</style>
