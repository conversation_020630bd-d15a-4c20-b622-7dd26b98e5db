export const actions = {
  getPageData() {
    return this.$axios
      .get(`${process.env.NUXT_ENV_API_URL}/business-page-data`)
      .then((response) => JSON.parse(response.data))
      .then((data) => data)
      .catch((e) => console.log(e))
  },
  submitForm(ctx, data) {
    return this.$axios.post(
      `${process.env.NUXT_ENV_API_URL}/business-page-send-form`,
      JSON.stringify(data),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  },
}
