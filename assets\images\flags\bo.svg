<svg xmlns="http://www.w3.org/2000/svg" height="480" width="640" fill="#28ff09" viewBox="0 0 640 480">
  <path fill="#007934" d="M0 0h640v480H0z"/>
  <path fill="#ffe000" d="M0 0h640v320H0z"/>
  <path fill="#d52b1e" d="M0 0h640v160H0z"/>
  <g stroke="#000" stroke-width="1.5">
    <g stroke-width=".217">
      <path d="M400.692 187.885c1.08.916 183.545 161.535 184.346 162.235.472-.555 1.388-1.626 1.837-2.128-.752-.678-183.282-161.2-184.356-162.178.326-.602-2.497 2.7-1.827 2.07z" fill="#a05a2c" transform="matrix(-.60671 .07311 .08527 .52021 595.663 83.676)"/>
      <path d="M389.184 177.624c.763 1.213 2.953 4.297 2.626 6.843 1.145-1.144 1.684-1.964 2.762-3.194-.588 1.808-.203 2.756.67 3.257.625.72 2.36.287 3.45-.12-1.014.96-2.145 1.964-3.68 2.36-1.536.396-2.094.372-3.029.614 1.03.46 3.657 1.081 5.439 1.152.891.036 2.6-.021 3.27-.651 1.08.916 2.9-1.093 1.827-2.071.326-.602.25-1.486.19-2.66-.07-1.326-.633-3.554-1.871-5.544a17.245 17.245 0 0 1-.334 3.308c-.205 1.049-.818 1.965-1.413 2.948.29-.977.348-2.402-.623-3.287-.97-.886-2.175-.718-3.233-.038.879-1.05 1.569-1.703 2.64-2.907-1.261.164-5.538-1.076-6.997-1.817-1.459-.74-6.223-4.041-8.921-6.06 2.321 2.356 6.482 6.68 7.227 7.867z" fill="#e7e7e7" transform="matrix(-.60671 .07311 .08527 .52021 595.663 83.676)"/>
      <path d="M400.692 187.885c1.08.916 183.545 161.535 184.346 162.235.472-.555 1.388-1.626 1.837-2.128-.752-.678-183.282-161.2-184.356-162.178.326-.602-2.497 2.7-1.827 2.07z" fill="#a05a2c" transform="matrix(-.60671 -.07311 -.08527 .52021 646.151 154.34)"/>
      <path d="M389.184 177.624c.763 1.213 2.953 4.297 2.626 6.843 1.145-1.144 1.684-1.964 2.762-3.194-.588 1.808-.203 2.756.67 3.257.625.72 2.36.287 3.45-.12-1.014.96-2.145 1.964-3.68 2.36-1.536.396-2.094.372-3.029.614 1.03.46 3.657 1.081 5.439 1.152.891.036 2.6-.021 3.27-.651 1.08.916 2.9-1.093 1.827-2.071.326-.602.25-1.486.19-2.66-.07-1.326-.633-3.554-1.871-5.544a17.245 17.245 0 0 1-.334 3.308c-.205 1.049-.818 1.965-1.413 2.948.29-.977.348-2.402-.623-3.287-.97-.886-2.175-.718-3.233-.038.879-1.05 1.569-1.703 2.64-2.907-1.261.164-5.538-1.076-6.997-1.817-1.459-.74-6.223-4.041-8.921-6.06 2.321 2.356 6.482 6.68 7.227 7.867z" fill="#e7e7e7" transform="matrix(-.60671 -.07311 -.08527 .52021 646.151 154.34)"/>
      <g>
        <path d="M400.692 187.885c1.08.916 183.545 161.535 184.346 162.235.472-.555 1.388-1.626 1.837-2.128-.752-.678-183.282-161.2-184.356-162.178.326-.602-2.497 2.7-1.827 2.07z" fill="#a05a2c" transform="matrix(-.61268 0 0 .52533 621.816 118.783)"/>
        <path d="M389.184 177.624c.763 1.213 2.953 4.297 2.626 6.843 1.145-1.144 1.684-1.964 2.762-3.194-.588 1.808-.203 2.756.67 3.257.625.72 2.36.287 3.45-.12-1.014.96-2.145 1.964-3.68 2.36-1.536.396-2.094.372-3.029.614 1.03.46 3.657 1.081 5.439 1.152.891.036 2.6-.021 3.27-.651 1.08.916 2.9-1.093 1.827-2.071.326-.602.25-1.486.19-2.66-.07-1.326-.633-3.554-1.871-5.544a17.245 17.245 0 0 1-.334 3.308c-.205 1.049-.818 1.965-1.413 2.948.29-.977.348-2.402-.623-3.287-.97-.886-2.175-.718-3.233-.038.879-1.05 1.569-1.703 2.64-2.907-1.261.164-5.538-1.076-6.997-1.817-1.459-.74-6.223-4.041-8.921-6.06 2.321 2.356 6.482 6.68 7.227 7.867z" fill="#e7e7e7" transform="matrix(-.61268 0 0 .52533 621.816 118.783)"/>
      </g>
    </g>
    <g stroke-width=".217">
      <path d="M400.692 187.885c1.08.916 183.545 161.535 184.346 162.235.472-.555 1.388-1.626 1.837-2.128-.752-.678-183.282-161.2-184.356-162.178.326-.602-2.497 2.7-1.827 2.07z" fill="#a05a2c" transform="matrix(.60671 .07311 -.08527 .52021 44.337 83.676)"/>
      <path d="M389.184 177.624c.763 1.213 2.953 4.297 2.626 6.843 1.145-1.144 1.684-1.964 2.762-3.194-.588 1.808-.203 2.756.67 3.257.625.72 2.36.287 3.45-.12-1.014.96-2.145 1.964-3.68 2.36-1.536.396-2.094.372-3.029.614 1.03.46 3.657 1.081 5.439 1.152.891.036 2.6-.021 3.27-.651 1.08.916 2.9-1.093 1.827-2.071.326-.602.25-1.486.19-2.66-.07-1.326-.633-3.554-1.871-5.544a17.245 17.245 0 0 1-.334 3.308c-.205 1.049-.818 1.965-1.413 2.948.29-.977.348-2.402-.623-3.287-.97-.886-2.175-.718-3.233-.038.879-1.05 1.569-1.703 2.64-2.907-1.261.164-5.538-1.076-6.997-1.817-1.459-.74-6.223-4.041-8.921-6.06 2.321 2.356 6.482 6.68 7.227 7.867z" fill="#e7e7e7" transform="matrix(.60671 .07311 -.08527 .52021 44.337 83.676)"/>
      <path d="M400.692 187.885c1.08.916 183.545 161.535 184.346 162.235.472-.555 1.388-1.626 1.837-2.128-.752-.678-183.282-161.2-184.356-162.178.326-.602-2.497 2.7-1.827 2.07z" fill="#a05a2c" transform="matrix(.60671 -.07311 .08527 .52021 -6.151 154.34)"/>
      <path d="M389.184 177.624c.763 1.213 2.953 4.297 2.626 6.843 1.145-1.144 1.684-1.964 2.762-3.194-.588 1.808-.203 2.756.67 3.257.625.72 2.36.287 3.45-.12-1.014.96-2.145 1.964-3.68 2.36-1.536.396-2.094.372-3.029.614 1.03.46 3.657 1.081 5.439 1.152.891.036 2.6-.021 3.27-.651 1.08.916 2.9-1.093 1.827-2.071.326-.602.25-1.486.19-2.66-.07-1.326-.633-3.554-1.871-5.544a17.245 17.245 0 0 1-.334 3.308c-.205 1.049-.818 1.965-1.413 2.948.29-.977.348-2.402-.623-3.287-.97-.886-2.175-.718-3.233-.038.879-1.05 1.569-1.703 2.64-2.907-1.261.164-5.538-1.076-6.997-1.817-1.459-.74-6.223-4.041-8.921-6.06 2.321 2.356 6.482 6.68 7.227 7.867z" fill="#e7e7e7" transform="matrix(.60671 -.07311 .08527 .52021 -6.151 154.34)"/>
      <g>
        <path d="M400.692 187.885c1.08.916 183.545 161.535 184.346 162.235.472-.555 1.388-1.626 1.837-2.128-.752-.678-183.282-161.2-184.356-162.178.326-.602-2.497 2.7-1.827 2.07z" fill="#a05a2c" transform="matrix(.61268 0 0 .52533 18.184 118.783)"/>
        <path d="M389.184 177.624c.763 1.213 2.953 4.297 2.626 6.843 1.145-1.144 1.684-1.964 2.762-3.194-.588 1.808-.203 2.756.67 3.257.625.72 2.36.287 3.45-.12-1.014.96-2.145 1.964-3.68 2.36-1.536.396-2.094.372-3.029.614 1.03.46 3.657 1.081 5.439 1.152.891.036 2.6-.021 3.27-.651 1.08.916 2.9-1.093 1.827-2.071.326-.602.25-1.486.19-2.66-.07-1.326-.633-3.554-1.871-5.544a17.245 17.245 0 0 1-.334 3.308c-.205 1.049-.818 1.965-1.413 2.948.29-.977.348-2.402-.623-3.287-.97-.886-2.175-.718-3.233-.038.879-1.05 1.569-1.703 2.64-2.907-1.261.164-5.538-1.076-6.997-1.817-1.459-.74-6.223-4.041-8.921-6.06 2.321 2.356 6.482 6.68 7.227 7.867z" fill="#e7e7e7" transform="matrix(.61268 0 0 .52533 18.184 118.783)"/>
      </g>
    </g>
    <g stroke-width=".238">
      <path d="M408.421 331.297c-4.148-7.959-21.27-18.781-24.222-19.09 7.87 17.358 20.839 23.639 24.222 19.09z" fill="#00e519" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M408.343 331.298c-3.383 4.549-16.274-1.733-24.144-19.011 11.722 11.098 22.203 19.168 24.144 19.011z" fill="#ffe533" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M408.343 331.298c-3.383 4.549-16.274-1.733-24.144-19.011 11.722 11.098 22.203 19.168 24.144 19.011z" fill="none" stroke-linecap="round" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M408.421 331.22c-4.42-8.465-22.357-18.936-24.261-19.129 7.91 17.319 20.878 23.678 24.261 19.13z" fill="none" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M384.558 175.702l-2.79 118.6c1.24 2.796 4.078 1.046 4.544 1.589 1.045 4.388 4.19 6.6 4.577 7.687.85 5.904-2.417 8.937-2.727 9.17-3.694 2.45-.436 7.887-1.097 8.858.076 2.33 3.766 3.454 3.452 6.095.272-.193.021 19.075-.02 21.133.62 1.398.764 14.373.764 14.373 3.494 1.472 21.213-2.506 21.407-2.236-1.267-17.284-9.67-46.683-8.697-47.887-.61-12.198-3.86-26.568-7.354-28.04-3.57-5.203-2.512-14.022-.762-15.383-3.728-1.784-4.619-5.318-4.813-5.589l1.166-85.54c-2.134-2.135-2.29-.736-3.885.625 0 0-.618-3.03-.385-3.263-1.087-1.476-3.03-.93-3.38-.192z" fill="#a05a2c" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M392.66 153.164c-.081 2.331-1.323 1.788-1.867 1.866l-2.02-.23c-2.019-.854-3.613-.114-3.963.625l-3.041 138.878c1.24 2.795 4.272 1.316 4.775 1.899l1.953-94.943s-.435-33.874-.348-43.196l5.439-.238c1.552.232 2.447-.663 2.566-2.567l-2.207-50.497-1.249 47.51-.038.893" fill="#cce5e5" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M392.66 153.164c-.081 2.331-1.323 1.788-1.867 1.866l-2.02-.23c-2.019-.854-3.613-.114-3.963.625l-3.041 138.878c1.24 2.795 4.272 1.316 4.775 1.899l1.953-94.943s-.434-33.873-.348-43.196l5.439-.238c1.552.232 2.447-.663 2.566-2.567l-2.207-50.497-1.249 47.51-.038.893" fill="#e7e7e7" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M403.592 303.916c4.93 1.898 5.78 7.259.417 10.838M403.741 309.277c1.242 1.863 3.108.618 3.108.618M392.975 273.78c-2.225 11.851-.757 21.018-.798 23.62 1.665 5.787.334 16.666-.95 18.103l1.953 31.348" fill="none" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M381.807 294.807c2.564.076 13.212-4.518 19.656-.018 2.128 9.67 2.042 18.528 2.546 19.965-9.365 3.387-15.076 4.597-17.098 4.832-.698-1.242-.307-3.65-.307-3.65-.58-2.37 1.79-3.188 1.79-3.188 1.908-4.78.437-9.362-2.437-10.913-3.572-2.017-2.368-1.902-2.834-2.133-1.202-1.903-1.316-4.895-1.316-4.895z" fill="#cce5e5" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M381.807 294.807c2.564.076 13.212-4.518 19.656-.018 2.128 9.67 2.042 18.528 2.546 19.965-9.365 3.387-15.076 4.597-17.098 4.832-.698-1.242-.307-3.65-.307-3.65-.58-2.37 1.79-3.188 1.79-3.188 1.908-4.78.437-9.362-2.437-10.913-3.572-2.017-2.368-1.902-2.834-2.133-1.202-1.903-1.316-4.895-1.316-4.895z" fill="none" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M388.875 298.141c3.338 1.396 5.899 4.734 5.546 8.502l-5.546-8.502" fill="#cce5e5" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M388.875 298.141c3.338 1.396 5.899 4.734 5.546 8.502M399.74 310.562c-.662.661-1.71.7-2.37.04a1.71 1.71 0 0 1 .003-2.407c.66-.66 1.708-.623 2.368.036.66.66.66 1.67-.001 2.33zM398.543 302.211a1.712 1.712 0 0 1-2.408.002c-.66-.66-.621-1.708.04-2.37a1.62 1.62 0 0 1 2.33 0c.66.659.699 1.708.038 2.368z" fill="none" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M383.187 188.717c-1.087-1.086-.892-3.068.47-4.43l9.129.187s.815.583 1.047 2.135c.193 1.592-1.284 2.14-1.284 2.14l-9.362-.032" fill="#cce5e5" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
      <path d="M383.187 188.717c-1.087-1.086-.892-3.068.47-4.43l9.129.187s.815.583 1.047 2.135c.193 1.592-1.284 2.14-1.284 2.14l-9.362-.032" fill="none" transform="matrix(-.50918 -.19438 -.2267 .43658 583.204 218.206)"/>
    </g>
    <g stroke-width=".238">
      <path d="M408.421 331.297c-4.148-7.959-21.27-18.781-24.222-19.09 7.87 17.358 20.839 23.639 24.222 19.09z" fill="#00e519" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M408.343 331.298c-3.383 4.549-16.274-1.733-24.144-19.011 11.722 11.098 22.203 19.168 24.144 19.011z" fill="#ffe533" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M408.343 331.298c-3.383 4.549-16.274-1.733-24.144-19.011 11.722 11.098 22.203 19.168 24.144 19.011z" fill="none" stroke-linecap="round" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M408.421 331.22c-4.42-8.465-22.357-18.936-24.261-19.129 7.91 17.319 20.878 23.678 24.261 19.13z" fill="none" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M384.558 175.702l-2.79 118.6c1.24 2.796 4.078 1.046 4.544 1.589 1.045 4.388 4.19 6.6 4.577 7.687.85 5.904-2.417 8.937-2.727 9.17-3.694 2.45-.436 7.887-1.097 8.858.076 2.33 3.766 3.454 3.452 6.095.272-.193.021 19.075-.02 21.133.62 1.398.764 14.373.764 14.373 3.494 1.472 21.213-2.506 21.407-2.236-1.267-17.284-9.67-46.683-8.697-47.887-.61-12.198-3.86-26.568-7.354-28.04-3.57-5.203-2.512-14.022-.762-15.383-3.728-1.784-4.619-5.318-4.813-5.589l1.166-85.54c-2.134-2.135-2.29-.736-3.885.625 0 0-.618-3.03-.385-3.263-1.087-1.476-3.03-.93-3.38-.192z" fill="#a05a2c" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M384.558 175.702l-2.79 118.6c1.24 2.796 4.078 1.046 4.544 1.589 1.045 4.388 4.19 6.6 4.577 7.687.85 5.904-2.417 8.937-2.727 9.17-3.694 2.45-.436 7.887-1.097 8.858.076 2.33 3.766 3.454 3.452 6.095.272-.193.021 19.075-.02 21.133.62 1.398.764 14.373.764 14.373 3.494 1.472 21.213-2.506 21.407-2.236-1.267-17.284-9.67-46.683-8.697-47.887-.61-12.198-3.86-26.568-7.354-28.04-3.57-5.203-2.512-14.022-.762-15.383-3.728-1.784-4.619-5.318-4.813-5.589l1.166-85.54c-2.134-2.135-2.29-.736-3.885.625 0 0-.618-3.03-.385-3.263-1.087-1.476-3.03-.93-3.38-.192z" fill="none" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M392.66 153.164c-.081 2.331-1.323 1.788-1.867 1.866l-2.02-.23c-2.019-.854-3.613-.114-3.963.625l-3.041 138.878c1.24 2.795 4.272 1.316 4.775 1.899l1.953-94.943s-.435-33.874-.348-43.196l5.439-.238c1.552.232 2.447-.663 2.566-2.567l-2.207-50.497-1.249 47.51-.038.893" fill="#cce5e5" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M392.66 153.164c-.081 2.331-1.323 1.788-1.867 1.866l-2.02-.23c-2.019-.854-3.613-.114-3.963.625l-3.041 138.878c1.24 2.795 4.272 1.316 4.775 1.899l1.953-94.943s-.434-33.873-.348-43.196l5.439-.238c1.552.232 2.447-.663 2.566-2.567l-2.207-50.497-1.249 47.51-.038.893" fill="#e7e7e7" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M403.592 303.916c4.93 1.898 5.78 7.259.417 10.838M403.741 309.277c1.242 1.863 3.108.618 3.108.618M392.975 273.78c-2.225 11.851-.757 21.018-.798 23.62 1.665 5.787.334 16.666-.95 18.103l1.953 31.348" fill="none" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M381.807 294.807c2.564.076 13.212-4.518 19.656-.018 2.128 9.67 2.042 18.528 2.546 19.965-9.365 3.387-15.076 4.597-17.098 4.832-.698-1.242-.307-3.65-.307-3.65-.58-2.37 1.79-3.188 1.79-3.188 1.908-4.78.437-9.362-2.437-10.913-3.572-2.017-2.368-1.902-2.834-2.133-1.202-1.903-1.316-4.895-1.316-4.895z" fill="#cce5e5" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M381.807 294.807c2.564.076 13.212-4.518 19.656-.018 2.128 9.67 2.042 18.528 2.546 19.965-9.365 3.387-15.076 4.597-17.098 4.832-.698-1.242-.307-3.65-.307-3.65-.58-2.37 1.79-3.188 1.79-3.188 1.908-4.78.437-9.362-2.437-10.913-3.572-2.017-2.368-1.902-2.834-2.133-1.202-1.903-1.316-4.895-1.316-4.895z" fill="none" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M388.875 298.141c3.338 1.396 5.899 4.734 5.546 8.502l-5.546-8.502" fill="#cce5e5" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M388.875 298.141c3.338 1.396 5.899 4.734 5.546 8.502M399.74 310.562c-.662.661-1.71.7-2.37.04a1.71 1.71 0 0 1 .003-2.407c.66-.66 1.708-.623 2.368.036.66.66.66 1.67-.001 2.33zM398.543 302.211a1.712 1.712 0 0 1-2.408.002c-.66-.66-.621-1.708.04-2.37a1.62 1.62 0 0 1 2.33 0c.66.659.699 1.708.038 2.368z" fill="none" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M383.187 188.717c-1.087-1.086-.892-3.068.47-4.43l9.129.187s.815.583 1.047 2.135c.193 1.592-1.284 2.14-1.284 2.14l-9.362-.032" fill="#cce5e5" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
      <path d="M383.187 188.717c-1.087-1.086-.892-3.068.47-4.43l9.129.187s.815.583 1.047 2.135c.193 1.592-1.284 2.14-1.284 2.14l-9.362-.032" fill="none" transform="matrix(-.51306 -.18673 -.21778 .43991 576.226 216.583)"/>
    </g>
    <g stroke-width=".236">
      <path d="M408.421 331.297c-4.148-7.959-21.27-18.781-24.222-19.09 7.87 17.358 20.839 23.639 24.222 19.09z" fill="#00e519" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M408.343 331.298c-3.383 4.549-16.274-1.733-24.144-19.011 11.722 11.098 22.203 19.168 24.144 19.011z" fill="#ffe533" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M408.343 331.298c-3.383 4.549-16.274-1.733-24.144-19.011 11.722 11.098 22.203 19.168 24.144 19.011z" fill="none" stroke-linecap="round" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M408.421 331.22c-4.42-8.465-22.357-18.936-24.261-19.129 7.91 17.319 20.878 23.678 24.261 19.13z" fill="none" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M384.558 175.702l-2.79 118.6c1.24 2.796 4.078 1.046 4.544 1.589 1.045 4.388 4.19 6.6 4.577 7.687.85 5.904-2.417 8.937-2.727 9.17-3.694 2.45-.436 7.887-1.097 8.858.076 2.33 3.766 3.454 3.452 6.095.272-.193.021 19.075-.02 21.133.62 1.398.764 14.373.764 14.373 3.494 1.472 21.213-2.506 21.407-2.236-1.267-17.284-9.67-46.683-8.697-47.887-.61-12.198-3.86-26.568-7.354-28.04-3.57-5.203-2.512-14.022-.762-15.383-3.728-1.784-4.619-5.318-4.813-5.589l1.166-85.54c-2.134-2.135-2.29-.736-3.885.625 0 0-.618-3.03-.385-3.263-1.087-1.476-3.03-.93-3.38-.192z" fill="#a05a2c" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M392.66 153.164c-.081 2.331-1.323 1.788-1.867 1.866l-2.02-.23c-2.019-.854-3.613-.114-3.963.625l-3.041 138.878c1.24 2.795 4.272 1.316 4.775 1.899l1.953-94.943s-.435-33.874-.348-43.196l5.439-.238c1.552.232 2.447-.663 2.566-2.567l-2.207-50.497-1.249 47.51-.038.893" fill="#cce5e5" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M392.66 153.164c-.081 2.331-1.323 1.788-1.867 1.866l-2.02-.23c-2.019-.854-3.613-.114-3.963.625l-3.041 138.878c1.24 2.795 4.272 1.316 4.775 1.899l1.953-94.943s-.434-33.873-.348-43.196l5.439-.238c1.552.232 2.447-.663 2.566-2.567l-2.207-50.497-1.249 47.51-.038.893" fill="#e7e7e7" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M403.592 303.916c4.93 1.898 5.78 7.259.417 10.838M403.741 309.277c1.242 1.863 3.108.618 3.108.618M392.975 273.78c-2.225 11.851-.757 21.018-.798 23.62 1.665 5.787.334 16.666-.95 18.103l1.953 31.348" fill="none" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M381.807 294.807c2.564.076 13.212-4.518 19.656-.018 2.128 9.67 2.042 18.528 2.546 19.965-9.365 3.387-15.076 4.597-17.098 4.832-.698-1.242-.307-3.65-.307-3.65-.58-2.37 1.79-3.188 1.79-3.188 1.908-4.78.437-9.362-2.437-10.913-3.572-2.017-2.368-1.902-2.834-2.133-1.202-1.903-1.316-4.895-1.316-4.895z" fill="#cce5e5" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M381.807 294.807c2.564.076 13.212-4.518 19.656-.018 2.128 9.67 2.042 18.528 2.546 19.965-9.365 3.387-15.076 4.597-17.098 4.832-.698-1.242-.307-3.65-.307-3.65-.58-2.37 1.79-3.188 1.79-3.188 1.908-4.78.437-9.362-2.437-10.913-3.572-2.017-2.368-1.902-2.834-2.133-1.202-1.903-1.316-4.895-1.316-4.895z" fill="none" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M388.875 298.141c3.338 1.396 5.899 4.734 5.546 8.502l-5.546-8.502" fill="#cce5e5" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M388.875 298.141c3.338 1.396 5.899 4.734 5.546 8.502M399.74 310.562c-.662.661-1.71.7-2.37.04a1.71 1.71 0 0 1 .003-2.407c.66-.66 1.708-.623 2.368.036.66.66.66 1.67-.001 2.33zM398.543 302.211a1.712 1.712 0 0 1-2.408.002c-.66-.66-.621-1.708.04-2.37a1.62 1.62 0 0 1 2.33 0c.66.659.699 1.708.038 2.368z" fill="none" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M383.187 188.717c-1.087-1.086-.892-3.068.47-4.43l9.129.187s.815.583 1.047 2.135c.193 1.592-1.284 2.14-1.284 2.14l-9.362-.032" fill="#cce5e5" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
      <path d="M383.187 188.717c-1.087-1.086-.892-3.068.47-4.43l9.129.187s.815.583 1.047 2.135c.193 1.592-1.284 2.14-1.284 2.14l-9.362-.032" fill="none" transform="matrix(.51432 -.19635 .229 .441 54.694 217.373)"/>
    </g>
    <g stroke-width=".238">
      <path d="M408.421 331.297c-4.148-7.959-21.27-18.781-24.222-19.09 7.87 17.358 20.839 23.639 24.222 19.09z" fill="#00e519" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M408.343 331.298c-3.383 4.549-16.274-1.733-24.144-19.011 11.722 11.098 22.203 19.168 24.144 19.011z" fill="#ffe533" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M408.343 331.298c-3.383 4.549-16.274-1.733-24.144-19.011 11.722 11.098 22.203 19.168 24.144 19.011z" fill="none" stroke-linecap="round" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M408.421 331.22c-4.42-8.465-22.357-18.936-24.261-19.129 7.91 17.319 20.878 23.678 24.261 19.13z" fill="none" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M384.558 175.702l-2.79 118.6c1.24 2.796 4.078 1.046 4.544 1.589 1.045 4.388 4.19 6.6 4.577 7.687.85 5.904-2.417 8.937-2.727 9.17-3.694 2.45-.436 7.887-1.097 8.858.076 2.33 3.766 3.454 3.452 6.095.272-.193.021 19.075-.02 21.133.62 1.398.764 14.373.764 14.373 3.494 1.472 21.213-2.506 21.407-2.236-1.267-17.284-9.67-46.683-8.697-47.887-.61-12.198-3.86-26.568-7.354-28.04-3.57-5.203-2.512-14.022-.762-15.383-3.728-1.784-4.619-5.318-4.813-5.589l1.166-85.54c-2.134-2.135-2.29-.736-3.885.625 0 0-.618-3.03-.385-3.263-1.087-1.476-3.03-.93-3.38-.192z" fill="#a05a2c" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M384.558 175.702l-2.79 118.6c1.24 2.796 4.078 1.046 4.544 1.589 1.045 4.388 4.19 6.6 4.577 7.687.85 5.904-2.417 8.937-2.727 9.17-3.694 2.45-.436 7.887-1.097 8.858.076 2.33 3.766 3.454 3.452 6.095.272-.193.021 19.075-.02 21.133.62 1.398.764 14.373.764 14.373 3.494 1.472 21.213-2.506 21.407-2.236-1.267-17.284-9.67-46.683-8.697-47.887-.61-12.198-3.86-26.568-7.354-28.04-3.57-5.203-2.512-14.022-.762-15.383-3.728-1.784-4.619-5.318-4.813-5.589l1.166-85.54c-2.134-2.135-2.29-.736-3.885.625 0 0-.618-3.03-.385-3.263-1.087-1.476-3.03-.93-3.38-.192z" fill="none" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M392.66 153.164c-.081 2.331-1.323 1.788-1.867 1.866l-2.02-.23c-2.019-.854-3.613-.114-3.963.625l-3.041 138.878c1.24 2.795 4.272 1.316 4.775 1.899l1.953-94.943s-.435-33.874-.348-43.196l5.439-.238c1.552.232 2.447-.663 2.566-2.567l-2.207-50.497-1.249 47.51-.038.893" fill="#cce5e5" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M392.66 153.164c-.081 2.331-1.323 1.788-1.867 1.866l-2.02-.23c-2.019-.854-3.613-.114-3.963.625l-3.041 138.878c1.24 2.795 4.272 1.316 4.775 1.899l1.953-94.943s-.434-33.873-.348-43.196l5.439-.238c1.552.232 2.447-.663 2.566-2.567l-2.207-50.497-1.249 47.51-.038.893" fill="#e7e7e7" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M403.592 303.916c4.93 1.898 5.78 7.259.417 10.838M403.741 309.277c1.242 1.863 3.108.618 3.108.618M392.975 273.78c-2.225 11.851-.757 21.018-.798 23.62 1.665 5.787.334 16.666-.95 18.103l1.953 31.348" fill="none" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M381.807 294.807c2.564.076 13.212-4.518 19.656-.018 2.128 9.67 2.042 18.528 2.546 19.965-9.365 3.387-15.076 4.597-17.098 4.832-.698-1.242-.307-3.65-.307-3.65-.58-2.37 1.79-3.188 1.79-3.188 1.908-4.78.437-9.362-2.437-10.913-3.572-2.017-2.368-1.902-2.834-2.133-1.202-1.903-1.316-4.895-1.316-4.895z" fill="#cce5e5" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M381.807 294.807c2.564.076 13.212-4.518 19.656-.018 2.128 9.67 2.042 18.528 2.546 19.965-9.365 3.387-15.076 4.597-17.098 4.832-.698-1.242-.307-3.65-.307-3.65-.58-2.37 1.79-3.188 1.79-3.188 1.908-4.78.437-9.362-2.437-10.913-3.572-2.017-2.368-1.902-2.834-2.133-1.202-1.903-1.316-4.895-1.316-4.895z" fill="none" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M388.875 298.141c3.338 1.396 5.899 4.734 5.546 8.502l-5.546-8.502" fill="#cce5e5" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M388.875 298.141c3.338 1.396 5.899 4.734 5.546 8.502M399.74 310.562c-.662.661-1.71.7-2.37.04a1.71 1.71 0 0 1 .003-2.407c.66-.66 1.708-.623 2.368.036.66.66.66 1.67-.001 2.33zM398.543 302.211a1.712 1.712 0 0 1-2.408.002c-.66-.66-.621-1.708.04-2.37a1.62 1.62 0 0 1 2.33 0c.66.659.699 1.708.038 2.368z" fill="none" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M383.187 188.717c-1.087-1.086-.892-3.068.47-4.43l9.129.187s.815.583 1.047 2.135c.193 1.592-1.284 2.14-1.284 2.14l-9.362-.032" fill="#cce5e5" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
      <path d="M383.187 188.717c-1.087-1.086-.892-3.068.47-4.43l9.129.187s.815.583 1.047 2.135c.193 1.592-1.284 2.14-1.284 2.14l-9.362-.032" fill="none" transform="matrix(.51306 -.18673 .21778 .43991 63.939 216.583)"/>
    </g>
    <g stroke-width=".217">
      <path d="M5.335 370.913c.935-1.063 62.714-68.448 63.427-69.236a153.975 153.975 0 0 1-2.095-1.875c-.69.74-62.624 68.192-63.62 69.248-.597-.336 2.906 2.544 2.288 1.863z" fill="#a05a2c" transform="matrix(.61022 -.02391 .04577 .52565 295.02 55.826)"/>
      <g stroke-width=".359">
        <path d="M146.62 176.744c1.44 1.8 4.086 4.047 6.246 3.687.18 3.42-4.41 6.39-6.03 5.67.99-3.6-3.348-6.503-3.258-6.503l3.042-2.854z" fill="#e7e7e7" fill-rule="evenodd" transform="matrix(-.73175 .07875 -.11314 -.6318 477.099 312.329)" stroke-width=".179"/>
        <path d="M125.902 161.756c.232-3.518-.34-4.512-.865-8.856-.515-4.264-8.333-4.63-11.239-5.355 0 0 6.853-8.478 15.964-8.477 9.111.001 14.565 4.29 16.302 9.668 0 0-10.758-.532-11.666 3.821-.88 4.218-.454 5.166-.365 8.625" fill="#e7e7e7" transform="matrix(-.22646 .24601 -.29289 -.20204 427.993 212.114)"/>
        <path fill="#e7e7e7" d="M123.744 158.973h12.374v6.364h-12.374z" transform="matrix(-.22646 .24601 -.29289 -.20204 427.993 212.114)"/>
      </g>
    </g>
    <g fill-rule="evenodd">
      <path d="M308.17 290.32l-.922 9.04s1.399-1.713 1.735-2.282c.336-.569 1.549-2.09 1.808-7.359 0 0-.947-2.913-1.392-2.907-.742-.366-1.229 3.508-1.229 3.508zm2.223-20.151s-2.426 14.513-2.672 15.833c0 .451 1.231 1.648 2.121-1.158l1.466-10.551s-.659-2.812-.915-4.124z" fill="#007934" stroke-width=".12307499999999999"/>
      <path d="M315.88 284.57c-.592-.744-1.332-1.673-1.405-2.048l-.383 3.202s2.02 1.475 1.732 4.3l.392-.57.227-1.131s.415-1.886.418-3.015c0 0-.426-.182-.982-.738z" fill="#d52b1e" stroke-width=".12307499999999999"/>
      <path d="M307.248 299.358s3.353-2.868 3.543-9.64l.416-1.887s.069 1.504.741.366c.655-1.514.623-2.83.623-2.83s1.212-1.52 1.577.354c0 0-1.126 8.854-1.202 9.607-.097.942-.268 2.073-.268 2.073s-.664-1.12-1.337.018c-.728 1.139-1.885 2.66-4.093 1.939zm4.06-25.067l-1.466 10.551s1.169.548 1.365 2.99c.107 1.126.555.556.741.365.244-.756.064-2.257.064-2.257l.638-7.343s-1.27-3.367-1.342-4.306z" fill="#ffe000" stroke-width=".12307499999999999"/>
      <path d="M310.147 298.754l-.213 2.636s1.003-.014 1.676-1.152c.784-1.14 1.068-2.836 1.068-2.836s-.776-1.117-1.338.019c-.541.948-1.193 1.333-1.193 1.333zm1.8-10.556c.542-.947.715-2.642.623-2.829l-.682.572c.22 1.125.06 2.257.06 2.257z" fill="#ffe000" stroke-width=".12307499999999999"/>
      <path d="M315.25 287.136c-.461-.934-1.24-1.487-1.24-1.487l-1.489 11.867s-.419 3.015-2.279 3.793c0 0 1.061 10.328 4.414 7.46.484-.383 1.09-3.589.892-5.466-.235-2.065-.782-4.878-.834-6.006-.033-1.315.153-2.648.476-4.721.133-.754.523-2.407.598-2.596.186-.19-.04-1.723-.538-2.844z" fill="#d52b1e" stroke-width=".12307499999999999"/>
      <path d="M316.862 285.311s.907.928 1.074.925c.223-.003-2.353 19.353-2.353 19.353s.097-2.029-.655-5.97c-.611-3.2.177-7.5.866-9.561 0 0 .812-.606 1.068-4.747z" fill="#d52b1e" stroke-width=".12307499999999999"/>
      <path d="M312.574 278.79s-.501 5.46-.619 7.155c0 0 1.699-2.469 2.137-.218l.383-3.203s-1.478-2.423-1.9-3.733z" fill="#f7e214" stroke-width=".12307499999999999"/>
      <path d="M311.273 287.815s-.558-6.116-3.52-1.792c-.115.001-.272 2.43-.102 3.01.093.778.913 1.74 1.256 2.318.647.964 1.34-.213 1.34-.213s.674-.983 1.026-3.323z" fill="#007934" stroke-width=".12307499999999999"/>
    </g>
    <path d="M586.927 838.91c-1.33-42.48-14.677-113.67-15.34-162.078l-146.33-155.995s-18.061 119.962-71.193 186.536l232.863 131.61" fill="#d52b1e" transform="matrix(.08205 -.00386 0 .08205 239.643 170.372)"/>
    <path d="M597.494 844.433c6.162-29.214 13.559-62.066 18.467-123.541l-94.2-97.245c.377 38.745-43.493 96.608-46.462 168.84" fill="#ffe000" transform="matrix(.08205 -.00386 0 .08205 239.643 170.372)"/>
    <path d="M678.728 937.604c10.806-51.253-15.684-58.916 23.72-129.896L615.4 720.15c-16.22 48.317-27.14 79.396-25.727 121.875l74.161 56.265" fill="#007934" transform="matrix(.08205 -.00386 0 .08205 239.643 170.372)"/>
    <path d="M267.552 270.086c-1.446-4.298.517-12.73.299-18.134-.164-3.486 2.4-16.592 2.345-20.557l-14.116-8.816s-.614 14.325-2.239 29.928c-1.462 8.015-1.452 15.338-.402 21.455 1.5 8.696 2.975 12.266 6.487 15.944 6.235 6.452 19.659 2.724 19.659 2.724 11.309-2.34 17.822-9.463 17.822-9.463s-3.678.869-9.637 1.475c-13.062-.915-18.206 2.349-18.612-11.053" fill="#d52b1e" stroke-width=".12307499999999999"/>
    <path d="M305.381 278.77l.218-.095c-2.448.916-5.862 1.928-5.862 1.928l-7.959.613c-17.262.403-15.101-10.285-14.417-27.53.164-6.544 1.461-14.786 1.127-17.704l-11.572-6.63c-3.775 10.665-2.598 18.125-3.354 23.58-.394 5.877-1.647 17.353.258 22.36 2.697 11.65 11.819 11.175 24.166 10.15 6.126-.507 9.421-2.082 9.421-2.082l7.967-4.584" fill="#f7e214" stroke-width=".12307499999999999"/>
    <path d="M305.606 278.536c-2.394 1.24-5.917 2.154-5.917 2.154l-8.066.797c-12.515 1.062-19.841-7.729-18.237-27.606.01-7.022.235-10.142 2.658-19.496l6.878 4.035v.648c-.389 2.1-1.373 7.01-1.373 9.255 0 15.96 10.091 28.218 23.839 30.3l.218-.093" fill="#007a3d" stroke-width=".12307499999999999"/>
    <path d="M259.92 230.55c1.167 1.96 8.13 12.568 12.032 14.574M261.094 237.48c1.166 2.007 9.692 13.954 13.532 14.627M259.143 255.739c1.95 2.34 3.894 6.976 9.692 9.983M261.871 269.02c3.895 3.632 13.533 11.615 22.836 11.943M261.871 274.997c1.95 2.34 6.18 13.283 24.01 8.317M259.143 277.003c1.166 2.626 10.081 17.253 25.96 11.61" fill="none" stroke-width=".12307499999999999"/>
    <path d="M277.751 264.834c-1.446-4.298.517-12.73.3-18.134-.164-3.486 1.48-15.542 1.425-19.506l-13.197-9.867s-.614 14.325-2.239 29.928c-1.462 8.015-2.576 17.831-1.493 23.943 1.773 10.002 6.582 12.815 7.45 13.59 6.742 6.005 21.96 5.415 23.472 4.874 10.874-3.886 15.611-10.795 15.611-10.795s-5.152-.083-11.111.523c-13.062-.915-19.658-.37-20.065-13.772" fill="#d52b1e" stroke-width=".12307499999999999"/>
    <path d="M315.58 273.518l.218-.095c-2.448.917-5.862 1.929-5.862 1.929l-7.959.612c-17.262.404-15.101-10.285-14.417-27.53.164-6.544.39-12.422.055-15.34l-10.347-7.548c-3.774 10.664-2.751 16.68-3.507 22.135-.394 5.876-1.647 17.352.258 22.36 2.698 11.65 11.819 11.174 24.166 10.15 6.126-.508 9.422-2.083 9.422-2.083l7.966-4.584" fill="#ffe000" stroke-width=".12307499999999999"/>
    <path d="M315.887 273.362c-2.394 1.24-5.917 2.154-5.917 2.154l-8.067.797c-12.514 1.062-19.84-7.729-18.237-27.606.011-7.023-.223-7.515 2.2-16.87 3.833 2.484 11.16 8.74 11.16 8.74s-1.994 2.805-1.526 6.728c0 15.96 6.421 24.062 20.169 26.144l1.508-13.035" fill="#007934" stroke-width=".12307499999999999"/>
    <path d="M270.12 225.298c1.166 1.96 8.13 12.568 12.031 14.574M271.293 232.229c1.166 2.006 9.692 13.954 13.532 14.626M269.342 250.487c1.95 2.34 3.895 6.977 9.692 9.983M272.07 263.768c3.895 3.632 13.533 11.615 22.836 11.943M272.07 269.745c1.951 2.34 6.18 13.283 24.01 8.317M269.342 271.751c1.166 2.626 10.081 17.254 25.96 11.61" fill="none" stroke-width=".12307499999999999"/>
    <path d="M256.173 223.966s-.584 5.083-.64 5.995c-.269 4.408-.145 7.744.063 9.82-.014.224.85 5.498.552 5.78-1.013 1.156-1.049 1.285-2.117.429-.153-.176.46-5.577.506-6.32.104-.797.27-7.106.384-9.857.012-1.088.947-6.444.947-6.444s.125-1.14.3.582" fill="#e8a30e" stroke-width=".12307499999999999"/>
    <path d="M256.173 223.966s-.584 5.083-.64 5.995c-.269 4.408-.138 7.744.063 9.82-.014.224 1.03 7.565.705 5.787-1.013 1.156-1.221 1.592-2.289.737-.153-.176.48-5.892.525-6.635.104-.797.27-7.106.384-9.857.012-1.088.947-6.444.947-6.444s.125-1.14.3.582l.005.015z" fill="none" stroke-width=".12307499999999999"/>
    <path d="M256.064 222.184s-1.151 6.09-1.256 9.584c-.15 4.24-.332 5.439-.264 7.91.044.17-.414 3.193-.549 4.507-.096.678.099.174-.014.224-.865.53-1.454.104-1.982-.314-.157-.118 1.4-3.781 1.445-4.524.821-10.758 2.381-17.062 2.381-17.062s-.622 3.817.239-.325" fill="#f7e214" stroke-width=".12307499999999999"/>
    <path d="M256.064 222.184s-1.151 6.09-1.249 9.585c-.15 4.239-.332 5.438-.265 7.91.045.169-.413 3.192-.548 4.506-.096.678.098.174-.014.225-.866.53-1.454.103-1.982-.314-.157-.119 1.399-3.782 1.445-4.532.821-10.757 2.375-17.062 2.375-17.062s-.623 3.817.238-.326v.008zm-.499 17.315s-.913.414-1.015.18m-.04-1.255s.78-.02.903-.24m-.045-1.049s-.576.375-.732.136m.718-1.682l-.555.024m.591-1.52l-.609.022m.514-2.148s-.344.153-.385-.073m.493-1.7l-.497-.022m-.475 9.564s-.896.127-.995-.158m1.066-1.906s-.892.073-.93-.211m.967-1.277s-.726.023-.777-.035m.919-1.4l-.722-.032m.718-1.69l-.494-.078m.694-1.402s-.45.093-.545-.136m.596-1.591s-.406.264-.389-.017m-.049 8.959s-.888.008-.871-.264M266.372 218.714s-.583 5.083-.64 5.995c-.269 4.408-.145 7.744.063 9.82-.014.224.85 5.498.552 5.78-1.013 1.156-1.049 1.285-2.117.429-.153-.176.46-5.577.506-6.32.104-.797.27-7.106.384-9.857.012-1.088.947-6.443.947-6.443s.125-1.14.3.582" fill="#e8a30e" stroke-width=".12307499999999999"/>
    <path d="M266.372 218.714s-.583 5.083-.64 5.995c-.269 4.408-.138 7.744.063 9.82-.014.224 1.03 7.565.705 5.787-1.013 1.156-1.221 1.592-2.289.737-.153-.176.48-5.892.525-6.634.104-.798.27-7.107.384-9.858.012-1.088.947-6.443.947-6.443s.125-1.14.3.582l.005.014z" fill="none" stroke-width=".12307499999999999"/>
    <path d="M266.263 216.932s-1.151 6.09-1.255 9.585c-.15 4.239-.333 5.438-.265 7.909.044.17-.414 3.193-.549 4.507-.096.678.099.174-.014.225-.865.53-1.454.103-1.982-.314-.156-.119 1.4-3.782 1.445-4.525.821-10.757 2.381-17.062 2.381-17.062l.239-.325" fill="#e8a30e" stroke-width=".12307499999999999"/>
    <path d="M266.263 216.932s-1.151 6.09-1.249 9.585c-.15 4.239-.332 5.438-.264 7.91.044.169-.414 3.192-.55 4.507-.095.677.1.173-.013.224-.866.53-1.454.104-1.982-.314-.157-.119 1.4-3.782 1.445-4.532.822-10.757 2.375-17.062 2.375-17.062l.238-.325v.007zm-.499 17.315s-.913.414-1.014.18m-.04-1.255s.78-.02.903-.24m-.046-1.049s-.576.376-.732.136m.718-1.682l-.555.024m.591-1.519l-.609.021m.514-2.148s-.344.153-.385-.073m.493-1.7l-.497-.022m-.475 9.564s-.896.127-.994-.158m1.065-1.906s-.892.073-.93-.21m.967-1.278s-.726.023-.777-.035m.92-1.4l-.723-.032m.718-1.69l-.494-.077m.694-1.403s-.45.093-.545-.136m.596-1.59s-.406.263-.389-.018m-.049 8.959s-.888.008-.871-.264" fill="none" stroke-width=".12307499999999999"/>
    <g fill="#e8a30e" stroke-width=".217">
      <path d="M356.466 216.692s1.26 9.73 1.091 15.273c-.18 6.726 0 8.638-.367 12.549-.077.265.367 5.087.444 7.179.08 1.08-.176.268 0 .356 1.36.9 2.36.268 3.263-.356.357-.177-1.903-6.09-1.903-7.27-.268-17.1-2.183-27.198-2.183-27.198l-.357-.533" transform="matrix(.61184 .0275 -.03206 .5246 63.18 87.782)"/>
      <path d="M356.466 216.692s1.268 9.73 1.08 15.273c-.177 6.726 0 8.638-.368 12.549-.077.265.368 5.087.445 7.179.08 1.08-.177.268 0 .356 1.36.9 2.36.268 3.263-.356.356-.177-1.904-6.09-1.904-7.282-.268-17.1-2.171-27.198-2.171-27.198l-.357-.533zM357.378 244.613s1.448.265 1.639-.18M357.569 241.339s1.447.18 1.447-.268M357.569 238.979s1.168.088 1.268 0M357.469 236.696h1.18M357.646 233.969l.812-.089M357.569 231.609s.624.18.812-.177M357.569 228.87s.624.447.624 0M357.378 243.066s1.448.077 1.448-.357" transform="matrix(.61184 .0275 -.03206 .5246 63.18 87.782)"/>
    </g>
    <path d="M306.015 221.65l.797.06s-1.233-.09-.797-.06z" fill="#005000" stroke-width=".12307499999999999"/>
    <path d="M316.727 256.404s-.372-.16-.465 0c0 .08.093.16.186.16s.279-.16.279-.16zM315.705 257.52s1.58-.16 2.136-.16" fill="#fff" fill-rule="evenodd" stroke-width=".12307499999999999"/>
    <g>
      <g stroke-width="1.512">
        <g stroke-width=".194">
          <path d="M156.9 89.493c-.033 1.927-3.764 4.277-6.82.323-3.193-2.722-2.733-7.08-.329-7.767l27.817-34.577c1.144-.796 1.224-1.517 1.777-2.272 1.305 1.486 3.987 4.142 5.443 5.42-.81.86-1.461 1.67-1.618 2.323l-26.27 36.55z" fill="#e8a30e" fill-rule="evenodd" transform="matrix(1.8554 .0577 -.0868 1.58684 8.847 133.141)" stroke-width=".072"/>
          <g stroke-width=".045" fill-rule="evenodd">
            <path d="M123.245 190.484c.837-1.426 4.37 2.172 3.488 3.327-.882 1.155-4.256-1.888-3.488-3.327z" fill="#e7e7e7" stroke-linejoin="round" transform="matrix(2.96532 .0479 -.0869 2.53565 -11.09 -273.854)"/>
            <path d="M126.418 193.544c-.628.584-3.183-1.828-2.8-2.765.66-.884 3.483 2.186 2.8 2.765z" fill="#cccccf" transform="matrix(2.96532 .0479 -.0869 2.53565 -11.09 -273.854)"/>
          </g>
        </g>
        <path d="M3513.155 736.637c56.547-19.255 95.944-57.232 120-113.037M3934.483 1159.288c71.128-23.522 118.995-70.304 139.06-144.889M3964.46 1190.93c71.128-23.522 118.996-70.304 139.06-144.888M4138.493 1357.47c71.128-23.523 127.323-76.134 147.388-150.718M4169.303 1389.112c71.128-23.522 128.155-76.966 148.22-151.55" fill="none" transform="matrix(-.0802 .0023 .00223 .08257 624.115 158.975)"/>
        <path d="M4338.34 1405.766c4.012 16.341 13.42 21.216 26.23 15.821M4192.589 1403.136c28.07 40.396 57.66 27.18 82.718 26.9" fill="none" stroke-linecap="round" transform="matrix(-.0802 .0023 .00223 .08257 624.115 158.975)"/>
        <path d="M4375.222 1406.928c.312 10.999-9.015 20.158-20.851 20.476-11.837.318-21.717-8.326-22.089-19.323-.372-10.997 8.905-20.2 20.74-20.573 11.834-.373 21.761 11.555 22.193 22.55" fill="#e8a30e" transform="matrix(-.0802 .0023 .00223 .08257 624.115 158.975)"/>
        <path d="M4336.24 1403.837c4.012 16.34 13.42 21.216 26.23 15.82M4192.589 1403.136c28.07 40.396 57.66 27.18 82.718 26.9" fill="none" stroke-linecap="round" transform="matrix(-.0802 .0023 .00223 .08257 624.115 158.975)"/>
      </g>
    </g>
    <g>
      <g fill-rule="evenodd">
        <path d="M331.83 290.32l.922 9.04s-1.399-1.713-1.735-2.282c-.336-.569-1.549-2.09-1.808-7.359 0 0 .947-2.913 1.392-2.907.742-.366 1.229 3.508 1.229 3.508zm-2.223-20.151s2.426 14.513 2.672 15.833c0 .451-1.231 1.648-2.121-1.158l-1.466-10.551s.659-2.812.915-4.124z" fill="#007934" stroke-width=".12307499999999999"/>
        <path d="M324.12 284.57c.592-.744 1.332-1.673 1.405-2.048l.383 3.202s-2.02 1.475-1.732 4.3l-.392-.57-.227-1.131s-.415-1.886-.418-3.015c0 0 .426-.182.982-.738z" fill="#d52b1e" stroke-width=".12307499999999999"/>
        <path d="M332.752 299.358s-3.353-2.868-3.543-9.64l-.416-1.887s-.069 1.504-.741.366c-.655-1.514-.623-2.83-.623-2.83s-1.212-1.52-1.577.354c0 0 1.126 8.854 1.202 9.607.097.942.268 2.073.268 2.073s.664-1.12 1.337.018c.728 1.139 1.885 2.66 4.093 1.939zm-4.06-25.067l1.466 10.551s-1.169.548-1.365 2.99c-.107 1.126-.555.556-.741.365-.244-.756-.064-2.257-.064-2.257l-.638-7.343s1.27-3.367 1.342-4.306z" fill="#ffe000" stroke-width=".12307499999999999"/>
        <path d="M329.853 298.754l.213 2.636s-1.003-.014-1.676-1.152c-.784-1.14-1.068-2.836-1.068-2.836s.776-1.117 1.338.019c.541.948 1.193 1.333 1.193 1.333zm-1.8-10.556c-.542-.947-.715-2.642-.623-2.829l.682.572c-.22 1.125-.06 2.257-.06 2.257z" fill="#ffe000" stroke-width=".12307499999999999"/>
        <path d="M324.75 287.136c.461-.934 1.24-1.487 1.24-1.487l1.489 11.867s.419 3.015 2.279 3.793c0 0-1.061 10.328-4.414 7.46-.484-.383-1.09-3.589-.892-5.466.235-2.065.782-4.878.834-6.006.033-1.315-.153-2.648-.476-4.721-.133-.754-.523-2.407-.598-2.596-.186-.19.04-1.723.538-2.844z" fill="#d52b1e" stroke-width=".12307499999999999"/>
        <path d="M323.138 285.311s-.907.928-1.074.925c-.223-.003 2.353 19.353 2.353 19.353s-.097-2.029.655-5.97c.611-3.2-.177-7.5-.866-9.561 0 0-.812-.606-1.068-4.747z" fill="#d52b1e" stroke-width=".12307499999999999"/>
        <path d="M327.426 278.79s.501 5.46.619 7.155c0 0-1.699-2.469-2.137-.218l-.383-3.203s1.478-2.423 1.9-3.733z" fill="#f7e214" stroke-width=".12307499999999999"/>
        <path d="M328.727 287.815s.558-6.116 3.52-1.792c.115.001.272 2.43.102 3.01-.093.778-.913 1.74-1.256 2.318-.647.964-1.34-.213-1.34-.213s-.674-.983-1.026-3.323z" fill="#007934" stroke-width=".12307499999999999"/>
      </g>
      <path d="M586.927 838.91c-1.33-42.48-14.677-113.67-15.34-162.078l-146.33-155.995s-18.061 119.962-71.193 186.536l232.863 131.61" fill="#d52b1e" transform="matrix(-.08205 -.00386 0 .08205 400.357 170.372)"/>
      <path d="M597.494 844.433c6.162-29.214 13.559-62.066 18.467-123.541l-94.2-97.245c.377 38.745-43.493 96.608-46.462 168.84" fill="#ffe000" transform="matrix(-.08205 -.00386 0 .08205 400.357 170.372)"/>
      <path d="M678.728 937.604c10.806-51.253-15.684-58.916 23.72-129.896L615.4 720.15c-16.22 48.317-27.14 79.396-25.727 121.875l74.161 56.265" fill="#007934" transform="matrix(-.08205 -.00386 0 .08205 400.357 170.372)"/>
      <path d="M372.448 270.086c1.446-4.298-.517-12.73-.299-18.134.164-3.486-2.4-16.592-2.345-20.557l14.116-8.816s.614 14.325 2.239 29.928c1.462 8.015 1.452 15.338.402 21.455-1.5 8.696-2.975 12.266-6.487 15.944-6.235 6.452-19.659 2.724-19.659 2.724-11.309-2.34-17.822-9.463-17.822-9.463s3.678.869 9.637 1.475c13.062-.915 18.206 2.349 18.612-11.053" fill="#d52b1e" stroke-width=".12307499999999999"/>
      <path d="M334.619 278.77l-.218-.095c2.448.916 5.862 1.928 5.862 1.928l7.959.613c17.262.403 15.101-10.285 14.417-27.53-.164-6.544-1.461-14.786-1.127-17.704l11.572-6.63c3.775 10.665 2.598 18.125 3.354 23.58.394 5.877 1.647 17.353-.258 22.36-2.697 11.65-11.819 11.175-24.166 10.15-6.126-.507-9.421-2.082-9.421-2.082l-7.967-4.584" fill="#f7e214" stroke-width=".12307499999999999"/>
      <path d="M334.394 278.536c2.394 1.24 5.917 2.154 5.917 2.154l8.066.797c12.515 1.062 19.841-7.729 18.237-27.606-.01-7.022-.235-10.142-2.658-19.496l-6.878 4.035v.648c.389 2.1 1.373 7.01 1.373 9.255 0 15.96-10.091 28.218-23.839 30.3l-.218-.093" fill="#007a3d" stroke-width=".12307499999999999"/>
      <path d="M380.08 230.55c-1.167 1.96-8.13 12.568-12.032 14.574M378.906 237.48c-1.166 2.007-9.692 13.954-13.532 14.627M380.857 255.739c-1.95 2.34-3.894 6.976-9.692 9.983M378.129 269.02c-3.895 3.632-13.533 11.615-22.836 11.943M378.129 274.997c-1.95 2.34-6.18 13.283-24.01 8.317M380.857 277.003c-1.166 2.626-10.081 17.253-25.96 11.61" fill="none" stroke-width=".12307499999999999"/>
      <path d="M362.249 264.834c1.446-4.298-.517-12.73-.3-18.134.164-3.486-1.48-15.542-1.425-19.506l13.197-9.867s.614 14.325 2.239 29.928c1.462 8.015 2.576 17.831 1.493 23.943-1.773 10.002-6.582 12.815-7.45 13.59-6.742 6.005-21.96 5.415-23.472 4.874-10.874-3.886-15.611-10.795-15.611-10.795s5.152-.083 11.111.523c13.062-.915 19.658-.37 20.065-13.772" fill="#d52b1e" stroke-width=".12307499999999999"/>
      <path d="M324.42 273.518l-.218-.095c2.448.917 5.862 1.929 5.862 1.929l7.959.612c17.262.404 15.101-10.285 14.417-27.53-.164-6.544-.39-12.422-.055-15.34l10.347-7.548c3.774 10.664 2.751 16.68 3.507 22.135.394 5.876 1.647 17.352-.258 22.36-2.698 11.65-11.819 11.174-24.166 10.15-6.126-.508-9.422-2.083-9.422-2.083l-7.966-4.584" fill="#ffe000" stroke-width=".12307499999999999"/>
      <path d="M324.113 273.362c2.394 1.24 5.917 2.154 5.917 2.154l8.067.797c12.514 1.062 19.84-7.729 18.237-27.606-.011-7.023.223-7.515-2.2-16.87-3.833 2.484-11.16 8.74-11.16 8.74s1.994 2.805 1.526 6.728c0 15.96-6.421 24.062-20.169 26.144l-1.508-13.035" fill="#007934" stroke-width=".12307499999999999"/>
      <path d="M369.88 225.298c-1.166 1.96-8.13 12.568-12.031 14.574M368.707 232.229c-1.166 2.006-9.692 13.954-13.532 14.626M370.658 250.487c-1.95 2.34-3.895 6.977-9.692 9.983M367.93 263.768c-3.895 3.632-13.533 11.615-22.836 11.943M367.93 269.745c-1.951 2.34-6.18 13.283-24.01 8.317M370.658 271.751c-1.166 2.626-10.081 17.254-25.96 11.61" fill="none" stroke-width=".12307499999999999"/>
      <path d="M383.827 223.966s.584 5.083.64 5.995c.269 4.408.145 7.744-.063 9.82.014.224-.85 5.498-.552 5.78 1.013 1.156 1.049 1.285 2.117.429.153-.176-.46-5.577-.506-6.32-.104-.797-.27-7.106-.384-9.857-.012-1.088-.947-6.444-.947-6.444s-.125-1.14-.3.582" fill="#f7e214" stroke-width=".12307499999999999"/>
      <path d="M383.827 223.966s.584 5.083.64 5.995c.269 4.408.138 7.744-.063 9.82.014.224-1.03 7.565-.705 5.787 1.013 1.156 1.221 1.592 2.289.737.153-.176-.48-5.892-.525-6.635-.104-.797-.27-7.106-.384-9.857-.012-1.088-.947-6.444-.947-6.444s-.125-1.14-.3.582l-.005.015z" fill="#e8a30e" stroke-width=".12307499999999999"/>
      <path d="M383.936 222.184s1.151 6.09 1.256 9.584c.15 4.24.332 5.439.264 7.91-.044.17.414 3.193.549 4.507.096.678-.099.174.014.224.865.53 1.454.104 1.982-.314.157-.118-1.4-3.781-1.445-4.524-.821-10.758-2.381-17.062-2.381-17.062s.622 3.817-.239-.325" fill="#f7e214" stroke-width=".12307499999999999"/>
      <path d="M383.936 222.184s1.151 6.09 1.249 9.585c.15 4.239.332 5.438.265 7.91-.045.169.413 3.192.548 4.506.096.678-.098.174.014.225.866.53 1.454.103 1.982-.314.157-.119-1.399-3.782-1.445-4.532-.821-10.757-2.375-17.062-2.375-17.062s.623 3.817-.238-.326v.008zm.499 17.315s.913.414 1.015.18m.04-1.255s-.78-.02-.903-.24m.045-1.049s.576.375.732.136m-.718-1.682l.555.024m-.591-1.52l.609.022m-.514-2.148s.344.153.385-.073m-.493-1.7l.497-.022m.475 9.564s.896.127.995-.158m-1.066-1.906s.892.073.93-.211m-.967-1.277s.726.023.777-.035m-.919-1.4l.722-.032m-.718-1.69l.494-.078m-.694-1.402s.45.093.545-.136m-.596-1.591s.406.264.389-.017m.049 8.959s.888.008.871-.264" fill="#e8a30e" stroke-width=".12307499999999999"/>
      <path d="M373.628 218.714s.583 5.083.64 5.995c.269 4.408.145 7.744-.063 9.82.014.224-.85 5.498-.552 5.78 1.013 1.156 1.049 1.285 2.117.429.153-.176-.46-5.577-.506-6.32-.104-.797-.27-7.106-.384-9.857-.012-1.088-.947-6.443-.947-6.443s-.125-1.14-.3.582" fill="#f7e214" stroke-width=".12307499999999999"/>
      <path d="M373.628 218.714s.583 5.083.64 5.995c.269 4.408.138 7.744-.063 9.82.014.224-1.03 7.565-.705 5.787 1.013 1.156 1.221 1.592 2.289.737.153-.176-.48-5.892-.525-6.634-.104-.798-.27-7.107-.384-9.858-.012-1.088-.947-6.443-.947-6.443s-.125-1.14-.3.582l-.005.014z" fill="#e8a30e" stroke-width=".12307499999999999"/>
      <path d="M373.737 216.932s1.151 6.09 1.255 9.585c.15 4.239.333 5.438.265 7.909-.044.17.414 3.193.549 4.507.096.678-.099.174.014.225.865.53 1.454.103 1.982-.314.156-.119-1.4-3.782-1.445-4.525-.821-10.757-2.381-17.062-2.381-17.062l-.239-.325" fill="#f7e214" stroke-width=".12307499999999999"/>
      <path d="M373.737 216.932s1.151 6.09 1.249 9.585c.15 4.239.332 5.438.264 7.91-.044.169.414 3.192.55 4.507.095.677-.1.173.013.224.866.53 1.454.104 1.982-.314.157-.119-1.4-3.782-1.445-4.532-.822-10.757-2.375-17.062-2.375-17.062l-.238-.325v.007zm.499 17.315s.913.414 1.014.18m.04-1.255s-.78-.02-.903-.24m.046-1.049s.576.376.732.136m-.718-1.682l.555.024m-.591-1.519l.609.021m-.514-2.148s.344.153.385-.073m-.493-1.7l.497-.022m.475 9.564s.896.127.994-.158m-1.065-1.906s.892.073.93-.21m-.967-1.278s.726.023.777-.035m-.92-1.4l.723-.032m-.718-1.69l.494-.077m-.694-1.403s.45.093.545-.136m-.596-1.59s.406.263.389-.018m.049 8.959s.888.008.871-.264" fill="#e8a30e" stroke-width=".12307499999999999"/>
      <g fill="#e8a30e" stroke-width=".217">
        <path d="M356.466 216.692s1.26 9.73 1.091 15.273c-.18 6.726 0 8.638-.367 12.549-.077.265.367 5.087.444 7.179.08 1.08-.176.268 0 .356 1.36.9 2.36.268 3.263-.356.357-.177-1.903-6.09-1.903-7.27-.268-17.1-2.183-27.198-2.183-27.198l-.357-.533" transform="matrix(-.61184 .0275 .03206 .5246 576.82 87.782)"/>
        <path d="M356.466 216.692s1.268 9.73 1.08 15.273c-.177 6.726 0 8.638-.368 12.549-.077.265.368 5.087.445 7.179.08 1.08-.177.268 0 .356 1.36.9 2.36.268 3.263-.356.356-.177-1.904-6.09-1.904-7.282-.268-17.1-2.171-27.198-2.171-27.198l-.357-.533zM357.378 244.613s1.448.265 1.639-.18M357.569 241.339s1.447.18 1.447-.268M357.569 238.979s1.168.088 1.268 0M357.469 236.696h1.18M357.646 233.969l.812-.089M357.569 231.609s.624.18.812-.177M357.569 228.87s.624.447.624 0M357.378 243.066s1.448.077 1.448-.357" transform="matrix(-.61184 .0275 .03206 .5246 576.82 87.782)"/>
      </g>
      <path d="M333.985 221.65l-.797.06s1.233-.09.797-.06z" fill="#005000" stroke-width=".12307499999999999"/>
      <path d="M323.273 256.404s.372-.16.465 0c0 .08-.093.16-.186.16s-.279-.16-.279-.16zM324.295 257.52s-1.58-.16-2.136-.16" fill="#fff" fill-rule="evenodd" stroke-width=".12307499999999999"/>
    </g>
    <g>
      <path d="M291.666 215.476c-.028-2.1 1.95-3.315 2.254-3.52.97-.656 1.63-1.224 3.722-1.497 0 0 .143.433.135.805-.007.372-.462 1.658-2.07 2.745-1.638 1.107-4.04 1.467-4.04 1.467z" fill="#d52b1e" stroke-width=".12307499999999999"/>
      <path d="M291.643 214.675l29.644 38.48 1.408-1.387-30.24-39.087-.812 1.994z" fill="#a05a2c" fill-rule="evenodd" stroke-width=".12307499999999999"/>
      <path d="M291.186 207.16s3.207-.333 2.783-2.166c-.453-1.813-2.613-1.778-3.515-1.863-.932-.062-3.874.622-4.746 1.497-.903.895-2.745 2.426-2.148 4.997.597 2.572 1.354 4.342 2.3 5.944.939 1.648.703 3.234.453 3.933-.077.296-.373 1.27.43 1.625 1.183.504 1.478.508 2.501-.632 1.048-1.115 2.451-2.92 2.422-5.02-.028-2.098 1.95-3.314 2.254-3.519.97-.656 1.63-1.224 3.722-1.497 0 0-.742-1.21-1.857-1.15-1.136.035-3.394-.966-4.599-2.149z" fill="#d52b1e" stroke-width=".12307499999999999"/>
      <path d="M291.186 207.16s3.207-.333 2.783-2.166c-.453-1.813-2.613-1.778-3.515-1.863-.932-.062-3.874.622-4.746 1.497-.903.895-2.745 2.426-2.148 4.997.597 2.572 1.354 4.342 2.3 5.944.939 1.648.703 3.234.453 3.933-.077.296-.373 1.27.43 1.625 1.183.504 1.478.508 2.501-.632 1.048-1.115 2.451-2.92 2.422-5.02-.028-2.098 1.95-3.314 2.254-3.519.97-.656 1.63-1.224 3.722-1.497 0 0-.742-1.21-1.857-1.15-1.136.035-3.394-.966-4.599-2.149z" fill="none" stroke-width=".12307499999999999"/>
      <path d="M291.186 207.16c-.432.007-1.712-.554-2.6-.24-.864.34-2.653 1.386-2.366 2.904M296.649 209.622s-1.85.761-3.153 1.642c-.547.37-2.359 2.043-3.527 3.24-1.032 1.023-1.26 2.4-3.474 3.897M295.483 209.351c-.38.338-.76.676-1.411 1.035-.621.339-.82.88-1.214 1.31" fill="none" stroke-width=".12307499999999999"/>
    </g>
    <g>
      <g stroke-width="1.512">
        <g stroke-width=".194">
          <path d="M156.9 89.493c-.033 1.927-3.764 4.277-6.82.323-3.193-2.722-2.733-7.08-.329-7.767l27.817-34.577c1.144-.796 1.224-1.517 1.777-2.272 1.305 1.486 3.987 4.142 5.443 5.42-.81.86-1.461 1.67-1.618 2.323l-26.27 36.55z" fill="#e8a30e" fill-rule="evenodd" transform="matrix(-1.8554 .0577 .0868 1.58684 631.153 133.141)" stroke-width=".072"/>
          <g stroke-width=".045" fill-rule="evenodd">
            <path d="M123.245 190.484c.837-1.426 4.37 2.172 3.488 3.327-.882 1.155-4.256-1.888-3.488-3.327z" fill="#e7e7e7" stroke-linejoin="round" transform="matrix(-2.96532 .0479 .0869 2.53565 651.09 -273.854)"/>
            <path d="M126.418 193.544c-.628.584-3.183-1.828-2.8-2.765.66-.884 3.483 2.186 2.8 2.765z" fill="#cccccf" transform="matrix(-2.96532 .0479 .0869 2.53565 651.09 -273.854)"/>
          </g>
        </g>
        <path d="M3513.155 736.637c56.547-19.255 95.944-57.232 120-113.037M3934.483 1159.288c71.128-23.522 118.995-70.304 139.06-144.889M3964.46 1190.93c71.128-23.522 118.996-70.304 139.06-144.888M4138.493 1357.47c71.128-23.523 127.323-76.134 147.388-150.718M4169.303 1389.112c71.128-23.522 128.155-76.966 148.22-151.55" fill="none" transform="matrix(.0802 .0023 -.00223 .08257 15.885 158.975)"/>
        <path d="M4338.34 1405.766c4.012 16.341 13.42 21.216 26.23 15.821M4192.589 1403.136c28.07 40.396 57.66 27.18 82.718 26.9" fill="none" stroke-linecap="round" transform="matrix(.0802 .0023 -.00223 .08257 15.885 158.975)"/>
        <path d="M4375.222 1406.928c.312 10.999-9.015 20.158-20.851 20.476-11.837.318-21.717-8.326-22.089-19.323-.372-10.997 8.905-20.2 20.74-20.573 11.834-.373 21.761 11.555 22.193 22.55" fill="#e8a30e" transform="matrix(.0802 .0023 -.00223 .08257 15.885 158.975)"/>
        <path d="M4336.24 1403.837c4.012 16.34 13.42 21.216 26.23 15.82M4192.589 1403.136c28.07 40.396 57.66 27.18 82.718 26.9" fill="none" stroke-linecap="round" transform="matrix(.0802 .0023 -.00223 .08257 15.885 158.975)"/>
      </g>
    </g>
  </g>
  <g fill="#007934" stroke="#000" stroke-width="2.163">
    <path d="M339.65 175.519c1.774 2.839 4.406 7.962 5.125 11.886 1.325 7.24-.747 15.265-7.053 20.937-5.138 4.62-13.202 6.049-16.569 6.74-3.359.689-5.789 1.77-6.344 2.511-.028-.522-.105-1.045.456-1.568 1.595-.703 4.183-1.12 7.792-1.863 7.251-1.494 14.869-4.213 19.02-12.125 5.411-10.318 2.187-18.427-2.501-26.476l.074-.042z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(.05408 .05219 -.05054 .0481 358.084 175.202)" fill="#d52b1e" stroke-width="1.701"/>
    <path d="M346.576 204.939c-1.079.64-2.147.808-3.004 1.093-.662.221-1.8.58-2.193.728-.375.141-.96.405-1.46.632-.794.359-1.653 1.355-1.653 1.355s1.207 1.266 2.65 1.087c1.13-.14 1.7-.45 2.267-.726.59-.287.576-.612 1.425-1.224.939-.677 1.597-2.014 1.968-2.945zM340.924 206.135c-.345.542-1.172.5-1.645.426l-.213.27c.649.01 1.536.017 1.98-.568-.011-.087-.064-.128-.122-.128z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M346.561 204.988c-.976.95-2.798 2.008-4.627 2.79-1.83.782-3.97 1.032-5 1.026l-.272.287c1.483-.138 3.438-.407 5.094-1.087 2.16-.886 3.934-2.025 4.82-2.96l-.015-.056zM344.183 209.563c-1.947-.069-3.019.537-4.828.933-1.691.37-3.67-.51-4.764 1.117 4.407 2.856 7.589.935 9.592-2.05z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M340.22 213.564c-.84-.72-8.05-3.16-9.202-.432 1.715 1.897 6.736 2.32 9.201.432z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M338.51 215.484c-.761-.12-1.659-.297-2.536-.398-.881-.102-1.227-.255-1.878-.345-1.065-.147-2.285-1.642-6.04-.5 1.453 3.349 6.415 4.052 10.454 1.243zM340.134 213.618c-3.842.767-8.357-.002-10.142-1.054l-.284.19c4.115 1.753 8.114 1.522 10.426.864zM344.122 209.562c-2.262 1.518-5.178 2.649-11.423 1.812l-.086.216c8.558.734 9.405-.834 11.51-2.028z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M338.482 215.511c-2.986.106-4.667 1.437-10.26-1.316-.457-.225-1.067-.382-1.42-.55l-.513.19c.39.173 1.018.169 1.368.323 6.984 3.082 6.788 1.641 10.825 1.353z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(-.0379 -.06375 -.06265 .03283 363.213 205.172)" fill="#d52b1e" stroke-width="1.701"/>
    <path d="M327.147 212.253c-.644.165-.83.932-.885 1.383l-.333.12c.17-.594.407-1.406 1.122-1.65.085.035.113.094.096.147z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(.0748 .0178 -.01674 .06604 342.926 152.195)" fill="#d52b1e" stroke-width="1.701"/>
    <path d="M341.622 203.153c-.14-2.086-1.153.805-3.197-3.855-.595-1.355-.59-2.219-.993-4.252 1.113 1.758 2.974 2.232 3.73 3.59.744 1.336.554 3.484.46 4.517z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M337.472 195.2s1.007 2.35 2.458 4.014c1.459 1.674 1.732 3.71 1.732 3.71" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M338.47 197.196c1.282 1.728 3.007 3.775 3.088 5.924l.2.075c-.298-2.827-1.97-4.132-3-5.558l-.289-.44zM349.317 199.864c-.94.773-1.927 1.052-2.7 1.434-.597.296-1.098.688-1.45.88-.337.184-.885.27-1.33.554-.704.452-2.082 2.033-2.082 2.033s1.306 1.07 2.116.895c2.355-.457 3.042-1.391 4.23-2.257.918-.75.979-2.534 1.216-3.539z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M340.695 204.836l-.221.325c1.351-.133 3.504-1.43 4.94-2.153 1.93-.972 3.012-1.711 3.826-3.093-.922 1.381-2.126 2.166-3.94 3.042-1.522.736-3.686 1.981-4.605 1.879z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M350.118 195.296c-.788.913-1.71 1.348-2.404 1.849-.536.387-.96.854-1.272 1.1-.299.236-.824.408-1.21.76-.614.56-1.525 2.19-1.525 2.19s.653.698 1.42.394c2.374-.294 3.071-1.42 3.954-3.446.494-1.05.983-1.819 1.037-2.847z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(-.07012 .03046 .02916 .06203 337.828 164.892)" fill="#d52b1e" stroke-width="1.701"/>
    <path d="M342.51 201.585l-.16.356c2.846-1.297 6.579-3.913 7.801-6.578-1.738 2.874-4.608 4.77-7.64 6.222zM350.767 190.9c-.677.95-1.516 1.44-2.133 1.979-.477.417-.843.904-1.12 1.167-.265.252-.747.457-1.087.83-.538.59-1.278 2.255-1.278 2.255s.868.817 1.57.466c.7-.35 1.64-1.146 2.105-1.56.484-.433.603-1.475 1.171-2.27.662-.925.795-1.854.772-2.867z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M348.718 193.838c-1.38 1.572-3.079 3.071-4.67 3.757l-.124.36c2.589-1.467 3.68-2.794 4.826-4.067l-.032-.05zM349.787 188.192c-.47.923-1.177 1.47-1.665 2.028-.378.43-.637.907-.852 1.176-.206.257-.626.505-.88.878-.405.59-.81 2.141-.81 2.141s.588.627 1.188.22c.6-.405 1.46-1.235 1.827-1.662.38-.446.573-1.41.97-2.182.461-.898.424-1.723.222-2.599zM345.418 187.099c-.25.54-.35 1.071-.513 1.629l-.064-.399c.134-.437.24-.897.449-1.298.043-.01.085-.012.128.068z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M348.37 191.04c-1.026 1.564-2.367 3.112-3.748 3.938l-.055.331c2.188-1.65 2.985-2.96 3.843-4.23l-.04-.04zM342.75 200.935c-.114-.499-.206-.891-.533-1.34-.05-.002-.084.013-.095.06.298.482.424.905.507 1.49l.12-.21z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(.04214 -.03823 .0377 .03686 317.377 169.143)" fill="#d52b1e" stroke-width="2.249"/>
    <path d="M343.15 182.53c-.357-.31-.8-.588-1.25-.864-.05.018-.056.054-.041.099.516.326.97.644 1.354 1.006l-.064-.241zM348.672 182.886c-1.13 2.385-3.85 4.052-2.76 7.552 2.81 2.538 2.99-4.542 2.76-7.552z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M346.094 177.666c-.463 2.438-2.628 4.454-1.209 7.686 3.984 1.002 2.085-4.368 1.209-7.686z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M343.737 174.014c2.002 3.723 2.483 5.876.12 8.317 0 0-1.24-1.166-1.536-3.31-.25-1.815 1.23-3.988 1.416-5.007z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M340.413 171.853c.531 2.36-1.511 3.442 1.41 6.482 2.085-2.48 1.09-3.1-1.41-6.482z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M339.628 175.983c-3.368.36-2.056-2.56-3.016-5.255 1.983 1.478 4.67 2.073 3.016 5.255zM341.823 180.438c-.948-4.39-3.968-2.673-5.64-4.608.865 2.832 2.134 4.765 5.64 4.608zM343.182 184.523c-2.631-.173-4.765-1.47-6.182-4.38 2.7 1.204 5.692 1.326 6.182 4.38z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M343.58 188.336c-1.037-1.192-1.063-1.961-1.458-2.756-.655-1.315-1.349-2.48-3.042-3.707.012 3.106.454 6.537 4.5 6.463z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M344.179 191.58c-1.615-2.25-3.112-4.162-5.555-5.732 1.014 2.21.545 6.297 5.555 5.733z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M343.723 196.042c-4.957-.139-4.488-4.815-4.53-7.003.896 1.339 1.99 2.503 2.807 3.466.89 1.051 1.748 2.239 1.723 3.537z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M343.125 198.827c-.4-1.014-.093-1.423-.978-2.314-.956-.963-2.79-2.685-3.75-4.662-.14 1.748-.162 4.446 1.192 5.35 1.025.682 1.918.874 3.536 1.626zM338.795 206.074c-3.89-3.026-1.552-5.579-1.225-7.842.988 2.552 3.771 4.752 1.225 7.842zM340.39 176.898c-1.25-2.346-2.01-3.631-3.677-6.078 1.925 2.783 2.756 4.55 3.862 6.456" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M338.852 206.831c.158-3.081-.681-5.736-1.225-8.517.514 2.936 1.244 5.892.969 8.76l.256-.243zM343.47 199.444c-.405-1.884-4.118-3.135-5.045-7.512.76 4.351 4.524 5.448 4.903 7.897l.142-.385zM344.3 196.882l-.1.478c-.549-2.95-4.014-4.402-5.016-8.337 1.464 4.264 4.289 4.827 5.117 7.859zM344.986 192.465c-1.706-2.6-4.09-3.582-6.345-6.635 2.11 3 4.77 4.406 6.385 7.036l-.04-.401zM344.925 189.042c-1.876-.802-3.824-3.054-5.861-7.17 1.336 3.045 3.03 5.8 5.861 7.63v-.46z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M344.06 185.027c-2.19-1.731-5.03-2.881-7.071-4.914 1.696 1.935 4.956 3.288 7.13 5.2l-.06-.286zM342.367 180.725c-2.212-1.504-4.344-1.972-6.184-4.818 1.478 2.785 3.83 3.49 6.265 5.028l-.08-.21zM348.672 182.943c-.793 3.073-1.463 6.302-3.565 8.375l.02-.402c.96-.538 2.48-3.74 3.545-7.973zM346.114 177.78c-.145 3.176-.09 6.535-1.733 8.337l-.12-.363c1.675-1.357 1.535-4.946 1.853-7.974zM343.858 174.167c.562 2.794.603 5.38-.141 9.56l-.161-.306c.466-2.463.996-4.986.302-9.254zM340.474 171.949c1.123 2.37 1.982 4.77 1.249 7.342l-.202-.287c.87-2.352-.147-4.703-1.047-7.055z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <g fill-rule="evenodd">
      <path d="M336.132 203.154c1.328 3.195-.427 5.516-2.503 6.86-1.616-4.643 1.75-4.123 2.503-6.86z" stroke-width=".12307469999999998"/>
      <path d="M336.188 203.144c.228 1.977-1.921 4.19-2.587 7.534l-.3.272c.987-4.078 3.07-5.647 2.887-7.806z" stroke-width=".12307469999999998"/>
    </g>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(.05601 .01001 -.00615 .05237 338.38 154.373)" fill="#d52b1e" stroke-width="2.249"/>
    <path d="M343.526 182.955c.028-.472-.022-.992-.079-1.516-.045-.03-.077-.012-.103.027.057.609.082 1.161.03 1.687l.152-.198z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(.04356 -.02164 .00984 .0445 331.588 162.543)" fill="#d52b1e" stroke-width="2.653"/>
    <path d="M343.156 182.354c-.109-.387-.288-.77-.473-1.153-.04 0-.06.03-.068.075.208.45.378.872.484 1.313l.057-.235z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(.05689 -.00117 .00424 .05256 334.205 161.601)" fill="#d52b1e" stroke-width="2.249"/>
    <path d="M344.857 188.619c-.065-.468-.216-.968-.375-1.471-.05-.02-.078.003-.096.047.175.585.309 1.122.36 1.648l.111-.224z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(.0396 -.04244 .03543 .04092 320.579 173.713)" fill="#d52b1e" stroke-width="2.202"/>
    <path d="M344.796 188.574c-.335-.345-.75-.653-1.173-.958-.048.02-.053.06-.04.109.486.362.912.715 1.273 1.117l-.06-.268z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(.05312 .0204 -.01592 .05027 343.176 171.764)" fill="#d52b1e" stroke-width="2.249"/>
    <path d="M342.839 200.803c.117-.458.165-.978.208-1.503-.039-.038-.074-.027-.107.007-.058.608-.137 1.155-.288 1.662l.187-.166z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(-.04214 -.03823 -.0377 .03686 368.488 187.701)" fill="#d52b1e" stroke-width="2.249"/>
    <path d="M342.716 201.088c.357-.31.8-.588 1.249-.863.051.017.057.054.042.098-.517.327-.97.644-1.355 1.006l.064-.24z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(.03761 .0427 -.0374 .03717 353.24 180.582)" fill="#d52b1e" stroke-width="2.249"/>
    <path d="M339.48 206.158c.316-.352.6-.79.882-1.236-.017-.052-.053-.058-.098-.044-.334.512-.657.96-1.025 1.34l.242-.06z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(-.07659 -.00857 -.00906 .06738 354.597 173.163)" fill="#d52b1e" stroke-width="1.701"/>
    <path d="M339.378 205.072c-.234.603-.067.837.072 1.1l-.147.308c-.207-.346-.285-.729-.102-1.447.086-.033.15-.01.177.039zM327.265 214.455c-.665.056-.998-.315-1.215-.72l-.356-.003c.376.502.775.898 1.534.891.068-.06.072-.125.037-.168z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(-.01693 .07142 -.06997 -.01428 363.697 211.457)" fill="#d52b1e" stroke-width="1.701"/>
    <path d="M332.694 206.08c.503 3.082-1.295 5.586-4.006 6.363-.886-3.973 2.777-4.115 4.006-6.363z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M332.7 206.104c-.996 2.419-2.343 4.67-4.756 7.15l-.519.068c2.686-2.124 4.16-4.884 5.274-7.218z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <path d="M148.402 484.164a5.782 8.122 0 1 1-11.564 0 5.782 8.122 0 1 1 11.564 0z" transform="matrix(.03467 .06304 -.05716 .0142 350.326 197.074)" fill="#d52b1e" stroke-width="1.923"/>
    <path d="M325.91 213.792c.492-.157.971-.415 1.449-.682-.007-.06-.05-.087-.11-.099-.561.3-1.092.546-1.658.699l.318.082z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
    <g stroke-width="2.423" fill-rule="evenodd">
      <path d="M328.534 208.798c-.13 1.238-1.104 2.351-2.003 3.22-.897.869-.998 1.194-2.264 1.57-1.217-2.77 2.853-3.32 4.267-4.79z" stroke-width=".12306417"/>
      <path d="M328.522 208.77c-1.358 2.539-3.52 3.788-4.697 5.427l-.168-.015c1.688-2.225 3.122-2.647 4.865-5.413z" stroke-width=".12306417"/>
    </g>
    <g>
      <path d="M300.423 175.519c-1.775 2.839-4.407 7.962-5.126 11.886-1.325 7.24.747 15.265 7.053 20.937 5.138 4.62 13.202 6.049 16.57 6.74 3.358.689 5.788 1.77 6.343 2.511.028-.522.105-1.045-.455-1.568-1.596-.703-4.184-1.12-7.793-1.863-7.25-1.494-14.869-4.213-19.02-12.125-5.411-10.318-2.186-18.427 2.501-26.476l-.073-.042z" fill-rule="evenodd" stroke-width=".12307469999999998"/>
      <g stroke-width="1.702">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.07409 0 0 -.07057 268.978 254.186)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.07409 0 0 -.07057 268.978 254.186)"/>
      </g>
      <g stroke-width="2.796">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.02842 .04392 -.03516 .01387 348.73 227.419)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.02842 .04392 -.03516 .01387 348.73 227.419)"/>
      </g>
      <g stroke-width="2.653">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.03535 -.02428 .03054 .0399 304.227 138.101)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.03535 -.02428 .03054 .0399 304.227 138.101)"/>
      </g>
      <g stroke-width="3.117">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.01435 -.0395 .03392 .0153 288.606 144.578)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.01435 -.0395 .03392 .0153 288.606 144.578)"/>
      </g>
      <g stroke-width="3.755">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.02689 -.02818 -.0238 -.01503 329.441 168.948)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.02689 -.02818 -.0238 -.01503 329.441 168.948)"/>
      </g>
      <g stroke-width="3.129">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.00698 -.0462 .03173 .01153 284.167 144.81)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.00698 -.0462 .03173 .01153 284.167 144.81)"/>
      </g>
      <g stroke-width="2.626">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.00092 -.04987 .04397 .00412 272.562 148.828)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.00092 -.04987 .04397 .00412 272.562 148.828)"/>
      </g>
      <g stroke-width="3.129">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.0074 -.04614 .03374 .00133 272.288 155.275)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.0074 -.04614 .03374 .00133 272.288 155.275)"/>
      </g>
      <g stroke-width="3.039">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.03127 -.02967 -.02827 -.02564 333.548 176.3)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.03127 -.02967 -.02827 -.02564 333.548 176.3)"/>
      </g>
      <g stroke-width="3.039">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.03333 -.02734 -.02635 -.02761 332.749 180.826)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.03333 -.02734 -.02635 -.02761 332.749 180.826)"/>
      </g>
      <g stroke-width="2.837">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.0345 -.03067 -.02937 -.02843 334.27 181.854)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.0345 -.03067 -.02937 -.02843 334.27 181.854)"/>
      </g>
      <g stroke-width="4.239" fill-rule="evenodd">
        <path d="M296.838 183.006s-.286-.553-.29-1.066c-.004-.513.127-.778.127-.778l-.2-.123s-.1.55-.098.814c0 .265.01.332.01.332s-.36-.308-.438-.483c-.078-.174-.179-.434-.179-.434l-.174.078s.163.542.301.648c.483.37.777 1.111.777 1.111" stroke-width=".12305817"/>
        <path d="M295.595 180.302a.369.543-6.466 1 1 .123 1.08.369.543-6.466 1 1-.123-1.08z" style="marker:none" overflow="visible" fill="#000" stroke-width=".1230638"/>
        <path d="M296.837 180.294a.543.369-70.71 1 1-.359 1.026.543.369-70.71 1 1 .36-1.026z" style="marker:none" overflow="visible" fill="#000" stroke-width=".1230638"/>
      </g>
      <g stroke-width="4.239" fill-rule="evenodd">
        <path d="M296.727 182.95s.571-.25.898-.645c.326-.395.392-.684.392-.684l.233.03s-.27.49-.438.695c-.167.204-.216.251-.216.251s.473-.012.644-.098c.17-.087.413-.225.413-.225l.086.17s-.469.318-.643.313c-.608-.017-1.304.372-1.304.372" stroke-width=".12305817"/>
        <path d="M299.397 181.635a.543.369-44.426 1 1-.776.76.543.369-44.426 1 1 .776-.76zM298.439 180.845a.543.369-70.182 1 1-.368 1.023.543.369-70.182 1 1 .368-1.023z" style="marker:none" overflow="visible" fill="#000" stroke-width=".1230638"/>
      </g>
      <g stroke-width="3.004">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.01074 -.04747 .03516 -.00084 268.672 158.435)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.01074 -.04747 .03516 -.00084 268.672 158.435)"/>
      </g>
      <g stroke-width="2.724">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.03385 -.03415 -.0324 -.02762 334.855 181.986)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.03385 -.03415 -.0324 -.02762 334.855 181.986)"/>
      </g>
      <g stroke-width="2.931">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.03483 -.0357 -.0308 -.01906 334.11 178.856)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.03483 -.0357 -.0308 -.01906 334.11 178.856)"/>
      </g>
      <g stroke-width="2.447">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.01362 -.04871 .04988 -.00733 258.087 164.177)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.01362 -.04871 .04988 -.00733 258.087 164.177)"/>
      </g>
      <g stroke-width="2.842">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.02858 -.04283 -.03473 -.0136 332.565 174.813)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.02858 -.04283 -.03473 -.0136 332.565 174.813)"/>
      </g>
      <g stroke-width="2.422">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.02157 -.0478 .04628 -.01712 255.259 173.917)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.02157 -.0478 .04628 -.01712 255.259 173.917)"/>
      </g>
      <g stroke-width="3.885" fill-rule="evenodd">
        <path d="M295.052 191.59s-.45-.51-.59-1.052c-.14-.542-.072-.857-.072-.857l-.244-.077s.04.608.112.888c.071.28.098.348.098.348s-.462-.23-.591-.394c-.129-.165-.305-.413-.305-.413l-.164.129s.317.53.492.606c.609.263 1.117.97 1.117.97" stroke-width=".12307679999999999"/>
        <path d="M293.019 189.058a.402.593-20.556 1 1 .416 1.11.402.593-20.556 1 1-.416-1.11z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12306711999999999"/>
        <path d="M294.33 188.72a.402.593 5.2 1 1-.107 1.18.402.593 5.2 1 1 .108-1.18z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12306711999999999"/>
      </g>
      <g stroke-width="3.885" fill-rule="evenodd">
        <path d="M295.042 191.55s.538-.414.779-.92c.24-.505.233-.828.233-.828l.255-.03s-.156.59-.279.851c-.123.262-.162.324-.162.324s.497-.139.655-.275c.158-.137.377-.348.377-.348l.137.158s-.412.46-.597.502c-.649.143-1.282.74-1.282.74" stroke-width=".12307679999999999"/>
        <path d="M297.519 189.45a.593.402-58.515 1 1-.62 1.012.593.402-58.515 1 1 .62-1.012zM296.295 188.87a.402.593 5.729 1 1-.119 1.179.402.593 5.729 1 1 .119-1.18z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12306711999999999"/>
      </g>
      <g stroke-width="2.537">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.03201 -.04797 .0389 -.01523 253.552 175.774)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.03201 -.04797 .0389 -.01523 253.552 175.774)"/>
      </g>
      <g stroke-width="2.212">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.02363 -.05235 -.05068 -.01874 339.21 175.067)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.02363 -.05235 -.05068 -.01874 339.21 175.067)"/>
      </g>
      <g stroke-width="2.336">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.02214 -.05352 -.04671 -.01248 336.956 173.835)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.02214 -.05352 -.04671 -.01248 336.956 173.835)"/>
      </g>
      <g stroke-width="1.984">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.03635 -.0527 .05184 -.03066 244.367 184.794)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.03635 -.0527 .05184 -.03066 244.367 184.794)"/>
      </g>
      <g stroke-width="2.614">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.00719 -.0538 -.04161 .00308 328.473 171.161)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.00719 -.0538 -.04161 .00308 328.473 171.161)"/>
      </g>
      <g stroke-width="1.874">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.05293 -.0429 .04597 -.04422 241.264 204.932)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.05293 -.0429 .04597 -.04422 241.264 204.932)"/>
      </g>
      <g stroke-width="2.222">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.0479 -.04483 .04024 -.0264 245.833 190.965)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.0479 -.04483 .04024 -.0264 245.833 190.965)"/>
      </g>
      <g stroke-width="2.124">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.01206 -.0593 -.05503 -.00787 337.411 171.36)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.01206 -.0593 -.05503 -.00787 337.411 171.36)"/>
      </g>
      <g stroke-width="3.469" fill-rule="evenodd">
        <path d="M297.44 201.471s-.69-.322-1.076-.815c-.387-.493-.458-.848-.458-.848l-.286.03s.315.607.513.862c.199.255.257.314.257.314s-.578-.03-.784-.14c-.205-.112-.497-.288-.497-.288l-.11.206s.562.402.775.402c.743-.002 1.581.495 1.581.495" stroke-width=".12308011999999999"/>
        <path d="M294.22 199.782a.45.664-44.111 1 1 .923.954.45.664-44.111 1 1-.924-.954z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12306015000000001"/>
        <path d="M295.414 198.848a.45.664-18.355 1 1 .419 1.26.45.664-18.355 1 1-.419-1.26z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12306015000000001"/>
      </g>
      <g stroke-width="3.469" fill-rule="evenodd">
        <path d="M297.531 201.436s.366-.667.387-1.293c.021-.627-.13-.955-.13-.955l.247-.144s.105.675.095.998c-.01.323-.022.405-.022.405s.449-.365.55-.576c.1-.211.231-.526.231-.526l.211.101s-.216.657-.388.782c-.602.438-.984 1.334-.984 1.334" stroke-width=".12308011999999999"/>
        <path d="M299.133 198.171a.45.664 7.93 1 1-.183 1.316.45.664 7.93 1 1 .183-1.316z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12306015000000001"/>
        <path d="M297.617 198.123a.45.664-17.827 1 1 .406 1.264.45.664-17.827 1 1-.406-1.264z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12306015000000001"/>
      </g>
      <g stroke-width="2.211">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.05587 -.0246 .02314 -.04526 254.426 218.653)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.05587 -.0246 .02314 -.04526 254.426 218.653)"/>
      </g>
      <g stroke-width="2.937">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(.0011 -.04911 -.03573 -.00164 323.245 178.559)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(.0011 -.04911 -.03573 -.00164 323.245 178.559)"/>
      </g>
      <g stroke-width="1.965">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.06412 -.02235 .0309 -.05043 248.71 225.745)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.06412 -.02235 .0309 -.05043 248.71 225.745)"/>
      </g>
      <g stroke-width="2.415">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.02272 -.04147 -.04626 .02988 318.545 167.86)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.02272 -.04147 -.04626 .02988 318.545 167.86)"/>
      </g>
      <g stroke-width="3.01" fill-rule="evenodd">
        <path d="M303.337 208.66s-.583.456-1.207.094c-.625-.363-.853-.712-.853-.712l-.292.154s.595.515.917.703c.321.189.409.226.409.226s-.631.215-.899.185-.655-.095-.655-.095l-.03.268s.756.114 1.001.098c.245-.016 1.53-.515 1.53-.515" stroke-width=".1230789"/>
        <path d="M299.46 208.735a.52.765-65.886 1 1 1.398.626.52.765-65.886 1 1-1.397-.626z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12313015"/>
        <path d="M300.34 207.224a.52.765-40.13 1 1 .987 1.17.52.765-40.13 1 1-.986-1.17z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12313015"/>
      </g>
      <g stroke-width="3.01" fill-rule="evenodd">
        <path d="M303.405 209.004s.49-.553.168-1.2c-.323-.645-.657-.894-.657-.894l.171-.283s.477.627.645.96c.168.332.2.422.2.422s.254-.617.24-.886c-.013-.269-.053-.66-.053-.66l.269-.014s.067.763.036 1.006c-.031.244-.61 1.496-.61 1.496" stroke-width=".1230789"/>
        <path d="M303.72 205.14a.52.765-20.553 1 1 .538 1.433.52.765-20.553 1 1-.538-1.433z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12313015"/>
        <path d="M302.157 205.924a.52.765-46.309 1 1 1.107 1.057.52.765-46.309 1 1-1.107-1.057z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12313015"/>
      </g>
      <g stroke-width="1.835">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.06885 -.00864 .00927 -.06415 261.043 243.504)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.06885 -.00864 .00927 -.06415 261.043 243.504)"/>
      </g>
      <g stroke-width="2.613" fill-rule="evenodd">
        <path d="M310.77 212.544s-.467.713-1.283.548c-.815-.165-1.192-.461-1.192-.461l-.263.276s.84.341 1.26.428c.42.087.53.096.53.096s-.609.467-.912.533c-.304.065-.75.138-.75.138l.065.304s.868-.155 1.13-.263c.261-.108 1.48-1.127 1.48-1.127" stroke-width=".12307230000000001"/>
        <path d="M306.569 214.058a.598.881-84.589 1 1 1.755.166.598.881-84.589 1 1-1.755-.166z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12305121000000001"/>
        <path d="M306.97 212.084a.598.881-58.833 1 1 1.509.912.598.881-58.833 1 1-1.509-.912z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12305121000000001"/>
      </g>
      <g stroke-width="2.27">
        <path d="M-609.043 575.416c2.993-5.86 54.135-52.969 100.641-49.438 34.051 2.586 39.685 11.2 39.685 11.2s3.174 8.864-27.059 15.358c-30.544 6.561-83.494 28.465-113.267 22.88z" fill-rule="evenodd" transform="matrix(-.03216 -.03785 -.0473 .03578 317.109 168.575)"/>
        <path d="M-470.399 536.956c-36.55-4.34-73.797 11.164-102.962 22.854" transform="matrix(-.03216 -.03785 -.0473 .03578 317.109 168.575)"/>
      </g>
      <g stroke-width="2.613" fill-rule="evenodd">
        <path d="M311.347 213.067s.22-.823-.447-1.321c-.667-.497-1.14-.58-1.14-.58l.03-.38s.815.397 1.16.653c.345.255.425.33.425.33s-.058-.765-.211-1.035c-.154-.27-.398-.65-.398-.65l.27-.154s.464.75.558 1.016c.094.267.148 1.854.148 1.854" stroke-width=".12307230000000001"/>
        <path d="M309.672 208.928a.598.881-47.264 1 1 1.294 1.196.598.881-47.264 1 1-1.294-1.196z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12305121000000001"/>
        <path d="M308.47 210.544a.598.881-73.02 1 1 1.685.515.598.881-73.02 1 1-1.686-.515z" style="marker:none" overflow="visible" fill="#000" stroke-width=".12305121000000001"/>
      </g>
    </g>
  </g>
  <path d="M319.103 208.988s-2.34 5.437-1.532 5.976c0 0 2.375-4.22 4.46-5.818 1.084-1.022 1.743.01 1.883-.89.14-.899-2.853-2.189-2.853-2.189l-1.915 2.696" fill="#452c25"/>
  <path d="M593.678 214.479s-8.426 19.153-5.515 21.053c0 0 8.549-14.868 16.052-20.496 3.902-3.601 6.274.032 6.777-3.135.502-3.168-10.267-7.711-10.267-7.711l-6.894 9.497" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".585"/>
  <path d="M310.563 213.127s-3.402 5.924-2.51 5.994c.892.07 4.53-7.554 4.53-7.554l-1.24.213-.78 1.347z" fill="#452c25"/>
  <path d="M562.943 229.062s-12.246 20.868-9.035 21.113c3.211.246 16.305-26.609 16.305-26.609l-4.464.748-2.806 4.748z" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
  <path d="M311.591 211.477s-3.556 5.828-2.667 5.922c.89.094 4.727-7.427 4.727-7.427l-1.245.179-.815 1.326z" fill="#452c25"/>
  <path d="M566.642 223.246s-12.8 20.532-9.598 20.863c3.203.332 17.012-26.163 17.012-26.163l-4.482.629-2.932 4.671z" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
  <path d="M312.31 210.463s-4.05 5.482-3.17 5.656c.877.175 5.353-6.968 5.353-6.968l-1.256.065-.926 1.247z" fill="#452c25"/>
  <path d="M569.231 219.674s-14.575 19.312-11.414 19.928c3.16.615 19.27-24.548 19.27-24.548l-4.521.228-3.335 4.392z" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
  <path d="M313.393 209.5s-4.741 4.868-3.895 5.163c.847.295 6.232-6.16 6.232-6.16l-1.253-.11-1.084 1.106z" fill="#452c25"/>
  <path d="M573.128 216.281s-17.065 17.151-14.019 18.19c3.047 1.039 22.43-21.698 22.43-21.698l-4.51-.39-3.901 3.898z" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
  <path d="M313.563 207.729s-4.138 5.413-3.263 5.602c.875.19 5.466-6.876 5.466-6.876l-1.256.043-.947 1.23z" fill="#452c25"/>
  <path d="M573.737 210.044s-14.893 19.069-11.743 19.736c3.15.667 19.674-24.225 19.674-24.225l-4.523.153-3.408 4.336z" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
  <path d="M312.507 212.38s-4.05 5.483-3.171 5.657c.878.175 5.354-6.968 5.354-6.968l-1.256.065-.927 1.247z" fill="#452c25"/>
  <path d="M569.939 226.431s-14.575 19.312-11.414 19.928c3.16.615 19.27-24.548 19.27-24.548l-4.521.228-3.335 4.392z" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
  <path d="M314.786 211.297s-2.37 4.463-2.1 4.647c.271.185 3.15-2.545 4.573-5.2 1.422-2.656-2.575.442-2.575.442" fill="#452c25"/>
  <path d="M578.14 222.614s-8.532 15.721-7.558 16.371c.976.65 11.337-8.965 16.457-18.32 5.12-9.355-9.265 1.559-9.265 1.559" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
  <path d="M315.04 210.854s-2.341 5.437-1.532 5.976c0 0 2.936-3.28 3.703-5.886.766-2.606 0-.18 0-.18l-.213-2.83-1.916 2.695" fill="#452c25"/>
  <path d="M579.054 221.054s-8.426 19.153-5.515 21.053c0 0 10.569-11.555 13.329-20.736 2.757-9.181 0-.633 0-.633l-.767-9.973-6.894 9.497" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".585"/>
  <path d="M313.824 210.38s-4.742 4.868-3.895 5.163c.846.295 6.232-6.16 6.232-6.16l-1.253-.11-1.084 1.106z" fill="#452c25"/>
  <path d="M574.678 219.38s-17.066 17.151-14.02 18.19c3.046 1.039 22.431-21.698 22.431-21.698l-4.511-.39-3.9 3.898z" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
  <path d="M314.2 211.039s-4.741 4.868-3.895 5.163c.847.295 6.233-6.159 6.233-6.159l-1.253-.11-1.084 1.106z" fill="#452c25"/>
  <path d="M576.034 221.704s-17.066 17.151-14.02 18.19c3.048 1.039 22.431-21.698 22.431-21.698l-4.51-.39-3.901 3.898z" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
  <path d="M314.577 211.808s-4.741 4.869-3.895 5.164c.846.295 6.232-6.16 6.232-6.16l-1.253-.11-1.084 1.106z" fill="#452c25"/>
  <path d="M577.389 224.415s-17.065 17.151-14.019 18.19c3.046 1.039 22.43-21.698 22.43-21.698l-4.51-.39-3.901 3.898z" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
  <path d="M314.932 205.124s-3.523 4.5-3.015 5.275c.508.774 3.726-2.066 4.742-4.057 1.016-1.992-1.694-1.365-1.694-1.365" fill="#452c25"/>
  <path d="M578.665 200.869s-12.678 15.851-10.851 18.58c1.829 2.729 13.41-7.277 17.067-14.292 3.657-7.016-6.095-4.807-6.095-4.807" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
  <path d="M314.818 209.904s-3.019 5.779-2.24 5.447c.78-.332 3.84-4.678 4.179-5.637.339-.959.225-1.96.225-1.96l-2.338 1.437.2 1.12" fill="#452c25"/>
  <path d="M578.257 217.706s-10.867 20.358-8.063 19.189c2.804-1.17 13.82-16.48 15.039-19.858 1.22-3.378.809-6.908.809-6.908l-8.412 5.067.722 3.944" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
  <path d="M314.84 208.003s2.505-4.606-.018.868c-2.523 5.474-3.467 4.57-3.467 4.57-.194-.312 2.194-4.02 2.194-4.02s1.774-2.854 2.199-3.126" fill="#452c25"/>
  <path d="M578.333 211.01s9.018-16.228-.063 3.056c-9.08 19.284-12.477 16.1-12.477 16.1-.7-1.097 7.896-14.163 7.896-14.163s6.383-10.054 7.913-11.009" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".404"/>
  <path d="M317.526 207.391s2.787-4.574-.006.87c-2.794 5.442-3.884 4.522-3.884 4.522-.226-.315 2.44-3.992 2.44-3.992s1.98-2.83 2.46-3.096" fill="#452c25"/>
  <path d="M588.003 208.855s10.029-16.116-.024 3.06c-10.054 19.176-13.976 15.935-13.976 15.935-.815-1.111 8.783-14.066 8.783-14.066s7.122-9.971 8.851-10.905" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".431"/>
  <path d="M584.324 202.053s-12.678 15.851-10.851 18.58c1.829 2.729 13.41-7.277 17.067-14.292 3.657-7.016-6.095-4.807-6.095-4.807" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="#452c25" stroke="#000" stroke-width=".473"/>
  <g>
    <path d="M352.82 251.078a32.82 36.923 0 1 1-65.64 0 32.82 36.923 0 1 1 65.64 0z" fill="#e8a30e"/>
    <path d="M293.7 251.078c0-17.153 12.069-30.318 26.3-30.318s26.3 13.165 26.3 30.318" style="line-height:normal;-inkscape-font-specification:Sans;text-indent:0;text-align:start;text-decoration-line:none;text-transform:none;block-progression:tb;marker:none" color="#000" font-weight="400" font-family="Sans" overflow="visible" fill="none" stroke="#390" stroke-width=".8205"/>
    <path d="M287.224 253.014C288.119 272.506 302.452 288 320 288s31.88-15.495 32.774-34.987h-65.551z" fill="#007934" stroke="#eee" stroke-width=".08205"/>
    <path d="M-237.597 1162.174a577.607 643.151 0 1 1-1155.215 0 577.607 643.151 0 1 1 1155.215 0z" transform="matrix(.05682 0 0 .05741 366.321 184.357)" fill="none" stroke="#000" stroke-width="6.465"/>
    <g stroke="#000" stroke-width="1.112">
      <path d="M200.51 493.622c-4.872 19.055.499 34.742 9.811 49.522 7.2 11.43 11.932 23.61 11.707 36.697a106.659 106.659 0 0 0-9.414 5.98l-75.258-49.986 50.524 74.427a109.144 109.144 0 0 0-2.643 3.845c-14.33-6.455-27.783-7.735-40.602-5.256-18.121 3.506-33.506-.29-49.571-9.864 10.029 16.919 24.991 24.21 42.027 28.076 13.2 2.996 25.132 8.254 34.238 17.703-.991 3.56-1.81 7.162-2.432 10.818l-88.583 17.963 88.384 16.902c.243 1.52.535 3.037.846 4.555-14.702 5.562-25.155 14.148-32.467 24.968-10.335 15.293-23.825 23.491-41.955 28.08 19.055 4.873 34.715-.56 49.496-9.873 11.43-7.201 23.61-11.932 36.697-11.707 1.847 3.275 3.838 6.399 5.98 9.414l-49.96 75.321 74.464-50.614c1.244.9 2.503 1.825 3.782 2.67-6.456 14.33-7.71 27.846-5.23 40.665 3.505 18.122-.316 33.443-9.89 49.508 16.919-10.029 24.21-24.991 28.076-42.028 2.996-13.2 8.254-25.131 17.703-34.237 3.567.994 7.154 1.808 10.817 2.431l17.963 88.584 16.903-88.384c1.52-.243 3.036-.536 4.554-.846 5.56 14.706 14.151 25.156 24.969 32.467 15.292 10.335 23.491 23.825 28.08 41.955 4.873-19.055-.561-34.715-9.874-49.496-7.215-11.452-11.893-23.595-11.643-36.723a106.654 106.654 0 0 0 9.35-5.955l75.322 49.961-50.55-74.49a109.142 109.142 0 0 0 2.606-3.756c14.334 6.474 27.84 7.71 40.665 5.23 18.121-3.506 33.443.316 49.508 9.89-10.03-16.919-24.965-24.146-42.002-28.013-13.185-2.992-25.103-8.351-34.2-17.792.989-3.554 1.747-7.142 2.368-10.791l88.61-17.9-88.41-16.966c-.243-1.52-.536-3.037-.846-4.555 14.715-5.557 25.153-14.146 32.467-24.968 10.335-15.292 23.85-23.428 41.981-28.017-19.055-4.872-34.742.498-49.523 9.81-11.451 7.215-23.595 11.894-36.723 11.644a106.67 106.67 0 0 0-5.954-9.35l49.987-75.258-74.517 50.487a109.173 109.173 0 0 0-3.755-2.607c6.465-14.326 7.735-27.783 5.256-40.602-3.506-18.12.29-33.506 9.864-49.57-16.92 10.028-24.146 24.964-28.013 42-3 13.215-8.349 25.186-17.83 34.29-3.543-.984-7.116-1.838-10.754-2.457l-17.9-88.61-16.965 88.41c-1.52.243-3.037.536-4.555.846-5.562-14.706-14.147-25.153-24.968-32.467-15.293-10.335-23.428-23.851-28.018-41.981z" style="marker:none" overflow="visible" fill="#d52b1e" stroke-width="1.669" transform="matrix(.07375 0 0 .07375 299.718 188.957)"/>
      <g transform="matrix(.14262 0 0 .14262 328.862 239.516)" stroke="none">
        <use transform="rotate(22.5 138.154 65.845)" width="330" height="330" fill="#fcbf49"/>
        <use transform="matrix(-1 0 0 1 35.714 -47.857)" width="330" height="330" fill="#fcbf49"/>
        <g fill="#000">
          <g transform="translate(-30.71 2.139)">
            <path d="M9.571-21.857c-11.5-10-26-11.5-34-4-5.806 7.763-5.289 16.904 1.5 26.5-.5 0-1.5.5-2 1-5.042-9.603-5.91-19.491-2.5-28.5 11-9 26-10 37 5"/>
            <path d="M-8.429-24.857c-6.5 0-8 1.5-11 4s-4.5 2-5 2.5 0 2 1 1.5 3-1.5 6-4 6-2.5 9-2.5c9 0 14 7.5 15 7s-5-8.5-15-8.5"/>
            <path d="M3.071-16.357c-6.5-7-18.5-7.5-23.5.5h2c5-8 17-4.5 18-1v1"/>
            <circle cx="22" cy="-9" r="4.5" transform="translate(-31.429 -7.857)"/>
            <path d="M-20.429-15.857c5 4.5 16 5 23.5-.5l-3.5-.5c-5 5.5-13 5-18 1v-1"/>
            <path d="M3.571-13.857c-8.5 6.5-17 6-22 3s-5-4-4-4 2 1 6 3 10 2 20-2M-20.929 1.143a3 3 0 1 1-4 3c-.5 1-2.5 4-6.5 4h-1l1 1.5c1 0 4 0 6-1.5a4.5 4.5 0 1 0 4.5-7M-14.929 22.143c-4.5-3-6.5-7.5-11.5-7.5-1 0-3 .5-5 1.5h-1l1 1.5c2 0 5-2.5 8.5-.5s5.5 4 8 5M-16.429 22.143c-10-3-12-1-15-1h-1l1 2c4 0 6-3 15-1"/>
            <path d="M-14.929 22.143c-11-1-7.5 5.5-16.5 5.5h-1l1 1.5c11 0 6-6 16.5-7"/>
          </g>
          <g transform="matrix(-1 0 0 1 -93.571 2.139)">
            <path d="M9.571-21.857c-11.5-10-26-11.5-34-4-5.806 7.763-5.289 16.904 1.5 26.5-.5 0-1.5.5-2 1-5.042-9.603-5.91-19.491-2.5-28.5 11-9 26-10 37 5"/>
            <path d="M-8.429-24.857c-6.5 0-8 1.5-11 4s-4.5 2-5 2.5 0 2 1 1.5 3-1.5 6-4 6-2.5 9-2.5c9 0 14 7.5 15 7s-5-8.5-15-8.5"/>
            <path d="M3.071-16.357c-6.5-7-18.5-7.5-23.5.5h2c5-8 17-4.5 18-1v1"/>
            <circle cx="22" cy="-9" r="4.5" transform="translate(-31.429 -7.857)"/>
            <path d="M-20.429-15.857c5 4.5 16 5 23.5-.5l-3.5-.5c-5 5.5-13 5-18 1v-1"/>
            <path d="M3.571-13.857c-8.5 6.5-17 6-22 3s-5-4-4-4 2 1 6 3 10 2 20-2M-20.929 1.143a3 3 0 1 1-4 3c-.5 1-2.5 4-6.5 4h-1l1 1.5c1 0 4 0 6-1.5a4.5 4.5 0 1 0 4.5-7M-14.929 22.143c-4.5-3-6.5-7.5-11.5-7.5-1 0-3 .5-5 1.5h-1l1 1.5c2 0 5-2.5 8.5-.5s5.5 4 8 5M-16.429 22.143c-10-3-12-1-15-1h-1l1 2c4 0 6-3 15-1"/>
            <path d="M-14.929 22.143c-11-1-7.5 5.5-16.5 5.5h-1l1 1.5c11 0 6-6 16.5-7"/>
          </g>
        </g>
      </g>
    </g>
    <g fill="#007934" stroke="#000" stroke-width="1.02">
      <path d="M975.077 1010.892c13.593 1.219 27.742.363 39.851-3.698 11.06-4.1 23.17-6.992 35.28-6.188 0-1.622 3.52-2.88 1.482-4.1-4.51-2.478-10.07-2.023-15.63-4.515-9.084-3.697-14.582-10.326-23.664-14.893-.989-.35-5.005-2.439-5.005-4.514 19.663 25.345 63.528 10.669 101.39 6.59 3.521.817 12.603-2.024 20.14-4.515 9.084-3.282 31.26-1.64 36.821-5.388.927-.35-9.573-6.158-11.611-7.832-5.5-6.183-19.153-4.915-26.197-11.555-11.06-11.148-27.68-14.037-41.396-23.158-3.027-1.622-9.084-.888-13.593-1.75-5.662-4.29-.283-4.317-43.504-36.27-38.614-13.844-36.762-25.355-60.712-33.358-8.525-3.698-16.439-10.55-23.482-8.46-12.603 3.697-39.704 18.12-51.814 25.575-10.07 6.183-22.588 17.116-31.175 24.16-2.256 1.85-28.31 24.556-58.132 41.562-29.82 17.006-73.024 40.383-76.793 40.788 68.439-5.49-60.65 20.646 247.744 31.519z" stroke-linecap="round" transform="matrix(.11822 .00395 -.0041 .12303 212.096 130.2)"/>
      <path d="M804.225 940.415c17.044-9.615 82.265-46.694 102.021-71.935-2.804.272 5.85 7.467 5.271 10.528 6.428-.43 2.89-7.037 8.232-7.466 4.118 0 7.077-1.719 10.616-3.062 4.117-.86 1.734 1.718 1.734 3.062-5.273 10.099-17.044 16.223-26.504 24.119 1.156.86 2.311 1.719 1.734 3.06 3.465.378 7.582.378 9.965-1.718.579-.43.579-1.773.579-2.632 3.538 1.29 2.311 3.922 1.156 5.694-2.312 5.265-12.35 4.78-15.312 9.615-3.467 5.211-7.005 9.616-12.348 13.591 4.115-3.491 8.81-7.896 14.154-8.326 8.233-.86 11.194-6.124 18.272-7.843 7.653-1.719 12.348-7.896 18.271-12.731-2.31 3.492-7.079 6.554-5.273 10.53.579.429 2.48 1.68 4.863.766-5.273 7.037-16.057 12.34-19.596 20.667-3.538-1.29-5.849 1.288-8.232 1.719-3.466 1.288-2.311 6.552-4.694 8.325-9.967 5.694-14.733 14.934-18.848 22.777-.58.86-8.233 5.26-11.195 6.126-5.272 2.2-37.907 28.147-38.483 24.654-3.467-16.648-36.27 14.656-112.745-8.996M985.95 971.962c-.578-.648-1.733-.966-1.733-1.285 1.155-2.61-4.117-2.938-4.117-5.583 8.81 0 15.888 9.467 21.23 3.57 1.158-1.326-2.31-3.25 2.89-4.894 1.156-.282-1.155-1.285-.578-1.967-2.888 0-5.271.32-7.077-.282-2.311-.642-3.539-1.284-6.427-1.966-2.89-.642-4.117-3.572 0-4.575 7.654-1.325 15.889 3.572 21.81.642 4.694-2.288 9.388-2.93 14.082-4.575 2.312-.643 10.04-.281 7.654 1.926-1.732 1.324-5.85.964-8.232 1.606-4.116 1.284-7.654 3.25-11.772 4.896 2.89-.32 2.312 2.93 5.85 1.966 5.273-1.605 10.616-3.571 16.465-4.254-.577-1.284 1.158-2.288 1.158-3.612h1.732c-2.89-3.893 5.272-2.247 8.233-5.54 0 .643.506.643 1.155.965-4.116 1.604-2.311 4.213-3.466 6.22-.578.643-2.311.964-1.733 1.967 2.311 1.606 2.311-1.284 4.116-.642 1.733.321 4.117.963 5.272.642 2.888 0 5.85-1.284 4.044-3.612-2.312-1.926-4.695-3.573-4.695-6.181 0-.321-.576-.642-1.732-.963 4.694 0 8.233 1.324 9.965 3.251 2.311 1.967 2.89 4.897 6.5 5.579 7.005.963 6.426-2.288 6.426-5.579 5.273.32 12.93 2.287 11.773 4.897-.506 1.926-4.693 3.571-8.81 3.933-2.888.28-1.156 2.287-2.888 2.609-3.467 0-7.656.642-8.812 1.966-1.155 1.967.577 5.864 2.888 7.18 3.467 1.612 10.545-.638 15.888 0 .507-2.929 4.118-3.885 7.077-5.895 3.467-1.927-1.732-3.573-4.693-4.897-2.311-.643-.577-1.285.578-2.93 1.733-1.926 7.077-.321 7.654-1.926 1.156-1.966-1.155-4.897 1.156-6.222 1.734-1.284 4.117.963 3.466 2.61 2.31-.281 2.89-1.607 5.272-1.927 2.888-.32 4.695 1.926 3.467 2.609-3.467 2.288-6.428 3.893-5.272 7.184 0 1.645-4.116 2.288-2.311 3.612 3.466 2.287 3.466 4.576 4.694 7.186 1.155 2.928 7.655 3.575 13.505 2.29-2.311-5.548 11.194-3.256 17.116-4.9.505 0 .505-.966 0-1.284-2.89-1.605-3.467-2.249-2.89-4.576 0-.281-1.083-.964-1.732-1.284 8.811 2.608 24.405 6.071 30.905 10.97-8.233 1.603-23.828-1.494-32.06.469-9.968 2.61-20.004 5.856-30.694 5.856-4.694 0-9.388-1.921-14.082-1.921m-94.966-2.61c1.156.278-2.887-.319-3.466-.647" transform="matrix(.11822 .00395 -.0041 .12303 212.096 130.2)"/>
      <path d="M957.988 1017.332c16.347-1.417 19.336 10.721 29.697-.303 9.869 1.416 18.847-2.031 18.935-3.392 23.022 3.865 93.807-4.732 88.948-9.403-7.598-7.808-19.604-10.455-29.227-17.056-2.14-1.2-6.543-1.741-9.798-2.51-5.433-1.405-12.543.02-17.203-2.137-7.5-3.734-15.442-6.93-22.149-11.714-4.216-2.697-5.782-7.163-10.413-10.451-4.949-3.276-11.939-3.71-16.974-5.658-7.571-2.638-11.927-9.445-19.04-14.254-2.467-1.758-6.56-1.475-9.403-3.018-6.065-2.81-10.863-9.118-16.093-8.069-9.356 1.851-14.648 9.418-23.804 13.754-7.613 3.595-10.969 9.922-17.537 14.149-1.725 1.11-21.709 14.806-44.199 24.597-22.49 9.791-52.018 21.717-54.786 21.812 0 0 20.639 13.598 67.46 10.183l28.978 7.958 15.934-3.224s39.907-1.312 40.674-1.264z" transform="matrix(.11822 .00395 -.0041 .12303 212.096 130.2)"/>
      <path d="M860.345 985.454c10.364-5.193 50.022-25.216 61.996-38.77-1.71.154 3.586 3.972 3.241 5.61 3.918-.249 1.743-3.77 4.999-4.016 2.51-.013 4.309-.94 6.463-1.67 2.507-.473 1.061.913 1.065 1.631-3.187 5.415-10.345 8.725-16.09 12.976.706.456 1.413.911 1.064 1.63 2.114.191 4.623.178 6.07-.95.352-.23.348-.949.346-1.408 2.16.678 1.42 2.089.72 3.04-1.394 2.821-7.515 2.594-9.308 5.188-2.099 2.795-4.244 5.16-7.49 7.303 2.5-1.88 5.349-4.248 8.606-4.495 5.016-.485 6.807-3.309 11.117-4.25 4.66-.942 7.505-4.259 11.103-6.862-1.4 1.874-4.297 3.526-3.186 5.645.354.228 1.77 1.398 3.22.902-3.196 3.778-10.008 6.14-12.142 10.6-2.16-.678-3.562.708-5.014.945-2.11.7-1.39 3.51-2.838 4.465-6.06 3.075-8.94 8.03-11.428 12.234-.35.461-5.004 2.837-6.807 3.31-3.209 1.192-23.031 15.163-23.393 13.298-2.158-8.887-21.4 4.952-68.084-7.452" transform="matrix(.11822 .00395 -.0041 .12303 212.096 130.2)"/>
    </g>
    <path d="M320 214.155c-18.126 0-32.82 16.53-32.82 36.923C287.18 271.47 301.873 288 320 288c18.126 0 32.82-16.531 32.82-36.923 0-20.392-14.694-36.923-32.82-36.923zm0 8.246c13.214 0 24.659 12.288 24.659 28.677S333.214 279.752 320 279.752c-13.214 0-24.659-12.285-24.659-28.674 0-16.39 11.445-28.677 24.659-28.677z" style="line-height:normal;-inkscape-font-specification:Sans;text-indent:0;text-align:start;text-decoration-line:none;text-transform:none;block-progression:tb;marker:none" color="#000" font-weight="400" font-family="Sans" overflow="visible" fill="#00a6de" stroke="#000" stroke-width=".12307499999999999"/>
    <g fill="#e8a30e" fill-rule="nonzero" stroke="#000" stroke-width="1.274">
      <path d="M133.866 24.413c.106 6.945-1.984 7.193-12.543 8.965-10.347 1.736-12.33 1.772-12.473-4.819-.106-6.945 2.197-7.228 12.544-8.965 9.78-1.63 12.33-2.48 12.472 4.82z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M136.098 24.024c.142 4.358-6.626 5.42-14.74 6.767-8.15 1.347-14.563 2.268-14.704-1.842-.178-4.358 6.59-5.421 14.74-6.768 8.114-1.346 14.527-2.693 14.704 1.843zM56.906 333.886c22.251-73.417 28.098-128.87-3.756-207.567 52.866-62.22 91.665-40.642 126.708 3.543-33.342 77.705-24.626 133.087-5.563 204.555-41.456 27.036-77.563 26.256-117.39-.531z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M115.335 87.626c-1.099.035-2.197.07-3.296.177 3.685 89.256.567 177.803-.07 266.421 1.452.071 2.94.107 4.393.107-.248-89.185 3.933-175.536-.815-266.705h-.212z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M125.362 88.547c-9.354 81.248-12.827 163.347-10.559 265.784 1.276 0 2.587 0 3.862-.036-.531-101.09 2.339-183.26 10.347-264.862-1.205-.319-2.445-.638-3.65-.886zM143.291 3.579c-9.39 12.756-14.563 12.65-9.85 32.421 10.382-8.717 10.098-15.945 9.85-32.421z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M122.457 12.118c6.13 7.973 15.094 12.118 9.39 33.626-11.197-11.94-9.745-13.146-9.39-33.626z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M120.543 34.087c8.114 7.263 15.555 8.787 9.992 27.531-7.122-10.488-10.204-9.602-9.992-27.531z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M118.311 45.78c6.165 7.83 15.13 11.905 9.46 32.952-11.196-11.693-9.743-12.862-9.46-32.952z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M114.732 62.823c7.193 7.689 17.61 11.799 11.233 32.102-12.437-12.933-6.308-15.945-11.233-32.102z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M113.35 80.043c6.733 7.441 17.363 11.8 11.339 31.43-9.602-10.843-10.843-12.544-11.339-31.43zM149.492 21.543c-4.89 5.032-16.157 4.536-16.618 26.008 13.89-9.85 13.748-7.972 16.618-26.008z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M148.748 36.815c-5.209 4.642-17.256 4.075-17.646 24.2 14.811-9.07 14.67-7.298 17.646-24.2z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M146.976 50.634c-5.385 5.031-17.858 4.429-18.283 26.15 15.342-9.815 15.165-7.938 18.283-26.15z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M146.055 69.626c-5.67 2.587-18.921-2.551-19.027 24.378 11.338-3.72 10.559-11.906 19.027-24.378z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M139.748 86.031c-7.902 4.075-12.366 3.26-13.819 24.91 11.445-9.532 11.303-7.76 13.819-24.91zM148.43 98.787c-19.985 70.619-24.202 137.48-22.217 254.87 1.63-.212 3.224-.46 4.854-.744-.992-116.397 2.622-180.815 20.976-251.539a76.177 76.177 0 0 0-3.614-2.587zM181.028 20.976c-11.41 9.957-16.441 8.504-15.237 28.985 11.693-5.74 12.65-12.863 15.237-28.985z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M159.094 23.846c4.678 9.39 12.756 15.733 3.473 35.185-8.93-14.527-7.3-15.342-3.473-35.185z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M153.46 44.717c6.733 9.177 13.749 12.614 5.068 29.444-5.174-12.118-8.363-12.047-5.067-29.444z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M149.28 55.524c4.677 9.248 12.79 15.555 3.578 34.547-8.964-14.315-7.334-15.095-3.578-34.547z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M142.795 71.15c5.74 9.39 15.272 16.122 5.528 34.193-9.992-15.84-3.437-17.15-5.528-34.193z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M138.472 87.555c5.386 9 15.024 16.051 5.74 33.59-7.547-13.11-8.468-15.094-5.74-33.59zM184.04 40.11c-5.67 3.614-16.654.177-20.8 20.941 15.307-5.917 14.847-4.145 20.8-20.94z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M180.673 54.78c-5.917 3.153-17.645-.603-21.472 18.92 16.086-4.924 15.626-3.294 21.472-18.92z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M176.563 67.748c-6.165 3.472-18.283-.354-22.465 20.658 16.76-5.528 16.264-3.756 22.465-20.658z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M172.382 85.996c-6.024 1.028-18.106-7.44-22.854 18.744 11.763-.673 12.401-8.823 22.854-18.744z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M163.346 100.311c-8.433 1.878-12.685-.07-17.822 20.587 12.897-6.237 12.437-4.571 17.822-20.587zM167.173 115.157c-25.972 64.205-31.5 122.989-18.567 232.69 1.878-.71 3.792-1.524 5.705-2.375-12.862-104.74-7.795-163.381 16.051-226.878a101.355 101.355 0 0 0-3.189-3.437zM210.508 38.339c-12.402 8.007-17.256 5.775-18.213 26.22 12.225-3.827 13.96-10.7 18.213-26.22z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M188.398 37.63c3.65 10.027 11.02 17.646-.248 35.362-7.37-15.803-5.67-16.335.248-35.362z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M180.602 57.402c5.705 10.169 12.331 14.669 1.95 29.905-3.863-12.791-7.052-13.216-1.95-29.905z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M175.287 67.43c3.72 9.885 11.09 17.432-.035 34.724-7.406-15.591-5.705-16.087.035-34.725z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M167.244 81.85c4.713 10.205 13.465 18.355 1.878 34.69-8.256-17.257-1.594-17.54-1.878-34.69z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M161.22 97.37c4.394 9.78 13.252 18.284 2.162 34.122-6.095-14.138-6.803-16.264-2.162-34.122zM211.465 57.756c-5.989 2.622-16.548-2.516-22.855 17.362 15.839-3.401 15.201-1.736 22.855-17.362z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M206.61 71.717c-6.2 2.161-17.504-3.402-23.35 15.236 16.512-2.268 15.91-.709 23.35-15.236z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M201.154 83.87c-6.485 2.445-18.142-3.295-24.485 16.83 17.22-2.798 16.548-1.098 24.485-16.83z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M195.094 101.232c-6.094.071-17.22-10.24-24.696 14.882 11.763 1.24 13.252-6.732 24.696-14.882z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M184.606 113.953c-8.61.531-12.614-2.055-19.878 17.54 13.465-4.147 12.827-2.517 19.878-17.54zM101.374 89.858c-1.17.355-2.374.78-3.579 1.205 8.575 81.283 14.067 163.346 13.004 263.09 1.099.071 2.197.142 3.295.142 2.835-101.763-2.94-183.33-12.72-264.437zM83.339 1.913c9.318 13.252 14.492 13.43 9.673 32.989-10.347-9.32-10.028-16.548-9.673-32.989z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M104.138 11.587c-6.201 7.653-15.201 11.303-9.603 33.13 11.233-11.304 9.815-12.615 9.603-33.13z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M105.91 33.661c-8.186 6.804-15.627 7.937-10.17 27 7.193-10.133 10.24-9.035 10.17-27z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M108.07 45.496c-6.2 7.512-15.2 11.055-9.672 32.421 11.303-11.055 9.85-12.33 9.673-32.42z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M111.543 62.717c-7.228 7.334-17.68 10.842-11.41 31.5 12.509-12.225 6.379-15.591 11.41-31.5z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M112.819 80.043c-6.803 7.051-17.433 10.807-11.516 30.792 9.638-10.347 10.878-11.977 11.516-30.792zM77.031 19.524c4.855 5.28 16.123 5.456 16.477 26.929-13.854-10.63-13.713-8.752-16.477-26.93z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M77.67 34.83c5.173 4.926 17.255 5.032 17.503 25.158-14.775-9.85-14.598-8.114-17.504-25.157z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M79.37 48.72c5.35 5.35 17.823 5.457 18.142 27.213-15.272-10.7-15.13-8.823-18.142-27.213z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M80.185 67.783c5.67 2.906 18.921-1.488 18.886 25.441-11.339-4.358-10.488-12.507-18.886-25.44z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M86.386 84.543c7.901 4.5 12.33 3.969 13.677 25.654-11.41-10.134-11.268-8.362-13.677-25.654zM79.37 101.197c-1.17.85-2.339 1.772-3.543 2.728 18.496 70.122 25.299 134.965 23.67 248.776a90.42 90.42 0 0 0 4.286.744c2.623-115.122-4.464-182.02-24.413-252.248zM45.496 17.22c11.374 10.56 16.406 9.39 15.095 29.835C48.933 40.606 47.976 33.45 45.496 17.22z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M67.43 21.26c-4.713 9.142-12.863 15.059-3.686 35.008C72.78 42.236 71.15 41.35 67.43 21.26z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M72.921 42.449c-6.767 8.823-13.819 11.87-5.244 29.161 5.244-11.799 8.433-11.55 5.244-29.161z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M77.067 53.504c-4.748 9-12.898 14.846-3.827 34.335 9.071-13.82 7.441-14.67 3.827-34.335z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M83.41 69.484c-5.776 9.071-15.343 15.272-5.705 33.874 10.063-15.271 3.543-16.937 5.704-33.874z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M87.626 86.138c-5.386 8.716-15.095 15.2-5.917 33.236 7.618-12.65 8.539-14.563 5.917-33.236zM42.413 36.177c5.634 3.898 16.618 1.099 20.658 22.075-15.307-6.768-14.847-4.996-20.658-22.075z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M45.673 51.024c5.882 3.472 17.646.39 21.33 20.09-16.05-5.81-15.625-4.145-21.33-20.09z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M49.677 64.205c6.13 3.826 18.32.673 22.323 21.933-16.689-6.484-16.228-4.677-22.323-21.933z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M53.752 82.665c6.024 1.382 18.177-6.413 22.748 20.02-11.764-1.311-12.33-9.496-22.748-20.02z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M62.681 97.476c8.433 2.374 12.685.674 17.717 21.615-12.863-6.98-12.402-5.245-17.717-21.615zM60.91 117.673c-.957.992-1.914 2.02-2.87 3.083 23.491 65.09 27.318 120.862 19.665 224.575l1.063.744c1.24.531 2.445 1.063 3.685 1.559 8.716-105.2 4.429-162.106-21.544-229.96zM15.945 32.917c12.33 8.681 17.22 6.697 18.035 27.248C21.791 55.63 20.09 48.65 15.945 32.917z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M38.055 33.449c-3.72 9.815-11.126 17.008.036 35.362 7.44-15.378 5.74-16.016-.036-35.362z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M45.709 53.61c-5.776 9.85-12.437 14.032-2.126 29.835 3.968-12.579 7.122-12.827 2.126-29.835z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M50.953 63.957c-3.756 9.673-11.197 16.795-.177 34.724 7.547-15.165 5.846-15.768.177-34.724z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M58.925 78.839c-4.783 9.92-13.57 17.61-2.09 34.582 8.362-16.795 1.7-17.433 2.09-34.582z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M64.843 94.677c-4.43 9.532-13.359 17.54-2.374 34.016 6.2-13.819 6.909-15.874 2.374-34.016zM14.846 52.264c5.989 2.976 16.583-1.56 22.748 18.638-15.838-4.288-15.165-2.587-22.748-18.638z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M19.63 66.472c6.2 2.516 17.504-2.409 23.244 16.583C26.362 79.831 27 81.425 19.63 66.472z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M25.016 78.945c6.449 2.835 18.141-2.268 24.378 18.212-17.22-3.755-16.548-2.02-24.378-18.212z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M30.969 96.661c6.094.39 17.29-9.283 24.59 16.264-11.764.567-13.216-7.476-24.59-16.264z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M41.35 109.949c8.61 1.027 12.615-1.347 19.807 18.638-13.464-4.855-12.862-3.225-19.807-18.638zM102.295.531c8.717 13.89 13.855 14.422 8.15 33.556-9.886-9.993-9.248-17.185-8.15-33.556z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M122.634 11.657c-6.52 7.193-15.697 10.17-11.09 32.315 11.763-10.488 10.381-11.87 11.09-32.315z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M123.413 33.768c-8.468 6.236-15.98 6.838-11.374 26.22 7.618-9.602 10.63-8.291 11.374-26.22z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M125.008 45.709c-6.52 7.05-15.662 9.992-11.09 31.677 11.763-10.24 10.381-11.622 11.09-31.677z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M127.736 63.142c-7.582 6.803-18.177 9.567-12.862 30.614 13.075-11.339 7.087-15.13 12.862-30.614z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M128.232 80.504c-7.122 6.555-17.929 9.602-12.933 29.905 10.099-9.637 11.445-11.161 12.933-29.905zM95.173 17.681c4.642 5.599 15.874 6.52 15.272 27.992C97.087 34.122 97.157 36 95.173 17.681z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M95.138 32.988c4.96 5.28 17.008 6.201 16.37 26.292-14.315-10.878-14.244-9.107-16.37-26.292z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M96.2 46.949c5.103 5.705 17.576 6.661 16.902 28.382-14.775-11.729-14.74-9.815-16.901-28.382z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M96.13 66.012c5.563 3.295 18.992-.177 17.752 26.68-11.126-5.137-9.921-13.18-17.752-26.68z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M101.587 83.161c7.689 5.067 12.153 4.82 12.507 26.54-10.948-10.914-10.877-9.142-12.507-26.54zM91.098 94.004c-1.31.638-2.586 1.346-3.897 2.09 16.37 82.524 20.197 158.705 18.78 257.67.46.035.92.07 1.381.142l1.878-.426c2.374-88.299 0-174.578-18.142-259.476zM63.142 11.587C73.949 23.03 79.052 22.252 76.677 42.52c-11.338-7.335-11.87-14.528-13.535-30.933z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M84.827 17.362c-5.173 8.752-13.607 13.996-5.492 34.618 9.744-13.287 8.185-14.315 5.492-34.618z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M89.256 38.94c-7.228 8.257-14.457 10.737-6.768 28.63 5.847-11.373 9-10.842 6.768-28.63z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M92.8 50.28c-5.21 8.574-13.643 13.748-5.6 33.909 9.78-13.04 8.186-14.032 5.6-33.91z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M98.327 66.72c-6.272 8.575-16.122 13.997-7.477 33.308 10.878-14.422 4.43-16.654 7.477-33.308z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M101.693 83.622c-5.882 8.256-15.91 13.96-7.654 32.67 8.256-12.012 9.284-13.855 7.654-32.67zM59.067 30.26c5.421 4.323 16.547 2.41 19.488 23.634-14.917-7.973-14.563-6.13-19.488-23.634z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M61.547 45.283c5.705 3.934 17.61 1.808 20.303 21.756-15.767-7.086-15.413-5.385-20.303-21.756z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M64.913 58.748c5.918 4.323 18.213 2.126 21.154 23.634-16.37-7.795-15.98-5.953-21.154-23.634z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M67.996 77.492c5.953 1.843 18.496-4.96 21.72 21.756-11.692-2.232-11.87-10.453-21.72-21.756z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M76.146 92.976c8.326 3.012 12.65 1.666 16.582 22.926-12.472-7.973-12.118-6.237-16.582-22.926zM54.638 124.583c-.496.567-.992 1.134-1.488 1.736 19.381 47.94 24.838 87.236 21.082 127.24v.071c-.708 26.185-3.933 54.354-9.354 85.287 1.382.815 2.764 1.56 4.11 2.304l.78.283c15.271-88.547 13.819-153.957-15.13-216.921z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M71.114 107.858a153.221 153.221 0 0 0-3.65 3.296c22.182 73.133 25.229 140.563 22.04 238.89l2.94.991 1.276.32c4.217-100.064 1.489-170.009-22.606-243.497zM31.004 24.98c12.047 9.355 16.972 7.654 17.079 28.17-12.048-5.174-13.465-12.225-17.08-28.17z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M53.079 26.717c-4.04 9.602-11.729 16.405-1.205 35.326 8.008-14.952 6.307-15.661 1.205-35.326z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M60.06 47.303c-6.13 9.496-12.934 13.288-3.225 29.622 4.429-12.33 7.582-12.401 3.224-29.622z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M64.913 57.862c-4.11 9.46-11.799 16.193-1.417 34.69 8.079-14.741 6.413-15.45 1.417-34.69z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M72.354 73.17c-5.137 9.637-14.208 16.83-3.33 34.405 8.964-16.3 2.338-17.327 3.33-34.406z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M77.705 89.327c-4.784 9.248-13.96 16.76-3.579 33.803 6.697-13.465 7.476-15.484 3.579-33.803zM29.232 44.256c5.882 3.295 16.618-.673 22.075 19.842-15.661-5.173-15.094-3.401-22.075-19.842z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M33.52 58.713c6.094 2.834 17.574-1.489 22.641 17.787-16.405-4.11-15.838-2.48-22.641-17.787z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M38.445 71.433c6.342 3.189 18.248-1.276 23.74 19.524-17.079-4.713-16.476-2.941-23.74-19.524z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M43.76 89.469c6.094.708 17.61-8.363 24.023 17.539-11.799-.071-12.933-8.185-24.023-17.54z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M53.681 103.287c8.575 1.489 12.65-.673 19.134 19.666-13.287-5.599-12.72-3.933-19.134-19.666zM.496 46.7c13.146 6.662 17.717 3.934 20.693 24.202C8.646 68.35 6.272 61.689.496 46.7z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M22.43 43.689c-2.658 10.382-9.249 18.709 3.72 35.185 5.775-16.512 4.004-16.866-3.72-35.185z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M32.138 62.54c-4.713 10.7-10.843 15.909.992 29.976 2.587-13.146 5.705-13.89-.992-29.977z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M38.374 71.965c-2.693 10.24-9.283 18.496 3.472 34.547 5.847-16.3 4.11-16.618-3.472-34.547z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M47.835 85.465c-3.685 10.665-11.587 19.7 1.523 34.724 6.52-18.035-.106-17.61-1.523-34.724z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M55.346 100.311c-3.401 10.17-11.374 19.56 1.205 34.158 4.677-14.705 5.173-16.867-1.205-34.158zM1.453 66.083C7.689 68.103 17.68 61.902 25.902 81 9.78 79.264 10.63 80.858 1.452 66.083z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M7.689 79.476C14.067 81 24.732 74.268 32.386 92.232c-16.654-.567-15.874.957-24.697-12.756z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M14.28 91.028c6.696 1.771 17.751-5.174 26.043 14.208-17.433-.992-16.583.603-26.043-14.208z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M22.04 107.681c6.058-.602 16.121-11.976 26.043 12.225-11.587 2.444-13.855-5.315-26.044-12.225z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M33.697 119.232c8.61-.354 12.33-3.366 21.543 15.378-13.819-2.693-13.04-1.17-21.543-15.378zM139.606 93.543c-18.85 84.508-21.508 171.886-19.665 260.681a60.196 60.196 0 0 0 4.358-.354c-.78-99.921 2.41-175.925 19.24-258.2-1.31-.745-2.622-1.489-3.933-2.127zM167.563 14.492c-10.878 10.843-15.945 9.78-13.713 30.19 11.374-6.698 11.977-13.89 13.713-30.19z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M145.843 19.063c5.137 9.035 13.535 14.74 5.28 34.902-9.674-13.82-8.08-14.74-5.28-34.902z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M141.307 40.394c7.193 8.645 14.386 11.515 6.59 28.984-5.81-11.658-8.964-11.339-6.59-28.984z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M137.693 51.52c5.138 8.858 13.57 14.527 5.386 34.228-9.709-13.606-8.114-14.492-5.386-34.228z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M132.06 67.642c6.2 8.929 16.05 14.882 7.263 33.732C128.55 86.35 135 84.508 132.059 67.642z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M128.622 84.366c5.776 8.61 15.803 14.847 7.441 33.095-8.185-12.473-9.213-14.386-7.44-33.095zM171.531 33.378c-5.456 4.04-16.547 1.488-19.63 22.57 14.989-7.157 14.599-5.35 19.63-22.57z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M168.945 48.295c-5.705 3.614-17.61.815-20.41 20.587 15.804-6.2 15.414-4.5 20.41-20.587z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M165.543 61.583c-5.952 3.933-18.248 1.063-21.33 22.429 16.405-6.874 16.015-5.067 21.33-22.43z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M162.319 80.114c-5.953 1.524-18.46-5.988-21.827 20.551 11.693-1.594 11.906-9.78 21.827-20.55z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M154.063 95.138c-8.327 2.586-12.65.956-16.724 22.004 12.543-7.264 12.189-5.563 16.724-22.004zM159.343 107.469c-25.973 72.46-29.445 142.511-23.386 244.381a119.926 119.926 0 0 0 5.846-1.63c-6.697-103.039-3.366-168.98 20.728-239.775a122.426 122.426 0 0 0-3.188-2.976zM199.63 29.693c-12.118 8.681-17.008 6.697-17.256 27.213 12.083-4.5 13.535-11.48 17.256-27.213z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M177.52 30.189c4.004 9.85 11.657 17.043 1.027 35.398-7.901-15.414-6.236-16.052-1.027-35.398z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M170.469 50.386c6.059 9.85 12.826 13.996 3.011 29.799-4.323-12.579-7.511-12.827-3.011-29.8z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M165.508 60.697c4.04 9.673 11.693 16.795 1.205 34.724-7.973-15.2-6.272-15.767-1.205-34.724z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M157.996 75.543c5.067 9.957 14.102 17.646 3.118 34.583-8.858-16.795-2.232-17.433-3.118-34.583z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M152.54 91.417c4.712 9.532 13.854 17.54 3.366 33.98-6.591-13.818-7.37-15.873-3.367-33.98zM201.26 49.04c-5.882 2.976-16.583-1.595-22.181 18.602 15.697-4.252 15.094-2.551 22.18-18.603z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M196.902 63.283c-6.095 2.48-17.54-2.48-22.748 16.512 16.44-3.189 15.838-1.594 22.748-16.512z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M191.906 75.72c-6.378 2.8-18.213-2.267-23.847 18.178 17.114-3.72 16.476-2.02 23.847-18.178z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M186.484 93.437c-6.094.39-17.575-9.319-24.13 16.228 11.8.567 13.004-7.476 24.13-16.228z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M176.492 106.724c-8.575.993-12.685-1.381-19.24 18.603 13.287-4.855 12.72-3.225 19.24-18.603zM176.917 126.177c-30.33 65.622-27.744 135.425-15.236 215.681 1.56-.815 3.083-1.665 4.677-2.55-5.35-34.69-8.787-66.509-8.68-96.662v-.177c-1.56-35.044 4.145-70.548 22.216-112.607-.992-1.24-1.985-2.48-2.977-3.685zM229.996 53.114c-13.181 5.917-17.752 2.941-20.835 23.032 12.544-1.843 14.989-8.398 20.835-23.032z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M208.098 48.898c2.587 10.488 9.107 19.169-3.968 34.937-5.634-16.831-3.898-17.08 3.968-34.937z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M198.283 67.181c4.607 10.949 10.737 16.512-1.204 29.906-2.516-13.288-5.599-14.209 1.204-29.906z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M191.94 76.252c2.658 10.382 9.214 18.992-3.649 34.37-5.775-16.618-4.004-16.866 3.65-34.37z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M182.445 89.22c3.614 10.843 11.445 20.34-1.772 34.619-6.378-18.355.248-17.575 1.772-34.619z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M174.827 103.642c3.33 10.346 11.267 20.197-1.418 34.086-4.606-14.952-5.066-17.15 1.418-34.086zM228.933 72.46c-6.236 1.63-16.193-5.102-24.555 13.536 16.122-.85 15.307.709 24.555-13.535z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M222.626 85.465c-6.378 1.169-17.008-6.13-24.803 11.409 16.689.354 15.874 1.807 24.803-11.41z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M215.93 96.661c-6.698 1.382-17.682-6.165-26.115 12.756 17.433-.035 16.583 1.524 26.114-12.756z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M208.098 112.854c-6.059-.92-16.05-12.862-26.114 10.807 11.551 3.083 13.89-4.57 26.114-10.807z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M196.335 123.768c-8.61-.85-12.296-4.075-21.615 14.173 13.855-1.949 13.075-.46 21.615-14.173zM98.787 218.161l31.855.886c19.275.532 34.76 5.953 34.724 12.19 0 6.2-15.52 10.77-34.795 10.24l-31.854-.886c-19.276-.532-34.76-5.953-34.725-12.19.036-6.2 15.52-10.807 34.795-10.24z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
      <path d="M76.713 217.524l-9.532 19.771c1.488 1.56 3.366 2.516 5.457 2.587l5.74.142 10.7-22.146-12.365-.354zm24.803.708L90.85 240.378l12.331.354 10.7-22.18-12.365-.32zm24.838.674l-10.7 22.145 12.33.355 10.701-22.146-12.33-.354zm24.803.708l-10.7 22.146 12.366.354 9.39-19.488c-1.56-1.736-3.544-2.8-5.74-2.87l-5.316-.142z" transform="matrix(.03575 0 0 .02899 319.94 266.083)"/>
    </g>
    <g fill="#e7e7e7" stroke="#000" stroke-width="3.085">
      <path d="M-123.176 1511.601c.231-.046 2.996 43.372 6.382 64.529.414 2.59 3.55 4.092 4.255 6.618.761 2.733-.197 5.672-.237 8.51-.004.314.045.633 0 .945-.557 3.9-4 7.467-3.309 11.346.395 2.218 3.14 3.36 4.018 5.436 5.104 12.063 4.037 26.47 6.382 38.764.989 5.182-.949-.997 8.273 13.71.927 1.477 4.964 1.654 4.964 1.654l4.018.237 3.756 3.879-5.683 5.181-17.55 1.672-7.855-5.014s2.478-4.244 1.003-6.352c-1.057-1.511-4.835-.504-5.014-2.34-.327-3.362-.701-12.529-.836-12.535-.3-.014-.155-.595-.334-.836 0 0-12.781-14.673-17.215-23.232-2.293-4.427-2.145-9.913-4.68-14.206-1.623-2.75-5.226-4.1-6.518-7.02-10.75-24.288-21.523-71.13-21.56-70.866M-333.806 1500.42l45.303 11.326-18.01 62.892c-9.687 19.858-8.976 20.585-6.876 26.27 4.412 10.927 7.094 27.28 23.611 53.256 5.98 6.612 12.865 7.405 15.186 10.09.973 2.836 6.142 7.115 10.656 8.882h14.52c2.683-3.219 2.629-2.147 3.455-4.592-.809-2.968-7.805-1.947-12.33-6.022-1.93-7.709-11.724-19.563-12.558-27.673-4.453-17.719-2.745-18.236.12-36.206 1.428-9.257 11.112-29.67 14.981-40.783 7.801-11.86 16.285-32.525 16.473-47.46l-16.383-41.692-10.193-16.989M-307.151 1424.29c-75.622 20.367-49.746 84.639-28.886 85.866M-80.846 1319.762l12.895-22.825 1.448-8.413-.892-1.788-21.49 21.787-.374 1.447" transform="matrix(.03854 -.00105 .00336 .0412 316.202 206.397)"/>
      <path d="M-41.126 1358.04c4.574 1.771 10.397 3.537 11.667.943 1.574-3.213 2.362-5.487-1.03-8.094-1.631-1.467 1.226-3.657-.47-7.813-12.192-8.803-21.059-9.234-23.656-16.062l-.093-4.688c-.142-5.925-18.515-8.837-22.782-11.843 0 0-7.515-3.838-11.593-4.782-1.743-.403-5.375-.343-5.375-.343-18.855-.648-29.123 17.69-33.25 28.906 0 0-8.195 51.377-19.407 74.437-1.021 2.101-2.519 3.994-4.25 5.563-1.683 1.525-3.55 3.798-5.812 3.594-51.977-4.684-87.39-7.783-108.688-7.594-21.297.19-46.89 14.984-48.531 16.625 0 0-24.886 13.887-32.125 37.406-2.417 7.854-1.156 17.225 1.344 26.594 18.063 63.042 46.705.951 55.06 1.904l10.283 1.174c12.073 1.377 31.42 36.918 64.993 44.775 101.333 23.715 134.014-26.041 141.588-138.264l1.393-1.585v-3.827l6.028-8.801.095-12.438-1.339-5.931.383-2.296 29.37-2.775 5.19-2.033" transform="matrix(.03854 -.00105 .00336 .0412 316.202 206.397)"/>
      <path d="M-88.424 1324.949l14.756-26.12 1.658-9.626-1.022-2.046-24.592 24.93-.427 1.657M-345.177 1490.889c4.986 18.685 15.282 37.389 13.843 44.187-2.159 10.208-8.708 14.768-9.25 15.72-.497.872-2.898 2.128-3.687 2.75-3.416 2.688-5.997 8.222-7.781 12.187-2.2 4.889-3.573 9.281-3.407 14.125.619 17.994 8.412 55.142 8.938 55.687 0 0 5.945 3.612 7.437 7.063 1.52 3.513.627 6.838.063 10.625-.773 5.194-4.906 16.718-4.906 16.718s.042 2.254.687 2.938c1.806 1.914 8.188 5.312 8.188 5.312h14s-7.15-7.549-7.594-9.218c-.492-1.852-.688-3.273.031-5.344 1.328-3.825 5.668-8.296 7.157-10.563 1.603-2.44.656-8.937.656-8.937.064-2.364-1.876-3.5-1.938-4.5-.481-7.786-.507-13.398-.187-21.125.282-6.818 3.281-27.187 3.281-27.188 0 0 3.543-2.547 5-4.156 1.322-1.46 3.313-4.906 3.313-4.906s17.685-8.665 25.468-14.594c9.23-7.03 24.719-24.5 24.719-24.5s4.635-7.126 5.594-11.187c.325-1.378.207-2.85 0-4.25-1.123-7.602-3.522-15.092-6.688-22.094-3.075-6.8-6.77-13.497-11.812-19-.52-.567-1.844-1.375-1.844-1.375M-126.06 1534.245c-14.59 8.237-47.984 14.46-49.048 20.65 0 0-4.35 38.834-10.437 54.221-2.4 6.064-5.652 7.77-8.584 13.596-3.052 6.066-3.305 10.753-6.017 16.799-2.902 6.465-7.602 12.213-9.197 23.502l-.473 9.4 9.567 5.837-10.715 3.061-15.785-6.505-.096-14.351 4.88-14.733.382-2.392-3.253-9.089.191-32.336 4.88-24.013 4.113-88.686M-83.288 1362.833c6.113 5.096 14.467 8.511 34.706 3.013 3.568 2.358 7.336 5.279 16.683-5.364M-39.674 1347.53c.13-.13 3.596 2.47 4.956 2.503 1.06.025 2.559-1.967 1.752-2.653-.886-.753-2.568-1.619-3.454-1.102-1.286.751-3.226 1.473-3.254 1.252z" transform="matrix(.03854 -.00105 .00336 .0412 316.202 206.397)"/>
      <path d="M317.778 261.411s.072.002.099.062c.09.203.31.238.483.024a.334.334 0 0 1 .083-.065c-.036-.003-.059 0-.087-.028-.194-.193-.394-.03-.46-.016-.045.01-.076.017-.118.023z" fill="#000" stroke="none"/>
    </g>
    <g fill-rule="evenodd" stroke="#e3e4e5" stroke-width=".423">
      <g fill="#e7e7e7" stroke="#000">
        <path d="M329.132 272.906l.001.005a1.912 1.912 0 0 0-.014.744c-.054.27-.058.532-.014.75a1.945 1.945 0 0 0-.012.715 2.006 2.006 0 0 0-.01.676c.246.19.945.212 1.258.04a2.207 2.207 0 0 0-.002-.71c.046-.236.04-.474-.003-.714.047-.218.047-.48-.004-.75.043-.206.05-.446-.005-.745a2.245 2.245 0 0 0-.005-.775 2.09 2.09 0 0 0-.006-.746 2.216 2.216 0 0 0-.006-.775v-.007c.036-.23.038-.489-.005-.782a2.379 2.379 0 0 0-.006-.791c.038-.241.04-.497-.005-.783.044-.229.043-.482-.006-.782.065-.243.039-.549-.008-.79v-.003a2.47 2.47 0 0 0-.004-.78c.042-.26.042-.52-.005-.782a3.244 3.244 0 0 0-.006-.791c.033-.258.028-.51-.009-.781a2.92 2.92 0 0 0-.009-.781 2.57 2.57 0 0 0 0-.794 2.68 2.68 0 0 0-.008-.783c.035-.242.037-.5-.01-.78.042-.281.034-.534-.009-.767.034-.26.032-.45.002-.708.006-.08 0-.229-.009-.308l-.789-.012c-.033.055-.043.164-.021.311-.04.113-.038.6.008.713a2.307 2.307 0 0 0-.018.766c-.05.28-.052.538-.02.781a2.676 2.676 0 0 0-.02.783 2.57 2.57 0 0 0-.01.793 2.916 2.916 0 0 0-.018.781c-.04.271-.049.523-.019.781-.037.27-.043.532-.016.791-.05.261-.053.521-.015.782-.045.24-.05.496-.014.78-.05.24-.08.548-.018.793-.053.3-.058.552-.016.782-.05.284-.05.54-.015.782a2.382 2.382 0 0 0-.017.791 2.63 2.63 0 0 0-.014.789 2.215 2.215 0 0 0-.017.775 2.09 2.09 0 0 0-.015.746c-.053.29-.054.541-.016.77z" stroke-width=".12297878999999999"/>
        <path d="M329.124 273.647c.29.135 1.034.111 1.208.022M329.118 272.9c.277.118.94.112 1.21.022M329.154 272.118c.27.134.908.1 1.168.022M329.17 271.376c.297.135.945.098 1.146.02M329.184 270.61c.32.092.88.088 1.126.004M329.2 269.828c.329.079.842.082 1.105.004M329.215 269.035c.3.077.88.082 1.084.006M329.23 268.258c.26.084.81.076 1.063 0M329.252 267.473c.333.081.794.088 1.036.004M329.267 266.68c.34.08.82.07 1.015.005M329.28 265.9c.334.075.86.047.996.003M329.298 265.137c.313.057.839.027.973-.016M329.314 264.328c.308.083.818.045.95.002M329.335 263.565c.292.075.753.044.924-.017M329.359 262.767c.287.063.768.04.894-.001M329.37 261.981c.287.06.753.034.877-.006M329.398 261.2c.312.04.71.035.829-.003M329.416 260.402c.26.055.69.05.805.013M329.433 259.637c.283.045.673.042.785.006M329.402 258.927c.212.022.732.037.82.009M329.11 274.389c.295.135 1.049.111 1.226.022M329.096 275.1c.3.135 1.063.112 1.243.022" stroke-linejoin="round" stroke-width=".12297878999999999"/>
      </g>
      <g stroke="#e7e7e7">
        <path d="M331.51 257.072c.477-.105 1.164-.119 2.11-.144a13.7 13.7 0 0 0-.86-.256c.553-.076 1.871-.059 2.209-.062a4.93 4.93 0 0 0-.948-.395c.725-.12 1.59-.005 2.2.093-.284-.16-.606-.366-1.014-.477.87-.06 1.216.01 2.023.12a2.617 2.617 0 0 0-.882-.485c.967-.177 1.51-.214 2.457-.138-2.738-1.253-6.06-.601-8.06.777.201-.314.633-.791 1.055-1.151a2.524 2.524 0 0 0-.692.038c.293-.32.844-.75 1.217-.898-.273-.015-.503-.02-.748.028.367-.34.92-.7 1.391-.94a3.11 3.11 0 0 0-.84.019c.37-.437 1.018-.883 1.59-1.155a3.948 3.948 0 0 0-.935.072c.476-.435 1.114-.89 1.951-1.313-3.32.372-5.154 2.766-5.31 4.794-.885-1.909-3.296-3.733-6.614-3.594.99.302 1.778.67 2.39 1.042a4.344 4.344 0 0 0-1.006.076c.674.19 1.47.546 1.972.937a3.37 3.37 0 0 0-.892.116c.556.172 1.231.454 1.704.747-.27-.01-.512.033-.796.092.432.093 1.121.447 1.512.732a2.74 2.74 0 0 0-.74.072c.998.39 1.062.565 1.282.697-2.071-.443-5.065.241-7.367 2.915.334-.281 1.76-.812 2.212-.934-.236.282-.317.43-.504.785.518-.418 1.299-.763 2.045-1.06a4.806 4.806 0 0 0-.406.744c.542-.336.708-.384 1.82-.913-.081.136-.302.43-.408.71.34-.193 1.146-.487 1.693-.684.038-.014-.329.3-.495.676.125-.047.884-.321.913-.329-1.752 1.223-3.058 3.275-2.793 5.77.1-.39.564-1.51.826-1.958-.029.297.038.79.093 1.01.258-.745.541-1.34.964-1.985-.035.399-.025.838.03 1.019.235-.551.422-1.025 1-2.045-.037.152-.045.582-.01.963.192-.42.637-1.248 1.072-1.86-.022.225-.062.606-.045.765.046-.081.183-.307.343-.56.249-.392.552-.85.65-.923.077.077.15.171.217.28.272.436.467 1.105.528 1.797.146-.34.208-.686.227-.87.31.696.66 1.783.686 2.382.104-.318.158-.516.196-1.013.324.52.606 1.8.632 2.355.126-.217.23-.49.306-.895.317.716.442 1.606.519 2.22.813-2.96-.354-4.873-1.777-6.333.358.194.81.593 1.253 1.073a4.879 4.879 0 0 0-.264-.874c.526.374 1.301 1.144 1.517 1.367-.037-.293-.079-.448-.233-.821.62.43 1.175 1.167 1.489 1.573a5.422 5.422 0 0 0-.205-.943c.567.424 1.13 1.274 1.388 1.753-.007-.327-.099-.674-.176-1.031.771.652 1.056 1.341 1.3 1.758.006-2.673-3.087-5.077-5.962-5.328z" fill="#007934" stroke-width=".12297878999999999"/>
        <path d="M330.32 257.1c3.133-.448 7.159 2.251 7.152 5.3-.244-.417-.53-1.106-1.3-1.758.077.357.169.704.175 1.031-.256-.48-.82-1.33-1.387-1.753.13.404.18.755.205.943-.314-.406-.869-1.143-1.49-1.573.155.373.197.528.234.821-.216-.223-.991-.993-1.517-1.367.1.224.223.65.264.874-.443-.48-.896-.879-1.254-1.073M329.617 257.062c-1.876-1.209-5.857-.989-8.748 2.37.334-.282 1.76-.813 2.212-.935-.236.282-.317.43-.504.785.518-.418 1.299-.763 2.044-1.06a4.781 4.781 0 0 0-.405.744c.542-.336.708-.384 1.82-.913-.081.136-.303.43-.408.71.339-.193 1.146-.487 1.693-.684.038-.014-.33.3-.495.676.125-.047.884-.321.912-.33" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width=".12297878999999999"/>
        <path d="M329.743 256.585c-.399-2.205-3.041-4.744-6.933-4.58.99.302 1.778.67 2.39 1.042a4.339 4.339 0 0 0-1.006.076c.674.189 1.47.546 1.972.937a3.372 3.372 0 0 0-.892.116c.556.171 1.23.454 1.704.747-.27-.01-.512.033-.796.092.432.093 1.121.447 1.512.732-.305-.008-.52.02-.741.072.998.389 1.063.565 1.283.697M329.715 257.456c-2.607.796-5.127 3.362-4.77 6.74.102-.39.565-1.51.827-1.958-.029.297.038.79.093 1.01.258-.745.541-1.34.964-1.985-.035.399-.025.838.03 1.02.235-.552.421-1.026 1-2.046-.037.152-.045.582-.01.963a13.31 13.31 0 0 1 1.072-1.86c-.022.225-.062.606-.046.764.119-.206.832-1.362.995-1.482M328.247 258.785a8.728 8.728 0 0 0-.388 1.452M327.294 259.437a9.718 9.718 0 0 0-.466 1.826M326.336 260.35a8.913 8.913 0 0 0-.564 1.888" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width=".12297878999999999"/>
        <path class="fil1 str2" fill="none" stroke-linecap="round" stroke-linejoin="round" d="M329.87 258.622l.01-.603" stroke-width=".12297878999999999"/>
        <path d="M332.57 261.249c.08.476.119.726.09 1.409M331.579 259.603c.102.532.145.883.144 1.594M330.655 258.537c.115.328.184.75.185 1.293M335.588 259.435c.348.446.517.949.584 1.207M334.178 258.455c.34.401.673 1.115.782 1.465M332.994 258.059c.293.382.493.786.681 1.23M323.081 258.497a4.665 4.665 0 0 1 1.73-1.196M324.621 258.221c.388-.49.587-.8 1.604-1.242M326.035 258.053c.32-.41.834-.873 1.614-1.15M327.32 258.079c.51-.464.978-.71 1.602-.976M325.2 253.047c.672.073 1.598.307 2.165.68M326.166 254.06c.61.01 1.445.193 1.873.426M326.978 254.923c.604.012 1.36.26 1.98.616M327.694 255.747c.809.07 1.363.365 1.724.585M330.26 256.476c1.91-1.623 5.572-2.509 8.545-1.148-.948-.077-1.49-.039-2.457.138.396.113.772.385.882.484-.807-.108-1.153-.178-2.023-.119.408.111.73.317 1.014.477-.61-.098-1.475-.212-2.2-.093.151.03.719.255.948.395-.338.003-1.656-.014-2.21.062.38.101.633.18.862.256-.948.025-1.634.04-2.111.144M336.348 255.466c-.589-.238-1.617-.346-2.145-.312M335.207 255.831c-1.04-.345-2.03-.326-2.526-.25M334.021 256.215c-.915-.258-1.99-.264-2.486-.189M332.76 256.672c-.882-.212-1.513-.182-2.01-.107M331.586 257.635c.36.306.618.664.806 1.109" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width=".12297878999999999"/>
        <path d="M329.424 255.599c.156-2.028 1.99-4.422 5.31-4.794-.837.423-1.475.878-1.951 1.313.333-.066.563-.078.936-.072-.573.272-1.221.718-1.592 1.155.277-.05.562-.045.84-.02-.47.24-1.023.6-1.39.941.245-.048.475-.043.748-.028-.374.148-.924.577-1.218.898a2.52 2.52 0 0 1 .693-.038c-.422.36-.854.837-1.055 1.151M332.783 252.118c-.485.133-1.1.39-1.547.71M332.127 253.2a4.523 4.523 0 0 0-1.4.515M331.577 254.122c-.476.084-1.017.35-1.47.694M331.107 254.992a3.215 3.215 0 0 0-1.445.793M329.21 258.18a6.022 6.022 0 0 0-.29 1.159M330.09 257.33c1.85 1.615 4.149 3.695 3.09 7.549-.077-.615-.202-1.505-.52-2.221-.075.406-.18.678-.306.895-.025-.556-.307-1.836-.631-2.356a3.53 3.53 0 0 1-.196 1.015c-.025-.6-.376-1.687-.686-2.382a3.17 3.17 0 0 1-.227.869c-.076-.864-.36-1.69-.745-2.077" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width=".12297878999999999"/>
      </g>
    </g>
    <g style="line-height:125%;-inkscape-font-specification:'Linux Biolinum Bold';text-align:center" font-weight="700" font-size="100" font-family="Linux Biolinum" letter-spacing="60" word-spacing="0" text-anchor="middle" fill="#e8a30e" stroke="#000" stroke-width="1.5">
      <path d="M293.298 233.512l1.26.684.306-.563c.157-.289.204-.515.141-.68-.061-.167-.23-.325-.504-.474-.303-.165-.544-.226-.725-.184-.183.04-.333.168-.45.384-.152.28-.226.472-.222.577.004.103.07.189.194.256m1.644.893l1.62.879c.124.068.235.073.332.017.1-.06.216-.212.35-.458.127-.235.211-.432.252-.592.04-.164.035-.3-.016-.406a.714.714 0 0 0-.182-.254 1.884 1.884 0 0 0-.333-.217c-.756-.41-1.31-.29-1.66.362l-.363.669m-2.599-.773c.092-.169.208-.391.35-.667.14-.276.228-.447.264-.513a3.25 3.25 0 0 1 .553-.767c.195-.197.384-.324.567-.38a1.38 1.38 0 0 1 .495-.073c.15.008.292.049.426.122.188.102.33.275.429.52.1.242.142.49.13.744.287-.29.596-.486.924-.587.329-.101.654-.065.975.11.357.194.583.528.678 1.002.093.473-.04 1.039-.398 1.698-.122.226-.254.46-.396.706-.141.243-.256.446-.345.61l-.312.624-.025-.004c-.358-.27-.823-.56-1.396-.872l-1.764-.957c-.554-.301-1.05-.533-1.491-.696l-.014-.026c.145-.231.261-.43.35-.594M301.225 222.805c-.718.599-.593 1.478.374 2.636.488.585.974.93 1.46 1.036.483.105.922-.007 1.316-.336.356-.298.507-.657.452-1.077-.055-.42-.318-.912-.788-1.476-.543-.65-1.036-1.035-1.48-1.155-.445-.123-.89 0-1.334.372m3.729-.184c.528.633.758 1.315.689 2.044-.07.726-.439 1.368-1.107 1.926-.625.522-1.303.767-2.032.735-.729-.031-1.34-.341-1.83-.93-.522-.625-.754-1.317-.695-2.076.059-.76.405-1.403 1.039-1.932.668-.558 1.36-.814 2.077-.768.717.041 1.337.375 1.859 1M312.958 220.014c.15.538.264.872.345 1.002.317-.088.659-.199 1.026-.332.37-.133.661-.246.871-.338l.316-.134.024.027c.014.088.05.24.11.456.022.074.057.184.109.327l-.01.028c-.13.02-.29.056-.48.109l-2.405.67-.646.204-.018-.02c-.048-.371-.168-.906-.36-1.604l-.538-1.934a11.937 11.937 0 0 0-.53-1.558l.013-.028c.241-.05.45-.1.628-.15l.65-.206.014.02c.045.363.166.897.364 1.605l.517 1.856M322.203 217.151c.069-.627.096-1.174.083-1.643l.018-.022c.259.044.48.076.667.096l.68.05.005.025c-.114.423-.206.964-.278 1.622l-.217 1.995a11.879 11.879 0 0 0-.078 1.644l-.019.022c-.258-.044-.48-.076-.666-.096l-.68-.05-.006-.025c.115-.434.206-.974.274-1.623l.217-1.995M332.054 222.386c1.65-1.308 2.7-2.201 3.148-2.68.051.047.188.129.409.248.174.093.293.148.357.164-2.294 1.737-3.851 2.933-4.674 3.589l-.497-.267c.044-.456.114-1.143.21-2.061.096-.919.176-1.702.24-2.35.063-.65.1-1.113.111-1.386.075.058.315.196.72.413.284.153.47.244.558.273-.24 1.114-.435 2.466-.582 4.057M340.52 226.231c.466-.424.85-.816 1.15-1.177l.028-.004c.165.204.31.376.436.514l.476.487-.012.022c-.365.242-.792.586-1.282 1.032l-1.484 1.352c-.46.419-.843.812-1.147 1.18l-.029.004c-.164-.204-.31-.375-.435-.514l-.477-.487.013-.022c.373-.25.799-.594 1.28-1.035l1.483-1.352M345.505 236.238c.519-.533.96-1.005 1.326-1.417l-.022-.047-1.947.062c.088.215.204.476.347.784.072.155.171.36.296.618m-1.145-1.38c-.852.042-1.495.102-1.927.18a1.858 1.858 0 0 0-.134-.345 2.615 2.615 0 0 0-.216-.407c2.773-.018 4.74-.043 5.9-.075l.31.668c-2.01 2.11-3.32 3.548-3.929 4.31-.025-.09-.123-.32-.294-.689a5.363 5.363 0 0 0-.302-.593c.373-.318.84-.759 1.4-1.322a35.018 35.018 0 0 0-.334-.746c-.13-.281-.289-.608-.474-.982" style="-inkscape-font-specification:'Linux Biolinum Bold'" stroke-width=".12307499999999999"/>
    </g>
    <g fill="#e8a30e" stroke-width="1.792" stroke="#000" stroke-linecap="square" stroke-linejoin="round">
      <path d="M324.923 280.609l-.768 2.208-2.337.048 1.863 1.412-.677 2.238 1.92-1.336 1.918 1.336-.677-2.238 1.863-1.412-2.337-.048zM334.06 276.717l-.769 2.208-2.337.048 1.863 1.412-.677 2.238 1.92-1.335 1.918 1.335-.677-2.238 1.863-1.412-2.337-.048zM348.17 251.756l-.768 2.209-2.337.047 1.863 1.413-.677 2.238 1.919-1.336 1.919 1.336-.677-2.238 1.863-1.413-2.337-.047zM341.47 269.563l.768 2.209 2.337.047-1.863 1.413.677 2.238-1.919-1.336-1.919 1.336.677-2.238-1.863-1.413 2.338-.047zM346.204 261.422l.767 2.208 2.338.048-1.863 1.413.677 2.237-1.92-1.335-1.918 1.335.677-2.237-1.863-1.413 2.337-.048zM315.077 280.609l.768 2.208 2.337.048-1.863 1.412.677 2.238-1.92-1.336-1.918 1.336.677-2.238-1.863-1.412 2.337-.048zM305.94 276.717l.769 2.208 2.337.048-1.863 1.412.677 2.238-1.92-1.335-1.918 1.335.677-2.238-1.863-1.412 2.337-.048zM291.83 251.756l.768 2.209 2.337.047-1.863 1.413.677 2.238-1.919-1.336-1.919 1.336.677-2.238-1.863-1.413 2.337-.047zM298.53 269.563l-.768 2.209-2.337.047 1.863 1.413-.677 2.238 1.919-1.336 1.919 1.336-.677-2.238 1.863-1.413-2.338-.047zM293.796 261.422l-.767 2.208-2.338.048 1.863 1.413-.677 2.237 1.92-1.335 1.918 1.335-.677-2.237 1.863-1.413-2.337-.048z" stroke-width=".12309248"/>
    </g>
    <g stroke="#000">
      <g fill="#e7e7e7" stroke-width=".373">
        <path d="M361.88 211.78l-.023-4.445h.547l-6.793-4.387-5.466-.003-6.756 4.381.54.003.023 4.442 17.926.012" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M361.88 211.78l-.023-4.445h.547l-6.793-4.387-.515-6.684h-4.43l-.52 6.68-6.757 4.382.54.003.023 4.442 17.926.012.002-.003zM363.967 217.618l.002 1.39-21.884-.024-.009-1.385 21.894.019" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M363.967 217.618l.002 1.39-21.884-.024-.009-1.385 21.894.019h-.002zM343.538 217.534l-.018-4.706.886-.002-.049 4.76.054-4.722-1.277-.037v-1.01l19.558.022.01 1h-1.207l.023 4.766-.043-4.766h.899l.02 4.713" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M343.538 217.534l-.018-4.706.886-.002-.049 4.76.054-4.722-1.277-.037v-1.01l19.558.022.01 1h-1.207l.023 4.766-.043-4.766h.899l.02 4.713" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M347.743 216.375l-.014-3.282h-2.626l.016 3.279 2.624.003" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M345.818 213.778l.008 1.906h1.202l-.006-1.906h-1.203M360.582 216.384l-.016-3.279-2.617-.003.013 3.282h2.62" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M358.657 213.79l.014 1.9h1.2l-.01-1.9h-1.204M360.562 211.04l-.015-3.282h-2.62l.008 3.282h2.628-.002z" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M358.637 208.449l.008 1.903h1.2l-.002-1.903h-1.206M347.722 211.028l-.018-3.276-2.622-.003.012 3.277h2.628v.002z" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M345.797 208.437l.006 1.9 1.203.003-.009-1.903h-1.2M352.474 211.034l-.012-3.276-2.623-.003.01 3.279h2.625" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M350.55 208.44l.007 1.9 1.202.006-.008-1.906h-1.2M356.137 211.04l-.018-3.282-2.622-.003.013 3.279 2.627.006" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M354.211 208.443l.01 1.906h1.201l-.006-1.906h-1.205" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M356.787 210.033l.03 7.566-7.6-.012-.028-7.56 7.597.006" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M356.787 210.033l.03 7.566-7.6-.012-.028-7.56 7.597.006h.002zM349.143 212.193l7.652.006M350.098 217.596l-.026-5.347M355.892 217.6l-.026-5.344M359.561 206.81l-4.28-3.124-4.808-.006-4.246 3.119 13.334.012M350.851 202.998l4.045.003M351.94 201.565l.006.95h1.858l-.006-.95h-1.858" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
        <path d="M349.544 210.246h.554l-.162.297s.267.343.005.736l.145.274h-.534l.145-.26s-.273-.456.012-.75l-.164-.297M350.673 210.246h.554l-.16.297s.266.334.001.736l.144.274h-.532l.143-.26s-.267-.456.014-.75l-.164-.297M351.904 210.246h.552l-.165.297s.27.343.005.736l.145.28-.53-.007.143-.259s-.27-.453.01-.75l-.161-.298h.002zM353.293 210.248h.557l-.163.3s.266.338.002.733l.149.277h-.537l.144-.259s-.267-.459.018-.75l-.168-.3h-.002zM354.528 210.248h.553l-.164.3s.269.338.003.731l.147.28h-.533l.14-.26s-.27-.456.02-.75l-.166-.3M355.756 210.251l.552-.003-.163.3s.273.338.008.733l.139.278h-.534l.143-.26s-.266-.456.019-.75l-.163-.297" transform="matrix(.12173 0 0 .10848 277.026 225.119)"/>
      </g>
      <path d="M319.84 246.028h.32v.374h-.32v-.374z" fill="#e7e7e7" fill-rule="evenodd" stroke-width=".0319995"/>
      <path d="M320.003 245.569l-.006.479" fill="none" stroke-width=".073845"/>
      <path d="M319.825 245.748l.35.004" fill="none" stroke-width=".09329085000000001"/>
    </g>
  </g>
  <g>
    <path d="M317.139 210.304s-2.342 5.437-1.533 5.977c0 0 2.937-3.28 3.704-5.887.766-2.606 0-.18 0-.18l-.213-2.83-1.916 2.696" fill="#452c25"/>
    <path d="M586.607 219.117s-8.426 19.153-5.515 21.053c0 0 10.569-11.555 13.329-20.736 2.757-9.181 0-.633 0-.633l-.767-9.973-6.894 9.497" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".585"/>
    <path d="M317.633 207.706s-2.813 5.988-2.17 6.43c0 0 2.338-2.692 2.947-4.83.61-2.14 0-.148 0-.148l.782-3.85" fill="#452c25"/>
    <path d="M588.388 209.963s-10.125 21.095-7.809 22.654c0 0 8.411-9.485 10.605-17.02 2.193-7.536 0-.52 0-.52l2.812-13.559" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
    <path d="M320.532 206.372s-2.814 5.988-2.17 6.43c0 0 2.337-2.692 2.947-4.83.61-2.14 0-.149 0-.149l.78-3.848" fill="#452c25"/>
    <path d="M598.819 205.264s-10.125 21.095-7.81 22.654c0 0 8.411-9.485 10.606-17.021 2.194-7.536 0-.52 0-.52l2.811-13.559" transform="matrix(.27785 0 0 .28386 154.148 148.105)" fill="none" stroke="#000" stroke-width=".473"/>
    <path d="M356.838 195.606l3.34 1.895s.814.676-.974.278c-1.789-.397-5.603-1.714-12.808-6.35-3.258-2.097-4.286-2.038-4.286-2.038l4.541.123 10.187 6.092z" fill="#452c25"/>
    <path d="M356.838 195.606l3.34 1.895s.814.676-.974.278c-1.789-.397-5.603-1.714-12.808-6.35-3.258-2.097-4.286-2.038-4.286-2.038l4.541.123 10.187 6.092z" fill="none" stroke="#000" stroke-width=".11993183"/>
    <path d="M357.942 194.782l3.34 1.895s.814.676-.974.278c-1.789-.398-5.603-1.714-12.808-6.35-3.258-2.097-.637 1.729-.637 1.729l.552-2.516 10.527 4.964z" fill="#452c25"/>
    <path d="M357.942 194.782l3.34 1.895s.814.676-.974.278c-1.789-.398-5.603-1.714-12.808-6.35-3.258-2.097-.637 1.729-.637 1.729l.552-2.516 10.527 4.964z" fill="none" stroke="#000" stroke-width=".11993183"/>
    <path d="M363.498 196.607s-3.989-.742-5.44-1.804c0 0 .254.458-1.696-.43 0 0 .752 1.71-4.739-2.03-5.491-3.741-3.571-1.861-3.571-1.861l1.536-.397s8.43 3.368 9.354 3.72c.925.353 4.556 2.802 4.556 2.802" fill="#452c25"/>
    <path d="M363.498 196.607s-3.989-.742-5.44-1.804c0 0 .254.458-1.696-.43 0 0 .752 1.71-4.739-2.03-5.491-3.741-3.571-1.861-3.571-1.861l1.536-.397s8.43 3.368 9.354 3.72c.925.353 4.556 2.802 4.556 2.802z" fill="none" stroke="#000" stroke-width=".11683742000000001"/>
    <path d="M342.645 198.1l1.336 1.906s-.444 1.787-5.083-1.69c-4.638-3.475-4.394-3.187-4.394-3.187l2.562-.358 5.626 3.193" fill="#452c25"/>
    <path d="M342.645 198.1l1.336 1.906s-.444 1.787-5.083-1.69c-4.638-3.475-4.394-3.187-4.394-3.187l2.562-.358 5.626 3.193" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M336.62 199.056s2.154 2.819 1.675 3.17c-.48.35-3.024.199-4.84-2.56-1.817-2.758-.039-.14-.039-.14l.169-4.46 3.082 3.853" fill="#452c25"/>
    <path d="M336.62 199.056s2.154 2.819 1.675 3.17c-.48.35-3.024.199-4.84-2.56-1.817-2.758-.039-.14-.039-.14l.169-4.46 3.082 3.853" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M338.832 197.759s2.074 2.676 1.848 3.083c-.226.407-3.15.149-5.48-2.492-2.33-2.64-.347-3.485-.347-3.485l3.942 2.754" fill="#452c25"/>
    <path d="M338.814 197.689s2.092 2.746 1.866 3.153c-.226.407-3.15.149-5.48-2.492-2.33-2.64-.347-3.485-.347-3.485l3.96 2.824z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M350.651 196.179s5.911 2.542 1.527 2.376c0 0-8.588-2.313-13.03-6.118l1.256-1.662 10.178 5.256" fill="#452c25"/>
    <path d="M350.651 196.179s5.911 2.542 1.527 2.376c0 0-8.588-2.313-13.03-6.118l1.256-1.662 10.178 5.256" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M353.753 195.445s3.2 1.941 3.438 2.68c.238.737-9.943-1.902-15.313-6.374l2.398-1.175 9.477 4.87z" fill="#452c25"/>
    <path d="M353.753 195.445s3.2 1.941 3.438 2.68c.238.737-9.943-1.902-15.313-6.374l2.398-1.175 9.477 4.87z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M344.46 197.204s2.314 1.9 2.052 2.12c-.263.22-4.955-.482-8.639-3.317l.441-1.693 6.192 2.799" fill="#452c25"/>
    <path d="M344.46 197.204s2.314 1.9 2.052 2.12c-.263.22-4.955-.482-8.639-3.317l.441-1.693 6.192 2.799" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M348.418 197.043s2.282 1.574 1.81 1.74c-.474.165-2.265 1.572-10.76-3.404l-1.002-.55 1.349-2.12 8.607 4.195" fill="#452c25"/>
    <path d="M348.42 196.973s2.28 1.644 1.807 1.81c-.473.165-2.264 1.572-10.76-3.404l-1.001-.55 1.349-2.12 8.605 4.264z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M339.698 192.381s2.762 2.473 2.41 2.829c-.353.356-3.64-.427-5.09-1.505-1.45-1.077-2.505-2.458-2.505-2.458l3.08-.665 2.105 1.799z" fill="#452c25"/>
    <path d="M339.698 192.381s2.762 2.473 2.41 2.829c-.353.356-3.64-.427-5.09-1.505-1.45-1.077-2.505-2.458-2.505-2.458l3.08-.665 2.105 1.799z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M336.406 188.251l5.031 2.999s4.106 2.898 3.673 3.158c-.434.26-3.67-.752-5.965-2.003-2.293-1.251-4.954-3.946-4.954-3.946" fill="#452c25"/>
    <path d="M336.406 188.251l5.031 2.999s4.106 2.898 3.673 3.158c-.434.26-3.67-.752-5.965-2.003-2.293-1.251-4.954-3.946-4.954-3.946" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M333.231 202.433s.937 2.398.377 2.607c-.56.208-1.947-.219-3.062-2.393-1.114-2.174.971-1.255.971-1.255l1.714 1.041z" fill="#452c25"/>
    <path d="M333.231 202.433s.937 2.398.377 2.607c-.56.208-1.947-.219-3.062-2.393-1.114-2.174.756-1.208.756-1.208l1.929.994z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M334.9 200.882s1.406 2.325 1.1 2.59c-.307.266-2.162 1.068-4.183-1.93-2.02-2.998 2.08-2.367 2.08-2.367l1.003 1.707z" fill="#452c25"/>
    <path d="M334.9 200.882s1.406 2.325 1.1 2.59c-.307.266-2.162 1.068-4.183-1.93-2.02-2.998 2.08-2.367 2.08-2.367l1.003 1.707z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M330.696 190.392s4.798 9.266 4.4 9.758c-.4.493-2.296-.002-3.358-2.451-1.062-2.45-1.93-5.63-1.93-5.63l.888-1.677z" fill="#452c25"/>
    <path d="M330.696 190.392s4.798 9.266 4.4 9.758c-.4.493-2.296-.002-3.358-2.451-1.062-2.45-1.93-5.63-1.93-5.63l.888-1.677z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M336.286 192.75s4.004 3.403 3.182 3.832c-.822.43-2.415-.192-4.757-2.509-2.342-2.317 1.538-1.464 1.538-1.464" fill="#452c25"/>
    <path d="M336.267 192.68s4.023 3.473 3.201 3.902c-.822.43-2.415-.192-4.757-2.509-2.342-2.317 1.556-1.393 1.556-1.393z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M334.345 192.807s2.862 5.255 2.505 5.75c-.357.494-2.504-1.306-3.563-2.646-1.058-1.339-1.859-3.285-1.859-3.285" fill="#452c25"/>
    <path d="M334.345 192.807s2.862 5.255 2.505 5.75c-.357.494-2.504-1.306-3.563-2.646-1.058-1.339-1.859-3.285-1.859-3.285" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M312.9 203.599s-.17 3.098 0 3.33c.17.23 1.74.23 1.825-2.082.085-2.312-.34-2.404-.34-2.404l-1.528 1.063" fill="#452c25"/>
    <path d="M312.9 203.599s-.17 3.098 0 3.33c.17.23 1.74.23 1.825-2.082.085-2.312-.34-2.404-.34-2.404l-1.528 1.063" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M313.84 199.866s-1.104 3.468-.043 4.161c1.062.694 1.911-3.421 1.953-4.346.043-.925-1.91.185-1.91.185" fill="#452c25"/>
    <path d="M313.84 199.866s-1.104 3.468-.043 4.161c1.062.694 1.911-3.421 1.953-4.346.043-.925-1.91.185-1.91.185z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M314.598 204.292s.212 3.237.807 3.376c.594.138 1.57-.925 1.57-1.757 0-.832-1.018-2.774-1.018-2.774l-1.402 1.017" fill="#452c25"/>
    <path d="M314.598 204.292s.212 3.237.806 3.376c.595.138 1.571-.925 1.571-1.757 0-.832-1.019-2.774-1.019-2.774l-1.4 1.017" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M314.626 200.417s-1.104 3.467-.042 4.16c1.061.695 1.91-3.42 1.953-4.345.043-.925-1.91.185-1.91.185z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M314.712 194.852s-1.614 1.664-1.57 2.59c.041.924 2.377-1.25 2.59-1.666.211-.416-1.02-.924-1.02-.924" fill="#452c25"/>
    <path d="M314.712 194.852s-1.614 1.664-1.57 2.59c.041.924 2.377-1.25 2.59-1.666.211-.416-1.02-.924-1.02-.924z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M313.603 193.919s-1.274 2.358-.935 3.098c.34.74 1.571-.555 2.166-1.387s-1.231-1.711-1.231-1.711" fill="#452c25"/>
    <path d="M313.603 193.919s-1.274 2.358-.935 3.098c.34.74 1.571-.555 2.166-1.387s-1.231-1.711-1.231-1.711z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M331.51 190.795s2.034 3.832 1.475 4.04c-.56.208-1.419-.753-2.271-1.898-.853-1.146.796-2.142.796-2.142" fill="#452c25"/>
    <path d="M331.51 190.795s2.034 3.832 1.475 4.04c-.56.208-1.419-.753-2.271-1.898-.853-1.146.796-2.142.796-2.142z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M331.557 198.631s.738 3.223.09 3.52c-1.267.582-1.974-2.29-2.05-3.37-.086-1.206 1.96-.15 1.96-.15zM329.022 203.74s.171 2.414-.177 2.631c-.347.217-1.199.275-2.148-1.754-.95-2.029-.508-1.27-.508-1.27l2.334-1.153.507 1.316" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M331.356 203.19s.273 2.555-.24 2.627c-.512.071-1.875-.793-2.469-2.323-.684-1.764 2.38-1.244 2.38-1.244l.33.94z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M328.953 195.35s2.623 5.87 2.201 6.707c-.826 1.636-3.21-3.576-3.93-5.61-.805-2.276 1.729-1.097 1.729-1.097z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M327.736 196.33s3.577 6.462 2.068 6.612c-1.508.15-4.25-4.686-4.71-5.81-.46-1.124 2.642-.801 2.642-.801z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M327.522 197.672s1.76 6.192.627 5.645c-1.134-.548-2.5-5.212-2.685-6.218-.187-1.007 2.058.573 2.058.573zM333.705 188.868s2.713 4.144 1.833 4.267c-.88.123-4.097-2.769-4.126-3.248-.03-.478 2.293-1.02 2.293-1.02z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M332.01 189.042s3.458 4.722 2.415 4.962c-1.043.24-1.121-.499-1.121-.499s-2.76-2.488-2.946-3.117c-.185-.63 1.55-1.406 1.55-1.406M335.436 189.116s2.379 2.453 1.754 3.284c-.623.83-3.928-3.107-4.377-3.607-.45-.5 2.881.226 2.881.226M329.67 192.525s3.83 7.461 3.124 8.22c-.705.758-4.962-5.595-5.11-6.516-.148-.92 2.024-1.844 2.024-1.844" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M327.466 193.2s1.532 2.334 1.445 3.592c-.086 1.257-2.315-1.8-2.507-2.376-.193-.576 1.062-1.216 1.062-1.216z" fill="#452c25" stroke="#000" stroke-width=".15500181000000002"/>
    <path d="M330.617 191.99s1.191 2.41.826 3.135c-.365.725-1.988-1.464-2.553-2.32-.566-.856 1.727-.815 1.727-.815z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M325.09 194.358s1.87 2.074 1.78 3.18c-.09 1.105-2.804-1.613-3.042-2.123-.237-.508 1.263-1.057 1.263-1.057z" fill="#452c25" stroke="#000" stroke-width=".15950277000000002"/>
    <path d="M337.175 188.374s2.83 2.539 2.39 2.937c-.439.398-4.267-2.343-4.843-2.876-.577-.533 2.453-.061 2.453-.061z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M334.782 188.19l2.213 1.865s2.056 1.374 1.704 1.73c-.352.355-3.572-1.12-4.144-1.79-.572-.672-.505-1.363-.505-1.363l.732-.441zM342.076 188.777s8.38 3.398 8.054 4.218c-.326.82-8.786-2.72-10.114-3.654-1.327-.934 1.934-.615 1.934-.615" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M342.378 189.854s6.294 3.543 5.56 3.884c-.735.34-5.24-.864-7.564-2.44-2.325-1.576-3.315-2.45-3.315-2.45l2.87-.668 2.45 1.674zM326.885 194.16s1.192 2.41.826 3.135c-.365.725-1.55-.62-2.115-1.475-.566-.856 1.29-1.66 1.29-1.66zM327.168 199.593s.983 3.51-.101 4.16c-1.085.65-1.791-3.498-1.801-4.423-.011-.926 1.902.263 1.902.263z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M322.296 193.81s3.5-1.537 4.12-1.414c0 0 .734-.184 1.129-.491s1.298-1.045 1.298-1.045-.62-4.24 3.838-3.81c4.458.43 10.327.983 11.513 1.168 1.185.184 5.53 1.413 6.546 1.905 1.016.491 7.635 3.487 8.99 3.979.802.291 2.572 1.398 3.882 2.252.902.588-.028.167-.028.167s-10.72-5.69-14.51-6.452c-.948-.19.065 1.086.065 1.086s-2.462-.971-3.478-1.401c-1.016-.43-1.863-.615-2.822-.553a5.798 5.798 0 0 1-2.257-.308c-.565-.184-3.782-.307-4.346-.368-.564-.062-1.016-.185-1.016-.185l.226.37-1.58-.247-.452.676s-1.524.307-1.637-.184c-.112-.492-.903 2.212-1.298 3.011-.395.799-1.862.799-2.314 1.29-.451.492-.733.8-.959.922-.226.123-1.354.8-1.75.8-.395 0-2.82.184-2.82.184l-.622-.984.282-.368zM326.897 203.746s.062 3.103-.116 3.327c-.178.224-1.747.16-1.752-2.154-.005-2.314.422-2.389.422-2.389l1.49 1.125" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M327.274 201.366s-.297-1.343-.51-1.528c-.212-.186.085-.37.085-.37s-.552-1.344-.891-1.576c-.34-.231.042-.417.042-.417s-.594-.926-1.019-1.204c-.425-.278 0-.324 0-.324s-.444-1.244-1.478-1.814c0 0-.686-.722-1.386-.92-.7-.2-2.436-.384-4.534-.346-2.21.04-3.147 1.572-3.147 1.572l-.14 1.778.318-.217-.468 2.212c-.074.506.397 1.386.423 2.443.008.335.029.685.066 1.044.101.95.323 1.957.754 2.885.034.073.065.288.107.219.108-.186.35.626.568.917 0 0 .131 1.105.235.695.041-.159.396.626.615 1.118.079.179.497 1.394.481.84-.017-.605.368 1.29.368 1.562l.51-.74.254.833.424-.046-.127.787s1.189-.972 1.232-1.343c.042-.37.084-.648.084-.648l.383-.371.594-.927s1.487 1.158 1.656 1.575c.17.417.34.788.34.788l.382-.37.34.833.212-.417.207.579.018-.036.135.382c.103.096.34.145.786-.6.637-1.066.595-1.9.637-2.085.043-.185.298.324.298.324s.552-1.019.467-1.667c-.085-.649.34-.417.34-.417s.084-1.714 0-2.131c-.086-.417.296-.324.296-.324s-.127-2.038-.254-2.27c-.128-.232.297-.278.297-.278z" fill="#452c25" stroke="#000" stroke-width=".1331534"/>
    <path d="M324.298 196.65c.3.09.446.428.575.69-.008-.016.019-.092.003-.124-.128-.262-.278-.605-.578-.695.003 0-.02.124 0 .13M324.57 197.931c.553.469.6 1.108.57 1.807 0 .021.007.145.01.065.033-.74.025-1.489-.58-2 .013.011-.026.107 0 .128M325.72 198.587c.305.501.333 1.054.334 1.633 0 .074.013.074.013 0 0-.615-.016-1.218-.344-1.758.008.013-.021.095-.003.125M326.124 201.006c-.035.358-.1.71-.169 1.063a.43.43 0 0 0 .007.111c.07-.36.135-.718.17-1.083.003-.016-.004-.142-.009-.091M325.75 202.862c.03.235.06.468.035.705-.003.017.003.142.009.092.031-.299.002-.591-.034-.888-.006-.05-.011.075-.01.091M325.037 200.532c0 .397.004.793.036 1.188.004.05.01-.074.01-.091-.03-.365-.033-.73-.033-1.097 0-.073-.013-.073-.013 0M324.734 203.105c.031.376.032.753.032 1.131 0 .074.013.074.013 0 0-.408-.002-.816-.036-1.222-.004-.05-.01.074-.009.09M326.433 202.778c.164.173.264.376.302.614.003.025.008-.02.008-.023a.357.357 0 0 0-.002-.089 1.164 1.164 0 0 0-.308-.632c.015.017-.025.103 0 .13M326.331 200.181c.168.141.305.312.44.485-.015-.02.024-.1 0-.129a3.102 3.102 0 0 0-.44-.485c.014.012-.025.108 0 .13M324.935 202.124a5.928 5.928 0 0 1-.098 1.052.423.423 0 0 0 .006.112c.072-.384.102-.773.105-1.164 0-.073-.012-.073-.013 0M324.192 202.897c.03.316.03.631 0 .947 0 .017.005.142.01.091a6.235 6.235 0 0 0 0-1.13c-.005-.05-.011.075-.01.092"/>
    <path d="M310.74 194.327s-.85 3.19-.213 3.514c1.247.633 2.052-2.209 2.166-3.283.127-1.203-1.954-.231-1.954-.231" fill="#452c25"/>
    <path d="M310.74 194.327s-.85 3.19-.213 3.514c1.247.633 2.052-2.209 2.166-3.283.127-1.203-1.954-.231-1.954-.231z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M312.577 194.301s-1.614 1.665-1.57 2.59c.041.924 2.377-1.249 2.589-1.665.213-.416-1.02-.925-1.02-.925" fill="#452c25"/>
    <path d="M312.577 194.301s-1.614 1.665-1.57 2.59c.041.924 2.377-1.249 2.589-1.665.213-.416-1.02-.925-1.02-.925z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M285.056 194.536s-3.334 1.673-3.626 2.389c-.292.716 10.056-1.085 15.742-5.104l-2.306-1.365-9.81 4.08z" fill="#452c25"/>
    <path d="M285.056 194.536s-3.334 1.673-3.626 2.389c-.292.716 10.056-1.085 15.742-5.104l-2.306-1.365-9.81 4.08z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M288.953 195.208s-6.034 2.195-1.646 2.283c0 0 8.695-1.81 13.327-5.35l-1.17-1.732-10.435 4.655" fill="#452c25"/>
    <path d="M288.991 195.136s-6.072 2.267-1.684 2.355c0 0 8.695-1.81 13.327-5.35l-1.17-1.732-10.473 4.727z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M297.91 189.08s-6.411 3.283-5.69 3.653c.722.37 5.266-.647 7.644-2.127 2.378-1.48 3.397-2.312 3.397-2.312l-2.845-.786-2.506 1.572z" fill="#452c25"/>
    <path d="M297.91 189.08s-6.411 3.283-5.69 3.653c.722.37 5.266-.647 7.644-2.127 2.378-1.48 3.397-2.312 3.397-2.312l-2.845-.786-2.506 1.572z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M295.49 196.281s-2.292 1.918-2.026 2.136c.265.219 4.949-.52 8.598-3.383l-.462-1.69-6.158 2.847" fill="#452c25"/>
    <path d="M295.49 196.281s-2.292 1.918-2.026 2.136c.265.219 4.949-.52 8.598-3.383l-.462-1.69-6.158 2.847" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M291.502 195.983s-2.2 1.681-1.72 1.824c.481.143 2.342 1.463 10.579-3.91l.972-.597-1.453-2.052-8.39 4.597" fill="#452c25"/>
    <path d="M291.497 195.914s-2.195 1.75-1.715 1.893c.481.143 2.342 1.463 10.579-3.91l.972-.597-1.453-2.052-8.383 4.666z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M289.802 189.593c-6.927 2.423-13.874 7.25-13.874 7.25.68-.231 17.826-7.134 17.826-7.134M305.001 200.408s-1.486 2.266-1.189 2.544c.298.277 2.124 1.155 4.247-1.758 2.123-2.912-1.996-2.45-1.996-2.45L305 200.408z" fill="#452c25"/>
    <path d="M305.001 200.408s-1.486 2.266-1.189 2.544c.298.277 2.124 1.155 4.247-1.758 2.123-2.912-1.996-2.45-1.996-2.45L305 200.408z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M306.615 202.027s-1.019 2.358-.467 2.59c.552.23 1.953-.14 3.142-2.266 1.19-2.127-.552-1.064-.552-1.064l-2.123.74zM303.345 198.513s-2.25 2.728-1.783 3.098c.467.37 3.015.324 4.926-2.358 1.91-2.682.042-.139.042-.139l-.552-2.45-2.675 1.71" fill="#452c25"/>
    <path d="M306.615 202.027s-1.019 2.358-.467 2.59c.552.23 1.953-.14 3.142-2.266 1.19-2.127-.552-1.064-.552-1.064l-2.123.74zM303.345 198.513s-2.25 2.728-1.783 3.098c.467.37 3.015.324 4.926-2.358 1.91-2.682.042-.139.042-.139l-.552-2.45-2.675 1.71" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M301.18 197.126s-2.166 2.59-1.954 3.005c.213.416 3.143.278 5.563-2.265 2.42-2.544.467-3.468.467-3.468l-4.033 2.589" fill="#452c25"/>
    <path d="M301.18 197.126s-2.166 2.59-1.954 3.005c.213.416 3.143.278 5.563-2.265 2.42-2.544.467-3.468.467-3.468l-4.033 2.589" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M297.358 197.31l-1.401 1.85s.382 1.803 5.138-1.48c4.755-3.282 4.501-3.005 4.501-3.005l-2.548-.462-5.733 2.959" fill="#452c25"/>
    <path d="M297.337 197.241l-1.38 1.92s.382 1.802 5.138-1.48c4.755-3.283 4.501-3.006 4.501-3.006l-2.548-.462-5.711 3.028z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M282.19 194.478l-3.015 1.355s-.681.598 1.09.642c1.77.045 5.428-.445 11.79-4.053 2.878-1.632 4.7-3.187 4.7-3.187l-2.318-.86-12.247 6.103z" fill="#452c25"/>
    <path d="M282.19 194.478l-3.015 1.355s-.681.598 1.09.642c1.77.045 5.428-.445 11.79-4.053 2.878-1.632 4.7-3.187 4.7-3.187l-2.318-.86-12.247 6.103z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M277.027 196.38s4.337-.472 6.06-1.602c0 0-.352.527 1.866-.339 0 0-1.104 1.986 5.362-1.95 6.467-3.937.053-.08.053-.08l7.182-4.114-.42-.82s-13.722 5.876-14.76 6.204c-1.038.327-5.343 2.7-5.343 2.7" fill="#452c25"/>
    <path d="M290.342 192.45l7.208-4.155-.42-.82s-13.722 5.876-14.76 6.204c-1.038.327-5.343 2.7-5.343 2.7s4.337-.471 6.06-1.601c0 0-.352.527 1.866-.339 0 0-1.078 1.946 5.389-1.99z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M308.526 201.927s-.425 3.438.085 3.565c.51.127 1.953-.955 2.548-3.056.594-2.1-2.335-1.846-2.335-1.846l-.425 1.21" fill="#452c25"/>
    <path d="M308.526 201.927s-.425 3.438.085 3.565c.51.127 1.953-.955 2.548-3.056.594-2.1-2.335-1.846-2.335-1.846l-.425 1.21" fill="none" stroke="#000" stroke-width=".15584574"/>
    <path d="M310.776 203.506s-.254 2.405.085 2.636c.34.231 1.19.324 2.208-1.664 1.02-1.989.552-1.249.552-1.249l-2.293-1.248-.552 1.294" fill="#452c25"/>
    <path d="M310.776 203.39s-.254 2.52.085 2.752c.34.231 1.19.324 2.208-1.664 1.02-1.989.552-1.249.552-1.249l-2.293-1.248-.552 1.41z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M311.413 199.022s-1.868 4.069-.806 4.022c1.061-.046 2.505-3.098 2.717-3.791.212-.694-1.91-.231-1.91-.231" fill="#452c25"/>
    <path d="M311.413 199.022s-1.868 4.069-.806 4.022c1.06-.046 2.505-3.098 2.717-3.791.212-.694-1.91-.231-1.91-.231z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M311.251 198.986s-.703 4.484.3 4.104c1.004-.38 1.572-3.757 1.59-4.487.019-.732-1.89.383-1.89.383" fill="#452c25"/>
    <path d="M311.251 198.986s-.703 4.484.3 4.104c1.004-.38 1.572-3.757 1.59-4.487.019-.732-1.89.383-1.89.383z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M312.772 199.438s-1.104 3.467-.042 4.16c1.061.694 1.91-3.42 1.953-4.346.042-.924-1.91.186-1.91.186" fill="#452c25"/>
    <path d="M312.772 199.438s-1.104 3.467-.042 4.16c1.061.694 1.91-3.42 1.953-4.345.042-.925-1.91.185-1.91.185z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M308.42 198.296s-1.062 3.19-.425 3.514c1.246.633 2.264-2.208 2.378-3.283.127-1.202-1.953-.23-1.953-.23zM300.5 191.716s-2.844 2.358-2.505 2.728c.34.37 3.652-.277 5.138-1.294 1.486-1.018 2.08-1.573 2.08-1.573l-2.547-1.572-2.165 1.711z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M303.006 189.312s-1.826 1.48-1.486 1.85c.34.37 3.609-.972 4.203-1.62.595-.646.552-1.34.552-1.34l-3.27 1.11z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M304.874 189.08s-2.463 2.035-1.868 2.775c.594.74 4.034-2.543 4.5-2.96.468-.416-2.887.093-2.887.093" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M303.77 188.156l-4.968 2.358s-4.204 2.728-3.78 3.005c.425.278 3.695-.6 6.03-1.757 2.336-1.155 4.926-3.097 4.926-3.097M305.154 190.84s-5.375 4.621-4.568 5.084c.806.462 4.401-1.474 6.821-3.693" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M305.835 192.362s-3.041 5.134-2.702 5.642c.34.509 2.548-1.202 3.652-2.497 1.104-1.294 1.972-3.206 1.972-3.206" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M307.584 192.815s-3.296 6.125-2.914 6.634c.382.508 2.293.092 3.44-2.312 1.146-2.404 1.048-3.955 1.048-3.955l-1.574-.367z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M309.5 193.109s-3.664 7.56-2.942 8.3c.722.74 4.778-5.734 4.964-6.649.327-1.608-2.065-1.79-2.065-1.79M301.774 188.11s-3.312 1.572-2.632 1.895c.679.324 3.779-1.063 4.203-1.202.425-.139-1.57-.693-1.57-.693z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M302.284 188.618s-2.038 1.619-1.614 2.035c.425.416 3.525-1.249 4.12-1.757.593-.509-2.506-.278-2.506-.278z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M306.56 188.748s-2.785 3.745-1.932 3.893c.854.147 4.089-2.415 4.134-2.859.045-.443-2.202-1.034-2.202-1.034z" fill="#452c25" stroke="#000" stroke-width=".14093631"/>
    <path d="M307.804 189.59s-2.972 3.744-2.123 3.976c.85.23.934-.37.934-.37s2.335-1.942 2.505-2.45c.17-.51-1.231-1.203-1.231-1.203" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M308.739 190.468s-2.166 3.745-1.614 3.976c.552.231 1.443-.693 2.335-1.803.892-1.11-.721-2.173-.721-2.173z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M309.29 192.179s-1.274 2.358-.934 3.097c.34.74 1.571-.554 2.166-1.387.594-.832-1.232-1.71-1.232-1.71zM309.825 198.724s-.85 3.19-.213 3.514c1.247.634 2.052-2.208 2.166-3.282.127-1.203-1.953-.232-1.953-.232z" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M315.66 194.41s-.85-.278-1.487-.278c0 0-1.868-1.48-2.887-1.618-1.019-.139-.149-.162-.149-.162s-.106-2.427-.403-2.612c0 0-.17-2.497-1.741-2.682-1.571-.185-5.096.139-5.733-.092-.636-.232-2.588-1.046-6.284-.047-3.782 1.023-11.223 4.364-11.605 4.457-.382.092 8.548-1.96 11.053-3.07 0 0 2.505-.184 3.27-.415 0 0-2.845 1.386-.213.786 2.633-.602 2.038 0 2.038 0s-.254.6 1.232.277c1.486-.324 1.486 0 1.486 0s1.699.6 3.015-.185c0 0 .68 2.358 1.529 2.681 0 0 1.009 2.161 3.132 2.577 0 0 1.009.715 1.22.854l1.2.394 1.284-1.004" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M298.25 188.017s-8.492 3.052-8.195 3.884c.297.832 8.874-2.358 10.233-3.237 1.36-.878-1.91-.693-1.91-.693" fill="#452c25" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M321.76 194.598c.194.032.358.12.532.21.006.002.08.048.093.03.014-.02-.052-.078-.056-.082a1.539 1.539 0 0 0-.362-.269c-.222-.114-.438-.233-.687-.275-.05-.008.134.168.147.178.089.071.216.188.333.208M315.148 201.01c.126.241.145.52.271.761-.008-.015.02-.093.004-.124-.126-.241-.145-.52-.271-.762.007.016-.02.093-.004.125M316.302 194.47c.284-.101.585-.104.88-.139.015-.002.006-.13 0-.129-.295.035-.596.038-.88.138-.021.008.004.128 0 .13M317.996 194.331c.27-.127.555-.138.847-.138.01 0 .01-.13 0-.13-.292 0-.578.012-.847.139-.023.011.007.126 0 .13M315.048 195.83c-.035.005-.037.034-.04.066a.327.327 0 0 0 .003.088c.004.025.008-.02.009-.023-.003.024.013 0 .028-.003.016-.003.003-.13 0-.129M314.872 200.394c0 .238.003.477.037.713.004.025.01-.02.01-.023a.35.35 0 0 0-.003-.089c-.03-.2-.03-.4-.03-.601-.001-.074-.014-.074-.014 0"/>
    <path d="M316.587 213.894s-.96.743-1.069 1.093c-.108.35-.138.688-.138.688s.86-.104.754.37c0 0 .28.16.723-.869.443-1.03.862-1.473 1.176-1.443.315.03.715.335.917.55.202.215.48.375.83.275 0 0-.367-.56-.135-.683.232-.122.585-.099.585-.099s-.293-.697-.923-.798c-.63-.101-1.066-.277-.917-.55.15-.275.956-2.403.956-2.403l-1.068-1.568-.428 1.364s.14.806.145 1.013c.005.207-.85 1.97-.964 2.072-.115.102-2.404.987-2.677 1.074-.272.088-.842.765-.842.765l-.065.51s.384-.314.507-.128c0 0 .265-.335.504-.21.238.126.2.174.2.174s.424-.28.499-.416c.075-.137.095-.12.095-.12s.214-.057.526-.11" fill="#bd8759"/>
    <path d="M316.587 213.894s-.96.743-1.069 1.093c-.108.35-.138.688-.138.688s.86-.104.754.37c0 0 .28.16.723-.869.443-1.03.862-1.473 1.176-1.444.315.03.715.336.917.55.202.216.48.376.83.276 0 0-.367-.56-.135-.683.232-.122.585-.099.585-.099s-.293-.697-.923-.798c-.63-.101-1.066-.277-.917-.55.15-.275.956-2.403.956-2.403l-1.068-1.568-.428 1.364s.14.806.145 1.013c.005.207-.85 1.97-.964 2.072-.115.102-2.404.987-2.677 1.074-.272.088-.842.765-.842.765l-.065.51s.384-.314.507-.128c0 0 .265-.335.504-.21.238.126.2.174.2.174s.424-.28.499-.416c.075-.137.095-.12.095-.12s.214-.057.526-.11" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M323.117 209.17s.04 1.807.04 2.142c0 .337-.04.84-.232 1.134-.193.294-.386.462-.694.462-.31 0-1.119-.042-1.273.252-.155.294-.27.462-.27.462s.501-.294.578 0c.077.294-.192.546-.192.546s.462.084.886-.168.656-.294.849-.294c.193 0 .347.21.347.21s.116.63.077 1.008c-.039.378-.116 1.134.309 1.134 0 0 .154-.378.308-.42.154-.042.579.378.502.504 0 0 .077-.798-.078-1.26-.154-.462-.231-.966-.231-.966s1.311.462 1.427.63c.116.168.424.462.54.42.115-.042 0-.42.231-.504.232-.084.347.126.347.126s-.385-.882-.964-1.176c-.578-.294-.964-.294-1.195-.546-.232-.252-.27-.546-.309-.84-.038-.294.01-2.832.01-2.832l-.974-.191" fill="#bd8759"/>
    <path d="M323.137 209.038s.02 1.939.02 2.274c0 .337-.04.84-.232 1.134-.193.294-.386.462-.694.462-.31 0-1.119-.042-1.273.252-.154.294-.27.462-.27.462s.501-.294.578 0c.077.294-.192.546-.192.546s.462.084.886-.168.656-.294.85-.294c.192 0 .346.21.346.21s.116.63.077 1.008c-.039.378-.116 1.134.309 1.134 0 0 .154-.378.308-.42.154-.042.579.378.502.504 0 0 .077-.798-.078-1.26-.154-.462-.231-.966-.231-.966s1.311.462 1.427.63c.116.168.424.462.54.42.116-.042 0-.42.231-.504.232-.084.347.126.347.126s-.385-.882-.964-1.176c-.578-.294-.964-.294-1.195-.546-.232-.252-.27-.546-.309-.84-.038-.294.01-2.832.01-2.832l-.993-.156z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M315.069 197.32s-.128-1.287.233-1.425c0 0 .128-1.18 1.699-1.018 0 0 .53-.878 1.38-.416 0 0 .806-.393 1.274-.162.467.232 1.04.625 1.082.717 0 0 .68-.139.998.116.319.254.17 1.086.17 1.086s.764.585.828 1.102c.064.517.064.725-.107.863 0 0 .34.278.277.67-.064.394-.383.964-.446.964-.064 0 .042 1.025-.297 1.372-.34.347-.659.416-.807.462-.149.046-.53.601-.892.67-.36.07-.849-.554-.891-.74-.043-.184-.531-.392-.531-.392s-1.104 1.179-1.847.994c-.743-.185-1.104-.81-1.168-.925-.064-.116-.276-.97-.276-.97s-.828-.487-.764-.949c.063-.462.403-1.017.403-1.017l-.318-1.002z" fill="#dcddde"/>
    <path d="M317.828 211.941c-.1.018-.17.003-.236-.043M318.196 211.008a.801.801 0 0 1-.317-.113M317.816 211.487c.071.04.154.056.237.043M317.032 212.948c.045.031.1.051.158.056M323.619 212.068c.157.028.3.112.463.126M323.889 211.606c-.125.031-.231.007-.308-.084M323.966 211.06a.867.867 0 0 1-.424-.042M323.426 212.908c.147.02.283.092.424.126M323.465 213.496a.216.216 0 0 1 .193.042M323.58 215.092a.4.4 0 0 1 .27.042M318.76 213.029a.34.34 0 0 0-.27.211M319.545 213.103c-.169.098-.32.235-.341.432M314.668 213.97c.131.118.325.188.439.298M325.007 213.328a.352.352 0 0 0-.192.252M325.547 213.496a.185.185 0 0 0-.115.168" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M313.45 215.074l.551-.142.053.021s-.36.697-.263.866c.099.17-.492-.306-.34-.745" fill="#d9c0b9"/>
    <path d="M313.45 215.074l.551-.142.053.021s-.36.697-.263.866c.099.17-.492-.306-.34-.745z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M315.545 215.486s-.609 1.017.181 1.526c0 0-.049-.949.57-1.153l-.75-.373z" fill="#d9c0b9"/>
    <path d="M315.545 215.486s-.609 1.017.181 1.526c0 0-.049-.949.57-1.153l-.75-.373z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M319.587 214.537l-.184-.55.214-.288.432-.005.183.086s.348.793.044 1.21c0 0-.129-.448-.276-.51 0 0-.322-.05-.413.057" fill="#d9c0b9"/>
    <path d="M319.587 214.537l-.184-.55.214-.288.432-.005.183.086s.348.793.044 1.21c0 0-.129-.448-.276-.51 0 0-.322-.05-.413.057z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M320.938 213.374h.362l.137.338-.051.312-.312.144s-.515-.131-.389.482c0 0-.241-1.126.253-1.276" fill="#d9c0b9"/>
    <path d="M320.938 213.374h.362l.137.338-.051.312-.312.144s-.515-.131-.389.482c0 0-.241-1.126.253-1.276z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M323.492 215.893l.374-.412.401.212s-.166 1.088-.23 1.17c-.062.08-.074.093-.074.124s-.063-.206-.184-.344c-.12-.137-.246-.737-.287-.75" fill="#d9c0b9"/>
    <path d="M323.492 215.893l.374-.412.401.212s-.166 1.088-.23 1.17c-.062.08-.074.093-.074.124s-.063-.206-.184-.344c-.12-.137-.246-.737-.287-.75z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M325.926 214.8s.603.75.528.994c0 0 .62-.626-.178-1.513l-.35.519z" fill="#d9c0b9"/>
    <path d="M325.926 214.8s.603.75.528.994c0 0 .62-.626-.178-1.513l-.35.519z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M323.282 195.889c.12.034.194.122.235.24.003.005.008-.04.008-.045 0-.024.003-.056-.005-.08-.04-.119-.116-.209-.238-.244.002 0-.02.124 0 .129M323.417 197.966c.188.197.328.426.405.69-.006-.021.015-.089.004-.125a1.697 1.697 0 0 0-.409-.694c.016.016-.024.103 0 .129M322.977 196.443c.184.159.34.345.508.519-.015-.016.025-.104 0-.13-.169-.173-.324-.36-.508-.518.014.012-.025.107 0 .129M323.482 199.895c.053.311-.018.577-.134.852-.014.034.011.106.004.124.147-.352.201-.71.136-1.087a.433.433 0 0 0-.006.111M323.956 200.969c.024.16.029.317 0 .477a.402.402 0 0 0-.002.088c0 .004.004.048.008.023a2.158 2.158 0 0 0 0-.7c-.003-.025-.008.02-.008.023a.357.357 0 0 0 .002.089M325.787 204.748c.121.433.13.876.131 1.323 0 .073.013.073.013 0 0-.489-.008-.974-.14-1.447.006.021-.014.088-.004.124M325.483 205.628c-.1.305-.201.61-.34.9-.015.032.012.108.004.124a7.36 7.36 0 0 0 .339-.9c.011-.035-.01-.104-.004-.124M315.251 202.708c.284.37.437.812.677 1.21-.008-.014.02-.095.003-.125-.24-.399-.394-.843-.68-1.214.016.02-.023.1 0 .13M317.047 202.752c-.258.034-.322-.338-.542-.415.004.001-.02.122 0 .129.22.077.284.45.542.415.015-.002.004-.13 0-.129M316.03 204.471c.185.326.307.686.507 1.004-.008-.013.022-.095.004-.125-.2-.317-.323-.677-.509-1.003.008.014-.02.094-.003.124M317.217 204.89c.209.25.398.515.506.828-.006-.02.016-.09.004-.125-.11-.315-.3-.58-.51-.833.015.019-.024.1 0 .13M317.113 206.133c.106.217.2.44.305.657-.007-.016.02-.092.004-.124-.106-.218-.2-.44-.305-.658.007.017-.02.093-.004.125M318.232 206.375c.06.228.095.463.097.7 0 .073.013.073.012 0a3.29 3.29 0 0 0-.106-.824c.006.022-.013.088-.003.124M317.756 207.407c.073.329.17.65.34.942-.008-.014.02-.095.004-.125-.169-.287-.265-.604-.338-.928a.35.35 0 0 0-.006.111M318.03 203.02c.268.102.536.203.813.277 0 0 .019-.124 0-.13a8.087 8.087 0 0 1-.813-.276c.005.002-.021.12 0 .129M318.233 204.162c.32.178.656.32.983.485-.008-.004.023-.117 0-.13-.327-.164-.663-.306-.983-.484.01.005-.024.116 0 .13M318.538 205.443c.165.2.345.385.542.554-.014-.012.025-.107 0-.13a4.468 4.468 0 0 1-.542-.553c.016.02-.023.1 0 .129M318.875 207.033c.138.22.202.463.236.72.003.025.008-.02.009-.023a.348.348 0 0 0-.003-.088 1.79 1.79 0 0 0-.238-.734c.007.013-.022.096-.004.125M319.822 203.08c.03.153.066.305.103.457-.005-.023.013-.088.004-.125a10.11 10.11 0 0 1-.1-.443c-.005-.025-.008.019-.008.023a.308.308 0 0 0 .002.088M322.196 202.131a3.573 3.573 0 0 1-.237.485c-.018.03.01.11.003.125.09-.157.17-.317.237-.485.014-.034-.01-.106-.003-.125M323.174 202.09c0 .272-.005.543-.065.81a.382.382 0 0 0 .006.111c.069-.302.071-.612.072-.921 0-.074-.013-.074-.013 0M323.379 203.59c.025.233 0 .463-.033.694a.589.589 0 0 0 .007.112c.04-.3.066-.597.035-.898-.006-.05-.011.075-.01.091M324.125 205.011c-.044.322-.238.578-.47.788-.026.022.014.116 0 .129.238-.214.432-.477.477-.805a.55.55 0 0 0-.006-.112M321.721 203.62c-.067.134-.155.255-.237.38-.011.018-.005.061-.004.08 0 .004.003.052.007.045.083-.125.17-.246.237-.38.01-.02.005-.059.005-.08 0-.005-.004-.051-.008-.045M321.994 205.522a1.89 1.89 0 0 1-.339.38c-.025.02.014.119 0 .13.135-.107.238-.243.34-.381.01-.015.005-.048.005-.065 0-.007-.008-.061-.006-.064M321.147 206.525c-.33-.327-.486-.747-.337-1.223.011-.036-.01-.104-.004-.124-.166.529-.056 1.084.341 1.477-.015-.016.025-.105 0-.13M320.026 205.469c.025.196.024.386-.032.574-.011.036.009.104.003.125.079-.263.07-.541.035-.81-.003-.025-.008.019-.008.023a.36.36 0 0 0 .002.088M322.602 206.735c-.07.235-.196.441-.305.658-.016.032.012.109.004.125.108-.217.235-.423.305-.658.01-.036-.01-.104-.004-.125M324.263 207.428c-.144.252-.309.49-.475.727-.019.027.012.113.004.124.166-.237.33-.474.474-.727.017-.03-.012-.11-.003-.124M325.074 208.3c-.07.443-.104.89-.138 1.338-.002.021.006.145.011.065.033-.432.067-.864.133-1.292a.5.5 0 0 0-.006-.111M326.122 206.746c.002.15.031.3.034.45.002.09.013.006.012-.035-.003-.15-.032-.3-.034-.45-.002-.09-.013-.006-.012.035M314.977 201.861a2.74 2.74 0 0 0 .137 1.295c-.006-.02.016-.09.004-.124-.12-.348-.188-.678-.134-1.06a.587.587 0 0 0-.006-.11"/>
    <path d="M318.817 196.346c.716-.657 1.66-1.348 2.676-1.106 0 0 .017-.125 0-.13-1.015-.241-1.96.45-2.676 1.107-.025.023.015.115 0 .129M322.257 197.128c-.718-.488-1.972-.667-2.675-.065-.026.022.014.117 0 .13.703-.603 1.957-.424 2.675.064-.012-.008.025-.112 0-.129M319.516 198.23c.294.953.675 1.923.508 2.941a.5.5 0 0 0 .007.112c.18-1.104-.19-2.138-.512-3.177.007.021-.014.09-.003.125M318.368 198.81c.176.963.064 1.936-.002 2.905-.002.021.006.144.01.065.07-1.027.185-2.06-.002-3.081a.42.42 0 0 0-.006.11M319.9 198.103c.936.222 1.851.846 2.355 1.69-.008-.014.021-.095.004-.125-.506-.846-1.42-1.471-2.359-1.694 0 0-.018.125 0 .129M319.066 199.405c.003.592.05 1.185-.123 1.76-.01.036.01.103.003.124.185-.615.136-1.25.133-1.884 0-.073-.013-.074-.013 0M320.537 199.274c.588.486 1.054 1.22 1.142 1.998.006.051.01-.074.008-.09-.09-.795-.548-1.539-1.15-2.037.013.012-.025.108 0 .13M316.207 200.899c.195-.278.437-.595.763-.714.021-.008-.004-.127 0-.13-.329.12-.57.44-.766.719-.019.028.012.113.004.125M317.609 195.836c-.124-.53-.522-.842-.947-1.126.01.006-.023.1 0 .114.422.283.82.596.943 1.123-.004-.02.012-.078.004-.111M316.78 195.827c-.49-.233-.998-.26-1.53-.26-.009 0-.009.128 0 .128.532 0 1.04.028 1.53.26-.008-.003.022-.117 0-.128M318.31 195.745c.066-.509-.01-.989-.448-1.303.011.008-.022.098 0 .113.368.264.502.63.443 1.092a.472.472 0 0 0 .006.098M320.155 196.346c.46-.245.962-.199 1.465-.195.009 0 .01-.13 0-.13-.503-.003-1.005-.049-1.465.196-.024.013.009.124 0 .129M320.983 197.582c.71.075 1.253.44 1.72.976-.032-.036.048-.203 0-.258-.468-.536-1.01-.901-1.72-.976-.01-.002-.027.255 0 .258M317.859 199.54c-.168.68-.04 1.54-.825 1.818-.042.015.008.255 0 .258.795-.282.662-1.14.831-1.826.019-.074-.017-.205-.006-.25" fill="#fff"/>
    <path d="M319.515 199.009c.272.714.653 1.444.506 2.237-.01.05.009.245.013.223.18-.977-.17-1.812-.513-2.709.015.038-.033.18-.006.249M320.537 198.883c.693.088 1.303.705 1.395 1.414.006.05.017-.039.017-.046a.938.938 0 0 0-.005-.177c-.093-.721-.697-1.359-1.407-1.45-.01 0-.029.256 0 .26M320.53 199.907c.11.593.278 1.187.067 1.781-.024.07.021.21.007.249.262-.74.076-1.507-.06-2.253a.915.915 0 0 0-.013.223M320.155 197.322c.762-.13 1.569-.244 2.229.26-.026-.02.05-.22 0-.258-.66-.504-1.467-.39-2.23-.26-.03.005-.005.258 0 .258M318.76 195.807c.092-.63.647-1.08 1.204-1.284.042-.015-.009-.255 0-.258-.575.21-1.123.673-1.217 1.32a.918.918 0 0 0-.005.176c0 .008.01.096.018.046M318.064 196.186c-.019-.699-.325-1.262-.626-1.873.014.029-.034.164-.006.22.27.548.594 1.083.61 1.714.005.16.024.012.022-.061M317.422 196.56c-.136-.622-.546-1.034-1.089-1.319.018.01-.047.234 0 .259.524.274.944.678 1.076 1.283a.762.762 0 0 0 .013-.223M316.397 196.311c-.356-.248-.73-.481-1.163-.548-.006 0-.023.198 0 .202.433.066.807.3 1.163.547-.019-.013.04-.174 0-.2M318.617 199.236c.061.661.144 1.329-.185 1.932-.034.061.023.22.007.249.393-.723.269-1.577.196-2.364-.009-.1-.02.15-.017.183" fill="#fff"/>
    <path d="M319.387 198.879c.221.621.482 1.283.438 1.957-.002.043.012.29.022.13.054-.821-.181-1.572-.453-2.336.014.039-.031.179-.007.249M319.9 197.972c.632.194 1.26.401 1.72.911-.032-.034.048-.204 0-.258-.46-.51-1.088-.717-1.72-.911.005.001-.04.246 0 .258M318.19 196.568c.066-.55.125-1.098.13-1.653.003-.147-.023-.147-.025 0-.005.493-.064.982-.124 1.47-.004.034.006.284.019.183M317.034 196.217c-.467-.212-.882-.515-1.274-.845.028.023-.05.215 0 .258.392.33.807.634 1.274.846-.014-.007.045-.238 0-.259M316.843 199.536c-.3.19-.507.454-.64.786-.019.044-.01.112-.01.158 0 .01.012.102.016.09.133-.327.338-.589.633-.776.05-.031-.022-.244 0-.258M317.659 200.021c-.012.453-.261.828-.562 1.142-.049.051.031.226 0 .258.362-.378.572-.799.586-1.331.002-.082-.02-.25-.024-.069M319.797 196.458c.376-.594 1.284-.643 1.891-.809.038-.01-.002-.257 0-.258-.614.168-1.518.218-1.898.818-.037.058.023.224.007.25M315.62 196.448a2.073 2.073 0 0 1-.412-.192c.011.006-.03.142 0 .16.133.074.266.149.411.192-.002 0 .024-.152 0-.16M315.702 199.487c-.131.054-.267.108-.357.224-.022.028-.013.098-.013.13 0 .03 0 .06.004.09 0-.004.006.042.009.039.09-.116.226-.171.357-.225.02-.008.012-.113.012-.129a.732.732 0 0 0-.012-.13M316.251 199.627c-.27.12-.466.354-.634.593-.03.042.017.178.005.195.167-.236.363-.466.63-.585.035-.016-.012-.198 0-.203M317.291 199.884c-.235.574-.877 1.207-.411 1.849-.017-.022.045-.196.006-.25-.322-.444.245-.943.412-1.35.027-.068-.022-.212-.007-.249M318.024 199.875c.018.584-.085 1.161-.187 1.734a.895.895 0 0 0 .013.223c.12-.672.219-1.342.198-2.026-.006-.18-.027-.014-.024.069M319.904 199.184c.425.558.803 1.158.984 1.847-.01-.044.027-.176.007-.249-.183-.693-.563-1.295-.992-1.856.032.041-.046.198 0 .258M320.123 198.735c.7.534 1.48 1.036 1.974 1.79-.017-.024.043-.191.006-.248-.496-.76-1.276-1.264-1.98-1.8.026.02-.05.22 0 .258" fill="#fff"/>
    <path d="M320.535 197.813c.261-.025.504-.015.752.076.222.082.406.266.608.388.204.122.41.233.575.409.218.228.22.618.284.913.004.02.02-.148.01-.19-.051-.242-.079-.486-.161-.72-.08-.228-.278-.368-.47-.491-.23-.148-.457-.297-.685-.447-.271-.178-.603-.188-.913-.158-.022.002-.01.221 0 .22" fill="#fff"/>
    <path d="M322.25 199.248c.077.226.168.447.245.673-.009-.026.02-.116.005-.162-.078-.226-.168-.447-.246-.673.01.026-.02.117-.004.162M320.288 195.677c.493-.135.998-.225 1.483-.393.041-.014-.007-.255 0-.258-.485.168-.99.257-1.483.393-.038.01.002.258 0 .258M318.232 195.925c.069-.262.18-.475.33-.698.183-.274.21-.618.435-.869.048-.054-.031-.223 0-.258-.186.206-.25.472-.357.723-.128.302-.329.525-.415.853-.02.073.018.205.007.249M316.547 200.543c-.088.425-.072.868.085 1.274-.015-.038.033-.18.007-.25a1.365 1.365 0 0 1-.08-.801c.012-.056.008-.12.005-.177 0-.009-.008-.095-.017-.046M317.57 201.346c-.177.241-.18.608-.087.884a.358.358 0 0 0 .015-.085c.003-.046.007-.104-.008-.149-.052-.154-.013-.28.08-.407.019-.027.011-.09.011-.121 0-.014-.017-.115-.012-.122M318.33 200.938c-.009.245-.14.477-.242.692-.022.048.017.156.006.18.126-.267.243-.522.253-.823.002-.059-.013-.18-.018-.049M318.353 199.504v.308c0 .01-.003.13.012.13s.013-.12.013-.13v-.308c0-.01.003-.13-.013-.13-.014 0-.012.121-.012.13M318.005 199.685c.02.036.036.072.05.112.008.054.012.057.012.008a.77.77 0 0 0-.014.24c0 .01.008.097.017.047.04-.211.048-.463-.058-.656-.006-.013-.015.08-.015.09 0 .041-.013.12.009.159M317.51 199.52c-.05.16-.084.323-.112.49a.796.796 0 0 0-.004.176c0 .008.009.096.017.047.026-.158.058-.312.107-.464.015-.048.01-.11.009-.159 0-.011-.013-.1-.016-.09M317.13 199.543c-.23.283-.539.47-.851.645-.049.028.019.248 0 .259.312-.175.62-.363.851-.646.021-.026.013-.097.013-.129 0-.013-.017-.124-.013-.129M316.47 199.655a1.58 1.58 0 0 1-.466.253c-.042.014.007.255 0 .258a1.59 1.59 0 0 0 .467-.252c.022-.018.013-.106.013-.13 0-.008-.012-.13-.013-.129M315.537 199.426c-.136.06-.276.113-.412.173-.036.016.01.203 0 .208.136-.06.276-.113.412-.174.036-.016-.01-.203 0-.207M318.38 199.314c.004.168.03.332.062.498.01.049.017-.038.017-.047a.768.768 0 0 0-.004-.176c-.023-.114-.048-.228-.05-.344 0-.011-.012-.1-.015-.09-.017.047-.01.11-.01.159M318.82 199.111c.002.198.021.388.092.574.003.01.015-.08.015-.09.002-.048.008-.114-.009-.16a.96.96 0 0 1-.073-.324c0-.009.002-.129-.013-.129-.014 0-.012.12-.012.13M319.158 199.067c.06.083.126.197.129.303 0 .01.012.1.015.09.017-.047.01-.11.01-.159a.833.833 0 0 0-.147-.483c-.009-.013-.015.081-.015.09 0 .037-.014.127.009.16M319.574 198.904c.145.031.276.09.384.196-.002-.002.013-.119.013-.13 0-.026.01-.107-.013-.128a.763.763 0 0 0-.384-.197c-.002 0-.034.251 0 .259M319.794 198.567c.17.142.357.247.576.28.007.002.03-.253 0-.258a1.17 1.17 0 0 1-.576-.28l-.013.129c0 .024-.01.11.013.129M320.26 197.445c.15.001.294.026.44.056.003 0 .033-.252 0-.259a2.316 2.316 0 0 0-.44-.056c-.018 0-.02.259 0 .259M320.044 196.907c.09-.146.224-.254.354-.36.022-.018.012-.105.012-.13l-.012-.128c-.134.109-.27.22-.36.369-.023.036-.01.12-.01.159 0 .008.008.102.016.09M319.635 196.39a.973.973 0 0 1 .186-.46c.021-.028.013-.097.013-.13 0-.013-.018-.123-.013-.128a1.044 1.044 0 0 0-.203.67c0 .008.01.097.017.047M319.303 196.15c.169-.239.282-.511.439-.758.022-.035.01-.12.009-.159 0-.009-.008-.103-.016-.09-.156.247-.27.52-.44.758-.022.032-.009.122-.008.158 0 .01.007.103.016.09" fill="#fff"/>
    <path d="M318.545 195.302c.007.29.027.58.088.863.011.048.017-.037.018-.047a.673.673 0 0 0-.004-.176 3.656 3.656 0 0 1-.077-.709c-.004-.18-.026-.013-.025.069M317.319 195.504c.044.105.102.204.154.305.033.064.063.128.088.195a.94.94 0 0 1 .025.077c-.016-.053.012-.039-.02.016-.035.06.023.221.007.249.074-.128.062-.336.029-.477-.053-.222-.189-.406-.277-.614.015.036-.035.182-.006.25M317.075 196.008a4.51 4.51 0 0 0-.55-.449 1.69 1.69 0 0 0-.012.13c0 .022-.01.113.013.128.19.14.38.28.549.45-.003-.002.012-.12.012-.13 0-.027.01-.107-.012-.129M317.878 196.277c.113-.567.092-1.134-.14-1.667.015.035-.036.182-.008.249.17.388.22.769.135 1.195-.012.056-.008.12-.005.176 0 .009.008.096.017.047M319.08 195.537c.018 0 .018-.258 0-.258-.02 0-.02.258 0 .258" fill="#fff"/>
    <path d="M317.72 196.302c.03-.32.105-.634.137-.954.01-.09.009-.183 0-.273-.006-.066-.02-.066-.027 0-.032.32-.107.633-.137.953-.008.09-.009.184 0 .274.006.065.02.066.027 0M321.194 198.295c.278.09.552.28.681.554.009.018.023-.121.024-.136.002-.066.016-.175-.013-.238-.133-.28-.408-.474-.692-.568.01.003-.06.368 0 .388M320.255 200.224c.05.165.054.317.023.487a1.166 1.166 0 0 0-.007.265c0 .013.013.144.027.07.07-.386.081-.818-.032-1.196a.667.667 0 0 0-.024.135c-.002.076-.009.165.013.239M319.472 199.7c0 .32.02.64-.045.955-.018.084-.012.18-.007.265 0 .013.011.143.026.07.087-.425.066-.859.064-1.29 0-.013.004-.193-.018-.193s-.02.18-.02.193M319.074 200.046c-.11.426-.22.851-.306 1.282-.017.085-.012.18-.007.266 0 .013.011.142.026.07.084-.42.19-.832.298-1.244.028-.11-.028-.308-.01-.374M319.61 200.318c.003.365.046.733.005 1.097-.01.09-.008.184 0 .274.007.066.02.065.027 0 .051-.459.01-.912.006-1.371 0-.014.003-.194-.019-.194s-.02.18-.02.194M317.038 200.513c.008-.115.006-.138-.003-.07a.595.595 0 0 1-.075.193c-.032.057-.015.176-.013.238 0 .013.013.154.023.135.135-.243.125-.562.087-.83-.01-.075-.025.057-.026.069-.005.088-.005.178.007.265M318.168 199.747c.075.145.07.271.069.43 0 .013-.003.194.019.194.021 0 .018-.18.018-.194.003-.267.03-.56-.096-.804-.01-.018-.023.122-.023.136-.003.064-.017.178.013.238M318.475 199.614a.368.368 0 0 1 .136.182c.009.023.012.093.01.012 0 .038.003.078.01.116-.001-.002.005.043.014.02.025-.071.015-.166.013-.239-.003-.189-.03-.357-.183-.479-.006-.005-.013.063-.013.057a1.366 1.366 0 0 0-.006.137c0 .045 0 .091.006.137.002.011.002.047.013.057M318.855 199.27c.061.123.131.241.192.365.01.02.014-.017.015-.02a.973.973 0 0 0 .008-.116c0-.05 0-.1-.002-.15-.002-.024 0-.065-.011-.088-.062-.123-.132-.242-.192-.365-.01-.02-.014.017-.015.02a.923.923 0 0 0-.009.116c0 .05 0 .1.003.149.002.024 0 .066.01.089M319.706 198.961c.15.214.305.424.472.624-.006-.006.019-.174.019-.193 0-.047.013-.155-.02-.194a9.326 9.326 0 0 1-.461-.61c-.013-.02-.023.122-.023.135-.003.055-.021.19.013.238M320.145 198.232c.114.2.246.389.39.568-.006-.008.019-.175.019-.194 0-.047.013-.154-.02-.194a4.119 4.119 0 0 1-.379-.554c-.01-.019-.023.122-.023.136-.002.06-.019.181.014.238M315.867 196.28a1.715 1.715 0 0 0-.412-.252 1.118 1.118 0 0 0-.02.193c0 .025-.01.181.02.194.15.063.285.15.412.253 0 0 .018-.181.018-.194 0-.036.015-.167-.018-.194M317.207 195.819a.489.489 0 0 1 .042.117.74.74 0 0 0 .009.116c0-.002.006.043.015.02.025-.071.014-.166.013-.239a1.01 1.01 0 0 0-.068-.388c-.01-.021-.015.019-.015.02a.88.88 0 0 0-.008.116c-.003.068-.016.173.013.238M318.265 194.814c.006.255-.02.522-.151.744-.033.055-.016.178-.014.238 0 .013.012.154.024.135.205-.349.188-.827.178-1.22a.562.562 0 0 0-.024-.135c-.025.07-.015.165-.013.238M318.503 196.247c.225-.151.379-.383.5-.625.03-.06.014-.173.012-.238 0-.013-.014-.153-.023-.135-.118.237-.268.462-.49.61-.032.022-.018.162-.018.194 0 .011.015.196.019.194M319.162 196.219c.077-.08.142-.172.22-.253.011-.012.011-.042.013-.056.004-.046.006-.092.006-.137 0-.046 0-.092-.006-.137 0 .006-.008-.062-.014-.057-.077.08-.142.172-.22.252-.011.013-.011.042-.013.057a1.365 1.365 0 0 0-.005.137c0 .046 0 .092.005.137 0-.006.009.062.014.057M320.15 196.392a7.723 7.723 0 0 1-.74-.028c-.018-.002-.041.384 0 .388.246.024.493.027.74.028.028 0 .03-.388 0-.388M319.93 197.285c.33.197.65.412.99.589-.027-.014.07-.35 0-.387-.34-.178-.66-.392-.99-.59.032.02-.073.343 0 .388" fill="#fff"/>
    <path d="M319.958 197.958c.294.187.614.33.88.562 0 0 .018-.18.018-.194 0-.038.014-.165-.019-.194-.265-.23-.585-.374-.879-.561a1.975 1.975 0 0 0-.019.194c0 .03-.013.172.02.193M318.069 196.548c.042-.141.041-.293.041-.439 0-.013.003-.193-.019-.193s-.02.18-.02.193c0 .053 0 .008.003 0a.352.352 0 0 1-.014.066c-.022.073-.015.162-.014.238.003.038.003.078.01.116 0-.005.006.045.014.02M315.757 195.517c.257.316.584.545.902.792.235.183.409.495.663.64-.03-.017.073-.347 0-.388-.229-.13-.382-.406-.585-.577-.335-.281-.7-.51-.98-.854.047.058-.071.3 0 .387M316.306 196.56a7.673 7.673 0 0 1-.44-.504c-.004-.005-.014.063-.013.057a1.516 1.516 0 0 0-.006.136c0 .047-.013.155.02.194.14.174.283.345.439.505-.003-.003.019-.177.019-.194 0-.042.014-.16-.019-.193M315.999 199.12c-.104.189-.191.386-.325.555-.032.04-.019.146-.019.194 0 .02.026.185.02.193.136-.173.227-.375.334-.568.031-.057.015-.176.013-.238 0-.013-.013-.154-.023-.135M316.287 199.509c-.004.215-.078.43-.105.643a1.28 1.28 0 0 0 0 .275c.007.066.02.063.027 0 .034-.276.11-.536.115-.815.003-.073.012-.168-.013-.239a.593.593 0 0 0-.023.136M317.3 201.29c.183-.613.163-1.26.15-1.894-.005-.272-.039-.02-.037.103.01.48.015.954-.124 1.417-.032.108.03.31.01.374M317.894 199.803c.07.213.084.406.05.628a1.259 1.259 0 0 0-.006.265c0 .011.014.144.026.07.066-.44.08-.911-.06-1.337a.622.622 0 0 0-.023.136c-.003.073-.01.166.013.238M318.475 199.585c.214.262.279.61.258.943-.003.063.018.434.033.196.03-.505.044-1.116-.29-1.526.005.007-.02.174-.02.194 0 .046-.013.154.02.193" fill="#fff"/>
    <path d="M318.635 199.326c.134.557.237 1.119.294 1.69.015.151.031-.224.027-.274-.06-.605-.169-1.2-.31-1.79.015.068-.037.264-.01.374M319.184 199.185c.244.337.457.691.577 1.095a.66.66 0 0 0 .023-.136c.003-.075.009-.164-.013-.238-.12-.403-.333-.758-.577-1.094.024.033-.068.293-.01.373M320.32 198.251c-.113-.196-.352-.22-.554-.203-.038.003-.018.389 0 .387.19-.016.436.002.544.19.01.018.023-.122.024-.136.002-.06.018-.182-.014-.238" fill="#fff"/>
    <path d="M319.738 198.098a7.46 7.46 0 0 1 1.071-.056c.03 0 .03-.387 0-.387-.358 0-.715.004-1.07.056-.045.006-.012.39 0 .387M320.398 195.158c-.285.34-.436.803-.532 1.233-.018.083-.011.18-.006.265 0 .014.01.142.026.07.09-.41.242-.858.512-1.18.032-.04.019-.148.019-.194 0-.02-.025-.187-.02-.194M318.85 195.491c.053.327.188.629.28.945a.674.674 0 0 0 .022-.135c.003-.076.009-.165-.013-.238-.087-.303-.219-.593-.27-.906-.012-.075-.025.057-.026.07-.005.086-.007.177.007.264M318.558 196.056a1.549 1.549 0 0 1-.297-.526c-.008-.026-.016.023-.015.019a.884.884 0 0 0-.008.116c-.003.075-.01.165.013.238.06.204.175.377.307.54.004.005.013-.063.013-.057.005-.045.006-.091.006-.137 0-.047.013-.153-.02-.193M318.036 196.308c-.43-.14-.844-.311-1.209-.589.04.03-.075.33 0 .387.365.278.778.45 1.209.59-.009-.003.06-.368 0-.388" fill="#fff"/>
    <path d="M318.51 196.2s-1.708.292-2.159.395c-.45.104-1.47.207-1.826.207-.356 0-.902-.284-1.282-.362-.38-.077-1.613-.206-2.064.259-.45.465-.735.62-.877.774-.143.155-.714.662-.737.887-.022.224.144.56.428.56.285 0 .883.606.882.732-.002.212.782.439 1.52.435 1.33-.008 1.985-.679 3.98-.444 1.086.128 3.084-.723 3.44-1.24.356-.516.57-1.059.214-1.653-.356-.594-1.423-.533-1.519-.55" fill="#dba05f"/>
    <path d="M318.51 196.2s-1.708.292-2.159.395c-.45.104-1.47.207-1.826.207-.356 0-.902-.284-1.282-.362-.38-.077-1.613-.206-2.064.259-.45.465-.735.62-.877.774-.143.155-.714.662-.737.887-.022.224.144.56.428.56.285 0 .883.606.882.732-.002.212.782.439 1.52.435 1.33-.008 1.985-.679 3.98-.444 1.086.128 3.084-.723 3.44-1.24.356-.516.57-1.059.214-1.653-.356-.594-1.423-.533-1.519-.55z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M311.358 198.116s-.005.037-.012-.021l-.01-.046-.012-.031-.003-.035c-.007-.012.036.008.174-.127 0 .002.042-.065.117-.178a.839.839 0 0 1 .43-.28c.26-.061.502-.017.535-.015.011-.015-.59-.021-.63.049.003-.047.58-.067.611-.027.008-.013-.545-.018-.586.058 0-.034.501-.058.556-.012 0 0-.093-.01-.119-.008l.044.007h-.07l-.041-.004a.5.5 0 0 1 .117.023c0 .001.05-.014-.147-.004 0 0-.12-.022-.316.04-.257.038-.249.387-.638.61"/>
    <path d="M311.314 197.97c.023.002.19-.066.351-.363-.045-.081.604-.334.671-.253.003-.003.177 0 .279.016" fill="none" stroke="#939598" stroke-width=".00028131"/>
    <path d="M311.957 197.408c-.066-.009-.395.269-.365.285-.071.17-.256.282-.278.277M311.985 197.448c0-.022.446-.064.534-.031" fill="none" stroke="#000" stroke-width=".00028131"/>
    <path d="M311.902 197.567c-.012-.047.464-.111.509-.079" fill="none" stroke="#939598" stroke-width=".00028131"/>
    <path d="M312.4 197.87s.02.012-.007-.015l-.02-.023-.013-.02-.017-.015s-.015.006-.134.112c0 0-.052.04-.114.097-.002-.005-.116.096-.244.166-.136.02-.334.11-.334.144-.012-.053.392-.158.386-.165.029.02-.358.09-.365.169-.014-.048.355-.14.37-.141.019.021-.325.078-.335.152 0 .002.047-.046.066-.047l-.025.018c.012-.012.029-.014.044-.02l.025-.012s-.069.03-.067.045c.004.008-.039-.004.09-.039.002.012.325-.066.694-.406"/>
    <path d="M312.34 197.787c-.003-.005-.143.14-.29.244.002.024-.302.202-.38.18-.006-.009-.13.053-.182.116" fill="none" stroke="#939598" stroke-width=".00028131"/>
    <path d="M311.89 198.147c.023-.022.214-.152.214-.154.067-.072.235-.206.236-.206M311.897 198.18c.016.016-.284.057-.332.13" fill="none" stroke="#000" stroke-width=".00028131"/>
    <path d="M311.983 198.172c.02.039-.32.13-.334.14" fill="none" stroke="#939598" stroke-width=".00028131"/>
    <path d="M312.872 196.488s-.024-.543-.546-.75c-.522-.206-1.115-.232-1.328-.206-.214.026-.25.121-.516.164-.267.043-.552.172-.552.172s0-.026-.142.232c-.143.258-.38.388-.26.62.118.233.04.273.185.317.146.045-.072-.079-.072-.079s-.915.295-.692.855c.222.56.445.35.512.34.066-.012.517-.245.517-.245l.74-.692s.873-.34.968-.367c.095-.026.508.01.508.01l.678-.371z" fill="#c6262c"/>
    <path d="M312.872 196.488s-.024-.543-.546-.75c-.522-.206-1.115-.232-1.328-.206-.214.026-.25.121-.516.164-.267.043-.552.172-.552.172s0-.026-.142.232c-.143.258-.38.388-.26.62.118.233.04.273.185.317.146.045-.072-.079-.072-.079s-.915.295-.692.855c.222.56.445.35.512.34.066-.012.517-.245.517-.245l.74-.692s.873-.34.968-.367c.095-.026.508.01.508.01l.678-.371z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M312.209 197.76c0 .136-.137.31-.307.39-.17.08-.307.035-.307-.1s.137-.31.307-.39c.17-.08.307-.035.307.1"/>
    <path d="M308.585 200.238s-.716-1.232 1.27-1.835c0 0 .6.326.739.553 0 0-.346.578-1.478.729 0 0-.508.15-.53.553" fill="#d9c0b9"/>
    <path d="M308.585 200.238s-.716-1.232 1.27-1.835c0 0 .6.326.739.553 0 0-.346.578-1.478.729 0 0-.508.15-.53.553z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M308.793 200.037s.8.168 1.339-.227c.442-.323.6-.176.715-.15 0 0 .024-.403-.254-.704 0 0-.56.463-.976.589-.415.125-.696.084-.824.492" fill="#d9c0b9"/>
    <path d="M308.793 200.037s.8.168 1.339-.227c.442-.323.6-.176.715-.15 0 0 .024-.403-.254-.704 0 0-.56.463-.976.589-.415.125-.696.084-.824.492z" fill="none" stroke="#000" stroke-width=".13287209000000003"/>
    <path d="M311.015 199.632c.149 0 .3.002.446-.032.017-.004 0-.13 0-.13-.146.035-.297.033-.446.033-.01 0-.01.13 0 .13M310.735 197.563c-.123.001-.086.148-.16.21-.01.01-.006.052-.006.065l.006.064c.074-.061.04-.209.16-.21.01 0 .01-.129 0-.129M311.147 197.842c.062-.116.184-.164.295-.22.023-.01-.008-.124 0-.128-.113.056-.235.105-.298.223-.01.02-.005.059-.005.08 0 .004.005.051.008.045M311.681 198.555a.26.26 0 0 0 .24-.117c.01-.014.006-.048.006-.064 0-.007-.01-.062-.006-.065a.26.26 0 0 1-.24.117c-.006 0-.012.128 0 .129M312.103 198.403a.753.753 0 0 0 .378-.328c.01-.019.005-.059.005-.08 0-.004-.005-.05-.008-.045a.744.744 0 0 1-.375.324c-.022.01.006.127 0 .13M311.44 197.706c-.093.157-.27.237-.376.384-.02.027.011.114.003.125.106-.148.283-.227.376-.385.011-.018.006-.06.005-.08 0-.004-.004-.05-.008-.044M311.727 199.766c.338.018.665-.027.992-.116.018-.005-.003-.129 0-.13a3.11 3.11 0 0 1-.992.117c-.008 0-.012.129 0 .13M313.289 199.708c.442-.32.815-.595.96-1.156.009-.036-.01-.102-.004-.124-.144.556-.52.834-.956 1.15-.025.02.012.121 0 .13M313.551 198.94c.315-.188.54-.483.663-.83.013-.035-.01-.105-.003-.125a1.548 1.548 0 0 1-.66.825c-.025.015.01.123 0 .13M313.403 196.982c.241-.029.504.171.673.326-.015-.013.025-.106 0-.129-.169-.155-.432-.355-.673-.326-.014.002-.005.13 0 .13M319.665 197.958c.274-.443.124-.997-.16-1.394.008.011-.022.098-.003.125.24.335.4.758.16 1.145-.019.029.01.111.003.124M314.885 197.332c.277.2 1.04.711.831 1.131-.016.032.011.109.004.125.285-.575-.451-1.107-.834-1.385.012.008-.026.11 0 .129M317.034 199.394a1.45 1.45 0 0 0 .071-.366.44.44 0 0 0 0-.091c-.002-.022-.006-.022-.009 0a1.304 1.304 0 0 1-.065.332c-.009.023-.005.056-.005.08 0 .005.006.05.008.045" fill="#7a2e26"/>
    <path d="M317.859 199.19c.165-.027.33-.112.496-.224M309.897 198.724c-.325.2-.836.202-.938.65-.002.007 0 .03.003.028.1-.447.613-.448.935-.645.006-.004-.003-.03 0-.033" fill="#5e3f17"/>
    <path d="M309.677 197.29c.14.002.304.076.283.247a.397.397 0 0 0 0 .091c.002.023.006.022.009 0a.668.668 0 0 0-.023-.324c-.044-.11-.163-.142-.269-.144-.009 0-.01.13 0 .13M310.348 197.548a.654.654 0 0 0-.064-.58c.008.011-.022.096-.003.124.07.103.108.206.063.331-.012.035.01.105.004.125M310.89 196.974c-.034-.226-.113-.477-.321-.594.01.005-.024.115 0 .129.203.114.282.356.316.576.004.025.008-.019.008-.023a.36.36 0 0 0-.003-.088M311.368 196.858c.03-.21.012-.435-.162-.576.014.011-.026.109 0 .13.096.077.174.203.156.335a.464.464 0 0 0-.002.088c0 .003.005.048.008.023M310.569 196.15c.467-.236 1.418-.341 1.784.13-.016-.02.023-.098 0-.128-.366-.472-1.317-.367-1.784-.13-.024.011.008.125 0 .129" fill="#842116"/>
    <path d="M309.456 198.1c.106-.156.072-.367-.031-.514.007.01-.023.097-.004.124.028.039.05.078.07.122.022.054-.009.1-.038.144-.012.017-.005.06-.005.08 0 .004.004.05.008.045M314.224 199.47a.628.628 0 0 0 .289-.419.359.359 0 0 0 .002-.088c0-.004-.004-.047-.008-.023a.61.61 0 0 1-.283.4c-.025.016.011.123 0 .13M314.939 199.411a.38.38 0 0 0 .092-.252c0-.024.004-.056-.005-.08-.002-.004-.007.04-.007.046-.003.055-.046.119-.08.157-.011.012-.006.05-.006.065 0 .005.007.062.006.064M319.257 197.041a.92.92 0 0 1 .084.672.391.391 0 0 0 .007.111c.066-.295.064-.639-.088-.907.008.014-.02.094-.003.124M318.828 197.48c.085.21.079.429.054.652-.002.017.004.142.01.09.032-.294.052-.586-.06-.867.007.019-.018.09-.004.125M318.423 197.856c0 .157.002.314-.052.464-.014.035.01.105.003.124.068-.186.062-.392.062-.588 0-.073-.013-.073-.013 0" fill="#7a2e26"/>
    <path d="M324.253 210.469l.09.254s.02.05.064.091l-.135-.381-.019.036z" fill="#452c25"/>
    <path d="M314.452 194.95c-1.714.098-3.405-1.041-3.405-1.041-2.123-.416-2.247-2.336-2.247-2.336-.85-.324-1.58-2.506-1.58-2.506-1.316.786-2.964.009-2.964.009s0-.324-1.486 0-1.232-.277-1.232-.277.595-.602-2.038 0c-2.632.6.213-.786.213-.786-.764.23-3.27.416-3.27.416-.92.041-1.852.495-2.868.784-.633.18-1.715.39-2.262.624-1.46.624-4.385 1.965-6.8 2.913-3.177 1.246-5.694 2.195-5.528 2.155.198-.048 3.449-2.107 7.52-3.953 3.78-1.715 8.12-3.301 10.308-3.922 2.84-.941 5.164-.492 6.483-.013.637.23 4.15-.108 5.721.076 1.572.185 1.735 2.769 1.735 2.769.298.185.404 2.612.404 2.612s-.87.024.148.162c1.02.139 2.888 1.618 2.888 1.618.637 0 .709-.022.709-.022s.37-.467.88-.662.977-.39 1.55-.433c.573-.044 1.382-.05 1.955.016.573.065 1.25.07 2.057.222.807.152.892.195 1.146.282.255.087.483.03.483.03 1.05-.438 2.995-1.213 3.443-1.124 0 0 .734-.185 1.13-.492.394-.307 1.297-1.045 1.297-1.045s-.623-4.388 3.835-3.958c4.459.43 10.37 1.003 11.555 1.187 1.185.184 5.43 1.397 6.446 1.889 1.016.491 3.935 1.799 5.234 2.374.527.233 2.536 1.123 4.022 1.71 2.231.88 3.898 2.348 3.898 2.348-1.398-.592-2.528-1.268-3.635-1.749-.896-.389-1.832-.57-2.561-.893-1.546-.687-2.736-1.312-3.673-1.748-3.453-1.608-3.421-1.673-4.82-1.782-.964-.076.693 1.176.693 1.176s-3.16-1.23-4.176-1.66c-1.016-.43-1.862-.614-2.822-.553a5.797 5.797 0 0 1-2.257-.307c-.565-.184-3.782-.307-4.346-.369-.564-.061-1.016-.184-1.016-.184l.226.369-1.58-.246-.452.676s-1.523.307-1.637-.185c-.112-.491-.903 2.213-1.298 3.012-.395.798-2.198.534-2.649 1.025-.452.492-1.415.82-1.64.944-.226.123-.91.065-1.306.065-.587 0-.087.009-1.05.192 0 0-.834-.008-1.152-.138-.319-.13-1.02-.332-1.372-.433-.482-.137-3.163-.198-3.545-.177-.382.022-1.028.22-1.453.393-.424.174-1.168.63-1.168.738" fill="#dcddde"/>
    <path d="M314.452 194.95c-1.714.098-3.405-1.041-3.405-1.041-2.123-.416-2.247-2.336-2.247-2.336-.85-.324-1.58-2.506-1.58-2.506-1.316.786-2.964.009-2.964.009s0-.324-1.486 0-1.232-.277-1.232-.277.595-.602-2.038 0c-2.632.6.213-.786.213-.786-.764.23-3.27.416-3.27.416-.92.041-1.852.495-2.868.784-.633.18-1.715.39-2.262.624-1.46.624-4.385 1.965-6.8 2.913-3.177 1.246-5.694 2.195-5.528 2.155.198-.048 3.449-2.107 7.52-3.953 3.78-1.715 8.12-3.301 10.308-3.922 2.84-.941 5.164-.492 6.483-.013.637.23 4.15-.108 5.721.076 1.572.185 1.735 2.769 1.735 2.769.298.185.404 2.612.404 2.612s-.87.023.148.162c1.02.139 2.888 1.618 2.888 1.618.637 0 .709-.022.709-.022s.37-.467.88-.662.977-.39 1.55-.433c.573-.044 1.382-.05 1.955.016.573.065 1.25.07 2.057.222.807.152.892.195 1.146.282.255.087.483.03.483.03 1.05-.438 2.995-1.213 3.443-1.124 0 0 .734-.185 1.13-.492.394-.307 1.297-1.045 1.297-1.045s-.623-4.388 3.835-3.958c4.459.43 10.37 1.003 11.555 1.187 1.185.184 5.43 1.397 6.446 1.889 1.016.491 3.935 1.799 5.234 2.374.527.233 2.536 1.123 4.022 1.71 2.231.88 3.898 2.348 3.898 2.348-1.398-.592-2.528-1.268-3.635-1.749-.896-.389-1.832-.57-2.561-.893-1.546-.687-2.736-1.312-3.673-1.748-3.453-1.608-3.421-1.673-4.82-1.782-.964-.076.693 1.176.693 1.176s-3.16-1.23-4.176-1.66c-1.016-.43-1.862-.614-2.822-.553a5.797 5.797 0 0 1-2.257-.307c-.565-.184-3.782-.307-4.346-.369-.564-.061-1.016-.184-1.016-.184l.226.369-1.58-.246-.452.676s-1.523.307-1.637-.185c-.112-.491-.903 2.213-1.298 3.012-.395.798-2.198.534-2.649 1.025-.452.492-1.415.82-1.64.944-.226.123-.91.065-1.306.065-.587 0-.087.009-1.05.192 0 0-.834-.008-1.152-.138-.319-.13-1.02-.332-1.372-.433-.482-.138-3.163-.199-3.545-.177-.382.022-1.028.22-1.453.393-.424.174-1.168.63-1.168.738" fill="#e7e7e7" stroke="#000" stroke-width=".06460753"/>
    <path d="M314.628 194.436s-.18.138-.225.483c-.045.345-.023.208-.023.208" fill="#452c25"/>
    <path d="M323.291 193.93c.227.17.462.343.663.544.007.008.072.067.05.02a.543.543 0 0 0-.107-.142 6.537 6.537 0 0 0-.733-.606c-.042-.031.004.045.01.053a.553.553 0 0 0 .117.132" fill="#574f4c"/>
  </g>
</svg>
