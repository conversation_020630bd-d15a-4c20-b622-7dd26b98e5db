<template>
  <user-setting-template :title="$t('courses')" hide-footer>
    <v-row v-if="items.length">
      <v-col class="col-12">
        <v-expansion-panels v-model="panel" accordion>
          <course-item
            v-for="(item, idx) in items"
            :id="item.slug"
            :key="idx"
            :item="item"
            :index="idx"
            :is-active="idx === panel"
            :languages="languagesTaught"
            @scroll-to-top="scrollToTop"
          ></course-item>
        </v-expansion-panels>
      </v-col>
    </v-row>
    <v-row>
      <v-col class="col-12">
        <div :class="{ 'border-top': items.length }">
          <v-btn class="gradient font-weight-medium mt-4" @click="addCourse">
            <div class="mr-1">
              <v-img
                :src="require('~/assets/images/add-icon-gradient.svg')"
                width="20"
                height="20"
              ></v-img>
            </div>
            <div class="text--gradient">
              {{ $t('add_course') }}
            </div>
          </v-btn>
        </div>
      </v-col>
    </v-row>
  </user-setting-template>
</template>

<script>
import uniqid from 'uniqid'

import UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'
import CourseItem from '@/components/user-settings/CourseItem'
import { getSlugByString } from '~/helpers'

export default {
  name: 'CoursesInfo',
  components: { UserSettingTemplate, CourseItem },
  data() {
    return {
      getSlugByString,
      panel: undefined,
    }
  },
  computed: {
    items() {
      return this.$store.state.settings.courseItems.map((item) => ({
        ...item,
        slug: item.name ? this.getSlugByString(item.name) : item.uid,
      }))
    },
    languagesTaught() {
      return this.$store.state.settings.languagesItem?.languagesTaught ?? []
    },
  },
  watch: {
    panel() {
      const el = document.getElementById(this.items[this.panel]?.slug)

      if (el) {
        window.setTimeout(() => {
          this.goTo(el)
        }, 300)
      }
    },
  },
  beforeCreate() {
    this.$store.dispatch('settings/getCourses')
    this.$store.dispatch('settings/getLanguages')
  },
  methods: {
    addCourse() {
      const uid = uniqid()

      this.$store.commit('settings/ADD_COURSE_ITEM', uid)

      this.$nextTick(() => {
        this.panel = this.items.length - 1
      })
    },
    scrollToTop() {
      this.panel = undefined

      this.goTo()
    },
    goTo(target = 0) {
      this.$vuetify.goTo(target, {
        duration: 0,
        offset: 0,
        easing: 'linear',
      })
    },
  },
}
</script>

<style scoped lang="scss">
.border-top {
  border-top: 1px solid #ccc;
}

.v-expansion-panel {
  &::before {
    box-shadow: none;
  }
}
</style>
