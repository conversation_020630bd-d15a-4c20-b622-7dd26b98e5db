<template>
  <div></div>
</template>

<script>
export default {
  middleware({ route, redirect }) {
    const queryArrLength = Object.keys(route.query).length

    let url = `/teacher/${route.params.slug}`
    let i = 0

    if (queryArrLength > 0) {
      url += '?'

      for (const property in route.query) {
        i += 1
        url += `${property}=${route.query[property]}`

        if (i < queryArrLength) {
          url += '&'
        }
      }
    }

    return redirect(url)
  },
}
</script>
