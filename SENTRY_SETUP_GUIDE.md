# Sentry Setup Guide - langu-frontend-7b

## 🏗️ **Environment Setup**

### **1. Create Separate Sentry Projects**

You should create **3 separate projects** in your Sentry organization:

1. **`langu-frontend-dev`** (Optional - for development testing)
2. **`langu-frontend-staging`** (Required - for staging environment)
3. **`langu-frontend-prod`** (Required - for production environment)

### **2. Get DSN for Each Project**

After creating each project in Sentry:
1. Go to **Settings** → **Projects** → **[Project Name]**
2. Click on **Client Keys (DSN)**
3. Copy the DSN URL

### **3. Environment Configuration**

#### **Development (.env or .env.local)**
```bash
# Leave empty to disable Sentry in development
NUXT_ENV_SENTRY_DSN=''
NUXT_ENV_SENTRY_ENVIRONMENT='development'
```

#### **Staging (.env.staging)**
```bash
NUXT_ENV_SENTRY_DSN='https://<EMAIL>/4509759109529681'
NUXT_ENV_SENTRY_ENVIRONMENT='staging'
```

#### **Production (.env.prod)**
```bash
NUXT_ENV_SENTRY_DSN='https://<EMAIL>/4509773384515664'
NUXT_ENV_SENTRY_ENVIRONMENT='production'
```

## 🔧 **How It Works**

### **Environment Behavior**

| Environment | Sentry Status | Events Sent | Sample Rate | Logging |
|-------------|---------------|-------------|-------------|---------|
| **Development** | Disabled (unless DSN set) | Only manual tests & critical errors | 100% | Console logs |
| **Staging** | Enabled | All errors | 50% | Console + Sentry |
| **Production** | Enabled | All errors | 10% | Sentry only |

### **Automatic Error Capture**
- ✅ JavaScript errors and exceptions
- ✅ Unhandled promise rejections
- ✅ Vue.js component errors
- ✅ Network request failures
- ✅ Performance monitoring

### **Context Information**
- User information (when logged in)
- Browser and device details
- Page/route context
- Environment tags
- Custom project metadata

## 🧪 **Testing Sentry Integration**

### **Method 1: Test Page**
1. Navigate to `/sentry-test` in your browser
2. Click the test buttons to send different event types
3. Check your Sentry dashboard for events

### **Method 2: Browser Console**
```javascript
// In development (if DSN is set)
window.testSentry()

// Manual error test
this.$sentry.captureMessage('Test error from staging', 'error')

// Exception test
this.$sentry.captureException(new Error('Test exception'))
```

### **Method 3: Trigger Real Errors**
```javascript
// In browser console - will trigger unhandled error
throw new Error('Test unhandled error')

// Or cause a network error by calling non-existent API
fetch('/api/non-existent-endpoint')
```

## ✅ **Verification Checklist**

### **Staging Environment**
- [ ] Sentry project created for staging
- [ ] DSN configured in `.env.staging`
- [ ] Deploy to staging
- [ ] Visit `/sentry-test` page
- [ ] Run test events
- [ ] Check Sentry dashboard for events
- [ ] Verify environment tag shows "staging"

### **Production Environment**
- [ ] Sentry project created for production
- [ ] DSN configured in `.env.prod`
- [ ] Deploy to production
- [ ] Monitor for real errors
- [ ] Check Sentry dashboard
- [ ] Verify environment tag shows "production"

## 📊 **Monitoring Dashboard**

### **Key Metrics to Watch**
1. **Error Rate**: Percentage of sessions with errors
2. **New Issues**: Recently introduced bugs
3. **Performance**: Page load times and API response times
4. **User Impact**: How many users are affected by errors

### **Recommended Alerts**
```
1. Error rate > 5% in 5 minutes
2. New error types introduced
3. Performance degradation > 20%
4. High volume errors from specific pages
```

## 🔍 **Debugging Tips**

### **Check if Sentry is Working**
```javascript
// In browser console
console.log('Sentry available:', !!this.$sentry)
console.log('Environment:', process.env.NUXT_ENV_SENTRY_ENVIRONMENT)
console.log('DSN configured:', !!process.env.NUXT_ENV_SENTRY_DSN)
```

### **Common Issues**
1. **No events in Sentry**
   - Check DSN configuration
   - Verify environment variables
   - Check browser network tab for Sentry requests

2. **Too many events**
   - Adjust sample rates
   - Add more filtering in `beforeSend`

3. **Missing context**
   - Check user login status
   - Verify store state

## 🚀 **Deployment Commands**

### **Using Deployment Scripts (Recommended)**
```bash
# Deploy to staging
npm run deploy:staging

# Deploy to production
npm run deploy:production
```

### **Manual Deployment**
```bash
# Staging
cp .env.staging .env
npm run build
npm run start

# Production
cp .env.prod .env
npm run build
npm run start
```

## 📈 **Performance Impact**

- **Bundle Size**: ~50KB additional (gzipped)
- **Runtime Overhead**: Minimal (<1ms per event)
- **Network**: Only sends data when errors occur
- **Sample Rate**: Configurable to reduce load

## 🔐 **Security Notes**

- DSN is safe to expose (public key)
- Sensitive data is filtered before sending
- User PII handled according to privacy settings
- Source maps can be uploaded for better debugging

## 📞 **Support**

If you encounter issues:
1. Check this guide
2. Visit `/sentry-test` page for diagnostics
3. Check browser console for debug info
4. Review Sentry dashboard for event details