<template>
  <div v-if="videoId" class="video">
    <v-skeleton-loader
      v-if="!isLoaded"
      class="video-v-skeleton-loader"
      type="image"
    ></v-skeleton-loader>
    <client-only>
      <div class="video-helper">
        <iframe
          :src="`https://www.youtube.com/embed/${videoId}`"
          title="YouTube video player"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen
          @load="isLoaded = true"
        ></iframe>
      </div>
    </client-only>
  </div>
</template>

<script>
export default {
  name: 'LYoutube',
  props: {
    videoLink: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      isLoaded: false,
    }
  },
  computed: {
    videoId() {
      let videoId = null

      if (this.videoLink?.includes('v=')) {
        videoId = this.videoLink.split('v=')[1]

        const ampersandPosition = videoId.indexOf('&')

        if (ampersandPosition !== -1) {
          videoId = videoId.substring(0, ampersandPosition)
        }
      }

      return videoId
    },
  },
}
</script>

<style lang="scss" scoped>
.video {
  position: relative;
  height: 0;
  padding-bottom: 56%;
  border-radius: 8px;
  overflow: hidden;

  &-helper,
  &-v-skeleton-loader {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  &-helper {
    background-color: var(--v-greyLight-lighten2);
    z-index: 2;

    iframe {
      width: 100%;
      height: 100%;
    }
  }
}
</style>

<style lang="scss">
.video .video-v-skeleton-loader > div {
  height: 100% !important;
}
</style>
