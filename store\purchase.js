import { hashSHA256 } from './payments'

export const state = () => ({
  processId: null,
  additionalCredits: null,
  message: '',
  paymentMethods: [],
  selectedPaymentMethod: 1,
})

export const mutations = {
  SET_PROCESS_ID(state, payload) {
    state.processId = payload
  },
  SET_ADDITIONAL_CREDITS(state, payload) {
    state.additionalCredits = payload
  },
  SET_PAYMENT_METHODS(state, payload) {
    state.paymentMethods = payload
  },
  SET_MESSAGE(state, payload) {
    state.message = payload
  },
  SET_SELECTED_PAYMENT_METHOD(state, payload) {
    state.selectedPaymentMethod = payload
  },
}

export const getters = {
  additionalCredits: (state) =>
    state.additionalCredits
      ? parseFloat(state.additionalCredits.toFixed(2))
      : 0,
}

export const actions = {
  getPaymentMethods({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/payment/methods`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_PAYMENT_METHODS', data)
      })
  },
  getAdditionalCredits({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/additional-credits`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_ADDITIONAL_CREDITS', data)
      })
  },
  async scheduleLessons({ commit, rootState, dispatch, rootGetters }, slug) {
    const url = `${process.env.NUXT_ENV_API_URL}/purchase/selection/${slug}`
    const formData = new FormData()

    if (rootState.teacher_profile.selectedSlots.length) {
      rootState.teacher_profile.selectedSlots.forEach((item) => {
        item.forEach((el) => formData.append('slotsId[]', el.id))
      })
    }

    formData.append('serviceId', rootState.teacher_profile.selectedCourse.id)
    formData.append('languageId', rootState.teacher_profile.selectedLanguage.id)
    formData.append('length', rootState.teacher_profile.selectedCourse.length)

    if (rootState.teacher_profile.selectedCourse.isFreeTrialLesson) {
      formData.append(
        'isFreeTrial',
        rootState.teacher_profile.selectedCourse.isFreeTrialLesson
      )
    }

    try {
      const response = await this.$axios.post(url, formData)
      const data = JSON.parse(response.data)
      commit('SET_PROCESS_ID', data.processId)

      // Update event data with the process ID
      if (localStorage.getItem('event_data') !== null) {
        const eventData = JSON.parse(localStorage.getItem('event_data'))

        // Update transaction ID based on event type
        if (eventData.event === 'purchase') {
          eventData.ecommerce.transaction_id = data.processId
          eventData.ecommerce.items.forEach((item) => {
            item.payment_type =
              data.type === 0 ? 'trial' : data.type === 1 ? 'debit' : 'credit'
          })
        } else if (eventData.event === 'purchase_free_trial') {
          eventData.ecommerce.transaction_id_free_trial = data.processId
        } else if (eventData.event === 'purchase_paid_trial') {
          eventData.ecommerce.transaction_id_paid_trial = data.processId
          eventData.ecommerce.items.forEach((item) => {
            item.payment_type_paid_trial =
              data.type === 0 ? 'trial' : data.type === 1 ? 'debit' : 'credit'
          })
        }

        // Try to get user data from the API for the most up-to-date information
        let userData = null
        try {
          userData = await dispatch('payments/fetchUserData', null, {
            root: true,
          })
        } catch (error) {
          console.error('Error fetching user data from API:', error)
        }

        // If API call fails, fall back to store state
        if (!userData || !userData.email) {
          userData = rootGetters['user/item'] || {}
        }

        const tidioData = rootState.user.tidioData || {}

        const userEmail = tidioData.email || userData.email || ''
        const userName = `${userData.firstName || ''} ${
          userData.lastName || ''
        }`.trim()
        // Since hashSHA256 is an async function, we need to await the results
        const hashedEmail = await hashSHA256(userEmail)
        const hashedName = await hashSHA256(userName)
        if (
          ['purchase', 'purchase_paid_trial', 'purchase_free_trial'].includes(
            eventData.event
          )
        ) {
          eventData.ecommerce.items[0].user_name = hashedName
          eventData.ecommerce.items[0].email_id = hashedEmail
        }

        localStorage.setItem('event_data', JSON.stringify(eventData))
      }

      switch (data.type) {
        case 0:
          dispatch('bookFreeTrialLesson', data.processId)
          break
        case 1:
          dispatch('payment', data.processId)
          break
        case 2:
          dispatch('creditPaying', data.processId)
          break
      }
    } catch (error) {
      dispatch('loadingStop', null, { root: true })
    }
  },
  bookFreeTrialLesson({ state, commit, dispatch }, processId) {
    const url = `${process.env.NUXT_ENV_API_URL}/purchase/trial/${processId}`
    const formData = new FormData()

    formData.append('message', state.message)

    return this.$axios
      .post(url, formData)
      .then(async () => {
        await this.$cookiz.set('confirmation_page_allowed', 1, {
          domain: process.env.NUXT_ENV_COOKIE_DOMAIN,
          path: '/',
        })

        return this.$router.push('/user/lessons/confirmation')
      })
      .catch(() => dispatch('loadingStop', null, { root: true }))
  },
  payment({ state, commit, dispatch }, processId) {
    const url = `${process.env.NUXT_ENV_API_URL}/purchase/payment-method/${processId}`
    const formData = new FormData()

    formData.append('message', state.message)
    formData.append('web_payment_method_form', state.selectedPaymentMethod)

    return this.$axios
      .post(url, formData)
      .then((response) => response.data)
      .then((data) => {
        if (state.selectedPaymentMethod === 1) {
          return this.$router.push(`/checkout/stripe-form/${data.session.id}`)
        }

        if (state.selectedPaymentMethod === 2) {
          window.location.href = data.url
        }
      })
      .catch(() => dispatch('loadingStop', null, { root: true }))
  },
  creditPaying({ state, rootGetters, commit, dispatch }, processId) {
    const url = `${process.env.NUXT_ENV_API_URL}/user-credit-payin/${processId}`
    const formData = new FormData()

    formData.append('message', state.message)

    return this.$axios
      .post(url, formData)
      .then(async () => {
        if (rootGetters['teacher_profile/isSelectedTrial']) {
          await this.$cookiz.set('confirmation_page_allowed', 1, {
            domain: process.env.NUXT_ENV_COOKIE_DOMAIN,
            path: '/',
          })

          return this.$router.push('/user/lessons/confirmation')
        } else {
          await this.$cookiz.set('thank_you_page_allowed', 1, {
            domain: process.env.NUXT_ENV_COOKIE_DOMAIN,
            path: '/',
          })

          return this.$router.push('/user/lessons/thank-you')
        }
      })
      .catch(() => dispatch('loadingStop', null, { root: true }))
  },
  p24Paying({ state, commit, dispatch }, processId) {
    const url = `${process.env.NUXT_ENV_API_URL}/checkout/p24-payin/${processId}`

    return this.$axios.post(url).then((response) => response.data)
  },
}
