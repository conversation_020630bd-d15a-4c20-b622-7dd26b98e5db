<template>
  <div
    v-show="isVisible"
    id="other_cursor"
    ref="other_cursor"
    :class="['other_cursor', otherCursor.cursor]"
    :style="styles"
  >
    <div
      class="cursor-name"
      :style="{ backgroundColor: otherCursor.bgColorTooltip }"
    >
      {{ otherCursor.username }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'OtherCursor',
  data() {
    return {
      left: 0,
      top: 0,
    }
  },
  computed: {
    otherCursor() {
      return this.$store.getters['classroom/otherCursor']
    },
    otherUserRole() {
      return this.$store.getters['classroom/otherUserRole']
    },
    isVisible() {
      return !!this.otherCursor.username
    },
    zoom() {
      return this.$store.getters['classroom/zoomAsset']?.asset
    },
    styles() {
      return {
        top: `${this.top}px`,
        left: `${this.left}px`,
        backgroundImage:
          'url(' +
          require(`~/assets/images/classroom/${this.otherUserRole}-${this.otherCursor.cursor}.svg`) +
          ')',
      }
    },
  },
  watch: {
    otherCursor: {
      handler(data) {
        this.left = (data.coords.x - this.zoom.x) * this.zoom.zoomIndex
        this.top = (data.coords.y - this.zoom.y) * this.zoom.zoomIndex
      },
    },
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';

.other_cursor {
  width: 100%;
  position: absolute;
  z-index: 99998;
  background-size: contain !important;
  background-position: center !important;
  background-repeat: no-repeat !important;

  &.pointer {
    width: 22px;
    height: 23px;
  }

  &.pencil {
    width: 23px;
    height: 23px;
    margin: -22px 0 0;
  }

  &.eraser {
    width: 25px;
    height: 23px;
    margin: -20px 0 0 -10px;
  }

  .cursor-name {
    position: absolute;
    left: 35px;
    bottom: -20px;
    max-width: 180px;
    height: 20px;
    padding: 0 8px;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #fff;
    font-size: 13px;
    line-height: 20px;
    border-radius: 3px;
    font-weight: 600;
    overflow: hidden;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      left: 25px;
      bottom: -15px;
      max-width: 80px;
      height: 16px;
      font-weight: 400;
      font-size: 11px;
      line-height: 16px;
    }
  }
}
</style>
