@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.teacher-profile {
  padding-bottom: 40px;

  &-wrap {
    max-width: 1280px;
    margin: 0 auto;
    padding-bottom: 25px;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      display: flex;
    }
  }

  &-content {
    padding-top: 0;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      width: calc(100% - 315px);
    }
  }

  &-sidebar {
    width: 315px;

    &-sticky {
      position: sticky;
      top: 55px;
      width: calc(100% + 15px);
      padding-right: 17px;
      overflow: hidden;
    }

    &-helper {
      width: calc(100% + 34px);
      height: calc(100vh - 55px);
      padding: 20px 17px 20px 20px;
      overflow-y: auto;
    }
  }

  .general {
    padding: 24px 24px 48px 104px;

    @media only screen and (max-width: $mac-13-and-down) {
      padding: 20px 20px 40px 20px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding: 16px 16px 24px 16px;
    }

    .teacher-profile-card {
      &-top {
        position: relative;
        padding-left: 138px;

        @media only screen and (max-width: $mac-13-and-down) {
          padding-left: 185px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding-left: 125px;
        }

        @media only screen and (max-width: $xxs-and-down) {
          padding-left: 118px;
        }

        &-helper {
          margin-bottom: 10px;

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            margin-bottom: 14px;
          }
        }
      }

      &-rating {
        margin-top: 6px;

        @media #{map-get($display-breakpoints, 'md-and-up')} {
          padding-left: 20px;
          text-align: right;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
        }

        .new-verified-teacher {
          display: flex;
          text-align: left;

          div {
            width: calc(100% - 18px);
          }
        }
      }

      &-general-info {
        padding-bottom: 18px;

        @media #{map-get($display-breakpoints, 'md-and-up')} {
          display: flex;
          justify-content: space-between;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          min-height: 80px;
        }

        &:not(.has-feedback-tags) {
          min-height: 120px;
          padding-bottom: 28px;

          @media #{map-get($display-breakpoints, 'md-and-up')} {
            margin-bottom: 14px;
            border-bottom: 1px solid #ecf3ff;
          }
        }

        & > div:not(.teacher-card-rating) {
          max-width: 500px;
        }

        .review {
          font-size: 14px;
          font-weight: 500;
          color: rgba(45, 45, 45, 0.7);
          white-space: nowrap;

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            font-weight: 400;
          }

          @media only screen and (max-width: $xxs-and-down) {
            font-size: 12px;
          }
        }

        &.new-teacher {
          & > div:not(.teacher-card-rating) {
            max-width: 430px;

            @media only screen and (max-width: $mac-13-and-down) {
              max-width: 380px;
            }
          }
        }
      }

      &-avatar {
        position: absolute;
        left: -80px;
        top: 0;

        @media only screen and (max-width: $mac-13-and-down) {
          left: 0;
        }
      }

      &-name {
        margin-bottom: 10px;
        font-size: 24px;
        line-height: 1.33;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          margin-bottom: 12px;
          font-size: 22px;
        }

        @media only screen and (max-width: $xxs-and-down) {
          font-size: 18px;
        }
      }

      &-short-description {
        font-size: 16px;
        font-weight: 300;

        @media only screen and (max-width: $mac-13-and-down) {
          font-size: 15px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          margin-bottom: 20px;
          font-size: 14px;

          &:not(.has-feedback-tags) {
            padding-bottom: 18px;
            border-bottom: 1px solid #ecf3ff;
          }
        }
      }

      &-features {
        margin-bottom: 32px;

        @media #{map-get($display-breakpoints, 'md-and-up')} {
          display: flex;
        }

        @media only screen and (max-width: $mac-13-and-down) {
          width: calc(100% + 185px);
          margin-left: -185px;
          padding: 0 32px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          width: 100%;
          margin-left: 0;
          padding: 0;

          & > .d-flex > div {
            width: 50%;

            &:first-child {
            }
          }
        }

        .item {
          @media #{map-get($display-breakpoints, 'md-and-up')} {
            width: 33.3333%;
          }

          &__title {
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: 300;
          }

          &__text {
            font-size: 16px;
            font-weight: 700;
            line-height: 1.2;
            color: var(--v-greyDark-base);
          }
        }
      }

      &-specialities {
        position: relative;
        padding: 20px 32px;
        color: var(--v-greyDark-base);
        border-radius: 12px;
        overflow: hidden;

        @media only screen and (min-width: $mac-13-and-up) {
          display: flex;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding: 16px;
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
              118.56deg,
              var(--v-success-base) 3.04%,
              var(--v-primary-base) 27.45%
            ),
            #c4c4c4;
          opacity: 0.1;
        }

        .specialities,
        .qualifications {
          position: relative;

          &-title {
            font-weight: 700;
          }
        }

        .specialities {
          font-size: 16px;
          line-height: 1.2;

          @media only screen and (min-width: $mac-13-and-up) {
            width: calc(100% - 186px);
            padding-right: 20px;
          }

          @media only screen and (max-width: $mac-13-and-down) {
            margin-bottom: 32px;
          }

          &-content {
            display: flex;
            flex-wrap: wrap;

            @media only screen and (max-width: $xxs-and-down) {
              flex-direction: column;
            }

            .item {
              position: relative;
              flex: 0 0 33.3333%;
              margin-top: 16px;
              padding: 0 10px 0 26px;

              @media #{map-get($display-breakpoints, 'sm-and-down')} {
                flex: 0 0 50%;
                font-size: 14px;
              }

              &-icon {
                position: absolute;
                left: 0;
                top: 2px;
              }
            }
          }

          &--full-width {
            width: 100%;
            margin-bottom: 0;
            padding-right: 0;

            .item {
              flex: 0 0 25%;

              @media only screen and (max-width: $mac-13-and-down) {
                flex: 0 0 33.3333%;
              }

              @media #{map-get($display-breakpoints, 'sm-and-down')} {
                flex: 0 0 50%;
              }
            }
          }
        }

        .qualifications {
          line-height: 1.28;

          @media only screen and (min-width: $mac-13-and-up) {
            width: 190px;
          }

          &-title {
            margin-bottom: 16px;
            font-size: 16px;
          }

          &-content {
            font-size: 14px;

            .item {
              position: relative;
              margin-bottom: 8px;
              padding-left: 24px;

              &-icon {
                position: absolute;
                left: 0;
                top: 1px;
              }
            }

            .more {
              display: flex;
              align-items: center;
              margin-top: 12px;
              font-weight: 600;
              cursor: pointer;

              & > div {
                margin-left: 8px;
              }
            }
          }
        }
      }

      &-description {
        font-size: 17px;
        font-weight: 300;
        color: var(--v-greyDark-base);
        line-height: 1.43;

        @media only screen and (min-width: $mac-13-and-up) {
          display: flex;

          & > div {
            padding-right: 20px;
          }
        }

        @media only screen and (max-width: $mac-13-and-down) {
          font-size: 14px;
        }

        h6 {
          font-size: 15px;

          @media only screen and (max-width: $mac-13-and-down) {
            font-size: 13px;
          }
        }

        aside {
          flex: 0 0 270px;

          & > div {
            position: relative;
            padding: 24px;
            border-radius: 20px;
            overflow: hidden;

            @media #{map-get($display-breakpoints, 'sm-and-down')} {
              padding: 20px;
            }

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: linear-gradient(
                  118.56deg,
                  var(--v-success-base) 3.04%,
                  var(--v-primary-base) 27.45%
                ),
                #c4c4c4;
              opacity: 0.1;
            }

            & > div {
              position: relative;
            }

            h5 {
              font-size: 16px;
              font-weight: 700;
            }

            ul {
              margin: 0;
              padding-left: 18px;

              li {
                margin-top: 12px;
                line-height: 1.5;

                @media #{map-get($display-breakpoints, 'sm-and-down')} {
                  margin-top: 16px;
                }
              }
            }
          }
        }
      }
    }
  }

  .facts-about-teacher {
    position: relative;
  }

  .courses {
    margin-top: 40px;
    padding: 32px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding: 16px 16px 24px 16px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-top: 48px;
    }

    &-title {
      font-size: 20px;
      font-weight: 700;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 18px;
      }

      span {
        display: inline-block;
        padding-left: 8px;
        font-size: 18px;
        color: rgba(35, 35, 35, 0.8);
      }
    }

    &-text {
      margin-top: 8px;
      font-size: 16px;
      color: rgba(35, 35, 35, 0.6);

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 14px;
      }
    }

    &-list {
      margin-top: 24px;
    }

    &-show-more {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 24px;
      cursor: pointer;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 40px;
        font-size: 16px;
      }

      .v-image {
        flex: 0 0 12px;
        margin-left: 8px;
      }
    }
  }

  .reviews {
    margin-top: 80px;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-top: 48px;
    }

    &-title {
      font-size: 20px;
      font-weight: 700;
      line-height: 1.3;

      @media only screen and (max-width: $xxs-and-down) {
        font-size: 18px;
      }
    }

    &-content {
      max-height: 745px;
      margin-top: 24px;

      @media #{map-get($display-breakpoints, 'md-and-up')} {
        overflow-y: auto;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        width: calc(100% + 15px);
      }

      &::-webkit-scrollbar-track {
        -webkit-box-shadow: none;
        background-color: #e4e5ea;
        border-radius: 10px;
      }

      &::-webkit-scrollbar {
        width: 10px;
      }

      &::-webkit-scrollbar-thumb {
        margin-right: 5px;
        background-color: #4b4949;
        border-radius: 10px;
        outline: none;
      }
    }

    .item {
      position: relative;
      margin: 0 26px 24px 0;

      @media only screen and (max-width: $mac-13-and-down) {
        margin: 0 10px 10px 0;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin: 0 15px 0 0;
      }

      &-helper {
        position: relative;
        display: block;
        height: 100%;
        padding: 24px 24px 76px 24px;
        border-radius: 24px;
        color: #fff !important;
        text-decoration: none;
        background-color: var(--v-darkLight-base);

        @media only screen and (max-width: $mac-13-and-down) {
          padding-top: 80px;

          &::before {
            content: '';
            position: absolute;
            top: 20px;
            right: 24px;
            width: 40px;
            height: 36px;
            background-size: cover;
            background-repeat: no-repeat;
            background-image: url('~assets/images/quotes.svg');
            opacity: 0.3;
          }
        }
      }

      &-text {
        min-height: 60px;
        font-size: 15px;
        font-weight: 300;
        line-height: 1.3;
      }

      &-bottom {
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 25px;
        padding: 0 24px;
        font-size: 16px;
        line-height: 1.2;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding: 0 32px;
        }

        @media only screen and (max-width: $xxs-and-down) {
          padding: 0 15px;
        }

        &-helper {
          position: relative;
          padding: 0 0 0 46px;

          @media only screen and (max-width: $xxs-and-down) {
            padding: 0 0 0 60px;
            font-size: 14px;
          }

          @media only screen and (min-width: $mac-13-and-up) {
            padding-right: 50px;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              right: 0;
              width: 32px;
              height: 28px;
              background-size: cover;
              background-repeat: no-repeat;
              background-image: url('~assets/images/quotes.svg');
              opacity: 0.3;
            }
          }

          .v-image {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }

      &--gradient {
        .item-bottom-helper::before {
          background-image: url('~assets/images/quotes-w.svg');
        }

        .item-helper {
          background: linear-gradient(
            126.15deg,
            var(--v-success-base) 0%,
            var(--v-primary-base) 102.93%
          );

          @media only screen and (max-width: $mac-13-and-down) {
            &::before {
              background-image: url('~assets/images/quotes-w.svg');
            }
          }
        }
      }
    }

    .reviews-row {
      display: flex;

      @media only screen and (min-width: $mac-13-and-up) {
        &--t1 {
          & > div {
            &:first-child {
              max-width: 485px;
            }

            &:last-child {
              max-width: 414px;
            }
          }
        }

        &--t2 {
          & > div {
            &:first-child {
              max-width: 361px;
            }

            &:last-child {
              max-width: 545px;
            }
          }
        }
      }

      & > div {
        width: 100%;

        @media only screen and (max-width: $mac-13-and-down) {
          width: 50%;
        }
      }

      & > div:last-child {
        margin-right: 20px;

        @media only screen and (max-width: $mac-13-and-down) {
          margin-right: 10px;
        }
      }

      &:last-child {
        & > div {
          margin-bottom: 0;

          @media only screen and (min-width: $mac-13-and-up) {
            &:first-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

.fixed-buttons {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  padding: 58px 10px 18px;
  z-index: 6;

  @media only screen and (max-width: $xsm-and-down) {
    padding-top: 38px;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 0%,
      #ffffff 100%
    );
  }

  & > div {
    position: relative;
    flex-wrap: wrap;

    button {
      margin: 3px 6px;
    }
  }
}
