const defaultSelectedSorting = () => ({
  id: 9,
  name: 'recommended',
})

export const state = () => ({
  filters: null,
  specialities: [],
  activeFilters: [],
  needUpdateTeachers: false,
  motivationBanners: [
    {
      id: 1,
      description: 'life_banner_text',
      image: 'life.svg',
    },
    {
      id: 2,
      description: 'career_banner_text',
      image: 'career.svg',
    },
    {
      id: 3,
      description: 'education_banner_text',
      image: 'education.svg',
    },
  ],
  specialityBanners: [
    {
      id: 11,
      image: 'university-preparation.svg',
    },
    {
      id: 15,
      image: 'conversation.svg',
    },
    {
      id: 18,
      image: 'grammar.svg',
    },
    {
      id: 19,
      image: 'vocabulary.svg',
    },
    {
      id: 20,
      image: 'travel.svg',
    },
    {
      id: 21,
      image: 'young-learner.svg',
    },
    {
      id: 22,
      image: 'business.svg',
    },
    {
      id: 23,
      image: 'marketing.svg',
    },
    {
      id: 24,
      image: 'it.svg',
    },
    {
      id: 25,
      image: 'medicine.svg',
    },
    {
      id: 26,
      image: 'law.svg',
    },
    {
      id: 27,
      image: 'engineering.svg',
    },
    {
      id: 30,
      image: 'interview-prep.svg',
    },
    {
      id: 34,
      image: 'tourism.svg',
    },
    {
      id: 37,
      image: 'diplomacy.svg',
    },
    {
      id: 39,
      image: 'finance-banking.svg',
    },
    {
      id: 42,
      image: 'exam-preparation.svg',
    },
    {
      id: 49,
      image: 'writing.svg',
    },
    {
      id: 50,
      image: 'science.svg',
    },
  ],
  days: [
    { id: 1, name: 'monday', image: null },
    { id: 2, name: 'tuesday', image: null },
    { id: 3, name: 'wednesday', image: null },
    { id: 4, name: 'thursday', image: null },
    { id: 5, name: 'friday', image: null },
    { id: 6, name: 'saturday', image: null },
    { id: 0, name: 'sunday', image: null },
  ],
  times: [
    { id: 1, name: 'morning', period: '(6am - 12pm)', image: 'alarm' },
    { id: 2, name: 'afternoon', period: '(12pm - 5pm)', image: 'sun' },
    { id: 3, name: 'evening', period: '(5pm - 12am)', image: 'sunset' },
    { id: 4, name: 'night', period: '(12am - 6am)', image: 'moon' },
  ],
  sortByItems: [
    { id: 9, name: 'recommended' },
    { id: 4, name: 'most_availability' },
    { id: 1, name: 'most_experienced' },
    { id: 2, name: 'highest_price' },
    { id: 3, name: 'lowest_price' },
    { id: 6, name: 'highest_average_rating' },
    { id: 7, name: 'recently_added' },
  ],
  selectedLanguage: null,
  selectedMotivation: null,
  selectedSpecialities: [],
  selectedDays: [],
  selectedTimes: [],
  selectedProficiencyLevel: null,
  selectedTeacherPreference: null,
  selectedTeacherPreferenceLanguage: null,
  selectedSorting: defaultSelectedSorting(),
  selectedFeedbackTag: null,
  searchQuery: null,
  currencySetByUser: false,
})

export const mutations = {
  SET_FILTERS: (state, payload) => {
    state.filters = payload
  },
  SET_CURRENCY_BY_USER: (state, payload) => {
    state.currencySetByUser = payload
  },
  UPDATE_ACTIVE_FILTERS: (state, { data, type }) => {
    state.activeFilters = [...state.activeFilters, { ...data, type }]
  },
  RESET_ACTIVE_FILTERS: (state) => {
    state.activeFilters = []
  },
  SET_NEED_UPDATE_TEACHERS: (state, payload) => {
    state.needUpdateTeachers = payload
  },
  SET_SELECTED_SORTING(state, payload) {
    state.selectedSorting = payload
  },
  RESET_SELECTED_SORTING(state) {
    state.selectedSorting = defaultSelectedSorting()
  },
  SET_SELECTED_LANGUAGE(state, { language, updateActiveFilters = false }) {
    state.selectedLanguage = language

    if (updateActiveFilters) {
      state.activeFilters = state.activeFilters.filter(
        (i) => i.type !== 'language'
      )

      // Only add to active filters if language is not null (not "All Languages")
      if (language && language.id !== null) {
        state.activeFilters.push({ ...language, type: 'language' })
      }
    }
  },
  RESET_SELECTED_LANGUAGE(state) {
    state.selectedLanguage = null
    // Remove language from active filters
    state.activeFilters = state.activeFilters.filter(
      (i) => i.type !== 'language'
    )
  },
  RESET_SELECTED_SPECIALITIES(state) {
    state.selectedSpecialities = []
  },
  SET_SELECTED_MOTIVATION(state, { motivation, updateActiveFilters = false }) {
    state.selectedSpecialities = []
    state.selectedMotivation = motivation

    if (updateActiveFilters) {
      state.activeFilters = state.activeFilters.filter(
        (i) => i.type !== 'motivation'
      )

      state.activeFilters.push({ ...motivation, type: 'motivation' })
    }
  },
  RESET_SELECTED_MOTIVATION(state) {
    state.selectedMotivation = null
    state.selectedSpecialities = []
    state.specialities = []
  },
  SET_SPECIALITIES(state, payload) {
    state.specialities = payload
  },
  SET_SELECTED_SPECIALITIES(
    state,
    { specialities, updateActiveFilters = false }
  ) {
    state.selectedSpecialities = specialities

    if (updateActiveFilters) {
      state.activeFilters = state.activeFilters.filter(
        (i) => i.type !== 'speciality'
      )

      state.activeFilters.push(
        ...specialities.map((item) => ({ ...item, type: 'speciality' }))
      )
    }
  },
  UPDATE_SELECTED_SPECIALITIES(state, payload) {
    state.selectedSpecialities = state.selectedSpecialities.filter(
      (el) => el.id !== payload.id
    )
  },
  SET_SELECTED_PROFICIENCY_LEVEL(
    state,
    { proficiencyLevel, updateActiveFilters = false }
  ) {
    state.selectedProficiencyLevel = proficiencyLevel

    if (updateActiveFilters) {
      state.activeFilters = state.activeFilters.filter(
        (i) => i.type !== 'proficiencyLevel'
      )

      state.activeFilters.push({
        ...proficiencyLevel,
        type: 'proficiencyLevel',
      })
    }
  },
  RESET_SELECTED_PROFICIENCY_LEVEL(state) {
    state.selectedProficiencyLevel = null
  },
  SET_SELECTED_DAYS(state, { dates, updateActiveFilters = false }) {
    state.selectedDays = dates

    if (updateActiveFilters) {
      state.activeFilters = state.activeFilters.filter(
        (i) => i.type !== 'dates'
      )

      state.activeFilters.push(
        ...dates.map((item) => ({ ...item, type: 'dates' }))
      )
    }
  },
  UPDATE_SELECTED_DAYS(state, payload) {
    state.selectedDays = state.selectedDays.filter((el) => el.id !== payload.id)
  },
  RESET_SELECTED_DAYS(state) {
    state.selectedDays = []
  },
  SET_SELECTED_TIMES(state, { times, updateActiveFilters = false }) {
    state.selectedTimes = times

    if (updateActiveFilters) {
      state.activeFilters = state.activeFilters.filter((i) => i.type !== 'time')

      state.activeFilters.push(
        ...times.map((item) => ({ ...item, type: 'time' }))
      )
    }
  },
  UPDATE_SELECTED_TIMES(state, payload) {
    state.selectedTimes = state.selectedTimes.filter(
      (el) => el.id !== payload.id
    )
  },
  RESET_SELECTED_TIMES(state) {
    state.selectedTimes = []
  },
  SET_SELECTED_TEACHER_PREFERENCE(
    state,
    { teacherPreference, updateActiveFilters = false }
  ) {
    state.selectedTeacherPreference = teacherPreference

    if (updateActiveFilters && teacherPreference.id === 1) {
      state.activeFilters = state.activeFilters.filter(
        (i) => i.type !== 'teacherPreference'
      )

      state.activeFilters.push({
        ...teacherPreference,
        type: 'teacherPreference',
      })
    }
  },
  RESET_SELECTED_TEACHER_PREFERENCE(state) {
    const [defaultTeacherPreference] = state.filters.teacherPreference.filter(
      (item) => item.id === 0
    )

    state.selectedTeacherPreference = defaultTeacherPreference
    state.selectedTeacherPreferenceLanguage = null
  },
  SET_SELECTED_TEACHER_PREFERENCE_LANGUAGE(
    state,
    { teacherPreferenceLanguage, updateActiveFilters = false }
  ) {
    state.selectedTeacherPreferenceLanguage = teacherPreferenceLanguage

    if (updateActiveFilters) {
      state.activeFilters = state.activeFilters.filter(
        (i) => i.type !== 'matchLanguages'
      )

      state.activeFilters.push({
        ...teacherPreferenceLanguage,
        type: 'matchLanguages',
      })
    }
  },
  RESET_SELECTED_TEACHER_PREFERENCE_LANGUAGE(state) {
    state.selectedTeacherPreferenceLanguage = null
  },
  SET_SELECTED_FEEDBACK_TAG(state, payload) {
    state.selectedFeedbackTag = payload
    state.selectedSorting = defaultSelectedSorting()
  },
  RESET_SELECTED_FEEDBACK_TAG(state) {
    state.selectedFeedbackTag = null
  },
  SET_SEARCH_QUERY(state, { searchQuery, updateActiveFilters = false }) {
    state.searchQuery = searchQuery

    if (updateActiveFilters) {
      state.activeFilters = [
        ...state.activeFilters,
        { name: searchQuery, type: 'search' },
      ]
    }
  },
  RESET_SEARCH_QUERY(state) {
    state.searchQuery = null
  },
}

export const getters = {
  days: (state) => state.days,
  activeFilters: (state) => state.activeFilters,
  times: (state) => state.times,
  sortByItems: (state, getters) => {
    let items = state.sortByItems

    if (getters.hasSelectedFeedbackTag) {
      items = [...items, state.selectedFeedbackTag]
    }

    return items
  },
  selectedSorting: (state, getters) =>
    getters.hasSelectedFeedbackTag &&
    state.selectedSorting.id === defaultSelectedSorting().id
      ? state.selectedFeedbackTag
      : state.selectedSorting,
  feedbackTags: (state) => {
    const feedbackTags = state.filters?.feedbackTag

    return Array.isArray(feedbackTags)
      ? feedbackTags.map((item) => ({
          ...item,
          isFeedbackTag: true,
        }))
      : []
  },
  selectedLanguage: (state) => state.selectedLanguage,
  getCurrencySetByUser: (state) => state.currencySetByUser,
  selectedSpecialities: (state) => state.selectedSpecialities,
  selectedMotivation: (state) => state.selectedMotivation,
  selectedDays: (state) => state.selectedDays,
  selectedTimes: (state) => state.selectedTimes,
  selectedFeedbackTag: (state) => state.selectedFeedbackTag,
  searchQuery: (state) => state.searchQuery,
  selectedProficiencyLevel: (state) => state.selectedProficiencyLevel,
  selectedTeacherPreference: (state) => state.selectedTeacherPreference,
  selectedTeacherPreferenceLanguage: (state) =>
    state.selectedTeacherPreferenceLanguage,
  quantityActiveFilters: (state) => state.activeFilters.length,
  publishSpecialities: (state) =>
    state.specialities.filter((item) => item.isPublish) ?? [],
  languageChip: (state) =>
    state.activeFilters.find((i) => i.type === 'language'),
  motivationChip: (state) =>
    state.activeFilters.find((i) => i.type === 'motivation'),
  specialityChips: (state) =>
    state.activeFilters.filter((i) => i.type === 'speciality'),
  proficiencyLevelChip: (state) =>
    state.activeFilters.find((i) => i.type === 'proficiencyLevel'),
  teacherPreferenceChip: (state) =>
    state.activeFilters.find((i) => i.type === 'teacherPreference'),
  teacherMatchLanguageChip: (state) =>
    state.activeFilters.find((i) => i.type === 'matchLanguages'),
  dateChips: (state) => state.activeFilters.filter((i) => i.type === 'dates'),
  timeChips: (state) => state.activeFilters.filter((i) => i.type === 'time'),
  currencyChip: (state, getters, rootState) =>
    rootState.currency.item?.id !== rootState.systemCurrency?.id
      ? state.activeFilters.find((i) => i.type === 'currency')
      : null,
  currencyChipUSD: (state, getters) =>
    state.activeFilters.find((i) => i.type === 'currency'),
  searchChip: (state) => state.activeFilters.find((i) => i.type === 'search'),
  hasSelectedFeedbackTag: (state) => !!state.selectedFeedbackTag?.id,
}

export const actions = {
  getFilters({ commit, state, rootState }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teacher-search-form`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_FILTERS', data)

        // Check if we're on a URL with parameters that don't include a language parameter
        const currentRoute = rootState.route
        const hasUrlParams = currentRoute?.params?.params
        const hasLanguageParam =
          hasUrlParams && hasUrlParams.includes('language,')

        // If URL has parameters but no language parameter, set language to null (All languages)
        if (hasUrlParams && !hasLanguageParam) {
          commit('RESET_SELECTED_LANGUAGE')
          // Mark that language filter was removed so English doesn't auto-select
          if (process.client && typeof window !== 'undefined') {
            window.sessionStorage.setItem('isLanguageFilterRemoved', 'true')
          }
        } else if (!state.selectedLanguage && !hasUrlParams) {
          // Only set English as default if no language is selected and no URL parameters
          // Check sessionStorage on client-side to see if language was explicitly removed
          const isLanguageFilterRemoved =
            process.client &&
            typeof window !== 'undefined' &&
            window.sessionStorage?.getItem('isLanguageFilterRemoved')

          if (!isLanguageFilterRemoved) {
            const englishLanguage = data.languages?.find(
              (lang) =>
                lang.isoCode === 'GB' ||
                lang.name.toLowerCase().includes('english')
            )
            if (englishLanguage) {
              commit('SET_SELECTED_LANGUAGE', {
                language: englishLanguage,
                updateActiveFilters: true,
              })
            }
          }
        }

        return data
      })
  },
  setCurrencyByUser({ commit }, { setByUser = false }) {
    commit('SET_CURRENCY_BY_USER', setByUser)
  },
  updateCurrencyActiveFilter({ commit, rootState }) {
    if (rootState.currency.item.id !== rootState?.systemCurrency?.id) {
      commit('UPDATE_ACTIVE_FILTERS', {
        data: rootState.currency.item,
        type: 'currency',
      })
    }
  },
  resetCurrency({ state, dispatch, rootState }) {
    const selectedCurrency = state.filters.currencies.find(
      (item) => item.id === rootState?.systemCurrency?.id
    )

    if (selectedCurrency) {
      dispatch('currency/setItem', { item: selectedCurrency }, { root: true })
    }
  },
  resetSorting({ commit }) {
    commit('RESET_SELECTED_SORTING')
  },
  resetFilters({ commit, dispatch }) {
    commit('RESET_ACTIVE_FILTERS')
    commit('RESET_SELECTED_LANGUAGE')
    commit('RESET_SELECTED_PROFICIENCY_LEVEL')
    commit('RESET_SELECTED_SPECIALITIES')
    commit('RESET_SELECTED_MOTIVATION')
    commit('RESET_SELECTED_DAYS')
    commit('RESET_SELECTED_TIMES')
    commit('RESET_SELECTED_TEACHER_PREFERENCE')
    commit('RESET_SELECTED_FEEDBACK_TAG')
    commit('RESET_SEARCH_QUERY')

    dispatch('resetCurrency')
  },
}
