<template>
  <section v-if="items.length" ref="questions" class="questions">
    <div v-if="backgroundImage" ref="questionsSectionBg" class="section-bg">
      <v-img
        :src="require('~/assets/images/homepage/questions-bg.png')"
        position="center top"
        :options="{ rootMargin: '50%' }"
      ></v-img>
    </div>
    <v-container class="py-0">
      <v-row>
        <v-col class="col-12 py-0">
          <div class="section-head section-head--decorated">
            <h3
              :class="['section-head-title', titleClass]"
              style="color: #262626; -webkit-text-fill-color: #262626"
            >
              {{ $t('home_page.questions_section_title') }}
            </h3>
          </div>
        </v-col>
      </v-row>
      <v-row>
        <v-col class="col-12 py-0">
          <div class="questions-content">
            <l-expansion-panels :items="items" flat></l-expansion-panels>

            <div class="questions-button">
              <v-btn to="/faq" large color="primary">
                {{ $t('see_our_full_faqs') }}
              </v-btn>
            </div>
          </div>
        </v-col>
      </v-row>
    </v-container>
  </section>
</template>

<script>
import LExpansionPanels from '~/components/LExpansionPanels'

export default {
  name: 'FaqSection',
  components: { LExpansionPanels },
  props: {
    items: {
      type: Array,
      required: true,
    },
    backgroundImage: {
      type: Boolean,
      default: false,
    },
    titleClass: {
      type: String,
      default: null,
    },
  },
  mounted() {
    // this.$nextTick(() => {
    //   if (this.items.length && this.backgroundImage) {
    //     const questionsSectionHeight = this.$refs.questions.offsetHeight
    //
    //     this.$refs.questionsSectionBg.style.height = `${Math.min(
    //       questionsSectionHeight,
    //       896
    //     )}px`
    //   }
    // })
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.questions {
  position: relative;
  margin: 138px 0 82px;

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    margin: 95px 0 82px;
  }

  .section-bg {
    top: 72px;
  }

  .section-head {
    margin-bottom: 118px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin-bottom: 70px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-bottom: 40px;
    }
  }

  &-content {
    max-width: 920px;
    margin: 0 auto;

    @media only screen and (max-width: $xxs-and-down) {
      .v-expansion-panel-content__wrap {
        padding: 0 16px 20px !important;
      }
    }
  }

  &-button {
    display: flex;
    justify-content: center;
    margin-top: 45px;

    .v-btn {
      min-width: 202px !important;

      @media only screen and (max-width: $xxs-and-down) {
        min-width: 100% !important;
        width: 100% !important;
      }
    }
  }
}
</style>
