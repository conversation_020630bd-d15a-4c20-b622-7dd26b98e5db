export const state = () => ({
  item: {
    id: 1,
    htmlSymbol: '&#x20AC;',
    isoCode: 'EUR',
    unicodeSymbol: '20AC',
    setByUser: false,
  },
  items: [
    {
      id: 1,
      htmlSymbol: '&#x20AC;',
      isoCode: 'EUR',
      unicodeSymbol: '20AC',
    },
    {
      id: 2,
      htmlSymbol: '&#x00A3;',
      isoCode: 'GBP',
      unicodeSymbol: '00A3',
    },
    {
      id: 3,
      htmlSymbol: '&#x0024;',
      isoCode: 'USD',
      unicodeSymbol: '0024',
    },
    {
      id: 4,
      htmlSymbol: '&#x007A;',
      isoCode: 'PLN',
      unicodeSymbol: '007A',
    },
    {
      id: 5,
      htmlSymbol: '&#x0024;',
      isoCode: 'CAD',
      unicodeSymbol: '0024',
    },
    {
      id: 6,
      htmlSymbol: '&#x0024;',
      isoCode: 'AUD',
      unicodeSymbol: '0024',
    },
  ],
})

export const mutations = {
  SET_ITEM: (state, payload) => {
    state.item = payload
  },
}

export const getters = {
  items: (state) => state.items,
  currentCurrencyHtmlSymbol: (state) => {
    return state.item?.isoCode === 'PLN' ? 'PLN' : state.item?.htmlSymbol
  },
  currentCurrencySymbol: (state) => {
    const { id } = state.item

    let symbol = '€'

    switch (id) {
      case 2:
        symbol = '£'
        break
      case 3:
        symbol = '$'
        break
      case 4:
        symbol = 'PLN'
        break
      case 5:
        symbol = 'C$'
        break
      case 6:
        symbol = 'A$'
        break
    }

    return symbol
  },
}

export const actions = {
  setItem({ commit }, { item, isCookieUpdate = true, setByUser = false }) {
    if (item) {
      commit('SET_ITEM', item)

      if (isCookieUpdate) {
        this.$cookiz.set('currency', item.id, {
          domain: process.env.NUXT_ENV_COOKIE_DOMAIN,
          path: '/',
        })
      }
      if (setByUser) {
        commit('SET_ITEM', { ...item, setByUser })
      }
    }
  },
}
