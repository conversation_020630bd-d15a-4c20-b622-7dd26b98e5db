<svg width="412" height="515" viewBox="0 0 412 515" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M67.4571 31C78.1966 60.7918 76.8529 81.2808 94.9571 107.287C138.175 169.367 271.457 98.3117 325.957 164.128C350.326 193.556 355.587 237.699 325.957 261.854C257.457 317.698 135.424 236.303 82.4999 266C37.404 291.304 38.4325 316.666 56.0003 372C70.8255 418.696 197.861 392.392 246 401.5C300.049 411.726 354.5 453 354.5 453" stroke="#F1C4B0" stroke-width="6" stroke-dasharray="15 15"/>
<g filter="url(#filter0_d)">
<ellipse cx="55.8711" cy="54.8605" rx="50" ry="49.8605" fill="white"/>
</g>
<g clip-path="url(#clip0)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M78.2757 40.1836V72.5684C78.2757 74.5534 76.6506 76.1788 74.6625 76.1788H30.4846C28.4964 76.1788 26.8711 74.5534 26.8711 72.5684V40.1836H78.2757Z" fill="url(#paint0_linear)" fill-opacity="0.45"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M30.8348 31.1519H74.309C76.4896 31.1519 78.2757 32.9379 78.2757 35.1186V40.7422H26.8711V35.1186C26.8711 32.9379 28.6541 31.1519 30.8348 31.1519V31.1519Z" fill="#2D2D2D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M36.7096 61.073H47.8174C48.2824 61.073 48.66 61.425 48.66 61.8586V72.2145C48.66 72.6441 48.2824 73 47.8174 73H36.7096C36.2486 73 35.8711 72.6441 35.8711 72.2145V61.8586C35.8711 61.425 36.2485 61.073 36.7096 61.073ZM36.7096 44H47.8174C48.2824 44 48.66 44.3557 48.66 44.7853V55.1412C48.66 55.5746 48.2824 55.9267 47.8174 55.9267H36.7096C36.2486 55.9267 35.8711 55.5747 35.8711 55.1412V44.7853C35.8711 44.3555 36.2485 44 36.7096 44ZM55.9207 44H67.0286C67.4896 44 67.8711 44.3557 67.8711 44.7853V55.1412C67.8711 55.5746 67.4896 55.9267 67.0286 55.9267H55.9207C55.4558 55.9267 55.0781 55.5747 55.0781 55.1412V44.7853C55.0781 44.3555 55.4557 44 55.9207 44Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M64.1246 81.5132C70.3257 81.5132 75.3813 76.4546 75.3813 70.2567C75.3813 64.0554 70.3257 59 64.1246 59C57.9266 59 52.8711 64.0554 52.8711 70.2567C52.8711 76.4545 57.9266 81.5132 64.1246 81.5132Z" fill="#2D2D2D"/>
<g clip-path="url(#clip1)">
<path d="M43.301 50.7422H41.441C39.6117 50.7422 38.1234 52.2305 38.1234 54.0598V56.2529H46.6186V54.0598C46.6186 52.2305 45.1303 50.7422 43.301 50.7422Z" fill="#2D2D2D"/>
<path d="M42.3712 45C40.9566 45 39.8058 46.1509 39.8058 47.5654C39.8058 48.98 40.9566 50.1309 42.3712 50.1309C43.7858 50.1309 44.9366 48.98 44.9366 47.5654C44.9366 46.1509 43.7858 45 42.3712 45Z" fill="#2D2D2D"/>
</g>
<g clip-path="url(#clip2)">
<path d="M62.301 50.7422H60.441C58.6117 50.7422 57.1234 52.2305 57.1234 54.0598V56.2529H65.6186V54.0598C65.6186 52.2305 64.1303 50.7422 62.301 50.7422Z" fill="#2D2D2D"/>
<path d="M61.3712 45C59.9566 45 58.8058 46.1509 58.8058 47.5654C58.8058 48.98 59.9566 50.1309 61.3712 50.1309C62.7858 50.1309 63.9366 48.98 63.9366 47.5654C63.9366 46.1509 62.7858 45 61.3712 45Z" fill="#2D2D2D"/>
</g>
<g clip-path="url(#clip3)">
<path d="M43.301 67.7422H41.441C39.6117 67.7422 38.1234 69.2305 38.1234 71.0598V73.2529H46.6186V71.0598C46.6186 69.2305 45.1303 67.7422 43.301 67.7422Z" fill="#2D2D2D"/>
<path d="M42.3712 62C40.9566 62 39.8058 63.1509 39.8058 64.5654C39.8058 65.98 40.9566 67.1309 42.3712 67.1309C43.7858 67.1309 44.9366 65.98 44.9366 64.5654C44.9366 63.1509 43.7858 62 42.3712 62Z" fill="#2D2D2D"/>
</g>
<g clip-path="url(#clip4)">
<path d="M70.6363 66.8935L69.4956 65.7529C69.3392 65.5964 69.149 65.5181 68.9254 65.5181C68.7017 65.5181 68.5114 65.5964 68.355 65.7529L62.853 71.2632L60.3872 68.789C60.2306 68.6324 60.0405 68.5542 59.817 68.5542C59.5932 68.5542 59.4031 68.6324 59.2466 68.789L58.1059 69.9296C57.9494 70.0862 57.8711 70.2763 57.8711 70.5C57.8711 70.7236 57.9494 70.9139 58.1059 71.0704L61.142 74.1064L62.2827 75.247C62.4392 75.4037 62.6294 75.4819 62.853 75.4819C63.0766 75.4819 63.2668 75.4035 63.4233 75.247L64.564 74.1064L70.6363 68.0342C70.7927 67.8776 70.8711 67.6875 70.8711 67.4638C70.8712 67.2402 70.7927 67.0501 70.6363 66.8935Z" fill="white"/>
</g>
</g>
<g filter="url(#filter1_d)">
<ellipse cx="356.871" cy="189.861" rx="50" ry="49.8605" fill="white"/>
</g>
<g clip-path="url(#clip5)">
<path d="M356.371 202.76H327.871V164.454C327.871 162.797 329.214 161.454 330.871 161.454H356.371V187.521V202.76Z" fill="url(#paint1_linear)" fill-opacity="0.45"/>
<path d="M356.371 161.454H381.871C383.528 161.454 384.871 162.797 384.871 164.454V202.76H356.371V161.454Z" fill="url(#paint2_linear)" fill-opacity="0.45"/>
<path d="M371.057 216.784C371.057 215.862 370.301 215.132 369.4 214.933C366.87 214.374 364.719 212.562 363.787 210.076L362.743 207.292H356.371V218.454H369.387C370.31 218.454 371.057 217.707 371.057 216.784V216.784Z" fill="#2D2D2D"/>
<path d="M341.848 216.809C341.848 215.886 342.605 215.157 343.505 214.958C346.036 214.399 348.186 212.586 349.118 210.101L350.162 207.316H356.534V218.479H343.518C342.596 218.479 341.848 217.731 341.848 216.809V216.809Z" fill="#2D2D2D"/>
<path d="M356.554 210.362H335.963C331.494 210.362 327.871 206.74 327.871 202.271V202.271H356.554L358.434 206.505L356.554 210.362Z" fill="#2D2D2D"/>
<path d="M356.371 202.271H384.871V202.271C384.871 206.74 381.248 210.362 376.78 210.362H356.371V202.271Z" fill="#2D2D2D"/>
<path d="M367.168 177.846C367.168 176.923 367.916 176.176 368.838 176.176V176.176C369.76 176.176 370.508 176.923 370.508 177.846V181.752C370.508 182.674 369.76 183.422 368.838 183.422V183.422C367.916 183.422 367.168 182.674 367.168 181.752V177.846Z" fill="white"/>
<path d="M342.732 173.444C341.078 174.111 341.058 176.446 342.701 177.141L349.128 179.858C349.444 179.992 349.788 180.042 350.129 180.004L356.371 179.306L356.704 179.306C358.01 179.306 358.965 178.075 358.641 176.81L356.371 167.945L342.732 173.444Z" fill="#2D2D2D"/>
<path d="M370.049 177.14C371.687 176.442 371.665 174.111 370.013 173.445L356.371 167.945V177.469C356.371 178.508 357.167 179.374 358.202 179.462L363.038 179.873C363.364 179.901 363.691 179.848 363.991 179.72L370.049 177.14Z" fill="#2D2D2D"/>
<path d="M336.641 192.19C335.718 192.19 334.971 192.938 334.971 193.86V193.86C334.971 194.783 335.718 195.53 336.641 195.53H356.371L358.088 194.034L356.371 192.19H336.641Z" fill="white"/>
<path d="M356.371 192.19H375.744C376.667 192.19 377.414 192.938 377.414 193.86V193.86C377.414 194.783 376.667 195.53 375.744 195.53H356.371V192.19Z" fill="white"/>
<path d="M349.524 176.836V176.836C348.9 177.094 348.493 177.703 348.493 178.379V184.587C348.493 185.692 349.388 186.587 350.493 186.587H356.371L358.227 182.062C358.416 181.601 358.426 181.085 358.255 180.616L356.851 176.778C356.563 175.989 355.811 175.453 354.974 175.52C353.124 175.666 351.288 176.105 349.524 176.836Z" fill="#2D2D2D"/>
<path d="M364.249 178.379C364.249 177.703 363.842 177.095 363.218 176.836V176.836C361.645 176.184 360.014 175.765 358.367 175.577C357.269 175.453 356.371 176.36 356.371 177.464V186.587H362.249C363.354 186.587 364.249 185.692 364.249 184.587V178.379Z" fill="#2D2D2D"/>
<path d="M338.096 191.015C338.096 190.092 338.844 189.345 339.766 189.345V189.345C340.688 189.345 341.436 190.092 341.436 191.015V196.813C341.436 197.735 340.688 198.483 339.766 198.483V198.483C338.844 198.483 338.096 197.735 338.096 196.813V191.015Z" fill="#2D2D2D"/>
</g>
<g filter="url(#filter2_d)">
<ellipse cx="357" cy="459.861" rx="50" ry="49.8605" fill="white"/>
</g>
<path d="M356.5 472.306H328V434C328 432.343 329.343 431 331 431H356.5V457.067V472.306Z" fill="url(#paint3_linear)" fill-opacity="0.45"/>
<path d="M356.5 431H382C383.657 431 385 432.343 385 434V472.306H356.5V431Z" fill="url(#paint4_linear)" fill-opacity="0.45"/>
<path d="M336.67 462.846C335.748 462.846 335 463.593 335 464.516V464.516C335 465.438 335.748 466.186 336.67 466.186H356.4L358.117 464.689L356.4 462.846H336.67Z" fill="white"/>
<path d="M356.4 462.846H375.774C376.696 462.846 377.444 463.593 377.444 464.516V464.516C377.444 465.438 376.696 466.186 375.774 466.186H356.4V462.846Z" fill="white"/>
<path d="M358.125 461.67C358.125 460.748 358.873 460 359.795 460V460C360.717 460 361.465 460.748 361.465 461.67V467.468C361.465 468.39 360.717 469.138 359.795 469.138V469.138C358.873 469.138 358.125 468.39 358.125 467.468V461.67Z" fill="#2D2D2D"/>
<path d="M371.186 486.33C371.186 485.408 370.43 484.678 369.529 484.479C366.999 483.92 364.848 482.107 363.916 479.622L362.872 476.838H356.5V488H369.516C370.438 488 371.186 487.252 371.186 486.33V486.33Z" fill="#2D2D2D"/>
<path d="M341.977 486.355C341.977 485.432 342.733 484.703 343.634 484.504C346.164 483.945 348.315 482.132 349.247 479.647L350.291 476.862H356.663V488.025H343.647C342.725 488.025 341.977 487.277 341.977 486.355V486.355Z" fill="#2D2D2D"/>
<path d="M356.683 479.908H336.091C331.623 479.908 328 476.286 328 471.817V471.817H356.683L358.563 476.051L356.683 479.908Z" fill="#2D2D2D"/>
<path d="M356.5 471.817H385V471.817C385 476.286 381.377 479.908 376.909 479.908H356.5V471.817Z" fill="#2D2D2D"/>
<path d="M361 448.819C361 444.482 364.613 440.97 369.058 441C373.46 441.031 377.034 444.579 377 448.881C376.978 451.591 375.545 453.972 373.384 455.359C373.283 455.424 373.223 455.536 373.223 455.654V458.703C373.223 458.933 372.967 459.075 372.765 458.958L368.855 456.68C368.803 456.65 368.744 456.633 368.683 456.631C364.412 456.468 361 453.033 361 448.819V448.819Z" fill="white"/>
<path d="M357.947 444.474C357.947 439.219 353.669 434.963 348.405 435C343.193 435.037 338.96 439.336 339 444.549C339.026 447.833 340.723 450.719 343.282 452.399C343.402 452.477 343.473 452.613 343.473 452.756V456.451C343.473 456.729 343.776 456.902 344.015 456.759L348.645 454C348.707 453.963 348.777 453.943 348.849 453.94C353.907 453.743 357.947 449.58 357.947 444.474Z" fill="white"/>
<path d="M353.229 447.255L349.485 439.163C349.302 438.769 348.908 438.517 348.474 438.517C348.04 438.517 347.645 438.769 347.463 439.163C347.463 439.163 343.979 446.954 343.718 447.255C343.46 447.813 343.703 448.476 344.262 448.734C344.82 448.992 345.482 448.749 345.741 448.191L346.482 446.589H350.466L351.207 448.191C351.468 448.756 352.138 448.987 352.686 448.734C353.244 448.476 353.487 447.813 353.229 447.255V447.255ZM347.513 444.361L348.474 442.284L349.435 444.361H347.513Z" fill="#2D2D2D"/>
<path d="M373.206 445.603H370.084V444.75C370.084 444.336 369.728 444 369.289 444C368.851 444 368.495 444.336 368.495 444.75V445.603H365.794C365.355 445.603 365 445.939 365 446.354C365 446.768 365.356 447.104 365.794 447.104H370.586C370.159 447.871 369.542 448.728 368.655 449.513C368.293 449.148 367.971 448.748 367.693 448.317C367.465 447.963 366.976 447.85 366.601 448.066C366.227 448.281 366.108 448.743 366.336 449.097C366.647 449.58 367.005 450.028 367.404 450.442C366.872 450.771 366.303 451.052 365.704 451.276C365.296 451.429 365.097 451.865 365.258 452.25C365.422 452.64 365.89 452.821 366.289 452.671C367.114 452.362 367.889 451.963 368.604 451.485C369.537 452.163 370.586 452.665 371.729 452.972C372.14 453.082 372.589 452.858 372.71 452.455C372.83 452.057 372.585 451.642 372.163 451.529C371.332 451.305 370.562 450.958 369.863 450.498C371.106 449.363 371.879 448.114 372.348 447.104H373.206C373.645 447.104 374 446.768 374 446.354C374 445.939 373.645 445.603 373.206 445.603V445.603Z" fill="#2D2D2D"/>
<g filter="url(#filter3_d)">
<ellipse cx="55.8711" cy="324.861" rx="50" ry="49.8605" fill="white"/>
</g>
<g clip-path="url(#clip6)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M78.2757 310.048V342.433C78.2757 344.418 76.6506 346.043 74.6625 346.043H30.4846C28.4964 346.043 26.8711 344.418 26.8711 342.433V310.048H78.2757Z" fill="url(#paint5_linear)" fill-opacity="0.45"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M30.8348 301.016H74.309C76.4896 301.016 78.2757 302.802 78.2757 304.983V310.606H26.8711V304.983C26.8711 302.802 28.6541 301.016 30.8348 301.016V301.016Z" fill="#2D2D2D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M32.9081 330.516H41.7285C42.0978 330.516 42.3976 330.816 42.3976 331.185V340.006C42.3976 340.372 42.0978 340.675 41.7285 340.675H32.9081C32.5421 340.675 32.2423 340.372 32.2423 340.006V331.185C32.2423 330.816 32.542 330.516 32.9081 330.516ZM32.9081 315.975H41.7285C42.0978 315.975 42.3976 316.278 42.3976 316.643V325.464C42.3976 325.833 42.0978 326.133 41.7285 326.133H32.9081C32.5421 326.133 32.2423 325.833 32.2423 325.464V316.643C32.2423 316.277 32.542 315.975 32.9081 315.975ZM48.1632 315.975H56.9837C57.3498 315.975 57.6527 316.278 57.6527 316.643V325.464C57.6527 325.833 57.3498 326.133 56.9837 326.133H48.1632C47.794 326.133 47.4941 325.833 47.4941 325.464V316.643C47.4941 316.277 47.7939 315.975 48.1632 315.975ZM63.4152 315.975H72.2357C72.6048 315.975 72.9048 316.278 72.9048 316.643V325.464C72.9048 325.833 72.605 326.133 72.2357 326.133H63.4152C63.0491 326.133 62.7462 325.833 62.7462 325.464V316.643C62.7462 316.277 63.0491 315.975 63.4152 315.975ZM48.1632 330.516H56.9837C57.3498 330.516 57.6527 330.816 57.6527 331.185V340.006C57.6527 340.372 57.3498 340.675 56.9837 340.675H48.1632C47.794 340.675 47.4941 340.372 47.4941 340.006V331.185C47.4941 330.816 47.7939 330.516 48.1632 330.516Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M72.6142 352.424C78.8152 352.424 83.8709 347.365 83.8709 341.167C83.8709 334.966 78.8152 329.911 72.6142 329.911C66.4162 329.911 61.3607 334.966 61.3607 341.167C61.3607 347.365 66.4162 352.424 72.6142 352.424Z" fill="#2D2D2D"/>
<path d="M72.1312 334.329C72.1312 333.869 72.5036 333.493 72.9643 333.493C73.4282 333.493 73.8006 333.869 73.8006 334.329V341.903C73.8006 342.127 73.7123 342.333 73.5639 342.484L68.3063 348.669C68.0096 349.02 67.4858 349.064 67.1324 348.767C66.782 348.471 66.7378 347.944 67.0345 347.593L72.1311 341.6V334.329H72.1312Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d" x="0.871094" y="0" width="110" height="109.721" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="301.871" y="135" width="110" height="109.721" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter2_d" x="302" y="405" width="110" height="109.721" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter3_d" x="0.871094" y="270" width="110" height="109.721" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="54.8788" y1="24.805" x2="55.2627" y2="83.3168" gradientUnits="userSpaceOnUse">
<stop stop-color="#AB135C"/>
<stop offset="0.739583" stop-color="#F7AD48"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="345.082" y1="143.807" x2="345.904" y2="210.944" gradientUnits="userSpaceOnUse">
<stop stop-color="#AB135C"/>
<stop offset="0.739583" stop-color="#F7AD48"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="371.899" y1="143.807" x2="372.811" y2="210.941" gradientUnits="userSpaceOnUse">
<stop stop-color="#AB135C"/>
<stop offset="0.739583" stop-color="#F7AD48"/>
</linearGradient>
<linearGradient id="paint3_linear" x1="345.211" y1="413.353" x2="346.033" y2="480.489" gradientUnits="userSpaceOnUse">
<stop stop-color="#AB135C"/>
<stop offset="0.739583" stop-color="#F7AD48"/>
</linearGradient>
<linearGradient id="paint4_linear" x1="372.028" y1="413.353" x2="372.94" y2="480.487" gradientUnits="userSpaceOnUse">
<stop stop-color="#AB135C"/>
<stop offset="0.739583" stop-color="#F7AD48"/>
</linearGradient>
<linearGradient id="paint5_linear" x1="54.8788" y1="294.669" x2="55.2627" y2="353.181" gradientUnits="userSpaceOnUse">
<stop stop-color="#AB135C"/>
<stop offset="0.739583" stop-color="#F7AD48"/>
</linearGradient>
<clipPath id="clip0">
<rect width="57" height="57" fill="white" transform="translate(26.8711 26)"/>
</clipPath>
<clipPath id="clip1">
<rect width="11" height="11" fill="white" transform="translate(36.8711 45)"/>
</clipPath>
<clipPath id="clip2">
<rect width="11" height="11" fill="white" transform="translate(55.8711 45)"/>
</clipPath>
<clipPath id="clip3">
<rect width="11" height="11" fill="white" transform="translate(36.8711 62)"/>
</clipPath>
<clipPath id="clip4">
<rect width="13" height="13" fill="white" transform="translate(57.8711 64)"/>
</clipPath>
<clipPath id="clip5">
<rect width="57" height="57" fill="white" transform="translate(327.871 161.454)"/>
</clipPath>
<clipPath id="clip6">
<rect width="57" height="57" fill="white" transform="translate(26.8711 295.864)"/>
</clipPath>
</defs>
</svg>
