<template>
  <user-setting-template
    class="qualifications"
    :title="$t('teaching_qualifications')"
    hide-footer
  >
    <div v-if="items.length" class="mb-3 mb-md-5">
      <v-row>
        <v-col class="col-12">
          <div class="qualifications-wrap">
            <v-simple-table>
              <template #default>
                <thead>
                  <tr>
                    <th class="text-left subtitle-2 pb-2">
                      {{ $t('name') }}
                    </th>
                    <th class="text-left subtitle-2 pb-2">
                      {{ $t('verified') }}
                    </th>
                    <th class="text-left subtitle-2 pb-2"></th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, idx) in items" :key="idx">
                    <td class="body-1">{{ item.name }}</td>
                    <td>
                      <div class="item--verified body-1">
                        <svg width="16" height="16" viewBox="0 0 16 16">
                          <use
                            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#${
                              item.verified ? 'check' : 'close'
                            }-circle-icon`"
                          ></use>
                        </svg>
                        {{ $t(item.verified ? 'yes' : 'no') }}
                      </div>
                    </td>
                    <td>
                      <v-btn
                        icon
                        x-small
                        :color="item.verified ? 'success' : 'error'"
                        @click="removeClickHandler(item)"
                      >
                        <svg width="20" height="20" viewBox="0 0 34 34">
                          <use
                            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#close-big`"
                          ></use>
                        </svg>
                      </v-btn>
                    </td>
                  </tr>
                </tbody>
              </template>
            </v-simple-table>
          </div>
        </v-col>
      </v-row>
    </div>
    <div>
      <v-row>
        <v-col class="col-12">
          <v-btn
            class="btn-add gradient font-weight-medium"
            @click="isShownQualificationDialog = true"
          >
            <div class="mr-1">
              <v-img
                :src="require('~/assets/images/add-icon-gradient.svg')"
                width="20"
                height="20"
              ></v-img>
            </div>
            <div class="text--gradient">
              {{ $t('add') }}
            </div>
          </v-btn>
        </v-col>
      </v-row>
    </div>

    <add-qualification-dialog
      :is-shown-qualification-dialog="isShownQualificationDialog"
      @close-dialog="isShownQualificationDialog = false"
      @qualification-added="qualificationAdded"
    ></add-qualification-dialog>

    <confirm-dialog
      :is-shown-confirm-dialog="isShownConfirmDialog"
      @confirm="removeQualification"
      @close-dialog="isShownConfirmDialog = false"
    >
      {{ $t('teaching_qualification_will_be_deleted_do_you_confirm_that') }}
    </confirm-dialog>

    <qualification-success-dialog
      :is-shown-dialog="isShownQualificationAddedDialog"
      @close-dialog="isShownQualificationAddedDialog = false"
    ></qualification-success-dialog>
  </user-setting-template>
</template>

<script>
import UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'
import AddQualificationDialog from '@/components/user-settings/AddQualificationDialog'
import ConfirmDialog from '@/components/ConfirmDialog'
import QualificationSuccessDialog from '@/components/user-settings/QualificationSuccessDialog'

export default {
  name: 'TeachingQualificationsInfo',
  components: {
    UserSettingTemplate,
    AddQualificationDialog,
    ConfirmDialog,
    QualificationSuccessDialog,
  },
  data() {
    return {
      isShownQualificationDialog: false,
      isShownConfirmDialog: false,
      isShownQualificationAddedDialog: false,
      selectedItem: null,
    }
  },
  computed: {
    items() {
      return this.$store.state.settings.teachingQualificationItems
    },
  },
  beforeCreate() {
    this.$store.dispatch('settings/getTeachingQualifications')
  },
  methods: {
    removeClickHandler(item) {
      this.isShownConfirmDialog = true
      this.selectedItem = item
    },
    async removeQualification() {
      if (this.selectedItem) {
        if (this.selectedItem.id) {
          await this.$store.dispatch(
            'settings/removeTeachingQualification',
            this.selectedItem.id
          )
        }

        this.$store.commit(
          'settings/REMOVE_TEACHING_QUALIFICATION_ITEM',
          this.selectedItem
        )
      }

      this.isShownConfirmDialog = false
      this.selectedItem = null
    },
    qualificationAdded() {
      this.isShownQualificationDialog = false
      this.isShownQualificationAddedDialog = true
    },
  },
}
</script>

<style scoped lang="scss">
.qualifications {
  &-wrap {
    max-width: 400px;

    .item {
      &--verified {
        position: relative;
        padding-left: 22px;

        svg {
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }

  .btn-add {
    min-width: 106px !important;
  }
}
</style>
