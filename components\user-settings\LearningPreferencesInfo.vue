<template>
  <user-setting-template
    :title="$t('learning_preferences')"
    :submit-func="submitData"
  >
    <v-row>
      <v-col class="col-12 col-md-10">
        <div class="input-wrap">
          <div class="input-wrap-title body-1 font-weight-medium">
            {{ $t('you_prefer_teacher_who') }}:
          </div>
          <div>
            <v-radio-group
              v-model="selectedPreferences"
              hide-details
              class="mt-0 pt-0"
            >
              <div
                v-for="item in preferences"
                :key="item.id"
                class="d-flex justify-space-between"
              >
                <div>
                  <div class="radiobutton">
                    <v-radio
                      class="l-radio-button l-radio-button--type-2 mt-2"
                      color="success"
                      :ripple="false"
                      :value="item.id"
                      :label="item.name"
                    ></v-radio>
                  </div>
                </div>
              </div>
            </v-radio-group>
          </div>
        </div>
      </v-col>
    </v-row>
    <v-row>
      <v-col class="col-12 col-md-7">
        <div class="mt-2">
          <div class="input-wrap">
            <user-setting-autocomplete
              :value="{}"
              :items="languages"
              attach-id="language"
              :placeholder="$t('choose_language')"
              @change="updateLanguage($event, 'language')"
            ></user-setting-autocomplete>
          </div>
        </div>
      </v-col>
    </v-row>
  </user-setting-template>
</template>

<script>
import UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'
import UserSettingAutocomplete from '@/components/user-settings/UserSettingAutocomplete'

export default {
  name: 'LearningPreferencesInfo',
  components: { UserSettingTemplate, UserSettingAutocomplete },
  data() {
    return {
      isShownSpecialitiesDialog: false,
    }
  },
  computed: {
    preferences() {
      return this.$store.state.settings.preferenceItems
    },
    selectedPreferences: {
      get() {
        return this.preferences.find((item) => item.isSelected)?.id ?? 0
      },
      set(id) {
        return this.$store.commit(
          'settings/UPDATE_LEARNING_PREFERENCE_ITEMS',
          id
        )
      },
    },
    languages() {
      return this.$store.state.settings.languagesItem?.languages ?? []
    },
  },
  beforeCreate() {
    this.$store.dispatch('settings/getLearningPreferences')
    this.$store.dispatch('settings/getLanguages')
  },
  methods: {
    updateLanguage(item, property) {
      // const currentItems = [...this.item[property]]
      //
      // currentItems.push(item)
      //
      // this.$store.commit('settings/SET_LANGUAGES_ITEM', {
      //   ...this.item,
      //   [property]: currentItems,
      // })
    },
    submitData() {
      this.$store.dispatch('settings/updateLearningPreferences')
    },
  },
}
</script>

<style scoped lang="scss">
.checkbox:not(:last-child) {
  margin-bottom: 20px;
}
</style>
