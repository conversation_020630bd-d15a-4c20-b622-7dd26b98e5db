@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.user-settings {
  --sidebar-width: 295px;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    --sidebar-width: 235px;
  }

  margin-top: 10px;

  &-wrap {
    max-width: 1030px;

    & > div {
      width: 100%;
    }
  }

  &-title {
    font-size: 24px;
    line-height: 1.333;

    @media only screen and (max-width: $xxs-and-down) {
      font-size: 20px;
    }
  }

  &-content {
    width: calc(100% - var(--sidebar-width));
    padding-left: 20px;
  }

  &-sidebar {
    width: var(--sidebar-width);

    &-sticky {
      position: sticky;
      top: 80px;
    }
  }

  .nav-list {
    padding-left: 0;
    list-style-type: none;

    & > li {
      margin-bottom: 12px;
    }

    .v-btn {
      padding: 0 10px 0 40px;
      border-radius: 20px;
      font-size: 18px;
      background-color: transparent !important;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        padding: 0 10px 0 20px;
        font-size: 16px;
      }

      &__content {
        justify-content: flex-start;
        color: var(--v-greyDark-base);
        text-align: left;
      }

      &::before {
        transition: none !important;
      }

      &.active {
        background: linear-gradient(
            126.15deg,
            rgba(128, 182, 34, 0.1) 0%,
            rgba(60, 135, 248, 0.1) 102.93%
        );

        .v-btn__content {
          color: var(--v-dark-base);
          font-weight: 600 !important;
        }

        &:focus,
        &:hover {
          &::before {
            display: none !important;
          }
        }
      }

      &:focus,
      &:hover {
        &::before {
          background: linear-gradient(
              126.15deg,
              rgba(128, 182, 34, 0.1) 0%,
              rgba(60, 135, 248, 0.1) 102.93%
          );
          opacity: 0.6;
        }
      }
    }
  }

  .tabs-mobile {
    &.v-expansion-panels {
      border-radius: 0;

      & > .v-expansion-panel {
        margin-bottom: 16px;
        background-color: transparent !important;

        & > .v-expansion-panel-header {
          min-height: 48px;
          padding: 12px 40px;

          @media only screen and (max-width: $xxs-and-down) {
            padding: 12px 20px;
          }

          &--active {
            border-radius: 20px;
            background: linear-gradient(
                126.15deg,
                rgba(128, 182, 34, 0.1) 0%,
                rgba(60, 135, 248, 0.1) 102.93%
            );
          }
        }

        & > .v-expansion-panel-content {
          margin-top: 12px;
          background-color: #fff;
          box-shadow: 0 8px 17px rgba(17, 46, 90, 0.1);
          border-radius: 20px;

          & > .v-expansion-panel-content__wrap {
            padding: 24px 16px 32px;
          }
        }
      }
    }
  }
}
