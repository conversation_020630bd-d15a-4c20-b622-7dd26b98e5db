const STATUS_CREATED = 0
const STATUS_INITIALIZED = 1
const STATUS_FINISHED = 2

export const state = () => ({
  items: [],
  calendarItems: [],
  totalQuantity: 0,
})

export const mutations = {
  SET_ITEMS: (state, payload) => {
    state.items = payload
  },
  UPDATE_LESSON: (state, lesson) => {
    state.items?.forEach((item) => {
      if (item.lessonId === lesson.lessonId) {
        for (const property in lesson) {
          if (property !== 'lessonId') {
            item[property] = lesson[property]
          }
        }
      }
    })
  },
  SET_CALENDAR_ITEMS: (state, payload) => {
    state.calendarItems = payload
  },
  SET_TOTAL_QUANTITY: (state, payload) => {
    state.totalQuantity = payload
  },
}

export const getters = {
  lessons(state) {
    return state.items
  },
}

export const actions = {
  getUnscheduledLessons({ commit }, { page, perPage, type, searchQuery }) {
    let url = `${process.env.NUXT_ENV_API_URL}/users/unscheduled-lessons/${page}/${perPage}`

    if (searchQuery) {
      url += `?search=${searchQuery}`
    }

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        if (Array.isArray(data.purchases)) {
          commit(
            'SET_ITEMS',
            data.purchases.map((item) => ({
              ...item,
              type,
              isCreated: item.lessonStatus === STATUS_CREATED,
              isInitialized: item.lessonStatus === STATUS_INITIALIZED,
              isFinished: item.lessonStatus === STATUS_FINISHED,
            }))
          )
          commit('SET_TOTAL_QUANTITY', data.countPurchase)
        }
      })
  },
  getPastLessons({ commit }, { page, perPage, type, searchQuery }) {
    let url = `${process.env.NUXT_ENV_API_URL}/users/past-lessons/${page}/${perPage}`

    if (searchQuery) {
      url += `?search=${searchQuery}`
    }

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        if (Array.isArray(data.lessons)) {
          commit(
            'SET_ITEMS',
            data.lessons.map((item) => ({
              ...item,
              type,
              isCreated: item.lessonStatus === STATUS_CREATED,
              isInitialized: item.lessonStatus === STATUS_INITIALIZED,
              isFinished: item.lessonStatus === STATUS_FINISHED,
            }))
          )
          commit('SET_TOTAL_QUANTITY', data.countLessons)
        }
      })
  },
  getUpcomingLessons({ commit }, { page, perPage, type, searchQuery }) {
    let url = `${process.env.NUXT_ENV_API_URL}/users/upcoming-lessons/${page}/${perPage}`

    if (searchQuery) {
      url += `?search=${searchQuery}`
    }

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        if (Array.isArray(data.lessons)) {
          commit(
            'SET_ITEMS',
            data.lessons.map((item) => ({
              ...item,
              type,
              isCreated: item.lessonStatus === STATUS_CREATED,
              isInitialized: item.lessonStatus === STATUS_INITIALIZED,
              isFinished: item.lessonStatus === STATUS_FINISHED,
            }))
          )
          commit('SET_TOTAL_QUANTITY', data.countLessons)
        }
      })
  },
  getCalendarItems({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/calendar`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_CALENDAR_ITEMS', data)
      })
  },
  getLessonsByDate({ commit }, { date, type }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/lessons-by-date/1/99`
    const data = { date }

    if (type === 'past') data.type = 0
    if (type === 'upcoming') data.type = 1

    return this.$axios
      .post(url, JSON.stringify(data), {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        if (Array.isArray(data.lessons)) {
          commit(
            'SET_ITEMS',
            data.lessons.map((item) => ({
              ...item,
              type,
              isCreated: item.lessonStatus === STATUS_CREATED,
              isInitialized: item.lessonStatus === STATUS_INITIALIZED,
              isFinished: item.lessonStatus === STATUS_FINISHED,
            }))
          )
          commit('SET_TOTAL_QUANTITY', data.countLessons)
        }
      })
  },
  scheduleLesson({ commit }, data) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/unscheduled-lessons`

    return this.$axios.post(url, JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  },
  cancelLesson(ctx, lessonId) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/cancel-lesson/${lessonId}`

    return this.$axios.delete(url)
  },
  finishLesson(ctx, lessonId) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/finish-lesson/${lessonId}`

    return this.$axios.put(url)
  },
  getStudentInfo({ commit, dispatch }, studentId) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/info/student/${studentId}`

    dispatch('loadingAllow', false, { root: true })

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => data)
      .finally(() => dispatch('loadingAllow', true, { root: true }))
  },
  generatePdf(ctx, lessonId) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/pdf`

    return this.$axios
      .post(
        url,
        JSON.stringify({
          template: 'white-board',
          params: [{ lessonId }],
        }),
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
      .then((response) => {
        const binaryString = window.atob(response.data?.pdfContent)
        const binaryLen = binaryString.length
        const bytes = new Uint8Array(binaryLen)

        for (let i = 0; i < binaryLen; i++) {
          bytes[i] = binaryString.charCodeAt(i)
        }

        const blob = new Blob([bytes], { type: 'application/pdf' })

        const link = document.createElement('a')

        link.href = window.URL.createObjectURL(blob)
        link.download = `class-${lessonId}.pdf`

        link.click()
      })
      .catch(() => console.info('Download error'))
  },
  getFeedbackItem({ commit }, lessonId) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/${lessonId}/feedback`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => ({ ...data, lessonId }))
  },
  sendFeedback({ dispatch }, data) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/create/feedback`

    return this.$axios
      .post(url, JSON.stringify(data), {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        dispatch('user/updateLessonIdForFeedback', data.lessonId, {
          root: true,
        })
      })
  },
}
