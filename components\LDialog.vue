<template>
  <v-dialog
    :value="dialog"
    :width="width"
    :max-width="maxWidth"
    :dark="dark"
    :persistent="persistent"
    :content-class="contentClass"
    overlay-color="#000"
    overlay-opacity="0.55"
    no-click-animation
    :fullscreen="fullscreen"
    :style="{ zIndex }"
    :retain-focus="false"
    :eager="eager"
    @keydown.esc="keydownEscHandler"
    @click:outside="outsideClickHandler"
  >
    <v-card>
      <v-btn
        v-if="!hideCloseButton"
        icon
        width="28"
        height="28"
        class="dialog-close"
        @click="$emit('close-dialog')"
      >
        <v-img
          :src="require('~/assets/images/close-gradient.svg')"
          width="27"
          height="27"
        ></v-img>
      </v-btn>
      <div v-if="hasTitleSlot" class="dialog-header d-block">
        <div class="dialog-title text-uppercase mb-2 mb-lg-4">
          <slot name="title"></slot>
        </div>
      </div>

      <div class="dialog-content">
        <slot></slot>
      </div>

      <div v-if="hasFooterSlot" class="dialog-footer">
        <slot name="footer"></slot>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'LDialog',
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
    dark: {
      type: Boolean,
      default: false,
    },
    customClass: {
      type: String,
      default: '',
    },
    hideCloseButton: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: '100%',
    },
    maxWidth: {
      type: String,
      default: '680',
    },
    persistent: {
      type: Boolean,
      default: false,
    },
    eager: {
      type: Boolean,
      default: false,
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    zIndex: {
      type: String,
      default: '999999',
    },
  },
  computed: {
    contentClass() {
      return `dialog ${this.customClass || ''}`
    },
    hasTitleSlot() {
      return !!this.$slots.title
    },
    hasFooterSlot() {
      return !!this.$slots.footer
    },
  },
  methods: {
    keydownEscHandler() {
      if (!this.persistent) {
        this.$emit('close-dialog')
      }
    },
    outsideClickHandler() {
      if (!this.persistent) {
        this.$emit('close-dialog')
      }
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/vars';
@import '~vuetify/src/styles/settings/_variables';

.dialog {
  position: relative;

  &.v-dialog--fullscreen {
    & > .v-card {
      height: 100%;

      .dialog-close {
        top: 12px;
      }
    }
  }

  & > .v-card {
    padding: 40px 48px;

    @media only screen and (max-width: $xsm-and-down) {
      padding: 30px 20px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      padding: 20px 15px;
    }

    &.theme--dark {
      background-color: var(--v-dark-base) !important;
      border-color: var(--v-dark-base) !important;

      input:-webkit-autofill,
      input:-webkit-autofill:hover,
      input:-webkit-autofill:focus,
      textarea:-webkit-autofill,
      textarea:-webkit-autofill:hover,
      textarea:-webkit-autofill:focus,
      select:-webkit-autofill,
      select:-webkit-autofill:hover,
      select:-webkit-autofill:focus {
        -webkit-text-fill-color: #fff;
        -webkit-box-shadow: 0 0 0 1000px var(--v-dark-base) inset;
        transition: background-color 5000s ease-in-out 0s;
      }
    }
  }

  &-close {
    position: absolute;
    top: 16px;
    right: 16px;
    cursor: pointer;
    z-index: 5;
  }

  &-title {
    font-size: 40px;
    font-weight: 400;
    line-height: 1.3;
    text-align: center;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      font-size: 32px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 24px;
    }
  }

  &-content {
    font-size: 16px;
    line-height: 1.5;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      font-size: 15px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 13px;
    }
  }

  &-footer {
    margin-top: 56px;
  }
}
</style>
