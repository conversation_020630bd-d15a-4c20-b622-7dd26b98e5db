# Sentry Connection Error Fix

## Problem
Getting the following error on staging:
```
POST https://sentry.inshop.com.ua/api/52/envelope/?sentry_key=b6d4063d5143491f8e13a093e620beed&sentry_version=7 net::ERR_CONNECTION_REFUSED
```

## Root Cause
The `.env` file contained an old Sentry DSN pointing to `sentry.inshop.com.ua` instead of the correct Sentry instance.

## Fix Applied

### 1. Updated `.env` file
**Before:**
```bash
NUXT_ENV_SENTRY_DSN='https://<EMAIL>//52'
```

**After:**
```bash
NUXT_ENV_SENTRY_DSN='https://<EMAIL>/4509759109529681'
```

### 2. Environment File Status
✅ `.env.stage` - Already had correct Sentry DSN
✅ `.env.prod` - Already had correct Sentry DSN  
✅ `.env.staging` - Already had correct Sentry DSN
❌ `.env` - Had old Sentry DSN (now fixed)

## Next Steps

### 1. Clear Build Cache
```bash
# Clear Nuxt build cache
rm -rf .nuxt
rm -rf dist

# Clear node modules cache (if needed)
rm -rf node_modules/.cache
```

### 2. Restart Development/Staging Server
```bash
# For development
npm run dev

# For staging deployment
./deploy.sh staging
```

### 3. Verify Fix
1. **Check browser console** - No more Sentry connection errors
2. **Check Sentry dashboard** - Events should appear in the correct project
3. **Test error reporting** - Trigger a test error to verify Sentry is working

## Environment Variable Priority

The application loads environment variables in this order:
1. `.env` (local development - now fixed)
2. `.env.stage` (staging environment)
3. `.env.prod` (production environment)
4. System environment variables (highest priority)

## Sentry Configuration Summary

### Development Environment
- **DSN:** `https://<EMAIL>/4509759109529681`
- **Environment:** `development`
- **Status:** Disabled (as configured in nuxt.config.js)

### Staging Environment  
- **DSN:** `https://<EMAIL>/4509759109529681`
- **Environment:** `staging`
- **Status:** Enabled

### Production Environment
- **DSN:** `https://<EMAIL>/4509773384515664`
- **Environment:** `production`  
- **Status:** Enabled

## Verification Commands

### Check Current Environment Variables
```bash
# Check which .env file is being used
echo $NUXT_ENV_SENTRY_DSN

# Check Sentry configuration in browser console
console.log(process.env.NUXT_ENV_SENTRY_DSN)
```

### Test Sentry Integration
```bash
# Navigate to the test page (if exists)
# /sentry-test

# Or trigger a test error in browser console
throw new Error('Test Sentry integration')
```

## Files Modified
- `.env` - Updated Sentry DSN from old inshop.com.ua to correct Sentry instance

## Files Already Correct
- `.env.stage` - Staging configuration
- `.env.prod` - Production configuration  
- `.env.staging` - Alternative staging configuration
- `nuxt.config.js` - Sentry module configuration
- `plugins/sentry.client.js` - Client-side Sentry plugin

The fix should resolve the connection refused error and allow Sentry to work properly on staging.
