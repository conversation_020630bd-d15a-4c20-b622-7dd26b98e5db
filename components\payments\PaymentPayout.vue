<template>
  <payout-item :item="payoutData">
    <template #additionalActionsTop>
      <div class="d-flex align-center payout-status">
        <span class="mr-2">
          {{ formattedAmount }}
        </span>
      </div>
    </template>

    <template #additionalActionsBottom>
      <div class="d-flex align-center justify-space-between w-100">
        <div class="caption grey--text">
          <p>{{ $t('payout_method') }}</p>
          <p class="gradient-text">{{ item.counterPartyType || '-' }}</p>
        </div>
      </div>
    </template>
  </payout-item>
</template>

<script>
import PayoutItem from './PayoutItem.vue'
import { currencyFormatter } from '~/store/payments'
import { formatCurrencyLocale } from '~/helpers'

export default {
  name: 'PaymentPayout',
  components: {
    PayoutItem,
  },
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  computed: {
    payoutData() {
      return {
        day: this.item.day,
        date: this.item.date,
        time: this.item.time,
        status: this.item.status,
        counterPartyType: this.item.counterPartyType,
        amount: this.item.amount,
        currency: this.item.currency,
      }
    },
    userCurrency() {
      return this.$store.getters['user/currency']
    },
    userLocale() {
      // Get user's UI language/locale, fallback to browser locale or 'en'
      return this.$store.getters['user/isUserLogged']
        ? this.$store.state.user.item?.uiLanguage || this.$i18n.locale
        : this.$i18n.locale || 'en'
    },
    formattedAmount() {
      // Always use the user's currency for payouts
      if (this.userCurrency && this.userCurrency.isoCode) {
        return formatCurrencyLocale(
          this.item.amount,
          this.userCurrency.isoCode,
          this.userLocale,
          true
        )
      }

      // Fallback to the original formatter if user currency is not available
      return currencyFormatter(this.item.amount, this.item.currency)
    },
  },
  methods: {
    getCurrencySymbol(isoCode) {
      const currencySymbols = {
        EUR: '€',
        USD: '$',
        GBP: '£',
        PLN: 'zł',
        CAD: 'C$',
        AUD: 'A$',
      }
      return currencySymbols[isoCode] || isoCode
    },
  },
}
</script>

<style lang="scss" scoped>
.gradient-text {
  background: linear-gradient(126.15deg, #80b622 0%, #3c87f8 102.93%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 500;
}
.payout-status span {
  font-size: 24px;
  font-weight: 400;
  line-height: 32px;
}
.caption p {
  font-size: 16px;
  line-height: 18px;
  margin: 0 !important;
}
</style>
