<template>
  <messages-page :total-quantity="count" additional-user></messages-page>
</template>

<script>
import MessagesPage from '~/components/user-messages/MessagesPage'

export default {
  name: 'UserMessagesView',
  components: { MessagesPage },
  middleware: 'authenticated',
  async asyncData({ store, route, query }) {
    const searchQuery = query?.search
    const threadId = +route.params.threadId

    let count

    await store
      .dispatch('message/getItems', { page: 1, searchQuery })
      .then(async (data) => {
        count = data?.count ?? 0

        if (data?.threads?.length) {
          const thread = data.threads.find((item) => item.id === threadId)

          store.commit('message/SET_ITEM', thread || { id: threadId })
          await store.dispatch('message/getConversation', {
            threadId,
          })
        }
      })

    return { count }
  },
  head() {
    return {
      title: this.$t('user_messages_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('user_messages_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('user_messages_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('user_messages_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} user-messages-page`,
      },
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
  },
  watchQuery: true,
}
</script>
