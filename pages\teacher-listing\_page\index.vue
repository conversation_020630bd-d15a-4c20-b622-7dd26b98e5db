<template>
  <v-row>
    <teacher-listing
      :teachers="teachers"
      :faq-items="faqItems"
      :page="page"
    ></teacher-listing>
  </v-row>
</template>

<script>
import TeacherListing from '~/components/teacher-listing/TeacherListing'

export default {
  name: 'TeacherListingPage',
  components: { TeacherListing },
  async asyncData({ store, params, query }) {
    let filters

    await store
      .dispatch('teacher_filter/getFilters')
      .then((data) => (filters = data))
    await store.dispatch('teacher_filter/resetSorting')
    await store.dispatch('teacher_filter/resetFilters')

    const page = +params.page
    const currentCurrency = store.state.currency.item
    const selectedSorting = store.state.teacher_filter.selectedSorting
    const searchQuery = query?.search

    let paramsStr = `currency,${currentCurrency.id};sortOption,${selectedSorting.id}`

    if (searchQuery) {
      store.commit('teacher_filter/SET_SEARCH_QUERY', {
        searchQuery,
        updateActiveFilters: true,
      })
    }

    if (store.getters['user/isStudent']) {
      const userLanguage = store.getters['user/language']

      if (userLanguage) {
        store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
          language: userLanguage,
          updateActiveFilters: true,
        })

        paramsStr += `;language,${userLanguage.id}`
      }
    }

    if (!store.state.auth.passwordTokenItem) {
      await store.dispatch('teacher/getTeachers', {
        page,
        perPage: process.env.NUXT_ENV_PER_PAGE,
        params: paramsStr,
        searchQuery,
      })
    }

    return { filters, page }
  },
  head() {
    return {
      title: this.$t('teacher_listing_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('teacher_listing_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} teacher-listing-page`,
      },
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    teachers() {
      return this.$store.state.teacher.items
    },
    faqItems() {
      return this.$store.state.faq.teacherListItems
    },
  },
  watchQuery: true,
  async beforeMount() {
    if (!this.faqItems.length) {
      await this.$store.dispatch('loadingAllow', false)
      this.$store
        .dispatch('faq/getTeacherListPageFaqs')
        .finally(() => this.$store.dispatch('loadingAllow', true))
    }
  },
}
</script>

<style lang="scss">
@import './assets/styles/teacher-listing.scss';
</style>
