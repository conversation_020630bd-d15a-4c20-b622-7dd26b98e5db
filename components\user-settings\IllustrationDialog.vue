<template>
  <l-dialog
    :dialog="isShownIllustrationDialog"
    max-width="844"
    custom-class="illustration-picker"
    :fullscreen="$vuetify.breakpoint.smAndDown"
    v-on="$listeners"
  >
    <div class="illustration-picker-header">
      {{ $t('choose_illustration') }}:
    </div>
    <div
      class="illustration-picker-body l-scroll l-scroll--grey l-scroll--large"
    >
      <div class="illustration-picker-list d-flex">
        <div v-for="item in items" :key="item.id" class="item">
          <div
            :class="[
              'item-helper',
              { 'item-helper--selected': selectedImage === item.image },
            ]"
            @click="newSelectedImage = item.image"
          >
            <v-img
              :src="
                require(`~/assets/images/course-illustrations/${item.image}.svg`)
              "
            ></v-img>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="font-weight-medium"
          @click="$emit('update-image', selectedImage)"
        >
          <svg class="mr-1" width="18" height="18" viewBox="0 0 18 18">
            <use
              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#save-icon`"
            ></use>
          </svg>
          {{ $t('save_changes') }}
        </v-btn>
      </div>
    </template>
  </l-dialog>
</template>

<script>
import LDialog from '~/components/LDialog'

export default {
  name: 'IllustrationDialog',
  components: { LDialog },
  props: {
    isShownIllustrationDialog: {
      type: Boolean,
      required: true,
    },
    currentIllustration: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      newSelectedImage: null,
    }
  },
  computed: {
    items() {
      return this.$store.state.settings.illustrationItems
    },
    selectedImage() {
      return this.newSelectedImage || this.currentIllustration?.image
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';

.v-application .v-dialog.illustration-picker {
  height: 100%;

  @media only screen and (min-height: 1080px) {
    max-height: 975px !important;
  }

  .dialog {
    &-content {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    &-footer {
      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      width: 100%;
      height: 104px;
      margin-top: 0;
      padding: 0 28px;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        height: 74px;
      }
    }
  }

  & > .v-card {
    height: 100%;
    padding: 88px 28px 104px !important;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding: 50px 18px 74px !important;
    }
  }

  .illustration-picker {
    &-header {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 88px;
      display: flex;
      align-items: flex-end;
      padding: 0 60px 24px 28px;
      font-size: 20px;
      font-weight: 700;
      line-height: 1.1;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        align-items: center;
        height: 50px;
        padding-left: 18px;
        padding-bottom: 0;
        font-size: 18px;
      }
    }

    &-body {
      height: 100%;
      flex-grow: 1;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0 2px;
    }

    &-list {
      flex-wrap: wrap;
      width: calc(100% + 16px);

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        width: calc(100% + 12px);
      }

      .item {
        width: 20%;
        flex: 0 0 20%;
        padding: 8px 16px 8px 0;

        @media #{map-get($display-breakpoints, 'xs-only')} {
          width: 25%;
          flex: 0 0 25%;
          padding: 6px 12px 6px 0;
        }

        &-helper {
          position: relative;
          border-radius: 16px;
          background-clip: padding-box;

          &:not(.item-helper--selected) {
            cursor: pointer;

            &:hover {
              &::after {
                content: '';
                position: absolute;
                border-radius: inherit;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                border: 1px solid rgba(45, 45, 45, 0.31);
              }
            }
          }

          .v-image {
            border-radius: inherit;
            background: linear-gradient(
                95.18deg,
                #f2f8e9 15.34%,
                #ebf3fe 66.75%
              ),
              #c4c4c4;
          }

          &--selected {
            &::before {
              content: '';
              position: absolute;
              border-radius: 18px;
              top: 0;
              left: 0;
              bottom: 0;
              right: 0;
              margin: -2px;
              background: linear-gradient(
                  95.18deg,
                  var(--v-green-base) 15.34%,
                  var(--v-primary-base) 66.75%
                ),
                #c4c4c4;
            }
          }
        }
      }
    }
  }
}
</style>
