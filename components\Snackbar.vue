<template>
  <v-snackbar
    v-model="isShown"
    top
    right
    light
    min-height="48"
    :timeout="snackbarOptions.timeout"
    :color="snackbarOptions.type"
  >
    <div class="snackbar-wrap">
      <template v-if="snackbarOptions.type === 'success'">
        {{ $t(snackbarOptions.successMessage) }}
      </template>
      <template v-if="snackbarOptions.type === 'error'">
        {{ $t(snackbarOptions.errorMessage) }}
      </template>
    </div>

    <template #action>
      <v-btn
        class="file-remove-btn"
        width="24"
        height="24"
        dark
        icon
        @click="isShown = false"
      >
        <svg width="18" height="18" viewBox="0 0 34 34">
          <use
            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#close-big`"
          ></use>
        </svg>
      </v-btn>
    </template>
  </v-snackbar>
</template>

<script>
export default {
  name: 'Snackbar',
  props: {
    snackbarOptions: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    isShown: {
      get() {
        return this.$store.state.snackbar.isShown
      },
      set(value) {
        this.$store.commit('snackbar/SET_IS_SHOWN', value)
      },
    },
  },
}
</script>

<style lang="scss" scoped>
.snackbar-wrap {
  color: #fff;
}
</style>
