<template>
  <div class="free-slots">
    <div class="free-slots-title mb-2">
      {{ $t('calendar_preview') }}
    </div>
    <div class="free-slots-head unselected d-flex justify-space-between mb-2">
      <div class="free-slots-period">
        {{ firstDayOfWeek.format('D MMM') }} -
        {{ lastDayOfWeek.format('D MMM') }}
      </div>
      <div>
        <div class="d-flex">
          <div
            :class="['btn btn-prev', { 'btn--disabled': isPrevButtonDisabled }]"
            @click="!isPrevButtonDisabled ? toggleWeek(-7) : false"
          >
            <v-icon color="greyDark">{{ mdiChevronLeft }}</v-icon>
          </div>
          <div class="btn btn-next" @click="toggleWeek(7)">
            <v-icon color="greyDark">{{ mdiChevronRight }}</v-icon>
          </div>
        </div>
      </div>
    </div>
    <div class="free-slots-table unselected">
      <div class="slots-table">
        <div class="slots-table-top-bar">
          <div class="slots-table-top-bar-helper">
            <div v-for="i in 7" :key="i" class="item">
              {{ getDayOfWeek(i) | dayFormat('dd') }}
            </div>
          </div>
        </div>
        <div class="slots-table-wrap">
          <div class="slots-table-col slots-table-col--time">
            <div :class="['item d-flex align-center', $i18n.locale]">
              <div class="item-icon">
                <AlarmGradientIcon />
              </div>
              <template v-if="$i18n.locale === 'en'"> 6 am - 12 pm </template>
              <template v-else> 06:00 - 12:00 </template>
            </div>
            <div :class="['item d-flex align-center', $i18n.locale]">
              <div class="item-icon">
                <SunGradientIcon />
              </div>
              <template v-if="$i18n.locale === 'en'"> 12 pm - 5 pm </template>
              <template v-else> 12:00 - 17:00 </template>
            </div>
            <div :class="['item d-flex align-center', $i18n.locale]">
              <div class="item-icon">
                <SunsetGradientIcon />
              </div>
              <template v-if="$i18n.locale === 'en'"> 5 pm - 12 am </template>
              <template v-else> 17:00 - 00:00 </template>
            </div>
            <div :class="['item d-flex align-center', $i18n.locale]">
              <div class="item-icon">
                <MoonGradientIcon />
              </div>
              <template v-if="$i18n.locale === 'en'"> 12 am - 6 am </template>
              <template v-else> 00:00 - 06:00 </template>
            </div>
          </div>
          <div class="d-flex slots-table-col--day">
            <div
              v-for="(day, idx) in calendar"
              :key="`d-${idx}`"
              class="slots-table-col"
            >
              <div
                v-for="(period, index) in day"
                :key="`p-${index}`"
                :class="['item', { 'item--free': period.isFree }]"
                style="font-size: 7px"
                @click="period.isFree ? $emit('schedule-lessons') : null"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <template v-if="!hasFreeSlots">
        <div class="free-slots-table--disabled free-slots-table--unavailable">
          <div>{{ $t('no_availability') }} <span>🙁</span></div>
        </div>
      </template>
      <template v-else>
        <div
          v-if="!acceptNewStudents && !studentHasLessonsWithTeacher"
          class="free-slots-table--disabled subtitle-2"
        >
          <div
            v-html="$t('teacher_is_only_taking_bookings_from_current_students')"
          ></div>
        </div>
      </template>
    </div>

    <loader :is-loading="isLoading" absolute></loader>
  </div>
</template>

<script>
import { mdiChevronLeft, mdiChevronRight } from '@mdi/js'
import Loader from '~/components/Loader'
import AlarmGradientIcon from '~/components/images/AlarmGradientIcon'
import SunGradientIcon from '~/components/images/SunGradientIcon'
import SunsetGradientIcon from '~/components/images/SunsetGradientIcon'
import MoonGradientIcon from '~/components/images/MoonGradientIcon'

export default {
  name: 'FreeSlots',
  components: {
    Loader,
    AlarmGradientIcon,
    SunGradientIcon,
    SunsetGradientIcon,
    MoonGradientIcon,
  },
  filters: {
    dayFormat(time, format = 'HH:mm') {
      return time.format(format)
    },
  },
  props: {
    hasFreeSlots: {
      type: Boolean,
      required: true,
    },
    acceptNewStudents: {
      type: Boolean,
      required: true,
    },
    studentHasLessonsWithTeacher: {
      type: Boolean,
      required: true,
    },
    slug: {
      type: String,
      required: true,
    },
    currentTime: {
      type: Object,
      required: true,
    },
    isShownTimePickerDialog: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      mdiChevronLeft,
      mdiChevronRight,
      now: this.$dayjs(),
      calendar: [],
      isLoading: false,
    }
  },
  computed: {
    firstDayOfWeek() {
      return this.currentTime.day(1)
    },
    lastDayOfWeek() {
      return this.currentTime.day(7)
    },
    slots() {
      return this.$store.state.teacher_profile.slots
    },
    isPrevButtonDisabled() {
      return this.now.day(1).isSameOrAfter(this.firstDayOfWeek, 'day')
    },
    timeZone() {
      return this.$store.getters['user/timeZone']
    },
  },
  watch: {
    slots: {
      handler() {
        if (this.isShownTimePickerDialog) {
          setTimeout(this.generateCalendar, 0)
        }
      },
      deep: true,
    },
  },
  created() {
    this.generateCalendar()
  },
  methods: {
    generateCalendar() {
      const items = []

      for (let d = 1; d <= 7; d++) {
        const dateOffset = this.getDayOfWeek(d).tz(this.timeZone).utcOffset()

        for (let p = 0; p < 4; p++) {
          let period

          switch (p) {
            case 0:
              period = [this.getDayOfWeek(d, 6), this.getDayOfWeek(d, 12)]
              break
            case 1:
              period = [this.getDayOfWeek(d, 12), this.getDayOfWeek(d, 17)]
              break
            case 2:
              period = [this.getDayOfWeek(d, 17), this.getDayOfWeek(d, 24)]
              break
            case 3:
              period = [this.getDayOfWeek(d, 0), this.getDayOfWeek(d, 6)]
              break
          }

          const arr = this.slots.filter((item) => {
            const dateObj = this.$dayjs(item.date)
            const date = dateObj.add(dateOffset, 'minute')

            return (
              date.isBetween(
                this.$dayjs.utc(period[0]),
                this.$dayjs.utc(period[1]),
                'minute'
              ) &&
              date.isSameOrAfter(this.now.add(1, 'day'), 'minute') &&
              item.status === 0
            )
          })

          items.push({
            period,
            isFree: !!arr.length,
          })
        }
      }

      this.calendar = []

      for (let i = 0; i < 7; i++) {
        this.calendar.push(items.slice(i * 4, 4 * (i + 1)))
      }
    },
    getDayOfWeek(day, hour = 0) {
      return this.currentTime.day(day).hour(hour).minute(0).second(0)
    },
    async toggleWeek(day) {
      const date = this.firstDayOfWeek.add(day, 'day')

      await this.$store.dispatch('loadingAllow', false)

      this.isLoading = true

      this.$store
        .dispatch('teacher_profile/getSlots', {
          slug: this.slug,
          date,
        })
        .then(() => {
          this.$emit('update-current-time', date)
          this.$nextTick(this.generateCalendar)
        })
        .finally(() => {
          this.isLoading = false

          this.$store.dispatch('loadingAllow', true)
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.free-slots {
  &-title {
    font-size: 20px;
    font-weight: 700;
    line-height: 1.3;
  }

  &-head {
    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      cursor: pointer;

      &-prev {
        margin-right: 10px;
      }

      &--disabled {
        cursor: auto;
        opacity: 0.4;
      }
    }
  }

  &-period {
    font-size: 16px;
    font-weight: 700;
  }

  &-table {
    position: relative;
    width: calc(100% + 8px);
    margin-left: -4px;

    &--disabled {
      position: absolute;
      top: -40px;
      left: -4px;
      width: calc(100% + 8px);
      height: calc(100% + 40px);
      z-index: 2;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(45, 45, 45, 0.8);
        border-radius: 12px;
      }

      & > div {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        padding: 0 10px;
        color: #fff;
        text-align: center;
        transform: translate(-50%, -50%);
      }
    }

    &--unavailable {
      & > div {
        font-size: 24px;
        font-weight: 700;
        transform: translate(-50%, -50%) rotate(-15deg);

        & > span {
          opacity: 1;
        }
      }
    }

    .slots-table {
      &-wrap {
        display: flex;
      }

      &-top-bar {
        padding: 0 0 3px 68px;

        &-helper {
          display: flex;
          width: 100%;

          .item {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-grow: 1;
            height: 14px;
            font-size: 12px;
          }
        }
      }

      &-col {
        .item {
          height: 34px;
          margin: 0 3px 4px 0;
          border-radius: 2.5px;
        }

        &--time {
          .item {
            position: relative;
            width: 68px;
            padding: 3px 5px 3px 29px;
            font-size: 10px;
            line-height: 1.26;
            color: #fff;
            background-color: var(--v-dark-base);

            svg,
            .v-image {
              position: absolute;
              top: 50%;
              left: 6px;
              transform: translateY(-50%);
            }

            &.en {
              font-size: 12px;
              line-height: 1.16;
            }
          }
        }

        &--day {
          width: 100%;

          .slots-table-col {
            flex-grow: 1;
          }
          .item {
            font-size: 12px;
            line-height: 14px;
            background-color: #eaeaea;

            &--free {
              background-color: var(--v-success-base) !important;
              cursor: pointer;
            }
          }
        }

        &:last-child {
          .item {
            margin-right: 0;
          }
        }
      }
    }
  }
}
</style>
