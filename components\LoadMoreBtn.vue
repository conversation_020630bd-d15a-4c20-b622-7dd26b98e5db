<template>
  <v-btn
    :class="['load-more-btn', { 'load-more-btn--large': large }]"
    text
    width="100%"
    @click="fetchFunc"
  >
    <div class="load-more-btn-icon mr-1">
      <svg viewBox="0 0 17 12">
        <use
          :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#arrow-prev`"
        ></use>
      </svg>
    </div>
    {{ textBtn }}
  </v-btn>
</template>

<script>
export default {
  name: 'LoadMoreBtn',
  props: {
    large: {
      type: Boolean,
      default: false,
    },
    textBtn: {
      type: String,
      required: true,
    },
    fetchFunc: {
      type: Function,
      required: true,
    },
  },
}
</script>

<style lang="scss" scoped>
.load-more-btn {
  color: var(--v-greyDark-base);

  &-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 38px;
    color: #fff;
    border-radius: 50%;
    background: linear-gradient(
      126.15deg,
      var(--v-success-base) 0%,
      var(--v-primary-base) 102.93%
    );

    svg {
      width: 19px;
      height: 14px;
      transform: rotate(-90deg);
    }
  }

  &--large {
    height: 52px !important;

    .load-more-btn-icon {
      width: 52px;
      height: 52px;

      svg {
        width: 26px;
        height: 16px;
      }
    }
  }
}
</style>
