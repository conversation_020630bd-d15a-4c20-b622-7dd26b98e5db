<template>
  <div @mouseenter="mouseenter" @mouseleave="mouseleave">
    <vue-draggable-resizable
      ref="vueDraggableResizable"
      :draggable="enabledDraggable"
      :resizable="enabledResizeable"
      :w="width"
      :h="height + childHeaderHeight"
      :x="leftScale"
      :y="topScale"
      :z="index"
      :zoom-index="scalable ? zoom.zoomIndex : 1"
      :zoom-x="zoom.x"
      :zoom-y="zoom.y"
      :child-header-height="childHeaderHeight"
      :class="{
        student: role === 'student',
        teacher: role === 'teacher',
        'hide-resize-icon': hideResizeIcon,
      }"
      :style="{
        outline:
          isHoveredByAsset || isHovered
            ? `3px solid ${getRoleHoverColor}`
            : 'none',
      }"
      :lock-aspect-ratio="lockAspectRatio"
      :handles="handles"
      @dragging="onDrag"
      @resizing="onResize"
      @dragstop="onDragStop"
      @resizestop="onResizeStop"
      @update-asset="updateAsset"
      @click.native="onIndex"
    >
      <slot :onIndex="onIndex"></slot>
    </vue-draggable-resizable>
  </div>
</template>

<script>
import VueDraggableResizable from '~/components/classroom/vue-draggable-resizable/VueDraggableResizable'
import {
  defaultWidth,
  defaultHeight,
  mainCanvasWidth,
  mainCanvasHeight,
} from '~/helpers/constants'

import SetTool from '~/mixins/SetTool'

import '~/components/classroom/vue-draggable-resizable/vue-draggable-resizable.css'

export default {
  components: { VueDraggableResizable },
  mixins: [SetTool],
  props: {
    asset: {
      type: Object,
      required: true,
    },
    childHeaderHeight: {
      type: Number,
      default: 0,
    },
    lockAspectRatio: {
      type: Boolean,
      default: true,
    },
    scalable: {
      type: Boolean,
      default: true,
    },
    hoverEnabled: {
      type: Boolean,
      default: true,
    },
    isDraggableProp: {
      type: Boolean,
      default: true,
    },
    hideResizeIcon: {
      type: Boolean,
      default: false,
    },
    handles: {
      type: Array,
      default: () => ['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml'],
    },
  },
  data() {
    return {
      width: defaultWidth,
      height: defaultHeight,
      top: 50,
      left: 500,
      isHovered: false,
      isHoveredByAsset: false,
      index: 5,
      eventBodyClass: null,
      allowIndexChange: true,
      resizable: true,
      draggable: true,
      synchronizeable: true,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight,
      offset: 5,
    }
  },
  computed: {
    isCanvasOversizeX() {
      return mainCanvasWidth > this.viewportWidth
    },
    isCanvasOversizeY() {
      return mainCanvasHeight > this.viewportHeight
    },
    isScaledCanvasOversizeY() {
      return mainCanvasHeight * this.zoomIndex > this.viewportHeight
    },
    getRoleHoverColor() {
      return this.role === 'teacher'
        ? 'var(--v-teacherColor-base)'
        : 'var(--v-studentColor-base)'
    },
    enabledResizeable() {
      return (
        this.resizable &&
        this.$store.state.classroom.containerComponentEnabled &&
        !this.isLockedForStudent
      )
    },
    enabledDraggable() {
      return (
        this.isDraggableProp &&
        this.draggable &&
        this.$store.state.classroom.containerComponentEnabled &&
        !this.isLockedForStudent
      )
    },
    maxIndex() {
      return this.$store.state.classroom.maxIndex
    },
    zoom() {
      return this.$store.getters['classroom/zoomAsset'].asset
    },
    zoomIndex() {
      return this.zoom?.zoomIndex ?? 1
    },
    type() {
      return this.asset?.asset?.type
    },
    topScale() {
      if (this.type === 'toolbar') {
        return this.top < 0 || this.top + this.height > this.viewportHeight
          ? this.viewportHeight - this.height - 70
          : this.top
      }

      return this.synchronizeable ? this.top - this.zoom.y : this.top
    },
    leftScale() {
      if (this.type === 'toolbar') {
        return this.left + this.width > this.viewportWidth || this.left < 0
          ? this.viewportWidth - this.width - 15
          : this.left
      }

      return this.synchronizeable ? this.left - this.zoom.x : this.left
    },
    isLockedForStudent() {
      return (
        this.$store.getters['classroom/isLocked'] && this.role === 'student'
      )
    },
    isSocketConnected() {
      return this.$store.state.socket.isConnected
    },
  },
  watch: {
    'asset.asset'(asset) {
      this.move(asset)
    },
  },
  mounted() {
    this.move(this.asset.asset)
  },
  methods: {
    mouseenter() {
      if (this.hoverEnabled && this.synchronizeable) {
        this.isHovered = true

        this.socketAssetMoved({ isHovered: true })
      }

      // Twilio and TokBox video providers are no longer used - commented out
      /*
      if (
        this.type === 'twilio' ||
        this.type === 'tokbox' ||
        this.type === 'editor'
      ) {
        this.$store.commit(
          'classroom/setCursorNameBeforeChange',
          this.$store.getters['classroom/userParams']?.cursor ||
            'cursor-pointer'
        )
        this.$store.commit(
          'classroom/setToolNameBeforeChange',
          this.$store.getters['classroom/userParams']?.tool || 'pointer'
        )

        this.setTool('pointer', 'cursor-pointer')
      }
      */

      // Only handle editor type now
      if (this.type === 'editor') {
        this.$store.commit(
          'classroom/setCursorNameBeforeChange',
          this.$store.getters['classroom/userParams']?.cursor ||
            'cursor-pointer'
        )
        this.$store.commit(
          'classroom/setToolNameBeforeChange',
          this.$store.getters['classroom/userParams']?.tool || 'pointer'
        )

        this.setTool('pointer', 'cursor-pointer')
      }
    },
    mouseleave() {
      if (this.hoverEnabled && this.synchronizeable) {
        this.isHovered = false

        this.socketAssetMoved({ isHovered: false })
      }

      // Twilio and TokBox video providers are no longer used - commented out
      /*
      if (
        this.type === 'twilio' ||
        this.type === 'tokbox' ||
        this.type === 'editor'
      ) {
        this.setTool(
          this.$store.state.classroom.toolNameBeforeChange,
          this.$store.state.classroom.cursorNameBeforeChange
        )
      }
      */

      // Only handle editor type now
      if (this.type === 'editor') {
        this.setTool(
          this.$store.state.classroom.toolNameBeforeChange,
          this.$store.state.classroom.cursorNameBeforeChange
        )
      }
    },
    onIndex() {
      this.index = this.maxIndex + 1

      this.$store.commit('classroom/setMaxIndex', this.index)
      this.$store.commit('classroom/moveAsset', {
        id: this.asset.id,
        asset: { index: this.index },
      })
      this.$store.dispatch('classroom/moveAsset', {
        id: this.asset.id,
        lessonId: this.asset.lessonId,
        asset: {
          index: this.index,
        },
      })
    },
    updateAsset(left, top) {
      this.$store.commit('classroom/moveAsset', {
        id: this.asset.id,
        asset: {
          left: this.synchronizeable ? left + this.zoom.x : left,
          top: this.synchronizeable ? top + this.zoom.y : top,
          index: this.index,
        },
      })
    },
    onDrag(left, top) {
      if (this.synchronizeable) {
        this.left = left + this.zoom.x
        this.top = top + this.zoom.y

        if (this.allowIndexChange) {
          const el = document.body

          if (!el.classList.contains(this.eventBodyClass)) {
            this.eventBodyClass = 'dragging'

            el.classList.add(this.eventBodyClass)
          }

          this.allowIndexChange = false
          this.index = this.maxIndex + 1

          this.$store.commit('classroom/setMaxIndex', this.index)
        }

        const asset = {
          left: this.left,
          top: this.top,
          index: this.index,
        }

        this.$store.commit('classroom/moveAsset', {
          id: this.asset.id,
          asset,
        })

        this.socketAssetMoved(asset)
      }
    },
    onDragStop(left, top) {
      const el = document.body

      if (el.classList.contains(this.eventBodyClass)) {
        el.classList.remove(this.eventBodyClass)

        this.eventBodyClass = null
      }

      this.allowIndexChange = true

      this.$store.commit('classroom/setMaxIndex', this.index)
      this.$store.dispatch('classroom/moveAsset', {
        id: this.asset.id,
        lessonId: this.asset.lessonId,
        asset: {
          left: this.synchronizeable ? left + this.zoom.x : left,
          top: this.synchronizeable ? top + this.zoom.y : top,
          index: this.index,
        },
      })
    },
    onResize(left, top, width, height, className) {
      if (this.synchronizeable) {
        this.left = left + this.zoom.x
        this.top = top + this.zoom.y
        this.width = width
        this.height = height - this.childHeaderHeight

        if (this.allowIndexChange) {
          const el = document.body

          if (!el.classList.contains(this.eventBodyClass)) {
            this.eventBodyClass = className.split(' ')[1]

            el.classList.add(this.eventBodyClass)
          }

          this.allowIndexChange = false
          this.index = this.maxIndex + 1

          this.$store.commit('classroom/setMaxIndex', this.index)
        }

        const asset = {
          left: this.left,
          top: this.top,
          width: this.width,
          height: this.height,
          index: this.index,
        }

        this.$store.commit('classroom/moveAsset', {
          id: this.asset.id,
          asset,
        })

        this.socketAssetMoved(asset)
      }
    },
    onResizeStop(left, top, width, height) {
      if (this.eventBodyClass) {
        document.body.classList.remove(this.eventBodyClass)
      }

      this.allowIndexChange = true

      this.$store.dispatch('classroom/moveAsset', {
        id: this.asset.id,
        lessonId: this.asset.lessonId,
        asset: {
          left: this.synchronizeable ? left + this.zoom.x : left,
          top: this.synchronizeable ? top + this.zoom.y : top,
          width,
          height: height - this.childHeaderHeight,
          index: this.index,
        },
      })
    },
    socketAssetMoved(asset) {
      if (this.isSocketConnected) {
        this.$socket.emit('asset-moved', {
          id: this.asset.id,
          lessonId: this.asset.lessonId,
          asset,
        })
      }
    },
    move(asset) {
      if (asset.width !== undefined) {
        this.width = asset.width
      } else {
        // if (this.type === 'toolbar') {
        //   this.width = this.$refs.vueDraggableResizable.$el.clientWidth
        // }

        // eslint-disable-next-line no-lonely-if
        if (this.type === 'editor') {
          this.width =
            (this.isCanvasOversizeX ? this.viewportWidth : mainCanvasWidth) *
            0.66
        }
      }

      if (asset.height !== undefined) {
        this.height = asset.height
      } else {
        // if (this.type === 'toolbar') {
        //   this.height = this.$refs.vueDraggableResizable.$el.clientHeight
        // }

        // eslint-disable-next-line no-lonely-if
        if (this.type === 'editor') {
          let height =
            (this.isCanvasOversizeY ? this.viewportHeight : mainCanvasHeight) *
            0.8

          if (height > 1200) {
            height = 1200
          }

          if (height < 400) {
            height = 400
          }

          this.height = height - this.offset * 2
        }

        if (this.type === 'audio') {
          this.height = 0
        }
      }

      if (asset.top !== undefined) {
        this.top = asset.top
      } else {
        // if (this.type === 'toolbar') {
        //   this.top = (this.isScaledCanvasOversizeY ? this.viewportHeight : mainCanvasHeight * this.zoomIndex) - this.height - this.offset
        // }

        // eslint-disable-next-line no-lonely-if
        // Twilio and TokBox video providers are no longer used - commented out
        /*
        if (
          this.type === 'twilio' ||
          this.type === 'tokbox' ||
          this.type === 'editor'
        ) {
          this.top = this.offset
        }
        */

        // Only handle editor type now
        // eslint-disable-next-line no-lonely-if
        if (this.type === 'editor') {
          this.top = this.offset
        }
      }

      if (asset.left !== undefined) {
        this.left = asset.left
      } else {
        // if (this.type === 'toolbar') {
        //   this.left = (this.isScaledCanvasOversizeX ? this.viewportWidth : mainCanvasWidth ) - this.width - this.offset
        // }

        // Twilio and TokBox video providers are no longer used - commented out
        // /*
        if (this.type === 'whereby') {
          this.left =
            (this.isCanvasOversizeX ? this.viewportWidth : mainCanvasWidth) -
            this.width -
            this.offset
        }
        // */

        // eslint-disable-next-line no-lonely-if
        if (this.type === 'editor') {
          this.left = this.offset
        }
      }

      if (asset.index !== undefined) {
        this.index = asset.index
      }

      if (asset.resizable !== undefined) {
        this.resizable = asset.resizable
      }

      if (asset.draggable !== undefined) {
        this.draggable = asset.draggable
      }

      if (asset.synchronizeable !== undefined) {
        this.synchronizeable = asset.synchronizeable
      }

      if (asset.isHovered !== undefined) {
        this.isHoveredByAsset = asset.isHovered
      }
    },
  },
}
</script>
