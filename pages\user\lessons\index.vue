<template>
  <lessons-page route="/user/lessons" :type="type"></lessons-page>
</template>

<script>
import LessonsPage from '~/components/user-lessons/LessonsPage'

export default {
  name: 'UpcomingLessons',
  components: { LessonsPage },
  middleware: 'authenticated',
  async asyncData({ store, query }) {
    const type = 'upcoming'
    const searchQuery = query?.search

    await Promise.all([
      store.dispatch('lesson/getUpcomingLessons', {
        page: 1,
        perPage: process.env.NUXT_ENV_PER_PAGE,
        type,
        searchQuery,
      }),
      store.dispatch('lesson/getCalendarItems'),
    ])
    await store.dispatch('user/getUserStatus')

    return { type }
  },
  head() {
    return {
      title: this.$t('user_upcoming_lessons_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('user_upcoming_lessons_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('user_upcoming_lessons_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('user_upcoming_lessons_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} user-lessons-page user-upcoming-lessons-page`,
      },
    }
  },
  watchQuery: true,
}
</script>
