<script>
import { getFileExtension } from '~/helpers'
import { MAX_FILE_SIZE } from '~/helpers/constants'

export default {
  computed: {
    lessonId() {
      return this.$store.state.classroom.lessonId
    },
    role() {
      return this.$store.getters['classroom/role']
    },
    acceptedFiles() {
      return this.$store.state.classroom.acceptedFiles
    },
  },
  methods: {
    async uploadFiles(files) {
      files = [...files]

      const formData = new FormData()

      for (let i = 0; i <= files.length - 1; i++) {
        const file = files[i]
        const fileExtension = getFileExtension(file.name)

        if (file.size > MAX_FILE_SIZE) {
          await this.$store.dispatch('snackbar/error', {
            errorMessage: this.$t('filename_size_should_be_less_than', {
              fileName: file.name,
              value: `${(MAX_FILE_SIZE / 8 / 1000).toFixed(0)} Mb`,
            }),
            timeout: 5000,
          })

          continue
        }

        if (this.acceptedFiles.officeTypes.includes(fileExtension)) {
          const { data, fileName } = await this.$store.dispatch(
            'classroom/convertOfficeToPdf',
            file
          )

          formData.append(i.toString(), new Blob([data]), fileName)
        } else {
          formData.append(i.toString(), file)
        }
      }

      this.$store
        .dispatch('classroom/uploadFiles', formData)
        .then((assets) => {
          let offsetX = 0
          let offsetY = 0

          this.$store.commit('classroom/addAssets', assets)

          assets.forEach((asset) => {
            const item = {
              id: asset.id,
              lessonId: this.lessonId,
              asset: {
                ...asset.asset,
                index: this.$store.state.classroom.maxIndex + 1,
                owner: this.role,
                top:
                  this.$store.getters['classroom/zoomAsset'].asset.y +
                  offsetY +
                  100,
                left:
                  this.viewportWidth / 2 +
                  this.$store.getters['classroom/zoomAsset'].asset.x +
                  offsetX -
                  250,
              },
            }
            const ext = getFileExtension(item.asset.path)

            let type

            if (this.acceptedFiles?.pdfTypes.includes(ext)) {
              type = 'pdf'
            } else if (this.acceptedFiles?.imageTypes.includes(ext)) {
              type = 'image'
            } else if (this.acceptedFiles?.audioTypes.includes(ext)) {
              type = 'audio'
            } else {
              return
            }

            item.asset.type = type

            this.$store.commit('classroom/moveAsset', item)
            this.$store.dispatch('classroom/moveAsset', item)

            this.$socket.emit('asset-added', item)

            offsetX += 50
            offsetY += 50
          })
        })
        .catch((e) => {
          // @TODO classroom
          // Bugsnag.notify(e)
          throw e
        })
    },
  },
}
</script>
