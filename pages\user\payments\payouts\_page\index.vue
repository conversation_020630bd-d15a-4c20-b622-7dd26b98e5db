<template>
  <payments-page :type="'payouts'" :page="page"></payments-page>
</template>

<script>
import PaymentsPage from '~/components/payments/PaymentsPage'

export default {
  name: 'PayoutsPageWithPagination',
  components: { PaymentsPage },
  middleware: ['authenticated', 'paymentsPageClass'],

  async asyncData({ params, store, query }) {
    const page = parseInt(params.page) || 1
    const searchQuery = query?.search

    await store.dispatch('payments/fetchPayouts', {
      page,
      itemsPerPage: 20, // Set to 20 items per page
    })

    // Set current page in store
    store.commit('payments/SET_CURRENT_PAGE', page)

    return {
      page,
      type: 'payouts',
      searchQuery,
    }
  },

  head() {
    return {
      title: this.$t('teacher_payments_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('teacher_payments_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('teacher_payments_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('teacher_payments_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} user-payments-page`,
      },
    }
  },

  computed: {
    locale() {
      return this.$i18n.locale
    },
  },

  watchQuery: ['page', 'search'],
}
</script>
