<template>
  <l-dialog
    :dialog="isShownTimePickerDialog"
    max-width="1030"
    custom-class="time-picker"
    persistent
    :fullscreen="$vuetify.breakpoint.smAndDown"
    v-on="$listeners"
  >
    <div class="time-picker-header text--gradient">
      <template v-if="!isSelectedTrial">
        {{ $t('schedule_your_lessons') }}:
      </template>
      <template v-else> {{ $t('schedule_your_trial_lesson') }} </template>
    </div>
    <div class="wrap">
      <div class="time-picker-body">
        <div class="time-picker-selects mb-md-2">
          <div class="selects">
            <div class="selects-language">
              <template v-if="isSelectedDefaultCourse">
                <select-input
                  v-model="selectedLanguage"
                  :items="languages"
                  :menu-props="{ minWidth: 190 }"
                  item-value="isoCode"
                  :translation="false"
                  :readonly="!isSelectedDefaultCourse"
                ></select-input>
              </template>
              <template v-else>
                <div class="selects-lesson-value d-flex align-center">
                  <div
                    v-if="selectedCourse.language.isoCode"
                    class="icon icon-flag"
                  >
                    <v-img
                      v-if="selectedCourse.language.isoCode"
                      :src="
                        require(`~/assets/images/flags/${selectedCourse.language.isoCode}.svg`)
                      "
                      width="18"
                      height="18"
                    ></v-img>
                  </div>
                  {{ selectedCourse.language.name }}
                </div>
              </template>
            </div>

            <div
              v-if="!isSelectedTrial && courses.length"
              class="selects-course"
            >
              <select-input
                :value="selectedCourse_"
                :items="courses_"
                :menu-props="{ minWidth: 200 }"
                @change="changeCourse"
              ></select-input>
            </div>

            <div
              v-if="!isSelectedTrial || !isSelectedDefaultCourse"
              class="selects-lesson"
            >
              <template v-if="isSelectedDefaultCourse">
                <v-select
                  :value="selectedCourse"
                  :items="packages"
                  item-value="id"
                  class="l-select"
                  dense
                  hide-details
                  return-object
                  :readonly="!isSelectedDefaultCourse"
                  :menu-props="{
                    bottom: true,
                    offsetY: true,
                    minWidth: 140,
                    contentClass: 'select-list',
                  }"
                  @change="changeCourse"
                >
                  <template #append>
                    <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
                  </template>
                  <template #selection="{ item }">
                    <div class="icon">
                      <v-img
                        :src="require('~/assets/images/clock-gradient.svg')"
                        width="18"
                        height="18"
                      ></v-img>
                    </div>

                    {{ $tc('lessons_count', item.lessons) }}
                  </template>
                  <template #item="{ item }">
                    <div class="icon">
                      <v-img
                        :src="require('~/assets/images/clock-gradient.svg')"
                        width="16"
                        height="16"
                      ></v-img>
                    </div>

                    {{ $tc('lessons_count', item.lessons) }}
                  </template>
                </v-select>
              </template>
              <template v-else>
                <div class="selects-lesson-value d-flex align-center">
                  <div class="icon">
                    <v-img
                      :src="require('~/assets/images/clock-gradient.svg')"
                      width="18"
                      height="18"
                    ></v-img>
                  </div>

                  {{ $tc('lessons_count', selectedCourse.lessons) }}
                </div>
              </template>
            </div>

            <div v-if="!isSelectedTrial" class="selects-duration">
              <template v-if="isSelectedDefaultCourse">
                <v-select
                  v-model="selectedLessonLength"
                  :items="lessonLengthPackages"
                  item-value="length"
                  class="l-select"
                  dense
                  return-object
                  hide-details
                  :readonly="!isSelectedDefaultCourse"
                  :menu-props="{
                    bottom: true,
                    offsetY: true,
                    minWidth: 140,
                    contentClass: 'select-list',
                  }"
                >
                  <template #append>
                    <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
                  </template>
                  <template #selection="{ item }">
                    <div class="icon">
                      <v-img
                        :src="require('~/assets/images/clock-gradient.svg')"
                        width="18"
                        height="18"
                      ></v-img>
                    </div>

                    {{ $tc('minutes_count', item.length) }}
                  </template>
                  <template #item="{ item }">
                    <div class="icon">
                      <v-img
                        :src="require('~/assets/images/clock-gradient.svg')"
                        width="16"
                        height="16"
                      ></v-img>
                    </div>

                    {{ $tc('minutes_count', item.length) }}
                  </template>
                </v-select>
              </template>
              <template v-else>
                <div class="selects-lesson-value d-flex align-center">
                  <div class="icon">
                    <v-img
                      :src="require('~/assets/images/clock-gradient.svg')"
                      width="18"
                      height="18"
                    ></v-img>
                  </div>

                  {{ $tc('minutes_count', selectedCourse.length) }}
                </div>
              </template>
            </div>
          </div>
          <div v-if="!isSelectedTrial" class="selects-notice caption">
            *{{ $t('you_can_schedule_all_lessons_now_or_schedule_them_later') }}
          </div>
        </div>
        <time-picker
          :username="username"
          :lesson-length="selectedCourse.length"
          :quantity-lessons="selectedCourse.lessons"
          :current-time="currentTime"
          :is-shown-time-picker-dialog="isShownTimePickerDialog"
          v-on="$listeners"
        ></time-picker>
        <lesson-time-notice
          v-if="$vuetify.breakpoint.smAndDown"
          class="caption mt-1 d-md-none"
          one-line
          @show-login-sidebar="$emit('close-dialog')"
        ></lesson-time-notice>
      </div>
    </div>

    <div class="time-picker-footer">
      <lesson-time-notice
        v-if="$vuetify.breakpoint.mdAndUp"
        class="caption d-none d-md-block"
        one-line
        @show-login-sidebar="$emit('close-dialog')"
      ></lesson-time-notice>
      <div>
        <v-btn small color="primary" :disabled="isNotValid" @click="nextStep">{{
          $t('continue')
        }}</v-btn>
      </div>
    </div>
  </l-dialog>
</template>

<script>
import { mdiChevronDown } from '@mdi/js'
import LDialog from '~/components/LDialog'
import TimePicker from '~/components/TimePicker'
import LessonTimeNotice from '~/components/LessonTimeNotice'
import SelectInput from '~/components/form/SelectInput'

export default {
  name: 'TimePickerDialog',
  components: { LDialog, TimePicker, LessonTimeNotice, SelectInput },
  props: {
    isShownTimePickerDialog: {
      type: Boolean,
      required: true,
    },
    username: {
      type: String,
      required: true,
    },
    languages: {
      type: Array,
      required: true,
    },
    courses: {
      type: Array,
      required: true,
    },
    query: {
      type: Object,
      required: true,
    },
    currentTime: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      mdiChevronDown,
      defaultCourse: {
        isDefault: true,
        name: this.$t('general_learning'),
      },
    }
  },
  computed: {
    courses_() {
      return [this.defaultCourse, ...this.courses]
    },
    trialPackage() {
      return this.$store.getters['teacher_profile/trialPackage']
    },
    isSelectedTrial() {
      return this.$store.getters['teacher_profile/isSelectedTrial']
    },
    packages() {
      return this.$store.getters['teacher_profile/packages'].filter(
        (item) => !item.isCourse
      )
    },
    lessonLengthPackages() {
      return this.$store.getters['teacher_profile/lessonLengthPackages'].filter(
        (item) => !item.isTrial && !item.isCourse
      )
    },
    lengthPackages() {
      return this.$store.getters['teacher_profile/lengthPackages']
    },
    selectedCourse() {
      return this.$store.state.teacher_profile.selectedCourse
    },
    selectedCourse_() {
      return this.isSelectedDefaultCourse
        ? this.defaultCourse
        : this.selectedCourse
    },
    selectedLessonLength: {
      get() {
        return this.$store.state.teacher_profile.selectedLessonLength
      },
      set(value) {
        this.$store.dispatch('teacher_profile/setSelectedLessonLength', value)
      },
    },
    selectedLanguage: {
      get() {
        return this.$store.state.teacher_profile.selectedLanguage
      },
      set(value) {
        this.$store.commit('teacher_profile/SET_SELECTED_LANGUAGE', value)
      },
    },
    selectedSlots() {
      return this.$store.state.teacher_profile.selectedSlots
    },
    isSelectedDefaultCourse() {
      return this.$store.getters['teacher_profile/isSelectedDefaultCourse']
    },
    isNotValid() {
      return this.selectedSlots.length === 0
    },
  },
  mounted() {
    this.selectedLanguage = this.languages[0]
  },
  methods: {
    changeCourse(value) {
      this.$store.dispatch('teacher_profile/setSelectedCourse', value)
    },
    nextStep() {
      this.$emit('next-step')
    },
  },
}
</script>
