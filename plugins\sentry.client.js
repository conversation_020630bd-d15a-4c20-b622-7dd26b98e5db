// Sentry client-side plugin for langu-frontend-7b
// Only active in staging and production environments
export default function ({ $sentry, app, store }) {
  const environment = process.env.NUXT_ENV_SENTRY_ENVIRONMENT

  // Only initialize Sentry for staging and production
  if (!$sentry || !environment || environment === 'development') {
    return
  }

  // Initialize Sentry with enhanced context
  initializeSentryContext($sentry, store, environment)

  // Set up navigation tracking
  setupNavigationTracking($sentry, app)

  // Set up error handlers
  setupErrorHandlers($sentry)

  // Set up user context watcher
  setupUserContextWatcher($sentry, store)

  // Add test function for staging
  if (environment === 'staging' && process.client) {
    setupStagingTestHelpers($sentry)
  }
}

function initializeSentryContext($sentry, store, environment) {
  // Set application context
  $sentry.setContext('application', {
    name: 'langu-frontend-staging',
    version: process.env.npm_package_version || '1.0.0',
    environment,
    url: process.env.NUXT_ENV_URL,
    buildTime: new Date().toISOString(),
  })

  // Set initial user context if available
  if (store.state.user?.item) {
    setUserContext($sentry, store.state.user.item)
  }

  // Set technical context
  if (process.client) {
    $sentry.setContext('technical', {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
    })
  }
}

function setupNavigationTracking($sentry, app) {
  app.router.afterEach((to, from) => {
    // Add navigation breadcrumb
    $sentry.addBreadcrumb({
      category: 'navigation',
      message: `Navigated from ${from.path} to ${to.path}`,
      level: 'info',
      data: {
        from: from.path,
        to: to.path,
        name: to.name,
        params: Object.keys(to.params).length ? to.params : undefined,
        query: Object.keys(to.query).length ? to.query : undefined,
      },
    })

    // Update page context
    $sentry.setContext('page', {
      path: to.path,
      name: to.name,
      fullPath: to.fullPath,
      params: to.params,
      query: to.query,
      meta: to.meta,
    })

    // Set transaction name for performance monitoring
    $sentry.configureScope((scope) => {
      scope.setTransactionName(`${to.name || 'unknown'} (${to.path})`)
    })
  })
}

function setupErrorHandlers($sentry) {
  if (!process.client) return

  // Enhanced unhandled promise rejection handler
  window.addEventListener('unhandledrejection', (event) => {
    $sentry.captureException(event.reason, {
      tags: {
        source: 'unhandledrejection',
        type: 'promise',
      },
      extra: {
        promiseRejectionEvent: {
          type: event.type,
          cancelable: event.cancelable,
          defaultPrevented: event.defaultPrevented,
        },
      },
    })
  })

  // Global error handler
  window.addEventListener('error', (event) => {
    $sentry.captureException(event.error || new Error(event.message), {
      tags: {
        source: 'window.error',
        type: 'javascript',
      },
      extra: {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      },
    })
  })
}

function setupUserContextWatcher($sentry, store) {
  // Watch for user state changes
  store.watch(
    (state) => state.user?.item,
    (newUser) => {
      if (newUser) {
        setUserContext($sentry, newUser)
      } else {
        $sentry.setUser(null)
      }
    }
  )
}

function setUserContext($sentry, user) {
  $sentry.setUser({
    id: user.id,
    email: user.email,
    username: user.username,
    role: user.userType,
    firstName: user.firstName,
    lastName: user.lastName,
    isTeacher: user.userType === 'teacher',
    isStudent: user.userType === 'student',
  })

  // Add user-specific tags
  $sentry.setTag('user.type', user.userType)
  $sentry.setTag('user.verified', user.isVerified || false)
}

function setupStagingTestHelpers($sentry) {
  // Test helpers for staging environment
  window.sentryTest = {
    message: (msg = 'Test message from staging') => {
      $sentry.captureMessage(msg, 'info')
      // eslint-disable-next-line no-console
      console.log('✅ Sentry test message sent:', msg)
    },
    error: (msg = 'Test error from staging') => {
      $sentry.captureMessage(msg, 'error')
      // eslint-disable-next-line no-console
      console.log('❌ Sentry test error sent:', msg)
    },
    exception: (msg = 'Test exception from staging') => {
      const error = new Error(msg)
      error.name = 'StagingTestError'
      $sentry.captureException(error)
      // eslint-disable-next-line no-console
      console.log('💥 Sentry test exception sent:', msg)
    },
    user: () => {
      // eslint-disable-next-line no-console
      console.log(
        '👤 Current Sentry user context:',
        $sentry.getCurrentHub().getScope().getUser()
      )
    },
    context: () => {
      // eslint-disable-next-line no-console
      console.log(
        '🔍 Current Sentry context:',
        $sentry.getCurrentHub().getScope().getContext()
      )
    },
  }

  // eslint-disable-next-line no-console
  console.log('🔧 Sentry staging helpers available: window.sentryTest')
}
