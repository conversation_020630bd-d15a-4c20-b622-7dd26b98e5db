#!/bin/bash

# Deployment script for langu-frontend-7b with Sentry integration
# Usage: ./scripts/deploy.sh [staging|production]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if environment is provided
if [ $# -eq 0 ]; then
    print_error "Environment not specified!"
    echo "Usage: $0 [staging|production]"
    exit 1
fi

ENVIRONMENT=$1

# Validate environment
if [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "production" ]; then
    print_error "Invalid environment: $ENVIRONMENT"
    echo "Valid environments: staging, production"
    exit 1
fi

print_status "Starting deployment for $ENVIRONMENT environment..."

# Set environment variables based on deployment target
case $ENVIRONMENT in
    "staging")
        ENV_FILE=".env.staging"
        NODE_ENV="staging"
        print_status "Deploying to STAGING environment"
        ;;
    "production")
        ENV_FILE=".env.prod"
        NODE_ENV="production"
        print_status "Deploying to PRODUCTION environment"
        ;;
esac

# Check if environment file exists
if [ ! -f "$ENV_FILE" ]; then
    print_error "Environment file $ENV_FILE not found!"
    exit 1
fi

# Backup current .env if it exists
if [ -f ".env" ]; then
    print_status "Backing up current .env file..."
    cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
fi

# Copy environment-specific configuration
print_status "Setting up environment configuration..."
cp "$ENV_FILE" .env

# Verify Sentry configuration
print_status "Verifying Sentry configuration..."
SENTRY_DSN=$(grep "NUXT_ENV_SENTRY_DSN" .env | cut -d'=' -f2 | tr -d "'\"")
SENTRY_ENV=$(grep "NUXT_ENV_SENTRY_ENVIRONMENT" .env | cut -d'=' -f2 | tr -d "'\"")

if [ -z "$SENTRY_DSN" ] || [ "$SENTRY_DSN" = "" ]; then
    print_error "Sentry DSN not configured for $ENVIRONMENT!"
    exit 1
fi

if [ "$SENTRY_ENV" != "$ENVIRONMENT" ]; then
    print_error "Sentry environment mismatch! Expected: $ENVIRONMENT, Found: $SENTRY_ENV"
    exit 1
fi

print_success "Sentry configuration verified for $ENVIRONMENT"

# Install dependencies
print_status "Installing dependencies..."
npm ci --production=false

# Run linting (optional, can be skipped with --skip-lint)
if [[ ! "$*" =~ "--skip-lint" ]]; then
    print_status "Running linter..."
    npm run lint:fix || print_warning "Linting completed with warnings"
fi

# Build the application
print_status "Building application for $ENVIRONMENT..."
export NODE_ENV=$NODE_ENV
npm run build

# Verify build success
if [ ! -d ".nuxt" ]; then
    print_error "Build failed! .nuxt directory not found."
    exit 1
fi

print_success "Build completed successfully!"

# Test Sentry integration (staging only)
if [ "$ENVIRONMENT" = "staging" ]; then
    print_status "Testing Sentry integration..."
    
    # Start the application in background for testing
    npm run start &
    SERVER_PID=$!
    
    # Wait for server to start
    sleep 10
    
    # Test if server is running
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        print_success "Application started successfully"
        
        # Test Sentry endpoint
        if curl -f http://localhost:3000/sentry-test > /dev/null 2>&1; then
            print_success "Sentry test page accessible"
        else
            print_warning "Sentry test page not accessible"
        fi
    else
        print_error "Application failed to start"
    fi
    
    # Stop the test server
    kill $SERVER_PID 2>/dev/null || true
    wait $SERVER_PID 2>/dev/null || true
fi

# Display deployment summary
print_success "Deployment preparation completed!"
echo ""
echo "=== DEPLOYMENT SUMMARY ==="
echo "Environment: $ENVIRONMENT"
echo "Node Environment: $NODE_ENV"
echo "Sentry Environment: $SENTRY_ENV"
echo "Sentry DSN: ${SENTRY_DSN:0:50}..."
echo "Build Directory: .nuxt"
echo ""

# Display next steps
echo "=== NEXT STEPS ==="
case $ENVIRONMENT in
    "staging")
        echo "1. Deploy to staging server"
        echo "2. Test Sentry integration at /sentry-test"
        echo "3. Use window.sentryTest helpers for testing"
        echo "4. Monitor Sentry dashboard for events"
        ;;
    "production")
        echo "1. Deploy to production server"
        echo "2. Monitor Sentry dashboard for errors"
        echo "3. Check performance metrics"
        echo "4. Set up alerts for critical issues"
        ;;
esac

print_success "Deployment script completed successfully!"
