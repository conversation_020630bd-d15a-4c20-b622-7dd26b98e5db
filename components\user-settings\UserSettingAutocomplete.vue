<template>
  <div :id="attachId" :key="key" class="user-setting-autocomplete">
    <v-autocomplete
      ref="autocomplete"
      :value="value"
      :items="_items"
      dense
      filled
      outlined
      hide-selected
      hide-no-data
      return-object
      :hide-details="hideDetails"
      :rules="rules"
      :item-text="itemText"
      :placeholder="_placeholder"
      :attach="`#${attachId}`"
      :menu-props="{
        bottom: true,
        offsetY: true,
        nudgeBottom: 8,
        contentClass: 'select-list l-scroll',
        maxHeight: 205,
      }"
      @focus="clearSelection"
      v-on="$listeners"
      @change="key++"
    >
      <template #append>
        <svg width="12" height="12" viewBox="0 0 12 12">
          <use :xlink:href="chevronIcon"></use>
        </svg>
      </template>
      <template #item="{ item }">
        <div v-if="item.isoCode" class="icon">
          <v-img
            :src="require(`~/assets/images/flags/${item.isoCode}.svg`)"
            height="24"
            width="24"
            eager
          ></v-img>
        </div>
        <div class="text">{{ item[itemText] }}</div>
      </template>
    </v-autocomplete>
  </div>
</template>

<script>
export default {
  name: 'UserSettingAutocomplete',
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    items: {
      type: Array,
      required: true,
    },
    selectedItems: {
      type: Array,
      default: () => [],
    },
    attachId: {
      type: String,
      required: true,
    },
    itemText: {
      type: String,
      default: 'name',
    },
    rules: {
      type: Array,
      default: () => [],
    },
    hideDetails: {
      type: Boolean,
      default: true,
    },
    // eslint-disable-next-line vue/require-default-prop
    placeholder: [Boolean, String],
  },
  data: () => ({
    key: 1,
    chevronIcon: `${require('~/assets/images/icon-sprite.svg')}#chevron-down`,
  }),
  computed: {
    _placeholder() {
      return !this.placeholder
        ? ''
        : this.$t(this.placeholder || 'choose_language')
    },
    _items() {
      if (!this.selectedItems.length) {
        return this.items
      }

      return this.items.filter(
        (item) =>
          !(this.selectedItems?.map((item) => item.id) ?? []).includes(item.id)
      )
    },
  },
  methods: {
    clearSelection() {
      this.$nextTick(() => {
        const input = this.$refs.autocomplete?.$el?.querySelector('input')
        if (input) {
          input.setSelectionRange(0, 0) // deselect the text
          input.blur() // optional: blur to prevent flashing selection
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
.user-setting-autocomplete {
  position: relative;
}
</style>
