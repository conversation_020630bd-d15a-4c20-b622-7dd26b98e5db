<script>
export default {
  data() {
    return {
      timeoutId: null,
      userStatuses: {},
      arrStatusId: [],
    }
  },
  computed: {
    preparedArr() {
      return [...new Set(this.arrStatusId)]
    },
  },
  mounted() {
    this.timeoutId = window.setInterval(() => {
      this.refreshStatusOnline()

      if (!this.arrStatusId.length) {
        this.clearInterval()
      }
    }, 10000)
  },
  beforeDestroy() {
    if (this.timeoutId) {
      this.clearInterval()
    }
  },
  methods: {
    refreshStatusOnline() {
      if (this.arrStatusId.length) {
        this.$store
          .dispatch('user/refreshStatusOnline', this.preparedArr)
          .then((res) => (this.userStatuses = res))
      }
    },
    clearInterval() {
      window.clearInterval(this.timeoutId)
      this.timeoutId = null
    },
  },
}
</script>
