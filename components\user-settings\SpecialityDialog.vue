<template>
  <l-dialog
    v-resize="onResize"
    :dialog="isShownSpecialitiesDialog"
    max-width="820"
    custom-class="speciality-picker"
    v-on="$listeners"
  >
    <div ref="header" class="header">
      <div class="speciality-picker-title font-weight-medium">
        {{ $t('manage_specialties') }}:
      </div>
      <div class="speciality-picker-text body-2 mt-2">
        <ul>
          <li>1. {{ $t('drag_your_teaching_specialities') }}</li>
          <li>
            2.
            {{
              $t(
                'top_specialities_will_be_featured_on_teacher_search_results_page'
              )
            }}
          </li>
        </ul>
      </div>
      <v-row class="my-0">
        <v-col class="col-6 py-0">
          <div
            class="text--gradient text-center subtitle-2 font-weight-medium mt-3 mb-1"
          >
            {{ $t('available_specialties') }}:
          </div></v-col
        >
        <v-col class="col-6 py-0">
          <div
            class="text--gradient text-center subtitle-2 font-weight-medium mt-3 mb-1"
          >
            {{ $t('selected_specialties') }}:
          </div></v-col
        >
      </v-row>
    </div>
    <div class="speciality-picker-content" :style="{ height: contentElHeight }">
      <v-row class="my-0">
        <v-col class="col-6 py-0">
          <div class="column">
            <div
              class="column-helper l-scroll l-scroll--dark-grey l-scroll--large"
            >
              <div class="column-content">
                <draggable
                  v-model="availableSpecializations"
                  class="list-group"
                  group="specialities"
                  @end="onEnd"
                >
                  <div
                    v-for="element in availableSpecializations"
                    :key="element.name"
                    class="list-group-item body-1"
                  >
                    {{ element.name }}
                  </div>
                </draggable>
              </div>
            </div>
          </div>
        </v-col>

        <v-col class="col-6 py-0">
          <div class="column l-scroll">
            <div class="column-helper l-scroll--dark-grey l-scroll--large">
              <div class="column-content">
                <draggable
                  v-model="teacherSpecialities"
                  class="list-group"
                  group="specialities"
                >
                  <div
                    v-for="(element, idx) in teacherSpecialities"
                    :key="element.name"
                    :class="[
                      'list-group-item body-1',
                      { 'highest-priority': idx < 3 },
                    ]"
                  >
                    {{ element.name }}
                  </div>
                </draggable>
              </div>
            </div>
          </div>
        </v-col>
      </v-row>

      <div class="speciality-picker-bottom d-flex justify-end">
        <v-btn color="primary" class="font-weight-medium" @click="submitData">
          <svg class="mr-1" width="18" height="18" viewBox="0 0 18 18">
            <use
              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#save-icon`"
            ></use>
          </svg>
          {{ $t('save_changes') }}
        </v-btn>
      </div>
    </div>
  </l-dialog>
</template>

<script>
import draggable from 'vuedraggable'

export default {
  name: 'SpecialityDialog',
  components: {
    draggable,
  },
  props: {
    isShownSpecialitiesDialog: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      contentElHeight: 'auto',
    }
  },
  computed: {
    availableSpecializations: {
      get() {
        return this.$store.getters['settings/availableSpecializations']
      },
      set(value) {
        this.$store.commit('settings/UPDATE_AVAILABLE_SPECIALIZATIONS', value)
      },
    },
    teacherSpecialities: {
      get() {
        return this.$store.getters['settings/teacherSpecialities']
      },
      set(value) {
        this.$store.commit('settings/UPDATE_TEACHER_SPECIALITIES', value)
      },
    },
  },
  watch: {
    isShownSpecialitiesDialog(newValue, oldValue) {
      if (newValue) {
        this.$nextTick(() => {
          window.setTimeout(() => this.setContentElHeight())
        })
      }
    },
  },
  methods: {
    onEnd(e) {
      if (this.teacherSpecialities.length > 8) {
        const _availableSpecializations = [...this.availableSpecializations]
        const _teacherSpecialities = [...this.teacherSpecialities]

        _availableSpecializations.splice(
          e.oldIndex,
          0,
          _teacherSpecialities[e.newIndex]
        )
        _teacherSpecialities.splice(e.newIndex, 1)

        this.$store.commit(
          'settings/UPDATE_AVAILABLE_SPECIALIZATIONS',
          _availableSpecializations
        )
        this.$store.commit(
          'settings/UPDATE_TEACHER_SPECIALITIES',
          _teacherSpecialities
        )
      }
    },
    setContentElHeight() {
      this.contentElHeight = `calc(100% - ${
        this.$refs.header?.clientHeight ?? 0
      }px)`
    },
    onResize() {
      this.setContentElHeight()
    },
    submitData() {
      this.$store
        .dispatch('settings/updateSpecialities')
        .then(() => this.$emit('close-dialog'))
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/vars';
@import '~vuetify/src/styles/settings/_variables';

.speciality-picker {
  height: calc(100vh - 10%);

  .v-card,
  .dialog-content {
    height: 100%;
  }

  .v-card {
    @media #{map-get($display-breakpoints, 'md-and-down')} {
      padding: 32px 28px;
    }
  }

  .dialog-content {
    position: relative;
  }

  &-content {
    padding-bottom: 88px;

    & > .row {
      height: 100%;

      & > .col {
        height: inherit;

        & > .column {
          height: inherit;
        }
      }
    }
  }

  &-title {
    font-size: 20px;
  }

  &-text {
    color: var(--v-grey-base);
    letter-spacing: 0.3px;

    ul {
      padding-left: 0;
      list-style-type: none;

      & > li:not(:last-child) {
        margin-bottom: 4px;
      }
    }
  }

  &-bottom {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
  }

  .column {
    padding: 20px 15px;
    border: 1px solid #00a500;
    border-radius: 24px;

    &-helper {
      height: 100%;
      overflow-y: auto;
      overflow-x: hidden;

      & > div {
        height: 100%;

        & > div {
          height: 100%;
        }
      }
    }

    .list-group-item {
      cursor: move;

      &:not(:last-child) {
        margin-bottom: 8px;
      }

      &.highest-priority {
        color: var(--v-success-base);
      }
    }
  }
}
</style>
