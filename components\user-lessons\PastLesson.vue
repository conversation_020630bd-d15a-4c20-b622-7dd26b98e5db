<template>
  <lesson-item :item="item" :user-statuses="userStatuses">
    <template
      v-if="item.isInitialized || item.isFinished"
      #lessonAdditionalActionsTop
    >
      <div>
        <span class="action" @click="downloadPdfClickHandler">
          <v-img
            :src="require('~/assets/images/download-icon-gradient.svg')"
            width="16"
            height="16"
          ></v-img>
          {{ $t('whiteboard_pdf') }}
        </span>
      </div>
    </template>

    <template #lessonAdditionalActionsBottom="scope">
      <div v-if="allowedRate">
        <span class="action" @click="rateLesson">
          <v-img
            :src="require('~/assets/images/star-icon-gradient.svg')"
            width="14"
            height="14"
            style="left: 1px"
          ></v-img>
          {{ $t('rate_lesson') }}
        </span>
      </div>
      <template v-if="isTeacher">
        <template v-if="item.isFinished">
          <div>
            <div>
              <v-img
                :src="require('~/assets/images/coins-icon-gradient.svg')"
                width="16"
                height="16"
              ></v-img>
              {{ $t('class_finished') }}
            </div>
          </div>
        </template>
        <template v-else>
          <div>
            <span class="action" @click="showFinishDialog(scope)">
              <v-img
                :src="require('~/assets/images/check-gradient.svg')"
                width="12"
                height="13"
                style="left: 3px"
              ></v-img>
              {{ $t('mark_as_finished') }}
            </span>
          </div>
        </template>
      </template>
    </template>

    <template #dialog="scope">
      <template v-if="dialogType === 'finishDialog'">
        <div class="lesson-dialog-title">
          <div class="lesson-dialog-title-icon">
            <v-img
              :src="require('~/assets/images/dollar-coins-gradient.svg')"
              width="20"
              height="20"
            ></v-img>
          </div>
          <span class="font-weight-medium text--gradient">
            {{ $t('do_you_want_to_finish_this_class') }}
          </span>
        </div>
        <div class="lesson-dialog-content l-scroll l-scroll--grey">
          {{ $t('this_will_move_class_to_past_lessons_and_trigger_payment') }}
        </div>
        <div class="lesson-dialog-buttons">
          <v-btn
            color="greyDark"
            outlined
            class="font-weight-medium"
            @click="closeDialog(scope)"
          >
            {{ $t('do_not_finish_class') }}
          </v-btn>
          <v-btn
            color="primary"
            class="font-weight-medium"
            @click="finishClickHandler(scope)"
          >
            {{ $t('finish_class') }}
          </v-btn>
        </div>
      </template>
    </template>

    <lesson-evaluation-dialog
      v-if="allowedRate"
      :item="feedbackLessonData"
      :dialog="isShownEvaluationDialog"
      close-button
      :persistent="false"
      @close="closeEvaluationDialog"
    ></lesson-evaluation-dialog>
  </lesson-item>
</template>

<script>
import LessonItem from '~/components/user-lessons/LessonItem'
import LessonEvaluationDialog from '~/components/user-lessons/LessonEvaluationDialog'

export default {
  name: 'PastLesson',
  components: { LessonItem, LessonEvaluationDialog },
  props: {
    item: {
      type: Object,
      required: true,
    },
    userStatuses: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogType: null,
      feedbackLessonData: {},
      isShownEvaluationDialog: false,
    }
  },
  computed: {
    isTeacher() {
      return this.$store.getters['user/isTeacher']
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
    allowedRate() {
      return (
        this.isStudent && this.item.isSkippedFeedback && this.item.isFinished
      )
    },
  },
  methods: {
    downloadPdfClickHandler() {
      this.$store.dispatch('lesson/generatePdf', this.item.lessonId)
    },
    finishClickHandler(scope) {
      this.$store
        .dispatch('lesson/finishLesson', this.item.lessonId)
        .then(() => {
          this.$store.commit('lesson/UPDATE_LESSON', {
            lessonId: this.item.lessonId,
            lessonStatus: 2,
          })
          this.$store.dispatch('snackbar/success', {
            successMessage: 'class_successfully_finished',
          })
        })
        .catch((e) => {
          this.$store.dispatch('loadingStop')
          this.$store.dispatch('snackbar/error')

          console.info(e)
        })
        .finally(() => this.closeDialog(scope))
    },
    showFinishDialog(scope) {
      this.dialogType = 'finishDialog'

      scope.showDialog()
    },
    closeDialog(scope) {
      scope.closeDialog()

      setTimeout(() => {
        this.dialogType = null
      }, 500)
    },
    rateLesson() {
      this.$store
        .dispatch('lesson/getFeedbackItem', this.item.lessonId)
        .then((data) => {
          this.feedbackLessonData = data
          this.isShownEvaluationDialog = true
        })
    },
    closeEvaluationDialog() {
      this.isShownEvaluationDialog = false
      this.feedbackLessonData = {}
    },
  },
}
</script>
