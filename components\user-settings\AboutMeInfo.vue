<template>
  <user-setting-template
    v-if="item"
    :title="$t('about_me')"
    :submit-func="submitData"
  >
    <div class="mb-2 mb-md-4">
      <v-row>
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-1">
              {{ $t('what_should_students_expect_from_your_classes') }}
            </div>
            <div class="input-wrap-label">
              {{
                $t(
                  'describe_any_materials_evaluations_activities_you_like_to_use'
                )
              }}
            </div>
            <div>
              <v-textarea
                :value="item.whatToExpect"
                class="l-textarea"
                no-resize
                height="120"
                solo
                dense
                hide-details
                @input="updateValue($event, 'whatToExpect')"
              ></v-textarea>
            </div>
            <div class="input-wrap-notice text--gradient">
              {{
                $t(
                  'formatting_tip_use_asterisk_at_beginning_of_line_to_add_bullet_point'
                )
              }}
            </div>
          </div>
        </v-col>
      </v-row>
    </div>
    <div class="mb-2 mb-md-4">
      <v-row>
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-1">
              {{ $t('your_cv') }}
            </div>
            <div class="input-wrap-label">
              {{ $t('upload_cv_or_provide_link_to_your_linkedIn_profile') }}
            </div>
            <div>
              <text-input
                :value="item.linkedinUrl"
                type-class="border-gradient"
                height="44"
                hide-details
                :placeholder="$t('Linkedin.com')"
                @input="updateValue($event, 'linkedinUrl')"
              ></text-input>
            </div>
          </div>
        </v-col>
        <v-col class="col-12 col-md-10 mt-3">
          <div class="d-flex">
            <div class="upload-cv">
              <v-btn
                class="gradient font-weight-medium mt-1"
                @click="$refs.fileCV.$el.querySelector('input').click()"
              >
                <div>
                  <v-img
                    class="mr-1"
                    :src="require('~/assets/images/upload-icon-gradient.svg')"
                    width="20"
                    height="20"
                  ></v-img>
                </div>
                <div class="text--gradient">
                  {{ $t('upload_cv') }}
                </div>
              </v-btn>

              <div class="input-wrap">
                <v-file-input
                  ref="fileCV"
                  v-model="file"
                  class="l-file-input pt-0"
                  prepend-icon=""
                  hide-input
                  accept="image/png, image/jpeg, image/bmp, application/pdf"
                  @change="uploadCV"
                ></v-file-input>
                <div v-if="!fileValid" class="v-text-field__details">
                  <div class="input-wrap-error">
                    <div
                      class="v-messages theme--light error--text"
                      role="alert"
                    >
                      <div class="v-messages__wrapper">
                        <div class="v-messages__message">
                          {{
                            $t('file_size_should_be_less_than', {
                              value: '6 MB',
                            })
                          }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="item.cvUrl" class="download-cv">
              <v-btn
                text
                class="font-weight-medium mt-1"
                :href="cvUrl"
                target="_blank"
                download
                @click.prevent="downloadClickHandler"
              >
                <div>
                  <v-img
                    class="mr-1"
                    :src="require('~/assets/images/download-icon-gradient.svg')"
                    width="24"
                    height="24"
                  ></v-img>
                </div>
                <span class="text--gradient">
                  {{ $t('download_cv') }}
                </span>
              </v-btn>
            </div>
          </div>
        </v-col>
      </v-row>
    </div>
    <div>
      <v-row>
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-1">
              {{ $t('provide_link_to_your_introduction_video_on_youtube') }}
            </div>
            <div>
              <text-input
                :value="item.youtubeUrl"
                type-class="border-gradient"
                height="44"
                hide-details
                :placeholder="$t('youtube_link')"
                @input="updateValue($event, 'youtubeUrl')"
              ></text-input>
            </div>
          </div>
        </v-col>
      </v-row>
    </div>
  </user-setting-template>
</template>

<script>
import UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'
import TextInput from '@/components/form/TextInput'

export default {
  name: 'AboutMeInfo',
  components: { UserSettingTemplate, TextInput },
  data() {
    return {
      file: null,
      fileValid: true,
      fileSizeLimit: 6000000,
    }
  },
  computed: {
    item() {
      return this.$store.state.settings.aboutMeItem
    },
    cvUrl() {
      return this.item.cvUrl
    },
    fileName() {
      const arrPath = this.cvUrl.split('/')

      return arrPath[arrPath.length - 1]
    },
  },
  watch: {
    $route() {
      this.resetFile()
    },
  },
  beforeCreate() {
    this.$store.dispatch('settings/getAboutMe')
  },
  methods: {
    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_ABOUT_ME_ITEM', {
        [property]: value,
      })
    },
    async downloadClickHandler() {
      await this.$axios({
        url: this.cvUrl,
        method: 'GET',
        responseType: 'blob',
      })
        .then((response) => {
          const url = window.URL.createObjectURL(new Blob([response.data]))
          const link = document.createElement('a')

          link.href = url
          link.setAttribute('download', this.fileName)
          document.body.appendChild(link)
          link.click()
        })
        .catch(() => console.info('Download error'))
    },
    resetFile() {
      this.file = null
      this.fileValid = true
    },
    uploadCV(file) {
      this.fileValid = true

      if (file.size > this.fileSizeLimit) {
        this.fileValid = false

        return
      }

      this.$store.dispatch('settings/uploadCV', file)
    },
    submitData() {
      this.$store.dispatch('settings/updateAboutMe').then(() => {
        this.resetFile()
      })
    },
  },
}
</script>

<style scoped lang="scss">
.upload-cv,
.download-cv {
  position: relative;
  display: inline-block;

  .v-btn {
    min-width: 154px !important;
  }
}

.download-cv .v-btn {
  margin-left: 32px;
  font-size: 16px;
  cursor: pointer;
}
</style>
