<template>
  <div class="lesson d-sm-flex">
    <div class="lesson-date d-flex text-center">
      <div>
        <template v-if="$slots.date">
          <slot name="date"></slot>
        </template>
        <template v-else>
          <template v-if="$vuetify.breakpoint.smAndUp">
            <div class="weekday d-none d-sm-block">
              {{ $dayjs(startDate).format('dddd') }}
            </div>
            <div class="date d-none d-sm-block">
              {{ $dayjs(startDate).format('DD MMM') }}
            </div>
            <div class="time d-none d-sm-block">{{ startTime }}</div>
          </template>
          <template v-else>
            <div class="d-sm-none">
              {{ $dayjs(startDate).format('dddd') }},
              {{ $dayjs(startDate).format('DD MMM') }} - {{ startTime }}
            </div>
          </template>
        </template>
      </div>
      <div
        v-if="$vuetify.breakpoint.smAndUp"
        class="duration d-none d-sm-block"
      >
        <div class="duration-icon">
          <svg width="18" height="18" viewBox="0 0 18 18">
            <use
              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#clock-thin`"
            ></use>
          </svg>
        </div>
        {{ $t('lessonLength_mins', { lessonLength: item.lessonLength }) }}
      </div>
      <div v-else class="duration d-sm-none">
        &nbsp;({{
          $t('lessonLength_mins', { lessonLength: item.lessonLength })
        }})
      </div>
    </div>

    <div class="lesson-content">
      <div class="lesson-details">
        <div class="avatar mr-2">
          <v-avatar width="110" height="110">
            <v-img
              :src="getSrcAvatar(item.userAvatars, 'user_thumb_110x110')"
              :srcset="
                getSrcSetAvatar(
                  item.userAvatars,
                  'user_thumb_110x110',
                  'user_thumb_220x220'
                )
              "
              :options="{ rootMargin: '50%' }"
            ></v-img>
            <nuxt-link v-if="teacherLink" :to="teacherLink"></nuxt-link>
          </v-avatar>
          <user-status
            :user-id="userId"
            :user-statuses="userStatuses"
            large
          ></user-status>
        </div>
        <div class="details">
          <div class="user-info">
            <div class="user-info-name">
              {{ userName }}
              <div v-if="isTeacher" @click="studentInfoClickHandler">
                <svg width="16" height="16" viewBox="0 0 16 16">
                  <use
                    :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#info`"
                  ></use>
                </svg>
              </div>
            </div>
            <div
              :class="[
                'user-info-status',
                `user-info-status--${status}`,
                { 'text--red-gradient': status === 'idle' },
              ]"
            >
              <template v-if="status === 'online'">
                {{ $t('online_now') }}
              </template>
              <template v-else-if="status === 'idle'">
                {{ $t('online_but_idle') }}
              </template>
              <template v-else>
                {{ $t('offline_now') }}
              </template>
            </div>
          </div>
          <div class="lesson-info">
            <div class="avatar mr-2">
              <v-avatar width="85" height="85">
                <v-img
                  :src="getSrcAvatar(item.userAvatars, 'user_thumb_110x110')"
                  :srcset="
                    getSrcSetAvatar(
                      item.userAvatars,
                      'user_thumb_110x110',
                      'user_thumb_220x220'
                    )
                  "
                  :options="{ rootMargin: '50%' }"
                ></v-img>
                <nuxt-link v-if="teacherLink" :to="teacherLink"></nuxt-link>
              </v-avatar>
              <user-status
                :user-id="userId"
                :user-statuses="userStatuses"
                large
              ></user-status>
            </div>
            <div>
              <slot name="lessonInfo"></slot>

              <div v-if="!isUnscheduled">
                <span class="text-capitalize">{{ $t('lesson') }}:</span>
                <span class="text--gradient font-weight-bold text-no-wrap">
                  {{ item.lessonType }}
                </span>
              </div>
              <div>
                <span class="text-capitalize">{{ $t('language') }}:</span>
                <span class="text--gradient font-weight-bold text-no-wrap">
                  {{ item.language.name }}
                </span>
              </div>
              <div v-if="item.courseName && item.courseName.length">
                <span class="text-capitalize">{{ $t('course') }}:</span>
                <span class="text--gradient font-weight-bold">
                  {{ item.courseName }}
                </span>
              </div>
              <div class="lesson-actions-additional">
                <slot name="lessonAdditionalActionsTop"></slot>

                <template v-if="!isUnscheduleButtonHidden">
                  <div>
                    <span class="action" @click="showUnscheduledDialog">
                      <v-img
                        :src="require('~/assets/images/close-gradient-2.svg')"
                        width="11"
                        height="11"
                        style="left: 3px"
                      ></v-img>
                      {{ $t('unschedule_lesson') }}
                    </span>
                  </div>
                </template>

                <slot
                  name="lessonAdditionalActionsBottom"
                  :showDialog="showDialog"
                ></slot>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="lesson-actions">
        <div class="lesson-actions-buttons">
          <v-btn
            v-if="!item.userIsDeleted"
            width="158"
            class="btn-add gradient font-weight-medium ml-1 mb-1"
            @click="showMessageDialog"
          >
            <div class="mr-1">
              <v-img
                :src="require('~/assets/images/message-icon-gradient.svg')"
                width="20"
                height="20"
              ></v-img>
            </div>
            <div class="text--gradient">
              {{ $t('message') }}
            </div>
          </v-btn>

          <slot name="lessonActions"></slot>

          <template v-if="!isUnscheduled">
            <template v-if="isTeacher || (isStudent && !item.isCreated)">
              <!--    The attribute href is needed to trigger event 'users-joined-classroom'    -->
              <v-btn
                :href="`/lesson/${item.lessonId}/classroom`"
                color="primary"
                width="158"
                class="go-to-class-btn font-weight-medium ml-1 mb-1"
              >
                <svg class="mr-1" width="18" height="18" viewBox="0 0 18 18">
                  <use
                    :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#save-icon`"
                  ></use>
                </svg>
                {{ $t('go_to_class') }}
              </v-btn>
            </template>
            <template v-else>
              <v-btn
                color="primary"
                width="158"
                class="go-to-class-btn go-to-class-btn--disabled primary--light font-weight-medium ml-1 mb-1"
                @click="showInitializeDialog"
              >
                <svg
                  class="icon--rotated mr-1"
                  width="18"
                  height="18"
                  viewBox="0 0 18 18"
                >
                  <use
                    :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#save-icon`"
                  ></use>
                </svg>
                {{ $t('go_to_class') }}
                <svg
                  class="icon--right"
                  width="18"
                  height="18"
                  viewBox="0 0 16 16"
                >
                  <use
                    :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#info`"
                  ></use>
                </svg>
              </v-btn>
            </template>
          </template>
        </div>
        <div class="lesson-actions-additional">
          <slot name="lessonAdditionalActionsTop"></slot>

          <template v-if="!isUnscheduleButtonHidden">
            <div>
              <span class="action" @click="showUnscheduledDialog">
                <v-img
                  :src="require('~/assets/images/close-gradient-2.svg')"
                  width="11"
                  height="11"
                  style="left: 3px"
                ></v-img>
                {{ $t('unschedule_lesson') }}
              </span>
            </div>
          </template>

          <slot
            name="lessonAdditionalActionsBottom"
            :showDialog="showDialog"
          ></slot>
        </div>
      </div>

      <div
        :class="[
          'lesson-dialog d-flex flex-column justify-center',
          {
            'lesson-dialog--shown': isLessonDialogShown,
          },
          {
            'lesson-dialog--student-info': dialogType === 'studentInfoDialog',
          },
        ]"
      >
        <slot name="dialog" :closeDialog="closeDialog"></slot>

        <template v-if="dialogType === 'unscheduledDialog'">
          <div
            class="lesson-dialog-title font-weight-medium text--red-gradient"
          >
            {{
              $t('are_you_sure_you_want_to_unschedule_this_lesson_with', {
                name: item.userFirstName,
              })
            }}
          </div>
          <div class="lesson-dialog-content l-scroll l-scroll--grey">
            <template v-if="isStudent">
              <template v-if="item.isFreeTrial">
                {{
                  $t(
                    'you_will_be_able_to_reschedule_this_lesson_from_your_teachers_profile_page'
                  )
                }}
              </template>
              <template v-else>
                {{
                  $t(
                    'you_will_receive_credit_and_can_reschedule_lesson_for_anytime_your_teacher_is_available'
                  )
                }}
              </template>
            </template>
            <template v-else>
              {{ $t('student_will_be_given_credit_for_lesson') }}
            </template>
          </div>
          <div class="lesson-dialog-buttons">
            <v-btn
              color="greyDark"
              outlined
              class="font-weight-medium"
              @click="closeDialog"
            >
              {{ $t('do_not_cancel_lesson') }}
            </v-btn>
            <v-btn
              color="error"
              class="font-weight-medium"
              @click="cancelClickHandler"
            >
              {{ $t('cancel_lesson') }}
            </v-btn>
          </div>
        </template>

        <template v-if="dialogType === 'initializeDialog'">
          <div class="lesson-dialog-title font-weight-medium text--gradient">
            <div class="lesson-dialog-title-icon">
              <svg width="20" height="20" viewBox="0 0 12 12">
                <use
                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#attention`"
                ></use>
              </svg>
            </div>
            {{ $t('your_teacher_will_enter_classroom_first') }}
          </div>
          <div
            class="lesson-dialog-content l-scroll l-scroll--grey"
            v-html="
              $t(
                'after_your_teacher_enters_go_to_class_button_will_become_clickable_so_you_can_enter_as_well'
              )
            "
          ></div>
          <div class="lesson-dialog-buttons">
            <v-btn
              color="primary"
              max-width="158"
              class="font-weight-medium"
              @click="closeDialog"
            >
              OK!
            </v-btn>
          </div>
        </template>

        <template v-if="dialogType === 'studentInfoDialog'">
          <div class="lesson-dialog-title font-weight-medium text--gradient">
            <div class="lesson-dialog-title-icon">
              <svg width="20" height="20" viewBox="0 0 16 16">
                <use
                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#info`"
                ></use>
              </svg>
            </div>
            {{ $t('student_info') }}
          </div>
          <div class="lesson-dialog-content l-scroll l-scroll--grey">
            <div>
              <div>
                <ul>
                  <li>
                    <span class="font-weight-medium">{{ $t('name') }}:</span>
                    {{ studentInfo.name }}
                  </li>
                  <li>
                    <span class="font-weight-medium"
                      >{{ $t('lifetime_free_trials_scheduled') }}:</span
                    >
                    {{ studentInfo.freeTrialScheduled }}
                  </li>
                  <li>
                    <span class="font-weight-medium"
                      >{{ $t('lifetime_lessons_purchased') }}:</span
                    >
                    {{ studentInfo.lessonsPurchased }}
                  </li>
                  <li>
                    <span class="font-weight-medium"
                      >{{ $t('lifetime_teachers_booked_with') }}:</span
                    >
                    {{ studentInfo.teachersBookedWith }}
                  </li>
                </ul>
              </div>
              <div class="pl-1">
                <ul>
                  <li>
                    <span class="font-weight-medium"
                      >{{ $t('current_time') }}:</span
                    >
                    {{ $dayjs().tz(studentInfo.timezone).format('LT') }}
                    ({{ $dayjs().tz(studentInfo.timezone).format('z') }},
                    {{ studentInfo.timezone }})
                  </li>
                  <li>
                    <span class="font-weight-medium"
                      >{{ $t('total_spent_with_you') }} ({{
                        currencyIsoCode
                      }}):</span
                    >
                    {{ currencySymbol
                    }}{{ getPrice(studentInfo.totalSpendWithTeacher) }}
                  </li>
                  <li>
                    <span class="font-weight-medium"
                      >{{ $t('date_registered_on_langu') }}:</span
                    >
                    {{
                      $dayjs(studentInfo.dateRegistered)
                        .tz(timeZone)
                        .format('ll, LT')
                    }}
                  </li>
                  <li>
                    <span class="font-weight-medium"
                      >{{ $t('last_online') }}:</span
                    >
                    <template v-if="status === 'online'">
                      {{ $t('online_now') }}
                    </template>
                    <template v-else-if="status === 'idle'">
                      {{ $t('online_but_idle') }}
                    </template>
                    <template v-else>
                      {{
                        $dayjs(studentInfo.lastLoginDate)
                          .tz(timeZone)
                          .format('ll, LT')
                      }}
                    </template>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="lesson-dialog-buttons">
            <v-btn
              color="primary"
              max-width="158"
              class="font-weight-medium"
              @click="closeDialog"
            >
              OK
            </v-btn>
          </div>
        </template>
      </div>
    </div>

    <message-dialog
      v-if="isShownMessageDialog"
      :recipient-id="userId"
      :recipient-name="`${item.userFirstName} ${item.userLastName}`"
      :is-shown-message-dialog="isShownMessageDialog"
      @close-dialog="isShownMessageDialog = false"
    ></message-dialog>

    <slot></slot>
  </div>
</template>

<script>
import { getPrice } from '~/helpers'

import Avatars from '~/mixins/Avatars'
import UserStatus from '~/components/UserStatus'
import MessageDialog from '~/components/MessageDialog'

export default {
  name: 'LessonItem',
  components: { UserStatus, MessageDialog },
  mixins: [Avatars],
  props: {
    item: {
      type: Object,
      required: true,
    },
    userStatuses: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      getPrice,
      isLessonDialogShown: false,
      dialogType: null,
      isShownMessageDialog: false,
      studentInfo: null,
    }
  },
  computed: {
    isTeacher() {
      return this.$store.getters['user/isTeacher']
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
    userId() {
      return this.item[this.isTeacher ? 'studentId' : 'teacherId']
    },
    userIsDeleted() {
      return this.item.userIsDeleted
    },
    userName() {
      return this.userIsDeleted
        ? this.$t('deleted_user')
        : `${this.item.userFirstName} ${this.item.userLastName}`
    },
    currencyIsoCode() {
      return this.$store.state.currency.item.isoCode
    },
    currencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
    status() {
      let status = 'offline'

      if (
        Object.prototype.hasOwnProperty.call(
          this.userStatuses,
          this.userId?.toString()
        )
      ) {
        status = this.userStatuses[this.userId]
      }

      return status
    },
    isPast() {
      return this.item.type === 'past'
    },
    isUnscheduled() {
      return this.item.type === 'unscheduled'
    },
    timeZone() {
      return this.$store.getters['user/timeZone']
    },
    startDate() {
      return this.$dayjs(this.item.startDate).tz(this.timeZone)
    },
    startTime() {
      return this.startDate.format('LT')
    },
    isUnscheduleButtonHidden() {
      return (
        this.isUnscheduled ||
        this.item.isFinished ||
        (this.isStudent &&
          (this.isPast ||
            this.$dayjs().add(1, 'day').isAfter(this.startDate, 'minute')))
      )
    },
    teacherLink() {
      return this.isStudent &&
        !this.item.userIsDeleted &&
        this.item.teacherUsername
        ? `/teacher/${this.item.teacherUsername}`
        : null
    },
  },
  methods: {
    showMessageDialog() {
      this.$store
        .dispatch('message/checkConversation', this.userId)
        .then((res) => {
          if (res.threadId) {
            this.$store.dispatch('loadingStart')
            this.$router.push({ path: `/user/messages/${res.threadId}/view` })
          } else {
            this.isShownMessageDialog = true
          }
        })
    },
    cancelClickHandler() {
      this.$store.dispatch('loadingStart')
      this.$store
        .dispatch('lesson/cancelLesson', this.item.lessonId)
        .then(() => {
          location.reload()
        })
        .catch((e) => {
          this.$store.dispatch('loadingStop')
          this.$store.dispatch('snackbar/error')

          console.info(e)
        })
    },
    studentInfoClickHandler() {
      this.$store
        .dispatch('lesson/getStudentInfo', this.item.studentId)
        .then((res) => {
          this.studentInfo = res
          this.dialogType = 'studentInfoDialog'

          this.showDialog()
        })
    },
    showInitializeDialog() {
      this.dialogType = 'initializeDialog'

      this.showDialog()
    },
    showUnscheduledDialog() {
      this.dialogType = 'unscheduledDialog'

      this.showDialog()
    },
    showDialog() {
      this.isLessonDialogShown = true
    },
    closeDialog() {
      this.isLessonDialogShown = false

      setTimeout(() => {
        this.dialogType = null
      }, 500)
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/lesson-item.scss';
</style>
