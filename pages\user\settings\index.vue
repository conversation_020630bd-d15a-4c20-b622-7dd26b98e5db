<template>
  <v-col class="col-12 px-0">
    <div class="user-settings">
      <v-container fluid class="pa-0">
        <v-row no-gutters>
          <v-col class="col-12">
            <div class="user-settings-wrap mx-auto">
              <div v-if="fullName" class="user-settings-title mb-3 mb-md-2">
                {{ $t('welcome') }},
                <span class="font-weight-medium">
                  {{ fullName }}
                </span>
                👋
              </div>
              <div v-if="$vuetify.breakpoint.smAndUp" class="d-none d-sm-flex">
                <aside class="user-settings-sidebar">
                  <div class="user-settings-sidebar-sticky">
                    <div class="user-settings-tabs-nav">
                      <ul class="nav-list">
                        <li
                          v-for="(tab, idx) in tabs"
                          :key="idx"
                          class="nav-item"
                        >
                          <v-btn
                            :class="[
                              'font-weight-regular',
                              { active: tabActive === idx },
                            ]"
                            :dark="tab.value === tabActive"
                            width="100%"
                            height="48"
                            @click="tabClickHandler(tab.value)"
                          >
                            {{ tab.name }}
                          </v-btn>
                        </li>
                      </ul>
                    </div>
                  </div>
                </aside>
                <div ref="userSettingsContent" class="user-settings-content">
                  <keep-alive v-for="(tab, idx) in tabs" :key="idx">
                    <component
                      :is="tab.component"
                      v-if="tabActive === idx"
                      v-bind="{ basicInfoItem }"
                      @reload-page="reloadPage"
                    ></component>
                  </keep-alive>
                </div>
              </div>
              <div v-else class="d-sm-none">
                <client-only>
                  <v-expansion-panels
                    v-model="tabActive"
                    accordion
                    flat
                    class="tabs-mobile"
                  >
                    <v-expansion-panel v-for="(tab, idx) in tabs" :key="idx">
                      <v-expansion-panel-header
                        :ref="`panel-${idx}`"
                        disable-icon-rotate
                      >
                        {{ tab.name }}
                        <template #actions>
                          <template v-if="tabActive === idx">
                            <v-icon color="dark">
                              {{ mdiMinus }}
                            </v-icon>
                          </template>
                          <template v-else>
                            <v-img
                              :src="
                                require('~/assets/images/add-icon-gradient.svg')
                              "
                              width="24"
                              height="24"
                            ></v-img>
                          </template>
                        </template>
                      </v-expansion-panel-header>
                      <v-expansion-panel-content>
                        <keep-alive>
                          <component
                            :is="tab.component"
                            v-bind="{ basicInfoItem }"
                            @reload-page="reloadPage"
                          ></component>
                        </keep-alive>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </client-only>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </v-col>
</template>

<script>
import { mdiMinus } from '@mdi/js'
import UserSettingBasicInfo from '~/components/user-settings/BasicInfo'
import SummaryInfo from '~/components/user-settings/SummaryInfo'
import LanguagesInfo from '~/components/user-settings/LanguagesInfo'
import BackgroundInfo from '~/components/user-settings/BackgroundInfo'
import AboutMeInfo from '~/components/user-settings/AboutMeInfo'
import PricingTableInfo from '~/components/user-settings/PricingTableInfo'
import CoursesInfo from '~/components/user-settings/CoursesInfo'
import TeachingPreferencesInfo from '~/components/user-settings/TeachingPreferencesInfo'
import LearningPreferencesInfo from '~/components/user-settings/LearningPreferencesInfo'
import TeachingQualificationsInfo from '~/components/user-settings/TeachingQualificationsInfo'
import CalendarNotificationInfo from '~/components/user-settings/CalendarNotificationInfo'
import ReceiptInfo from '~/components/user-settings/ReceiptInfo'

export default {
  name: 'UserSettingsPage',
  components: {
    UserSettingBasicInfo,
    SummaryInfo,
    LanguagesInfo,
    BackgroundInfo,
    AboutMeInfo,
    PricingTableInfo,
    CoursesInfo,
    TeachingPreferencesInfo,
    LearningPreferencesInfo,
    TeachingQualificationsInfo,
    CalendarNotificationInfo,
    ReceiptInfo,
  },
  middleware: 'authenticated',
  async asyncData(ctx) {
    await ctx.store.dispatch('settings/getBasicInfo')
  },
  data() {
    return {
      mdiMinus,
      fullName: null,
      tabActive: null,
      snackbar: true,
      tabs: [
        {
          id: 0,
          name: this.$t('basic_info'),
          value: 'basic-info',
          component: UserSettingBasicInfo,
        },
        {
          id: 1,
          name: this.$t('languages'),
          value: 'languages',
          component: LanguagesInfo,
        },
        {
          id: 11,
          name: this.$t('calendar'),
          value: 'calendar',
          component: CalendarNotificationInfo,
        },
      ],
    }
  },
  head() {
    return {
      title: this.$t('user_settings_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('user_settings_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('user_settings_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('user_settings_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} user-settings-page`,
      },
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    basicInfoItem() {
      return this.$store.state.settings.basicInfoItem
    },
    isTeacher() {
      return this.$store.getters['user/isTeacher']
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
  },
  watch: {
    $route() {
      this.getRouteHash()
    },
    tabActive(newValue, oldValue) {
      window.location.hash = this.tabs[newValue]?.value ?? 'basic-info'
    },
  },
  mounted() {
    this.fullName = `${this.basicInfoItem.firstName} ${this.basicInfoItem.lastName}`

    if (this.isTeacher) {
      this.tabs = [
        ...this.tabs,
        ...[
          {
            id: 2,
            name: this.$t('summary'),
            value: 'summary',
            component: SummaryInfo,
          },
          {
            id: 3,
            name: this.$t('background'),
            value: 'background',
            component: BackgroundInfo,
          },
          {
            id: 4,
            name: this.$t('about_me'),
            value: 'about-me',
            component: AboutMeInfo,
          },
          {
            id: 5,
            name: this.$t('pricing_table'),
            value: 'pricing-table',
            component: PricingTableInfo,
          },
          {
            id: 6,
            name: this.$t('courses'),
            value: 'courses',
            component: CoursesInfo,
          },
          {
            id: 7,
            name: this.$t('teaching_preferences'),
            value: 'teaching-preferences',
            component: TeachingPreferencesInfo,
          },
          {
            id: 9,
            name: this.$t('teaching_qualifications'),
            value: 'teaching-qualifications',
            component: TeachingQualificationsInfo,
          },
        ],
      ]
    }

    if (this.isStudent) {
      this.tabs = [
        ...this.tabs,
        ...[
          // {
          //   id: 8,
          //   name: this.$t('learning_preferences'),
          //   value: 'learning-preferences',
          //   component: LearningPreferencesInfo,
          // },
          {
            id: 10,
            name: this.$t('receipt_information'),
            value: 'receipt-information',
            component: ReceiptInfo,
          },
        ],
      ]
    }

    this.tabs = this.tabs.sort((a, b) => a.id - b.id)

    this.getRouteHash()
  },
  methods: {
    getRouteHash() {
      const hash = this.$route.hash.replace('#', '')

      this.tabActive = this.tabs.map((item) => item.value).indexOf(hash) || 0

      if (window.scrollY > 110) {
        this.$nextTick(function () {
          const el = this.$vuetify.breakpoint.smAndUp
            ? this.$refs.userSettingsContent
            : this.$refs[`panel-${this.tabActive}`][0].$el

          if (el) {
            window.setTimeout(() => {
              this.$vuetify.goTo(el, {
                duration: 0,
                offset: 10,
                easing: 'linear',
              })
            }, 400)
          }
        })
      }
    },
    tabClickHandler(value) {
      window.location.hash = value
    },
    reloadPage() {
      window.location.reload()
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/user-settings.scss';
</style>
