<template>
  <div v-if="items.length" class="teacher-profile-feedback-tags">
    <span class="teacher-profile-feedback-tags-label text--gradient"
      >{{ $t('students_say') }}:</span
    >
    <l-chip
      v-for="tag in [...items].reverse().slice(0, 5)"
      :key="tag.tag.id"
      :label="`${tag.tag.name} (${tag.count})`"
      :close-btn="false"
      light
    ></l-chip>
  </div>
</template>

<script>
export default {
  name: 'FeedbackTags',
  props: {
    items: {
      type: Array,
      default: () => [],
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~vuetify/src/styles/settings/_variables';

.teacher-profile-feedback-tags {
  margin-bottom: 14px;

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    margin-bottom: 10px;
  }

  & > * {
    margin-right: 10px;
  }

  &-label {
    font-size: 16px;
    font-weight: 700;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 14px;
      font-weight: 600;
    }
  }

  & > .chip {
    margin-bottom: 7px;
  }
}
</style>
