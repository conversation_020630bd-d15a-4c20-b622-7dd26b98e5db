<template>
  <payment-item :item="paymentData">
    <template #additionalActionsTop>
      <div class="d-flex align-center">
        <v-chip
          small
          label
          :color="item.status === 'completed' ? 'success' : 'warning'"
          class="mr-2"
        >
          {{ item.status }}
        </v-chip>
        <span class="caption grey--text">
          {{ $t('invoice_no') }}: {{ item.invoiceNo }}
        </span>
      </div>
    </template>

    <template #additionalActionsBottom>
      <div class="d-flex align-center justify-space-between w-100">
        <div class="caption grey--text">
          {{ $t('lesson_no') }}: {{ item.lessonNo }}
        </div>
        <div class="text-h6 primary--text">
          {{ currentCurrencySymbol }}{{ formatValue(item.value) }}
        </div>
      </div>
    </template>
  </payment-item>
</template>

<script>
import PaymentItem from './PaymentItem.vue'

export default {
  name: 'PaymentLesson',
  components: {
    PaymentItem,
  },
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  computed: {
    paymentData() {
      return {
        day: this.item.day,
        date: this.item.date,
        time: this.item.time,
        student: this.item.student,
        lessonType: this.item.lessonType,
        status: this.item.status,
        invoiceNo: this.item.invoiceNo,
        lessonNo: this.item.lessonNo,
        value: this.item.value,
        finishedAt: this.item.finishedAt,
        transactionId: this.item.transactionId,
        invoiceNumber: this.item.invoiceNumber,
        lessonLength: this.item.lessonLength,
      }
    },
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
  },
  methods: {
    formatValue(value) {
      // Format the value with exactly 2 decimal places
      return Number(value).toFixed(2)
    },
  },
}
</script>

<style lang="scss" scoped>
// Add any additional custom styling if needed
.payment-item {
  &:hover {
    box-shadow: 0 4px 14px rgba(217, 225, 236, 0.47);
  }
}
</style>
