<template>
  <v-select
    :value="value"
    class="l-select"
    :items="items"
    :label="label"
    :height="height"
    :item-value="itemValue"
    :item-text="itemName"
    dense
    hide-details
    return-object
    :hide-selected="hideSelected"
    :readonly="readonly"
    :menu-props="_menuProps"
    v-on="$listeners"
  >
    <template v-if="$slots['prepend-inner']" #prepend-inner>
      <slot name="prepend-inner"></slot>
    </template>
    <template #append>
      <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
    </template>
    <template #selection="{ item }">
      <template v-if="!hideItemIcon">
        <div v-if="item.icon" class="icon">
          <v-img
            :src="require(`~/assets/images/${item.icon}.svg`)"
            width="18"
            height="18"
          ></v-img>
        </div>
        <div v-if="item.isoCode" class="icon icon-flag">
          <v-img
            v-if="item.isoCode"
            :src="require(`~/assets/images/flags/${item.isoCode}.svg`)"
            width="18"
            height="18"
          ></v-img>
        </div>
      </template>

      {{ translation ? $t(item.name) : item.name }}
    </template>
    <template #item="{ item }">
      <template v-if="!hideItemIcon">
        <div v-if="item.icon" class="icon">
          <v-img
            :src="require(`~/assets/images/${item.icon}.svg`)"
            width="16"
            height="16"
          ></v-img>
        </div>
        <div v-if="item.isoCode" class="icon icon-flag">
          <v-img
            :src="require(`~/assets/images/flags/${item.isoCode}.svg`)"
            width="18"
            height="18"
          ></v-img>
        </div>
      </template>

      {{ translation ? $t(item.name) : item.name }}
    </template>
  </v-select>
</template>

<script>
import { mdiChevronDown } from '@mdi/js'

export default {
  name: 'SelectInputNew',
  props: {
    // eslint-disable-next-line vue/require-default-prop
    value: [String, Number, Object],
    items: {
      type: Array,
      required: true,
    },
    label: {
      type: String,
      default: '',
    },
    height: {
      type: String,
      default: '24',
    },
    menuProps: {
      type: Object,
      default: () => ({}),
    },
    itemValue: {
      type: String,
      default: 'value',
    },
    itemName: {
      type: String,
      default: 'name',
    },
    prependInner: {
      type: String,
      default: null,
    },
    translation: {
      type: Boolean,
      default: true,
    },
    hideItemIcon: {
      type: Boolean,
      default: false,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    hideSelected: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mdiChevronDown,
    }
  },
  computed: {
    _menuProps() {
      return Object.assign(
        {},
        {
          bottom: true,
          offsetY: true,
          minWidth: 200,
          contentClass: 'select-list',
        },
        this.menuProps
      )
    },
  },
}
</script>
