<div class="header-menu d-flex">
              <nav class="main-menu d-none d-md-block">
                <ul class="main-menu-list d-flex">
                  <li
                    v-if="!isUserLogged || isStudent"
                    class="main-menu-item d-flex align-center"
                  >
                    <nuxt-link
                      class="main-menu-link text-uppercase"
                      :to="
                        localePath({
                          name: 'teacher-listing',
                          params: {
                            utm_source: 'homepage',
                            utm_medium: 'intro',
                          },
                        })
                      "
                    >
                      <div class="item-icon">
                        <SearchIcon />
                      </div>
                      {{ $t('find_teacher') }}
                    </nuxt-link>
                  </li>

                  <template v-if="!isUserLogged">
                    <li class="main-menu-item d-flex align-center">
                      <nuxt-link
                        class="main-menu-link text-uppercase"
                        :to="localePath({ path: '/business' })"
                      >
                        {{ $t('for_companies') }}
                      </nuxt-link>
                    </li>
                    <li class="main-menu-item d-flex align-center">
                      <nuxt-link
                        class="main-menu-link text-uppercase"
                        :to="localePath({ path: '/education' })"
                      >
                        {{ $t('for_kids') }}
                      </nuxt-link>
                    </li>
                    <li class="main-menu-item d-flex align-center">
                      <nuxt-link
                        class="main-menu-link text-uppercase"
                        :to="localePath({ path: '/faq' })"
                      >
                        {{ $t('faq') }}
                      </nuxt-link>
                    </li>
                    <!--                    <li class="main-menu-item d-flex align-center">-->
                    <!--                      <a-->
                    <!--                        href="/christmas"-->
                    <!--                        class="main-menu-link text-uppercase"-->
                    <!--                      >-->
                    <!--                        {{ $t('christmas') }}-->
                    <!--                      </a>-->
                    <!--                    </li>-->
                  </template>
                  <template v-else>
                    <li class="main-menu-item d-flex align-center">
                      <nuxt-link
                        class="main-menu-link text-uppercase"
                        :to="localePath({ path: '/user/lessons' })"
                      >
                        {{ $t('my_lessons') }}
                      </nuxt-link>
                    </li>
                    <li class="main-menu-item d-flex align-center">
                      <nuxt-link
                        :to="localePath('/user/messages')"
                        class="main-menu-link text-uppercase"
                      >
                        {{ $t('messages') }}
                        <span
                          v-if="newMessages"
                          class="l-badge main-menu-badge"
                          >{{ newMessages }}</span
                        >
                      </nuxt-link>
                    </li>
                    <li
                      v-if="isTeacher"
                      class="main-menu-item d-flex align-center"
                    >
                      <a
                        class="main-menu-link text-uppercase"
                        href="/user/payments/earnings"
                        >{{ $t('payments') }}</a
                      >
                    </li>
                    <li
                      v-if="isTeacher"
                      class="main-menu-item d-flex align-center"
                    >
                      <a
                        class="main-menu-link text-uppercase"
                        href="/user/reviews"
                        >{{ $t('reviews') }}</a
                      >
                    </li>
                    <li class="main-menu-item d-flex align-center">
                      <a
                        href="/user/library"
                        class="main-menu-link text-uppercase"
                      >
                        {{ $t('library') }}
                      </a>
                    </li>
                    <!--                    <li-->
                    <!--                      v-if="isStudent"-->
                    <!--                      class="main-menu-item d-flex align-center"-->
                    <!--                    >-->
                    <!--                      <a-->
                    <!--                        href="/christmas"-->
                    <!--                        class="main-menu-link text-uppercase"-->
                    <!--                      >-->
                    <!--                        {{ $t('christmas') }}-->
                    <!--                      </a>-->
                    <!--                    </li>-->
                    <li
                      v-if="isTeacher"
                      class="main-menu-item d-flex align-center"
                    >
                      <a
                        class="main-menu-link text-uppercase"
                        href="/user/availability"
                        >{{ $t('availability') }}</a
                      >
                    </li>
                  </template>
                </ul>
              </nav>
              <template v-if="!isUserLogged">
                <div class="user d-flex align-center">
                  <span
                    class="d-flex align-center user-link user-link--login text-uppercase"
                    @click.stop="showLoginSidebarClickHandler"
                  >
                    <span class="d-flex align-center v-image">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 512 512"
                      >
                        <path
                          d="M511.676,498.752l-12.8-51.2c-6.073-24.838-24.485-44.813-48.747-52.885l-93.867-31.275  c-22.891-9.536-33.365-46.4-35.627-60.395c17.442-14.504,28.665-35.14,31.36-57.664c-0.385-3.847,0.523-7.713,2.581-10.987  c3.326-0.833,6.049-3.215,7.317-6.4c6.142-14.872,9.997-30.588,11.435-46.613c0.003-0.871-0.104-1.738-0.32-2.581  c-1.528-6.227-5.189-11.722-10.347-15.531v-56.555c0-34.368-10.496-48.469-21.547-56.64C339.004,33.472,321.276,0,255.996,0  c-57.917,2.332-104.335,48.75-106.667,106.667v56.555c-5.158,3.809-8.819,9.304-10.347,15.531c-0.216,0.843-0.323,1.711-0.32,2.581  c1.436,16.033,5.291,31.756,11.435,46.635c0.924,3.015,3.347,5.334,6.4,6.123c1.195,0.597,3.435,3.691,3.435,11.243  c2.711,22.588,13.999,43.271,31.531,57.771c-2.24,13.973-12.651,50.816-34.901,60.117l-94.699,31.445  c-24.243,8.071-42.643,28.026-48.725,52.843l-12.8,51.2c-1.449,5.71,2.005,11.514,7.715,12.963c0.853,0.217,1.73,0.327,2.61,0.328  h490.667c5.891-0.002,10.665-4.779,10.664-10.67C511.993,500.461,511.886,499.595,511.676,498.752z"
                          :fill="isDarkMode ? '#fff' : '#232323'"
                        />
                      </svg>
                    </span>

                    <span class="d-none d-md-inline-block">{{
                      $t('log_in_sign_up')
                    }}</span>
                  </span>
                </div>
                <div
                  id="mainMenu"
                  :class="[
                    'additional-menu dropdown-menu d-flex align-center justify-center',
                    { 'additional-menu--logged': isUserLogged },
                  ]"
                  @mouseenter.stop="
                    () =>
                      $vuetify.breakpoint.mdAndUp
                        ? (showMainMenu = true)
                        : false
                  "
                  @mouseleave.stop="
                    () =>
                      $vuetify.breakpoint.mdAndUp
                        ? (showMainMenu = false)
                        : false
                  "
                >
                  <v-btn
                    icon
                    color="transparent"
                    width="35"
                    height="26"
                    @click="showMainMenu = !showMainMenu"
                  >
                    <v-icon :color="isDarkMode ? 'white' : 'dark'" size="20">
                      {{ mdiMenu }}
                    </v-icon>
                  </v-btn>
                </div>
                <div
                  id="languageMenu"
                  class="language dropdown-menu d-none d-md-flex"
                  @mouseenter.stop="showLanguageMenu = true"
                  @mouseleave.stop="showLanguageMenu = false"
                >
                  <v-btn
                    icon
                    color="transparent"
                    width="28"
                    height="21"
                    @click="showLanguageMenu = !showLanguageMenu"
                  >
                    <component :is="flagComponents[locale]"></component>
                  </v-btn>
                </div>
              </template>
              <template v-else>
                <div
                  id="mainMenu"
                  :class="[
                    'additional-menu dropdown-menu d-flex align-center',
                    { 'additional-menu--logged': isUserLogged },
                  ]"
                  @mouseenter.stop="showMainMenu = true"
                  @mouseleave.stop="showMainMenu = false"
                >
                  <div
                    class="d-flex align-center"
                    @click="showMainMenu = !showMainMenu"
                  >
                    <span
                      class="d-none d-md-flex align-md-center user-link user-link--login user-link--avatar text-uppercase"
                    >
                      <v-avatar min-width="31" width="31" height="31">
                        <v-img :src="avatar"></v-img>
                      </v-avatar>
                      <span class="d-none d-md-inline-block">
                        {{ user.firstName }}
                      </span>
                      <span class="caret"></span>
                    </span>

                    <v-icon class="d-md-none" color="dark" size="20">
                      {{ mdiMenu }}
                    </v-icon>
                  </div>
                </div>
              </template>
            </div>