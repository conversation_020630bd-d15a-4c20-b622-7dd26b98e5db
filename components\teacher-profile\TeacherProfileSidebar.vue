<template>
  <div class="booking-block">
    <div id="teacher-profile-prices" class="teacher-profile-panel prices">
      <price-per-lesson
        :has-free-slots="hasFreeSlots"
        :languages-taught="languagesTaught"
        :accept-new-students="acceptNewStudents"
        :student-has-lessons-with-teacher="studentHasLessonsWithTeacher"
        @schedule-lessons="$emit('show-time-picker-dialog')"
        @send-message="sendMessage"
      ></price-per-lesson>
    </div>

    <div
      id="teacher-profile-free-slots"
      class="user-free-slots teacher-profile-panel"
    >
      <free-slots
        :slug="slug"
        :current-time="currentTime"
        :has-free-slots="hasFreeSlots"
        :accept-new-students="acceptNewStudents"
        :student-has-lessons-with-teacher="studentHasLessonsWithTeacher"
        :is-shown-time-picker-dialog="isShownTimePickerDialog"
        @schedule-lessons="$emit('show-time-picker-dialog')"
        @update-current-time="$emit('update-current-time', $event)"
      ></free-slots>

      <div class="user-free-slots-button mt-3">
        <template v-if="!hasFreeSlots">
          <v-btn
            color="primary"
            class="font-weight-medium"
            width="100%"
            @click="sendMessage"
          >
            {{ $t('send_me_message') }}
          </v-btn>

          <template v-if="languagesTaught.length">
            <find-more-teachers-button
              v-for="(lt, idx) in languagesTaught"
              :key="idx"
              :language="lt"
              class="mt-2"
              outlined
            ></find-more-teachers-button>
          </template>
        </template>
        <template
          v-else-if="!acceptNewStudents && !studentHasLessonsWithTeacher"
        >
          <template v-if="languagesTaught.length">
            <find-more-teachers-button
              v-for="(lt, idx) in languagesTaught"
              :key="idx"
              :language="lt"
              :class="{ 'mt-2': idx === 1 }"
              outlined
            ></find-more-teachers-button>
          </template>
        </template>
        <template v-else>
          <v-btn
            class="gradient font-weight-medium"
            width="100%"
            @click="$emit('show-time-picker-dialog')"
          >
            <div class="text--gradient">
              {{ $t('see_full_calendar') }}
            </div>
          </v-btn>
        </template>
      </div>

      <lesson-time-notice
        class="user-free-slots-info-message caption mt-2"
      ></lesson-time-notice>
    </div>
  </div>
</template>

<script>
import LessonTimeNotice from '~/components/LessonTimeNotice'
import PricePerLesson from '~/components/teacher-profile/PricePerLesson'
import FreeSlots from '~/components/FreeSlots'
import FindMoreTeachersButton from '~/components/teacher-profile/FindMoreTeachersButton'

export default {
  name: 'TeacherProfileSidebar',
  components: {
    LessonTimeNotice,
    PricePerLesson,
    FreeSlots,
    FindMoreTeachersButton,
  },
  props: {
    slug: {
      type: String,
      required: true,
    },
    currentTime: {
      type: Object,
      required: true,
    },
    isShownTimePickerDialog: {
      type: Boolean,
      required: true,
    },
  },
  computed: {
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
    userProfile() {
      return this.$store.state.teacher_profile.item
    },
    hasFreeSlots() {
      return this.userProfile.hasFreeSlots
    },
    acceptNewStudents() {
      return this.userProfile.acceptNewStudents
    },
    studentHasLessonsWithTeacher() {
      return this.userProfile.studentHasLessonsWithThisTeacher
    },
    languagesTaught() {
      return this.userProfile?.languagesTaught ?? []
    },
  },
  methods: {
    sendMessage() {
      if (!this.isUserLogged) {
        this.$store.commit('SET_IS_LOGIN_SIDEBAR', true)

        return
      }

      this.$emit('show-message-dialog')
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.booking-block {
  .teacher-profile-panel {
    width: 100%;
    max-width: 295px;
  }

  .prices {
    padding: 20px;
    font-size: 14px;

    @media only screen and (max-width: $xsm-and-down) {
      max-width: 100%;
    }
  }

  .user-free-slots {
    position: relative;
    margin-top: 48px;
    padding: 18px 16px 24px;

    @media only screen and (max-width: $xsm-and-down) {
      max-width: 100%;
    }

    &-info-message {
      color: #969696;
    }
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    width: calc(100% + 30px);
    margin-left: -15px;

    .teacher-profile-panel {
      margin: 48px 15px 0;
    }
  }
}
</style>
