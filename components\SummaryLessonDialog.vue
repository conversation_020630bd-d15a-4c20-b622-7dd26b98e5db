<template>
  <l-dialog
    :dialog="isShownSummaryLessonDialog"
    max-width="725"
    custom-class="schedule-lesson-dialog"
    persistent
    :fullscreen="$vuetify.breakpoint.smAndDown"
    v-on="$listeners"
  >
    <v-form v-model="valid" @submit.prevent="scheduleLessons">
      <div class="schedule-lesson-dialog-header text--gradient">
        {{ $t('lesson_summary') }}:
      </div>
      <div
        :class="[
          'schedule-lesson-dialog-body pt-2 pt-sm-4',
          {
            'l-scroll l-scroll--grey l-scroll--large':
              $vuetify.breakpoint.xsOnly,
          },
        ]"
      >
        <v-row no-gutters>
          <v-col class="col-12 col-sm-6">
            <div class="details">
              <div class="details-row">
                <div class="subtitle-2 property font-weight-medium">
                  {{ $t('language') }}:
                </div>
                <div class="body-1 value value--icon">
                  <div v-if="selectedLanguage.isoCode" class="icon mr-1">
                    <v-img
                      :src="
                        require(`~/assets/images/flags/${selectedLanguage.isoCode}.svg`)
                      "
                      width="18"
                      height="18"
                    ></v-img>
                  </div>
                  {{ selectedLanguage.name }}
                </div>
              </div>
              <div class="details-row">
                <div class="subtitle-2 property font-weight-medium">
                  {{ $t('teacher_capitalize') }}:
                </div>
                <div class="body-1 value">
                  {{ teacher.firstName }} {{ teacher.lastName }}
                </div>
              </div>

              <div v-if="selectedCourse.isCourse" class="details-row">
                <div class="subtitle-2 property font-weight-medium">
                  {{ $t('course') }}:
                </div>
                <div class="body-1 value">
                  {{ selectedCourse.name }}
                  <span class="body-2 greyDark--text"
                    >({{ $tc('lessons_count', selectedCourse.lessons) }})</span
                  >
                </div>
              </div>
              <div v-else class="details-row">
                <div class="subtitle-2 property font-weight-medium">
                  {{ $t('package') }}:
                </div>
                <div class="body-1 value">
                  <template v-if="isSelectedTrial">
                    {{ $t('trial') }}
                  </template>
                  <template v-else>
                    {{ $tc('lessons_count', selectedCourse.lessons) }}
                  </template>
                </div>
              </div>

              <div class="details-row">
                <div class="subtitle-2 property font-weight-medium">
                  {{ $t('length') }}:
                </div>
                <div class="body-1 value">
                  {{ $tc('minutes_count', selectedCourse.length) }}
                </div>
              </div>
              <template v-if="selectedSlots.length">
                <div class="details-row">
                  <div class="subtitle-2 property font-weight-medium">
                    {{ $t('lesson_time') }}:
                  </div>
                  <div class="body-1 value">
                    <div v-for="(date, idx) in selectedSlots" :key="idx">
                      {{
                        $dayjs(date)
                          .add($dayjs(date).tz(timezone).utcOffset(), 'minute')
                          .format('ll, LT')
                      }}
                    </div>
                  </div>
                </div>
              </template>
              <div class="details-notice notice caption mt-2">
                {{ $t('time_listed_are_in_timezone', { timezone }) }}
              </div>
              <div
                v-if="!isFreeTrialPackage && lessonsLeft > 0"
                class="details-notice notice caption mt-2"
              >
                {{
                  $t('you_can_schedule_your_remaining_lessons', {
                    count: $tc('remaining_lessons_count', lessonsLeft),
                  })
                }}
              </div>
            </div>
          </v-col>
          <v-col class="col-12 col-sm-6 mt-3 mt-sm-0">
            <div v-if="isSelectedTrial" class="message mb-4">
              <div class="subtitle-2 font-weight-medium">
                {{ $t('write_message_to_your_teacher') }}:
              </div>
              <div class="mt-1">
                <v-textarea
                  v-model="message"
                  class="l-textarea"
                  no-resize
                  height="100"
                  :counter="messageCounter"
                  solo
                  dense
                  :rules="messageRules"
                  :hint="messageHint"
                  persistent-hint
                  :placeholder="
                    $t(
                      'briefly_introduce_yourself_write_your_teacher_few_words'
                    )
                  "
                >
                  <template #counter="{ props }">
                    <div
                      v-show="props.value > 0"
                      class="v-counter theme--light"
                    >
                      {{ props.value }}
                    </div>
                  </template>
                </v-textarea>
              </div>
              <div v-if="isFreeTrialPackage" class="mt-3">
                <v-checkbox
                  :value="isAgree"
                  :label="
                    $t(
                      'i_understand_that_my_teacher_is_making_time_for_this_trial'
                    )
                  "
                  class="l-checkbox caption"
                  :ripple="false"
                  :rules="agreeRules"
                  @change="isAgree = true"
                ></v-checkbox>
              </div>
            </div>
            <div v-if="!isFreeTrialPackage && !isEnoughCredits" class="payment">
              <div class="subtitle-2 font-weight-medium">
                {{ $t('choose_payment_method') }}:
              </div>
              <div class="mt-1 mt-sm-2">
                <v-radio-group
                  v-model="selectedPaymentMethod"
                  hide-details
                  class="mt-0 pt-0"
                >
                  <v-radio
                    v-for="paymentMethod in paymentMethods"
                    :key="paymentMethod.id"
                    :label="getLabelPayment(paymentMethod)"
                    class="l-radio-button"
                    :ripple="false"
                    :value="paymentMethod.id"
                  ></v-radio>
                </v-radio-group>
              </div>
            </div>
            <div class="details-row mt-3">
              <div class="subtitle-2 property font-weight-medium">
                {{ $t('total_price') }}:
              </div>
              <div class="body-1 value">
                <template v-if="isFreeTrialPackage">
                  {{ $t('free') }}
                </template>
                <template v-else>
                  {{ currentCurrencySymbol }}{{ totalPrice }}
                </template>
              </div>
            </div>
            <template v-if="!isFreeTrialPackage && additionalCredits">
              <div class="details-row mt-1">
                <div class="subtitle-2 property font-weight-medium">
                  {{ $t('langu_credit') }}:
                </div>
                <div class="body-1 value">
                  -{{ currentCurrencySymbol
                  }}{{ isEnoughCredits ? totalPrice : additionalCredits }}
                </div>
              </div>
              <div class="details-row mt-1">
                <div class="subtitle-2 property font-weight-medium">
                  {{ $t('total_due') }}:
                </div>
                <div class="body-1 value">
                  {{ currentCurrencySymbol }}{{ totalDuePrice }}
                </div>
              </div>
            </template>

            <div
              v-if="!isFreeTrialPackage"
              class="details-notice notice caption mt-2"
            >
              {{
                $t(
                  'your_teacher_will_receive_your_payment_once_lesson_has_successfully_concluded'
                )
              }}
            </div>
            <div
              v-if="!isFreeTrialPackage && additionalCredits"
              class="details-notice notice caption mt-1"
            >
              {{
                $t('after_this_purchase_you_will_have_credit_remaining', {
                  value: additionalCreditsLeft,
                })
              }}
            </div>
          </v-col>
        </v-row>
      </div>
      <div
        class="schedule-lesson-dialog-footer d-flex justify-space-between align-center"
      >
        <div class="prev-button body-1" @click="prevStep">
          <svg class="mr-1" width="17" height="12" viewBox="0 0 17 12">
            <use
              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#arrow-prev`"
            ></use>
          </svg>
          {{ $t('go_back') }}
        </div>
        <div>
          <v-btn
            v-if="isStudent"
            id="continue_trialOrPurchase"
            small
            color="primary"
            type="submit"
            :disabled="!valid"
          >
            <template v-if="isSelectedTrial && isFreeTrialPackage">
              {{ $t('book_trial') }}!
            </template>
            <template v-else-if="isEnoughCredits">
              {{ $t('complete_purchase') }}
            </template>
            <template v-else>
              {{ $t('continue_to_purchase') }}
            </template>
          </v-btn>
        </div>
      </div>
    </v-form>
  </l-dialog>
</template>

<script>
import LDialog from '@/components/LDialog'
import { hashUserData } from '@/utils/hash'

const MESSAGE_MIN_LENGTH = 100

export default {
  name: 'ScheduleLessonDialog',
  components: { LDialog },
  props: {
    isShownSummaryLessonDialog: {
      type: Boolean,
      required: true,
    },
    username: {
      type: String,
      required: true,
    },
    query: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      valid: true,
      isAgree: false,
      messageCounter: MESSAGE_MIN_LENGTH,
      messageRules: [
        (v) => v?.length >= MESSAGE_MIN_LENGTH || this.messageHint,
      ],
      agreeRules: [(v) => !!v || this.$t('field_required')],
    }
  },
  computed: {
    timezone() {
      return this.$store.getters['user/timeZone']
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
    teacher() {
      return this.$store.state.teacher_profile.item
    },
    paymentMethods() {
      return this.$store.state.purchase.paymentMethods
    },
    trialPackage() {
      return this.$store.getters['teacher_profile/trialPackage']
    },
    isSelectedTrial() {
      return this.$store.getters['teacher_profile/isSelectedTrial']
    },
    isFreeTrialPackage() {
      return this.isSelectedTrial && this.trialPackage.isFreeTrialLesson
    },
    selectedCourse() {
      return this.$store.state.teacher_profile.selectedCourse
    },
    selectedLanguage() {
      return this.$store.state.teacher_profile.selectedLanguage
    },
    selectedSlots() {
      return this.$store.state.teacher_profile.selectedSlots
        .map((item) => item[0].date)
        .sort((a, b) => new Date(a) - new Date(b))
    },
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
    totalPrice() {
      return this.$store.getters['teacher_profile/totalPrice']
    },
    lessonsLeft() {
      return this.selectedCourse.lessons - this.selectedSlots.length
    },
    message: {
      get() {
        return this.$store.state.purchase.message
      },
      set(value) {
        this.$store.commit('purchase/SET_MESSAGE', value)
      },
    },
    selectedPaymentMethod: {
      get() {
        return this.$store.state.purchase.selectedPaymentMethod
      },
      set(value) {
        this.$store.commit('purchase/SET_SELECTED_PAYMENT_METHOD', value)
      },
    },
    additionalCredits() {
      return this.$store.getters['purchase/additionalCredits']
    },
    isEnoughCredits() {
      return this.additionalCredits >= this.totalPrice
    },
    totalDuePrice() {
      return (this.isEnoughCredits
        ? 0
        : this.totalPrice - this.additionalCredits
      ).toFixed(2)
    },
    additionalCreditsLeft() {
      return (
        this.currentCurrencySymbol +
        (this.isEnoughCredits
          ? this.additionalCredits - this.totalPrice
          : '0.00')
      )
    },
    messageHint() {
      return this.$t('please_write_at_least_characters', {
        value: MESSAGE_MIN_LENGTH,
      })
    },
    userCurrency() {
      return this.$store.getters['user/currency']
    },
    getFormattedDate() {
      const date = new Date()
      const options = {
        year: 'numeric',
        month: 'short',
        day: '2-digit',
        hour: 'numeric',
        minute: 'numeric',
        hour12: true,
      }
      const formattedDate = date.toLocaleString('en-US', options)
      return formattedDate || 'Jan 01, 2000, 12:00 AM'
    },
  },
  methods: {
    prevStep() {
      this.$router.push({
        path: this.$route.path,
        query: { ...this.query, step: 'schedule-lessons' },
      })
      this.$emit('prev-step')
    },
    getLabelPayment(payment) {
      let label

      switch (payment.id) {
        case 1:
          label =
            this.userCurrency?.id !== 4
              ? this.$t('debit_or_credit_card')
              : this.$t('debit_or_credit_card_pl_version')
          break
        case 2:
          label = 'Przelewy24/BLIK'
          break
        default:
          label = payment.name
      }

      return label
    },
    async scheduleLessons() {
      const tidioData = this.$store.state.user.tidioData || {}
      // Try to get user data from the API for the most up-to-date information
      let userData = null
      try {
        userData = await this.$store.dispatch('payments/fetchUserData')
      } catch (error) {
        console.error('Error fetching user data from API:', error)
      }

      // If API call fails, fall back to store state
      if (!userData || !userData.email) {
        userData = this.$store.state.user.item || {}
      }

      const userEmail = tidioData.email || ''
      const userName = `${userData.firstName || ''} ${
        userData.lastName || ''
      }`.trim()

      // Hash the user data
      const hashedEmail = hashUserData(userEmail)
      const hashedName = hashUserData(userName)

      // Create or update event data with hashed values
      let eventData = null

      // Create free trial event if applicable
      if (this.isSelectedTrial && this.trialPackage.isFreeTrialLesson) {
        eventData = {
          event: 'purchase_free_trial',
          ecommerce: {
            transaction_id_free_trial: 'T_12345',
            items: [
              {
                item_id_free_trial:
                  this.$store.state.teacher_profile.item.id || '1234',
                teacher_name_free_trial: `${this.$store.state.teacher_profile.item.firstName.trim()} ${this.$store.state.teacher_profile.item.lastName.trim()}`,
                language_free_trial:
                  this.$store.state.teacher_profile.selectedLanguage.name ||
                  'English',
                lesson_length_free_trial:
                  `${this.selectedCourse.length} minutes` || '30 minutes',
                lesson_time_free_trial: this.getFormattedDate,
                package_type_free_trial: 'free_trial',
                package_free_trial:
                  `${this.selectedCourse.lessons} Lesson` || '1 Lesson',
                user_name: hashedName,
                email_id: hashedEmail,
              },
            ],
          },
        }
      }
      // Create paid trial event if applicable
      else if (this.isSelectedTrial && !this.trialPackage.isFreeTrialLesson) {
        eventData = {
          event: 'purchase_paid_trial',
          ecommerce: {
            transaction_id_paid_trial: 'T_12345',
            value_paid_trial:
              this.$store.getters['teacher_profile/totalPrice'] || 0,
            tax_paid_trial: null,
            currency_paid_trial:
              this.$store.getters['user/currency'].isoCode || 'USD',
            items: [
              {
                item_id_paid_trial:
                  this.$store.state.teacher_profile.item.id || '1234',
                teacher_name_paid_trial: `${this.$store.state.teacher_profile.item.firstName.trim()} ${this.$store.state.teacher_profile.item.lastName.trim()}`,
                total_price_paid_trial:
                  this.$store.getters['teacher_profile/totalPrice'] || 0,
                currency_paid_trial:
                  this.$store.getters['user/currency'].isoCode || 'USD',
                language_paid_trial:
                  this.$store.state.teacher_profile.selectedLanguage.name ||
                  'English',
                lesson_length_paid_trial:
                  `${this.selectedCourse.length} minutes` || '30 minutes',
                lesson_time_paid_trial: this.getFormattedDate,
                package_type_paid_trial: 'paid trial',
                package_paid_trial:
                  `${this.selectedCourse.lessons} Lesson` || '1 Lesson',
                payment_type_paid_trial: 'credit',
                user_name: hashedName,
                email_id: hashedEmail,
              },
            ],
          },
        }
      }
      // Create standard purchase event for regular lessons
      else {
        eventData = {
          event: 'purchase',
          ecommerce: {
            transaction_id: 'T_12345',
            value: this.$store.getters['teacher_profile/totalPrice'] || 0,
            tax: null,
            currency: this.$store.getters['user/currency'].isoCode || 'USD',
            user_id: this.$store.getters['user/getUserId'] || '0',
            items: [
              {
                item_id: this.$store.state.teacher_profile.item.id || '1234',
                teacher_name: `${this.$store.state.teacher_profile.item.firstName.trim()} ${this.$store.state.teacher_profile.item.lastName.trim()}`,
                total_price:
                  this.$store.getters['teacher_profile/totalPrice'] || 120,
                currency: this.$store.getters['user/currency'].isoCode || 'USD',
                language:
                  this.$store.state.teacher_profile.selectedLanguage.name ||
                  'English',
                lesson_length:
                  `${this.selectedCourse.length} minutes` || '30 minutes',
                lesson_time: this.getFormattedDate,
                package_type: 'Paid',
                package: `${this.selectedCourse.lessons} Lesson` || '1 Lesson',
                payment_type: 'credit',
                user_name: hashedName,
                email_id: hashedEmail,
              },
            ],
          },
        }
      }

      localStorage.setItem('event_data', JSON.stringify(eventData))

      // Print initial event data
      // eslint-disable-next-line no-console
      if (this.selectedPaymentMethod === 2) {
        window.localStorage.setItem('teacher-username', this.username)
      }

      this.$store.dispatch('loadingStart')
      this.$store.dispatch('purchase/scheduleLessons', this.username)
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.v-application .v-dialog.schedule-lesson-dialog {
  & > .v-card {
    padding: 32px 40px !important;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding: 50px 18px 74px !important;

      .dialog-content,
      .v-form,
      .schedule-lesson-dialog-body {
        height: 100%;
      }

      .schedule-lesson-dialog-body {
        overflow-y: auto;
      }
    }
  }

  .details {
    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      padding-right: 15px;
    }

    &-row {
      display: flex;
      margin-top: 16px;

      @media #{map-get($display-breakpoints, 'xs-only')} {
        margin-top: 8px;
      }

      &:first-child {
        margin-top: 0;
      }

      .property {
        width: 115px;
        line-height: 1.2 !important;
      }

      .value {
        padding-left: 5px;

        &--icon {
          position: relative;
          padding-left: 26px;

          .v-image {
            position: absolute;
            left: 0;
            top: 3px;
            border-radius: 50%;
            overflow: hidden;
          }
        }
      }
    }
  }

  .notice {
    position: relative;
    margin-top: 4px;
    color: #a4a4a4;

    p {
      margin: 10px 0;
    }

    a {
      color: inherit;
      text-decoration: none;

      &:hover {
        color: var(--v-orange-base);
      }
    }

    .spinner {
      position: absolute;
      bottom: -70px;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  .details-notice {
    color: #969696;
  }

  .l-checkbox {
    .v-label {
      font-size: 12px !important;
    }

    .v-input--selection-controls__input {
      margin-top: 3px;
    }
  }

  .schedule-lesson-dialog {
    &-header {
      display: inline-block;
      padding-right: 60px;
      font-size: 20px;
      font-weight: 700;
      line-height: 1.1;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 50px;
        display: flex;
        align-items: center;
        padding-left: 18px;
        font-size: 18px;
      }
    }

    &-body .row {
      .col:first-child {
        padding-right: 20px;
      }

      .col:last-child {
        padding-left: 20px;
      }
    }

    &-footer {
      @media #{map-get($display-breakpoints, 'md-and-up')} {
        margin-top: 28px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 74px;
        padding: 0 18px;
      }

      .prev-button {
        color: var(--v-orange-base);
        cursor: pointer;
      }
    }
  }
}
</style>
