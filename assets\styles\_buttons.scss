.v-btn:not(.v-btn--icon) {
  &.v-size--small,
  &.v-size--default {
    min-width: 142px !important;
  }

  &.v-size--large {
    min-width: 180px !important;
    font-weight: 700 !important;
  }
}

.v-application {
  .v-btn {
    box-shadow: none !important;
    transition: background 0.2s;

    &__content {
      flex: 0 0 100%;
      white-space: normal;
    }

    &.primary {
      background: linear-gradient(
          126.15deg,
          var(--v-success-base) 0%,
          var(--v-primary-base) 102.93%
      ) !important;

      &::before {
        background: linear-gradient(
            305.26deg,
            var(--v-success-base) 8.94%,
            var(--v-primary-base) 110.83%
        ) !important;
        box-shadow: 0 4px 34px rgba(128, 182, 34, 0.3);
      }

      &--light {
        background: linear-gradient(
            126.15deg,
            #bcd89a 0%,
            #adccf2 102.93%
        ) !important;

        &:hover::before {
          opacity: 0.6 !important;
        }

        .v-btn__content {
          color: var(--v-darkLight-base);
        }
      }

      &--education {
        border-radius: 30px;
        background: linear-gradient(106deg, #80B622 -67.76%, #71AB53 -15.97%, #66A474 18%, #589B9F 63.31%, #4E94BF 102.56%, #3C87F8 159.72%) !important;

        &::before {
          background: linear-gradient(
              305.26deg,
              #80B622 -67.76%, #71AB53 -15.97%, #66A474 18%, #589B9F 63.31%, #4E94BF 102.56%, #3C87F8 159.72%
          ) !important;
          box-shadow: 0 4px 34px rgba(128, 182, 34, 0.3);
        }

        .v-btn__content {
          color: #fff;
        }
      }
    }

    &.error {
      background: linear-gradient(
          122.42deg,
          #D67B7F 0%,
          #F9C176 100%
      ) !important;

      &::before {
        background: linear-gradient(
            305.26deg,
            #D67B7F 8.94%,
            #F9C176 110.83%
        ) !important;
        box-shadow: 0 4px 34px rgba(214, 123, 127, 0.3);
      }
    }

    &.v-btn--icon {
      &:hover::before {
        opacity: 0.2 !important;
      }
    }

    &.primary--text {
      &.v-btn--outlined {
        border-color: var(--v-orange-base) !important;
        box-shadow: none !important;

        &::before {
          background-color: transparent !important;
        }

        .v-btn__content {
          color: #fff !important;
        }

        &:hover {
          border-color: var(--v-success-base) !important;

          .v-btn__content {
            color: var(--v-success-base) !important;
          }
        }
      }
    }

    &.success {
      &::before {
        background: var(--v-greenLight-base) !important;
        box-shadow: 0 4px 32px rgba(149, 206, 50, 0.3);
      }
    }

    &.greyDark {
      box-shadow: none !important;

      &::before {
        background-color: var(--v-orangeLight-base) !important;
      }

      .v-btn__content {
        color: #fff !important;
      }
    }

    &.greyDark--text:hover {
      .v-btn__content {
        color: #fff !important;
      }
    }

    &.orange--text {
      background: transparent !important;

      .v-btn__content {
        color: var(--v-orange-base) !important;
      }

      &.v-btn--outlined {
        &:hover {
          border-color: var(--v-orange-base) !important;

          .v-btn__content {
            color: #fff !important;
          }

          &::before {
            background-color: var(--v-orange-base) !important;
            box-shadow: 0 4px 32px rgba(251, 176, 59, 0.3) !important;
          }
        }
      }
    }

    &.orange {
      background: var(--v-orange-base) !important;

      &::before {
        background-color: var(--v-orange-base) !important;
      }

      .v-btn__content {
        color: #fff !important;
      }

      &:hover {
        border-color: var(--v-orangeLight-base) !important;

        &::before {
          background-color: var(--v-orangeLight-base) !important;
          box-shadow: 0 4px 32px rgba(251, 176, 59, 0.3) !important;
        }

        .v-btn__content {
          color: #fff !important;
        }
      }
    }

    &.gradient {
      position: relative;
      background-color: transparent !important;

      &--bg-white {
        background-color: #fff !important;
      }


      &:focus,
      &:hover {
        &::before {
          display: none;
        }
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 24px;
        padding: 1px;
        background: linear-gradient(
            126.15deg,
            var(--v-success-base) 0%,
            var(--v-primary-base) 102.93%
        );
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask-composite: destination-out;
        mask-composite: exclude;
      }
    }

    &--text {
      &:focus,
      &:hover {
        &::before {
          display: none;
        }
      }
    }
  }
}
