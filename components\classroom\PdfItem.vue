<template>
  <classroom-container :asset="file" :child-header-height="92">
    <div ref="childComponent" class="image-wrap-classroom">
      <classroom-container-header :file="file" :title="file.asset.displayName">
        <canvas :id="`pdf-render--${file.id}`"></canvas>
        <div class="transparent">
          <konva
            v-if="file && width && height"
            :file="file"
            :width="width"
            :height="height"
            :scale="scale"
            :current-page="page"
            :style="{
              marginTop: `-${height}px`,
            }"
          ></konva>
        </div>
        <div
          class="pdf-controls"
          @mouseenter.prevent="mouseenterHandler"
          @mouseleave.prevent="mouseleaveHandler"
        >
          <v-btn
            icon
            width="36"
            height="36"
            :disabled="!isPrevPageEnabled"
            @click="showPrevPage"
          >
            <v-img
              :src="require('~/assets/images/classroom/arrow-left.svg')"
              height="14"
              contain
            />
          </v-btn>
          <span class="page-info">
            {{ $t('page') }} <span>{{ page }}</span> of {{ pageTotal
            }}<span></span>
          </span>
          <v-btn
            class="btn-next"
            icon
            small
            width="36"
            height="36"
            :disabled="!isNextPageEnabled"
            @click="showNextPage"
          >
            <v-img
              :src="require('~/assets/images/classroom/arrow-left.svg')"
              height="14"
              contain
            ></v-img>
          </v-btn>
        </div>
      </classroom-container-header>
    </div>
  </classroom-container>
</template>

<script>
import { debounce } from '~/helpers'

import ClassroomContainer from '~/components/classroom/ClassroomContainer'
import ClassroomContainerHeader from '~/components/classroom/ClassroomContainerHeader'
import { defaultWidth } from '~/helpers/constants'
import Konva from '~/components/classroom/Konva'
import SetTool from '~/mixins/SetTool'

export default {
  name: 'PdfItem',
  components: { ClassroomContainer, ClassroomContainerHeader, Konva },
  mixins: [SetTool],
  props: {
    file: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      pdfjsLib: {},
      renderInProgress: false,
      viewport: null,
      page: 1,
      pageTotal: 1,
      pdfDoc: null,
      pdfPage: null,
    }
  },
  computed: {
    scale() {
      if (this.width) {
        return this.width / defaultWidth
      }

      return 1
    },
    canvas() {
      return document.querySelector(`#pdf-render--${this.file.id}`)
    },
    zoomIndex() {
      return this.$store.getters['classroom/zoomAsset']?.asset?.zoomIndex ?? 1
    },
    width() {
      return this.file.asset?.width ?? 0
    },
    height() {
      return this.file.asset?.height ?? 0
    },
    isPrevPageEnabled() {
      return this.pageTotal > 1 && this.page > 1
    },
    isNextPageEnabled() {
      return this.pageTotal > 1 && this.page < this.pageTotal
    },
  },
  watch: {
    'file.asset.page'(page) {
      this.page = page

      this.renderPage()
    },
    zoomIndex() {
      this.renderPage()
    },
  },
  async mounted() {
    this.pdfjsLib = await require('pdfjs-dist')
    this.pdfjsLib.GlobalWorkerOptions.workerSrc = await require('pdfjs-dist/build/pdf.worker.entry')

    if (this.file.asset.page) {
      this.page = this.file.asset.page
    }

    this.pdfjsLib
      .getDocument(this.file.asset.path)
      .promise.then((pdfDoc) => {
        this.pdfDoc = pdfDoc
        this.pageTotal = this.pdfDoc.numPages

        this.renderPage()

        const asset = { originalWidth: defaultWidth }

        this.$store.commit('classroom/moveAsset', {
          id: this.file.id,
          asset,
        })

        this.$store.dispatch('classroom/moveAsset', {
          id: this.file.id,
          lessonId: this.file.lessonId,
          asset,
        })
      })
      .catch((err) => {
        alert(err.message)
      })

    new ResizeObserver(debounce(this.resize, 200)).observe(
      this.$refs.childComponent
    )
  },
  methods: {
    showPage() {
      this.$store.dispatch('classroom/moveAsset', {
        id: this.file.id,
        lessonId: this.file.lessonId,
        asset: {
          page: this.page,
        },
      })
    },
    showPrevPage() {
      if (this.page <= 1) {
        return
      }

      this.page -= 1

      this.renderPage()
      this.showPage()
    },
    showNextPage() {
      if (this.page >= this.pageTotal) {
        return
      }

      this.page += 1

      this.renderPage()
      this.showPage()
    },
    resize() {
      if (this.pdfPage) {
        if (this.renderInProgress) {
          return
        }

        this.renderInProgress = true

        const canvasContext = this.canvas.getContext('2d')
        const dpr = window.devicePixelRatio || 1
        const scaleCanvas = dpr * this.zoomIndex

        if (this.$refs.childComponent) {
          const {
            width: childComponentWidth,
          } = this.$refs.childComponent.getBoundingClientRect()
          const { width: originalViewport } = this.pdfPage.getViewport({
            scale: 1,
          })
          const scaleViewport =
            childComponentWidth / this.zoomIndex / originalViewport

          this.viewport = this.pdfPage.getViewport({
            scale: scaleCanvas * scaleViewport,
          })
          this.canvas.height = this.viewport.height
          this.canvas.width = this.viewport.width
          this.canvas.style.width = '100%'

          const renderTask = this.pdfPage.render({
            canvasContext,
            viewport: this.viewport,
          })

          renderTask.promise.then(() => {
            this.$store.commit('classroom/moveAsset', {
              id: this.file.id,
              asset: {
                width: this.canvas.width / scaleCanvas,
                height: this.canvas.height / scaleCanvas,
              },
            })

            this.renderInProgress = false
          })
        }
      }
    },
    renderPage() {
      this.pdfDoc.getPage(this.page).then((pdfPage) => {
        this.pdfPage = pdfPage

        this.resize()
      })
    },
    mouseenterHandler() {
      this.$store.commit(
        'classroom/setCursorNameBeforeChange',
        this.$store.state?.userParams?.cursor || 'cursor-pointer'
      )
      this.$store.commit(
        'classroom/setToolNameBeforeChange',
        this.$store.state?.userParams?.tool || 'pointer'
      )

      this.setTool('pointer', 'cursor-pointer')
    },
    mouseleaveHandler() {
      this.setTool(
        this.$store.state.classroom.toolNameBeforeChange,
        this.$store.state.classroom.cursorNameBeforeChange
      )
    },
  },
}
</script>

<style lang="scss">
.pdf-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.25);
  position: relative;
  padding: 5px 12px;
  background: #fff;

  button {
    border: none;
    background: none;
    cursor: pointer !important;

    & * {
      cursor: pointer !important;
    }

    &.btn-next .v-image {
      transform: rotate(180deg);
    }
  }

  button:disabled,
  button[disabled] {
    opacity: 0;
    visibility: hidden;
  }
}
</style>
