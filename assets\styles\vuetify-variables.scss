// Ref: https://github.com/nuxt-community/vuetify-module#customvariables
//
// The variables you want to modify

$color-pack: false;

$body-font-family: 'Lato', sans-serife;
$font-size-root: 18px;

$spacer: 8px;

$grid-gutter: 50px;
$grid-breakpoints: (
  'xs': 0,
  'sm': 768px,
  'md': 992px,
  'lg': 1464px - 24px,
  'xl': 1668px - 24px
);
$container-max-widths: (
  'md': 942px,
  'lg': 1140px,
  'xl': map-get($grid-breakpoints, 'xl')
);

$app-bar-elevation: 2;

$headings: (
  'h2': (
    'size': 24px,
    'weight': 700,
    'letter-spacing': 0,
    'line-height': 1.333
  ),
  'subtitle-1': (
    'size': 18px,
    'weight': 700,
    'letter-spacing': 0,
    'line-height': 1.555
  ),
  'subtitle-2': (
    'size': 16px,
    'weight': 700,
    'letter-spacing': 0,
    'line-height': 1.5
  ),
  'body-1': (
    'size': 16px,
    'weight': 400,
    'letter-spacing': 0,
    'line-height': 1.5
  ),
  'body-2': (
    'size': 14px,
    'weight': 400,
    'letter-spacing': 0,
    'line-height': 1.333
  ),
  'caption': (
    'size': 12px,
    'weight': 400,
    'letter-spacing': 0,
    'line-height': 1.333
  )
);

$font-weights: (
  'medium': 600
);

$btn-sizes: (
  'small': 38,
  'default': 40,
  'large': 44,
  'x-large': 50,
);
$btn-font-sizes: (
  'small': 15px,
  'default': 15px,
  'large': 16px,
  'x-large': 18px,
);
$btn-text-transform: none;
$btn-border-radius: 24px;
$btn-font-weight: 500;
$btn-hover-opacity: 1;
$btn-letter-spacing: normal;

$expansion-panel-border-radius: 8px;
$expansion-panel-header-padding: 20px 24px;
$expansion-panel-content-padding: 0 24px 20px;
$expansion-panel-header-min-height: 64px;

$text-field-border-radius: 24px;
$text-field-enclosed-details-padding: 0 16px;

$dialog-border-radius: 15px;
$dialog-card-title-padding: 32px 32px 10px 32px;
$dialog-card-text-padding: 32px 64px;
$dialog-margin: 10px;

$textarea-line-height: 1.23;
$textarea-enclosed-text-slot-padding: 0;
$textarea-enclosed-text-slot-margin: 0;
$textarea-dense-box-enclosed-single-outlined-margin-top: 0;

$snackbar-content-padding: 8px 24px;

$alert-padding: 4px 10px;
$alert-border-radius: 16px;
$alert-font-size: 14px;
$alert-border-width: 0;

$calendar-weekly-weekday-font-size: 13px;
$calendar-weekly-weekday-padding: 0 2px 6px 2px;
$calendar-weekly-day-label-margin: 14px 0 0 0;

$snackbar-z-index: 1000099;

$skeleton-loader-border-radius: 8px;
