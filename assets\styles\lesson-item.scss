@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.lesson {
  position: relative;
  min-height: 128px;
  padding-left: 142px;

  @media only screen and (max-width: $mac-13-and-down) {
    min-height: 130px;
    padding-left: 85px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-up')} {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 6px 10px rgba(17, 46, 90, 0.08);
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding-left: 112px;
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    padding-left: 0;
  }

  &-date {
    justify-content: center;
    align-items: center;
    width: 142px;
    padding: 11px;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(
        126.15deg,
        rgba(128, 182, 34, 0.74) 0%,
        rgba(60, 135, 248, 0.74) 102.93%
    );
    border-radius: 16px;
    color: #fff;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      flex-direction: column;
      justify-content: space-between;
      box-shadow: 4px 5px 8px rgba(102, 102, 102, 0.25);
      z-index: 3;
    }

    @media only screen and (max-width: $mac-13-and-down) {
      width: 85px;
      padding: 14px 6px;
      font-size: 15px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: 112px;
      padding: 10px 4px;
      font-size: 13px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      width: 100%;
      padding: 7px 10px 32px;
      font-size: 16px !important;
      font-weight: 600 !important;
      border-radius: 10px 10px 0 0;

      & > div {
        display: flex;
        align-items: center;
      }
    }

    @media only screen and (max-width: $xxs-and-up) {
      font-size: 14px !important;
      font-weight: 400 !important;
    }

    .weekday,
    .time {
      font-size: 13px;
      font-weight: 700;
      line-height: 1;
    }

    .date {
      font-weight: 700;
      font-size: 24px;
      line-height: 1.2;
      white-space: nowrap;

      @media only screen and (max-width: $mac-13-and-down) {
        margin-bottom: 4px;
        font-size: 20px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        margin-bottom: 0;
        font-weight: inherit;
        font-size: inherit;
      }
    }

    .remaining {
      line-height: 1.23;

      @media #{map-get($display-breakpoints, 'sm-and-up')} {
        font-size: 13px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        padding: 0 3px;
      }
    }

    .duration {
      white-space: nowrap;

      @media only screen and (min-width: $mac-13-and-up) {
        padding-left: 22px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-up')} {
        position: relative;
        font-weight: 400;
        color: #e8f1f7;
      }

      &-icon {
        position: absolute;
        left: 0;
        top: 50%;
        width: 18px;
        height: 18px;
        margin-top: -9px;
        color: var(--v-dark-lighten3);

        @media only screen and (max-width: $mac-13-and-down) {
          display: none;
        }
      }
    }
  }

  &-content {
    position: relative;
    display: flex;
    justify-content: space-between;
    width: 100%;
    border-radius: 0 10px 10px 0;
    overflow: hidden;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      min-height: 128px;
      margin-top: -25px;
      border-radius: 10px;
      background: #fff;
      box-shadow: 0 1px 5px rgba(130, 130, 130, 0.3);
      z-index: 2;
    }

    @media only screen and (max-width: $xsm-and-down) {
      flex-direction: column;
      padding: 12px 12px 4px;
    }
  }

  &-details {
    display: flex;
    padding: 8px 10px 8px 18px;

    @media only screen and (max-width: $mac-13-and-down) {
      padding: 12px 4px 8px 12px;

      & > .avatar {
        display: none;
      }
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      padding-top: 10px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      position: relative;
      padding: 0 0 0 100px;
    }

    .avatar {
      position: relative;

      @media only screen and (max-width: $xsm-and-down) {
        position: absolute;
        left: 0;
        top: 0;
      }

      .user-status {
        position: absolute;
        top: 0;
        right: 0;

        @media only screen and (max-width: $xxs-and-down) {
          top: 1px;
          right: 1px;
        }
      }

      a {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }

    .details {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .user-info {
        line-height: 1.1;

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          margin-bottom: 8px;
        }

        @media only screen and (min-width: $mac-13-and-up) {
          padding-top: 2px;
        }

        &-name {
          display: inline;
          font-size: 24px;

          @media #{map-get($display-breakpoints, 'lg-and-up')} {
            margin-right: 6px;
          }

          @media #{map-get($display-breakpoints, 'md-and-down')} {
            font-size: 22px;
          }

          @media only screen and (max-width: $mac-13-and-down) {
            font-size: 20px;
          }

          @media only screen and (max-width: $xxs-and-down) {
            font-size: 18px;
          }

          div {
            position: relative;
            top: -2px;
            display: inline-block;
            height: 16px;
            color: var(--v-dark-lighten3);
            cursor: pointer;
          }
        }

        &-status {
          display: inline;
          font-size: 13px;
          color: var(--v-dark-lighten3);
          text-transform: lowercase;

          &--online {
            color: var(--v-green-lighten1);
          }
        }
      }

      .lesson-info {
        font-size: 16px;
        color: var(--v-dark-lighten3);
        line-height: 1.1;

        @media #{map-get($display-breakpoints, 'sm-and-up')} {
          padding-bottom: 4px;
        }

        @media only screen and (min-width: $mac-13-and-up) {
          .avatar {
            display: none !important;
          }
        }

        @media only screen and (max-width: $mac-13-and-down) {
          display: flex;
          align-items: flex-end;
          font-size: 14px;
        }

        & > div:not(.avatar) > div:not(:last-child) {
          margin-bottom: 3px;

          @media only screen and (max-width: $xsm-and-down) {
            margin-bottom: 5px;
          }
        }

        .lesson-actions-additional {
          @media only screen and (min-width: $xsm-and-up) {
            display: none !important;
          }
        }
      }
    }
  }

  &-actions,
  &-dialog {
    .v-btn:not(.v-btn--icon).v-size--default {
      min-width: 158px !important;
      padding: 0 10px !important;
    }
  }

  &-actions {
    display: flex;
    width: 350px;
    padding: 18px 18px 10px 0;

    @media only screen and (max-width: $mac-13-and-down) {
      padding: 12px 12px 8px 0;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      padding-top: 10px;
    }

    @media only screen and (min-width: $xsm-and-up) {
      flex-direction: column;
      align-items: flex-end;
      justify-content: space-between;
      flex-grow: 1;
    }

    @media only screen and (max-width: $xsm-and-down) {
      width: 100%;
      margin-top: 8px;
      padding: 8px 0 0;
      border-top: 1px solid rgba(0, 0, 0, 0.08);
    }

    &-buttons {
      display: flex;
      justify-content: flex-end;

      @media only screen and (max-width: $xsm-and-down) {
        justify-content: space-between;
        width: 100%;

        .v-btn {
          width: 158px !important;
          margin-left: 0 !important;
          margin-right: 0 !important;
        }
      }

      .go-to-class-btn {
        .icon {
          &--right {
            margin-left: 3px;
          }

          &--rotated {
            transform: rotate(-90deg);
          }
        }
      }
    }

    &-additional {
      min-width: 158px;
      font-size: 13px;
      line-height: 1.333;

      @media only screen and (max-width: $xsm-and-down) {
        font-size: inherit;
        line-height: inherit;
      }

      & > div {
        @media only screen and (max-width: $xsm-and-down) {
          margin-top: 5px;
        }
      }

      & > div > div,
      a,
      span.action {
        position: relative;
        display: inline-block;
        padding-left: 20px;
        color: var(--v-darkLight-lighten3) !important;
        text-decoration: none;
        transition: color 0.3s;

        .v-image {
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }

      a,
      span.action {
        cursor: pointer;

        &:hover {
          color: var(--v-success-base) !important;
        }
      }
    }

    .lesson-actions-additional {
      @media only screen and (max-width: $xsm-and-down) {
        display: none !important;
      }
    }
  }

  &-dialog {
    position: absolute;
    top: 0;
    left: 100%;
    width: 100%;
    height: 100%;
    padding: 10px 18px 12px;
    font-size: 13px;
    line-height: 1.2;
    background-color: #fff;
    border-radius: 0 16px 16px 0;
    z-index: 2;
    transition: all 0.3s ease-in;

    @media only screen and (max-width: $mac-13-and-down) {
      padding: 8px 12px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      left: 0;
      top: 100%;
    }

    @media only screen and (max-width: $xsm-and-down) {
      &.justify-center {
        justify-content: space-between !important;
      }
    }

    &-title {
      display: flex;
      margin-bottom: 5px;
      font-size: 20px;

      @media only screen and (max-width: $xsm-and-down) {
        margin-bottom: 12px;
        font-size: 18px;
      }

      &-icon {
        display: flex;
        align-items: center;
        padding-right: 4px;
      }
    }

    &-content {
      color: var(--v-dark-lighten3);
      overflow-y: auto;
    }

    &-buttons {
      @media only screen and (max-width: $xsm-and-down) {
        display: flex;
        justify-content: space-between;
        margin-top: 8px;
        border-top: 1px solid rgba(0, 0, 0, 0.08);
      }

      .v-btn {
        max-width: 246px;
        margin-top: 8px;

        @media only screen and (min-width: $xsm-and-up) {
          width: 100%;
        }
      }

      & > *:first-child {
        margin-right: 16px;

        @media only screen and (max-width: $xsm-and-down) {
          margin-left: 4px;
        }
      }
    }

    &--student-info {
      .lesson-dialog-content {
        display: flex;

        @media only screen and (min-width: $xsm-and-up) {
          margin-right: 168px;
        }

        & > div {
          display: flex;
          flex-grow: 1;

          @media only screen and (max-width: $xsm-and-down) {
            width: 100%;
            max-width: 100%;
          }

          @media only screen and (max-width: $xxs-and-down) {
            flex-wrap: wrap;
          }

          & > div {
            @media only screen and (max-width: $xxs-and-down) {
              padding: 0 !important;
            }

            &:first-child {
              @media only screen and (min-width: $xxs-and-up) {
                padding-right: 45px;
                border-right: 1px solid var(--v-dark-lighten3);
              }
            }

            ul {
              padding: 0;
              list-style-type: none;
              line-height: 1.2;
            }
          }
        }
      }

      .lesson-dialog-buttons {
        @media only screen and (min-width: $mac-13-and-up) {
          right: 18px;
          bottom: 12px;
        }

        @media only screen and (min-width: $xsm-and-up) {
          position: absolute;
          right: 12px;
          bottom: 8px;
        }

        @media only screen and (max-width: $xsm-and-down) {
          display: flex;
          justify-content: flex-end;
          align-items: flex-end;
        }

        .v-btn {
          margin-right: 0 !important;
        }
      }
    }

    &--shown {
      left: 0;
      transition: all 0.3s ease-out;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        top: 0;
      }
    }
  }
}
