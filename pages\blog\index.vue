<template>
  <div id="dib-posts"></div>
</template>

<script>
export default {
  head() {
    return {
      title:
        this.$i18n.locale === 'pl'
          ? 'Langu | Blog: Artykuły i zasoby do efektywnej nauki języków online'
          : 'Langu | Blog: Articles and Resources for Effective Online Language Learning',
      script: [
        {
          src:
            'https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js',
          async: true,
          defer: true,
        },
      ],
    }
  },
  mounted() {
    this.loadDropInBlogScript()
  },
  methods: {
    loadDropInBlogScript() {
      if (typeof window.main === 'function') {
        // Call the main function directly if it exists
        window.main()
      } else {
        const existingScript = document.querySelector(
          'script[src="https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js"]'
        )
        if (!existingScript) {
          const script = document.createElement('script')
          script.src =
            'https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js'
          script.async = true
          script.defer = true
          script.onload = () => {
            // Ensure the main function is called after the script is loaded
            if (typeof window.main === 'function') {
              window.main()
            }
          }
          document.head.appendChild(script)
        }
      }
    },
  },
}
</script>

<style lang="scss">
@import '~assets/styles/faq-page.scss';
</style>
