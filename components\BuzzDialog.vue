<template>
  <l-dialog
    :dialog="dialog"
    custom-class="buzz-dialog"
    @close-dialog="closeDialog"
  >
    <div class="text-center">
      <div class="buzz-dialog-title text--gradient mb-3">
        {{ $t('time_for_class') }}
      </div>
      <v-avatar
        class="buzz-dialog-avatar"
        :size="`${$vuetify.breakpoint.xsOnly ? 120 : 180}px`"
      >
        <v-img
          :src="getSrcAvatar(item.avatars, 'user_thumb_180x180')"
          :srcset="
            getSrcSetAvatar(
              item.avatars,
              'user_thumb_180x180',
              'user_thumb_360x360'
            )
          "
          :options="{ rootMargin: '50%' }"
        ></v-img>
      </v-avatar>
      <div class="buzz-dialog-text body-1 mt-2 mt-sm-4">
        {{
          $t('teacher_is_inviting_you_into_classroom', {
            teacherFirstName: item.firstName,
          })
        }}
      </div>
      <div class="buzz-dialog-button mt-4 mt-sm-6">
        <!--    The attribute href is needed to trigger event 'users-joined-classroom'    -->
        <v-btn
          :href="`/lesson/${item.lessonId}/classroom`"
          color="primary"
          class="font-weight-medium"
        >
          <svg class="mr-1" width="18" height="18" viewBox="0 0 18 18">
            <use
              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#save-icon`"
            ></use>
          </svg>
          {{ $t('go_to_class') }}
        </v-btn>
      </div>
      <audio class="d-none" controls="none" autoplay>
        <source :src="buzzOgg" type="audio/ogg" />
        <source :src="buzzMp3" type="audio/mpeg" />
      </audio>
    </div>
  </l-dialog>
</template>

<script>
import LDialog from '@/components/LDialog'

const buzzOgg = require('~/assets/audio/buzz.ogg').default
const buzzMp3 = require('~/assets/audio/buzz.mp3').default

export default {
  name: 'BuzzDialog',
  components: { LDialog },
  props: {
    dialog: {
      type: Boolean,
      required: true,
    },
    item: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      buzzOgg,
      buzzMp3,
    }
  },
  beforeDestroy() {
    this.closeDialog()
  },
  methods: {
    closeDialog() {
      this.$emit('close')
    },
    getSrcAvatar(images, property, defaultImage = 'avatar.png') {
      return images?.[property]
        ? images[property]
        : require(`~/assets/images/homepage/${defaultImage}`)
    },
    getSrcSetAvatar(images, property1, property2) {
      return images?.[property1] && images?.[property2]
        ? `
            ${images[property1]} 1x,
            ${images[property2]} 2x,
          `
        : ''
    },
  },
}
</script>

<style lang="scss">
.buzz-dialog {
  &-title {
    padding: 0 40px;
    font-size: 20px;
    font-weight: 700;
    line-height: 1.1;
  }

  &-button {
    svg {
      transform: rotate(-90deg);
    }
  }
}
</style>
