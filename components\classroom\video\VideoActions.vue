<template>
  <div class="stream-controls">
    <div id="video-window-buttons" class="video-window-buttons-wrap">
      <div class="stream-controls-wrapper cursor-auto">
        <div class="toolbar-button-wrapper">
          <button
            class="toolbar-button-item cursor-pointer"
            data-stream-toggle-video
            type="button"
            :disabled="!isJoined"
            @click="$emit('toggle-video')"
          >
            <svg
              v-if="settings.isVideoEnabled"
              class="toolbar-button-icon"
              width="51"
              height="33"
              viewBox="0 0 51 33"
            >
              <use
                :xlink:href="`${require('~/assets/images/classroom/videocam.svg')}#videocam`"
              ></use>
            </svg>
            <svg
              v-else-if="!settings.isVideoEnabled"
              class="toolbar-button-icon"
              width="39"
              height="33"
              viewBox="0 0 39 33"
            >
              <use
                :xlink:href="`${require('~/assets/images/classroom/videocam.svg')}#videocam_off`"
              ></use>
            </svg>
          </button>
          <div class="hover-btn-info">
            <template v-if="settings.isVideoEnabled">
              {{ $t('turn_off_camera') }}
            </template>
            <template v-else>
              {{ $t('turn_on_camera') }}
            </template>
          </div>
        </div>
        <div class="toolbar-button-wrapper">
          <button
            class="toolbar-button-item cursor-pointer"
            data-stream-toggle-audio
            type="button"
            :disabled="!isJoined"
            @click="$emit('toggle-audio')"
          >
            <svg
              v-if="!settings.isMuted"
              class="toolbar-button-icon"
              width="23"
              height="33"
              viewBox="0 0 23 33"
            >
              <use
                :xlink:href="`${require('~/assets/images/classroom/microphone.svg')}#microphone`"
              ></use>
            </svg>
            <svg
              v-if="settings.isMuted"
              class="toolbar-button-icon"
              width="31"
              height="34"
              viewBox="0 0 31 34"
            >
              <use
                :xlink:href="`${require('~/assets/images/classroom/microphone.svg')}#microphone-off`"
              ></use>
            </svg>
          </button>
          <div class="hover-btn-info">
            <template v-if="!settings.isMuted">
              {{ $t('mute_microphone') }}
            </template>
            <template v-else>
              {{ $t('unmute_microphone') }}
            </template>
          </div>
        </div>
        <div class="toolbar-button-wrapper toolbar-button-wrapper-full-screen">
          <button
            class="toolbar-button-item cursor-pointer"
            data-stream-toggle-full-screen
            type="button"
            :disabled="!isJoined"
            @click="$emit('toggle-full-screen')"
          >
            <svg
              v-if="!settings.isFullscreenEnabled"
              class="toolbar-button-icon"
              width="31"
              height="31"
              viewBox="0 0 31 31"
            >
              <use
                :xlink:href="`${require('~/assets/images/classroom/full_screen.svg')}#full_screen`"
              ></use>
            </svg>
            <svg
              v-if="settings.isFullscreenEnabled"
              class="toolbar-button-icon"
              width="31"
              height="31"
              viewBox="0 0 31 31"
            >
              <use
                :xlink:href="`${require('~/assets/images/classroom/full_screen.svg')}#window_screen`"
              ></use>
            </svg>
          </button>
          <div class="hover-btn-info">
            <template v-if="!settings.isFullscreenEnabled">
              {{ $t('full_screen_video') }}
            </template>
            <template v-else>
              {{ $t('leave_full_screen_video') }}
            </template>
          </div>
        </div>
        <div class="toolbar-button-wrapper">
          <button
            class="toolbar-button-item cursor-pointer"
            data-stream-toggle-screen-share
            type="button"
            :disabled="
              isScreenShareDisabled || (!isJoined && type === 'twilio')
            "
            @click="$emit('toggle-screen-share')"
          >
            <svg
              v-if="settings.isScreenShareEnabled"
              class="toolbar-button-icon"
              width="38"
              height="35"
              viewBox="0 0 38 35"
            >
              <use
                :xlink:href="`${require('~/assets/images/classroom/not_share.svg')}#not_share`"
              ></use>
            </svg>
            <svg
              v-if="!settings.isScreenShareEnabled"
              class="toolbar-button-icon"
              width="37"
              height="28"
              viewBox="0 0 37 28"
            >
              <use
                :xlink:href="`${require('~/assets/images/classroom/not_share.svg')}#share`"
              ></use>
            </svg>
          </button>
          <div class="hover-btn-info">
            <template v-if="isScreenShareDisabled">
              {{ $t('share_is_disabled') }}
            </template>
            <template v-else>
              <template v-if="!settings.isScreenShareEnabled">
                {{ $t('share_my_screen') }}
              </template>
              <template v-else>
                {{ $t('stop_screenshare') }}
              </template>
            </template>
          </div>
        </div>

        <!-- Whereby-specific features -->
        <div v-if="type === 'whereby'" class="toolbar-button-wrapper">
          <button
            class="toolbar-button-item cursor-pointer"
            data-stream-toggle-hand-raise
            type="button"
            :disabled="!isJoined"
            @click="$emit('toggle-hand-raise')"
          >
            <svg
              :class="[
                'toolbar-button-icon',
                { 'hand-raised': settings.isHandRaised },
              ]"
              width="24"
              height="33"
              viewBox="0 0 24 33"
            >
              <use
                :xlink:href="`${require('~/assets/images/classroom/hand.svg')}#hand`"
              ></use>
            </svg>
          </button>
          <div class="hover-btn-info">
            <template v-if="settings.isHandRaised">
              {{ $t('lower_hand') }}
            </template>
            <template v-else>
              {{ $t('raise_hand') }}
            </template>
          </div>
        </div>

        <div v-if="type === 'whereby'" class="toolbar-button-wrapper">
          <button
            class="toolbar-button-item cursor-pointer"
            data-stream-toggle-chat
            type="button"
            :disabled="!isJoined"
            @click="$emit('toggle-chat')"
          >
            <svg
              :class="[
                'toolbar-button-icon',
                { 'chat-enabled': settings.isChatEnabled },
              ]"
              width="28"
              height="28"
              viewBox="0 0 28 28"
            >
              <use
                :xlink:href="`${require('~/assets/images/classroom/chat.svg')}#chat`"
              ></use>
            </svg>
          </button>
          <div class="hover-btn-info">
            <template v-if="settings.isChatEnabled">
              {{ $t('hide_chat') }}
            </template>
            <template v-else>
              {{ $t('show_chat') }}
            </template>
          </div>
        </div>

        <div v-if="type === 'whereby'" class="toolbar-button-wrapper">
          <button
            class="toolbar-button-item cursor-pointer"
            data-stream-toggle-participants
            type="button"
            :disabled="!isJoined"
            @click="$emit('toggle-participants')"
          >
            <svg
              :class="[
                'toolbar-button-icon',
                { 'participants-enabled': settings.isParticipantsEnabled },
              ]"
              width="32"
              height="28"
              viewBox="0 0 32 28"
            >
              <use
                :xlink:href="`${require('~/assets/images/classroom/participants.svg')}#participants`"
              ></use>
            </svg>
          </button>
          <div class="hover-btn-info">
            <template v-if="settings.isParticipantsEnabled">
              {{ $t('hide_participants') }}
            </template>
            <template v-else>
              {{ $t('show_participants') }}
            </template>
          </div>
        </div>
      </div>
      <!-- Video provider switcher commented out - Whereby is now the only option -->
      <!--
      <div v-if="role === 'teacher'" class="stream-controls-switch">
        <div>{{ $t('video') }}:</div>
        <div class="d-inline-flex ml-1">
          <div class="toolbar-button-wrapper">
            <v-btn
              :class="[
                'toolbar-button-item cursor-pointer pa-0',
                { 'toolbar-button-item--selected': type === 'twilio' },
              ]"
              width="18"
              height="18"
              text
              @click="$emit('switch-video-player', 'twilio')"
            >
              A
            </v-btn>
          </div>
          <div class="toolbar-button-wrapper">
            <v-btn
              :class="[
                'toolbar-button-item cursor-pointer pa-0',
                { 'toolbar-button-item--selected': type === 'tokbox' },
              ]"
              width="18"
              height="18"
              text
              @click="$emit('switch-video-player', 'tokbox')"
            >
              B
            </v-btn>
          </div>
          <div class="toolbar-button-wrapper">
            <v-btn
              :class="[
                'toolbar-button-item cursor-pointer pa-0',
                { 'toolbar-button-item--selected': type === 'whereby' },
              ]"
              width="18"
              height="18"
              text
              @click="$emit('switch-video-player', 'whereby')"
            >
              C
            </v-btn>
          </div>
        </div>
      </div>
      -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoActions',
  props: {
    settings: {
      type: Object,
      required: true,
    },
    isJoined: {
      type: Boolean,
      required: true,
    },
    isScreenShareDisabled: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      required: true,
    },
  },
  computed: {
    role() {
      return this.$store.getters['classroom/role']
    },
  },
}
</script>

<style lang="scss">
.stream-controls {
  position: absolute;
  left: 50%;
  bottom: -38px;
  z-index: 10;
  transform: translateX(-50%);

  .toolbar-button-wrapper {
    width: 40px;
    height: 40px;

    button {
      svg {
        margin: 0 auto;
      }

      &:focus {
        outline: none;
      }
    }

    &-full-screen button {
      padding: 9px;
    }
  }

  .hover-btn-info {
    left: 50%;
    right: auto;
    top: auto;
    bottom: -36px;
    transform: translateX(-50%);

    &::after {
      left: 50%;
      top: -9px;
      right: auto;
      border: 5px solid transparent;
      border-bottom: 5px solid #444444;
      transform: translateX(-50%);
    }
  }

  .stream-controls-switch {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: -6px;
    padding: 10px 6px 3px;
    background: #fff;
    border: none;
    border-radius: 0 0 6px 6px;
    font-size: 13px;
    line-height: 1;
    outline: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);

    .toolbar-button-wrapper {
      width: 18px;
      height: 18px;

      &:not(:last-child) {
        margin-right: 4px;
      }
    }

    .toolbar-button-item {
      min-width: 18px !important;
      font-size: 13px !important;
      border-radius: 50% !important;
      overflow: hidden;

      &--selected {
        color: #fff;
        background: linear-gradient(
          126.15deg,
          var(--v-success-base) 0%,
          var(--v-primary-base) 102.93%
        ) !important;
      }
    }

    //.hover-btn-info {
    //  bottom: -40px;
    //}
  }
}

// Whereby-specific feature styles
.toolbar-button-icon {
  &.hand-raised {
    fill: #ffc107 !important; // Warning color for raised hand
  }

  &.chat-enabled {
    fill: #2196f3 !important; // Primary color for enabled chat
  }

  &.participants-enabled {
    fill: #2196f3 !important; // Primary color for enabled participants
  }
}
</style>
