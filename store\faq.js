export const state = () => ({
  teacherListItems: [],
})

export const mutations = {
  SET_TEACHER_LIST_ITEMS: (state, payload) => {
    state.teacherListItems = payload
  },
}

export const actions = {
  getHomePageFaqs() {
    const url = `${process.env.NUXT_ENV_API_URL}/get-publish-faq`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => data)
      .catch((e) => console.log(e))
  },
  getTeacherListPageFaqs({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/get-publish-faq-teacher-list`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => commit('SET_TEACHER_LIST_ITEMS', data))
  },
}
