<template>
  <user-setting-template
    v-if="item"
    :title="$t('background')"
    :submit-func="submitData"
  >
    <div class="mb-md-2">
      <v-row class="mb-2">
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-1">
              {{ $t('your_teaching_background') }}
            </div>
            <div class="input-wrap-label">
              {{ $t('describe_your_language_teaching_background') }}
            </div>
            <div>
              <v-textarea
                :value="item.teachingBackground"
                class="l-textarea"
                no-resize
                height="120"
                solo
                dense
                :counter="lengthLimit"
                :rules="rules.maxLength"
                @input="updateValue($event, 'teachingBackground')"
              ></v-textarea>
            </div>
          </div>
        </v-col>
      </v-row>
    </div>
    <div>
      <v-row>
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-1">
              {{ $t('your_general_background') }}
            </div>
            <div class="input-wrap-label">
              {{ $t('describe_any_aspects_of_your_background') }}
            </div>
            <div>
              <v-textarea
                :value="item.generalBackground"
                class="l-textarea"
                no-resize
                height="120"
                solo
                dense
                :counter="lengthLimit"
                :rules="rules.maxLength"
                @input="updateValue($event, 'generalBackground')"
              ></v-textarea>
            </div>
          </div>
        </v-col>
      </v-row>
    </div>
  </user-setting-template>
</template>

<script>
import UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'

const lengthLimit = 1000

export default {
  name: 'BackgroundInfo',
  components: { UserSettingTemplate },
  data() {
    return {
      lengthLimit,
      rules: {
        maxLength: [(v) => v === null || v?.length <= lengthLimit],
      },
    }
  },
  computed: {
    item() {
      return this.$store.state.settings.backgroundItem
    },
  },
  beforeCreate() {
    this.$store.dispatch('settings/getBackground')
  },
  methods: {
    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_BACKGROUND_ITEM', {
        [property]: value,
      })
    },
    submitData() {
      this.$store.dispatch('settings/updateBackground')
    },
  },
}
</script>
