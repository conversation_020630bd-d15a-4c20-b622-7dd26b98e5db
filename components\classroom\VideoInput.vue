<template>
  <v-form @submit.prevent="submit">
    <div class="input-wrap">
      <text-input
        ref="input"
        :value="inputUrl"
        type-class="border-gradient"
        height="44"
        :rules="[rules.required]"
        :placeholder="$t('enter_youtube_url')"
        @input="inputUrl = $event"
      ></text-input>
      <div v-if="!isValid" class="input-wrap-error v-text-field__details">
        <div class="v-messages theme--light error--text" role="alert">
          <div class="v-messages__wrapper">
            <div class="v-messages__message">
              {{ $t('invalid_url_or_video_id') }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="video-buttons-wrap">
      <v-btn color="primary" small type="submit">
        {{ $t('load_video') }}
      </v-btn>
    </div>
  </v-form>
</template>

<script>
import TextInput from '~/components/form/TextInput'

export default {
  name: 'VideoInput',
  components: { TextInput },
  data() {
    return {
      inputUrl: null,
      isValid: true,
      videoId: null,
      rules: {
        required: (v) => !!v || this.$t('field_required'),
      },
    }
  },
  computed: {
    role() {
      return this.$store.getters['classroom/role']
    },
    isVideoInputOpened() {
      return this.$store.state.classroom.isVideoInputOpened
    },
  },
  watch: {
    inputUrl(newValue, oldValue) {
      this.isValid = true

      let videoId

      if (newValue?.includes('v=')) {
        videoId = newValue.split('v=')[1]

        const ampersandPosition = videoId.indexOf('&')

        if (ampersandPosition !== -1) {
          videoId = videoId.substring(0, ampersandPosition)
        }
      }

      if (newValue.length && (!videoId || !videoId.match(/[^"&=\s?]{11}/))) {
        this.isValid = false
      }

      this.videoId = videoId
    },
  },
  mounted() {
    this.$refs.input.focus()
  },
  methods: {
    async submit() {
      if (!this.isValid || !this.videoId) return

      await this.$store.dispatch('classroom/createAsset', {
        type: 'video',
        videoId: this.videoId,
        index: this.$store.state.classroom.maxIndex + 1,
        shapes: [],
        owner: this.role,
      })

      this.$store.commit('classroom/closeVideoInput')
    },
  },
}
</script>

<style lang="scss">
.video-component,
.video-component * {
  cursor: auto !important;
}

.video-component {
  .v-card {
    padding: 70px 15px 20px;
  }

  .v-input {
    .v-input__slot {
      background-color: #fff;
    }
  }

  .input-wrap {
    position: relative;

    &-error {
      position: absolute;
      bottom: 8px;
      left: 0;
      padding-left: 16px;
    }
  }

  .video-buttons-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  button,
  button * {
    cursor: pointer !important;
  }
}
</style>
