<template>
  <lessons-page route="/user/lessons" :type="type"></lessons-page>
</template>

<script>
import { hashUserData } from '@/utils/hash'
import LessonsPage from '~/components/user-lessons/LessonsPage'

export default {
  name: 'UpcomingLessons',
  components: { LessonsPage },
  middleware: ['authenticated', 'confirmationPageAllowed'],
  async asyncData({ store, query }) {
    const type = 'upcoming'
    const searchQuery = query?.search

    await Promise.all([
      store.dispatch('lesson/getUpcomingLessons', {
        page: 1,
        perPage: process.env.NUXT_ENV_PER_PAGE,
        type,
        searchQuery,
      }),
      store.dispatch('lesson/getCalendarItems'),
    ])

    return { type }
  },
  head() {
    return {
      title: this.$t('user_upcoming_lessons_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('user_upcoming_lessons_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('user_upcoming_lessons_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('user_upcoming_lessons_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} user-lessons-page user-upcoming-lessons-page`,
      },
    }
  },
  watchQuery: true,
  async beforeMount() {
    this.$cookiz.remove('confirmation_page_allowed', {
      domain: process.env.NUXT_ENV_COOKIE_DOMAIN,
      path: '/',
    })

    // Ensure dataLayer is properly initialized
    window.dataLayer = window.dataLayer || []

    if (localStorage.getItem('event_data') !== null) {
      try {
        const eventData = JSON.parse(localStorage.getItem('event_data'))
        // Try to get user data from the API for the most up-to-date information
        let userData = null
        try {
          userData = await this.$store.dispatch('payments/fetchUserData')
        } catch (error) {
          console.error('Error fetching user data from API:', error)
        }

        // If API call fails, fall back to store state
        if (!userData || !userData.email) {
          userData = this.$store.state.user.item || {}
        }

        const tidioData = this.$store.state.user.tidioData || {}
        const userEmail = tidioData.email || ''
        const userName = `${userData.firstName || ''} ${
          userData.lastName || ''
        }`.trim()
        const hashedEmail = hashUserData(userEmail)
        const hashedName = hashUserData(userName)
        // Add hashed user data to the items array
        if (
          eventData.ecommerce &&
          eventData.ecommerce.items &&
          eventData.ecommerce.items.length > 0
        ) {
          eventData.ecommerce.items.forEach((item) => {
            if (!item.user_name) {
              item.user_name = hashedName
            }
            if (!item.email_id) {
              item.email_id = hashedEmail
            }
          })
        }

        // Push event to dataLayer with a slight delay to ensure GTM is ready
        setTimeout(() => {
          // First push a clear ecommerce object to prevent data leakage
          window.dataLayer.push({
            ecommerce: null,
          })

          // Then push the actual event
          window.dataLayer.push(eventData)
        }, 500)

        // Remove event data from localStorage
        localStorage.removeItem('event_data')
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Error pushing event to dataLayer:', error)
      }
    }
  },
}
</script>
