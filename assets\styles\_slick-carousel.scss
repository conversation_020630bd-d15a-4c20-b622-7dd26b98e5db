.slick-arrow {
  position: relative;
  width: 63px;
  height: 63px;
  border-radius: 50%;
  text-indent: -9999px;
  background-color: var(--v-greyDark-base);
  z-index: 99;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    width: 19px;
    height: 27px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
  }

  &.slick-prev::before {
    left: calc(50% - 2px);
    background-image: url('~assets/images/chevron.svg');
    transform: translate(-50%,-50%);
  }

  &.slick-next::before {
    left: calc(50% + 2px);
    background-image: url('~assets/images/chevron.svg');
    transform: translate(-50%,-50%) rotate(180deg);
  }

  &.slick-disabled {
    background-color: var(--v-greyLight-base);
    cursor: auto;
  }
}

.slick-dots {
  display: flex !important;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 35px;
  padding-left: 0 !important;
  list-style-type: none;

  @media only screen and (max-width: $xsm-and-down) {
    margin-top: 28px;
  }

  @media only screen and (max-width: $xxs-and-down) {
    margin-top: 18px;
  }

  li {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 8px 12px;

    @media only screen and (max-width: $xsm-and-down) {
      margin: 6px 8px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      margin: 3px 5px;
    }

    &.slick-active button {
      width: 24px;
      height: 24px;
      background-color: var(--v-success-base) !important;

      @media only screen and (max-width: $xsm-and-down) {
        width: 16px;
        height: 16px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        width: 12px;
        height: 12px;
      }
    }

    button {
      width: 18px;
      height: 18px;
      padding: 0;
      background-color: var(--v-darkLight-base) !important;
      text-indent: -9999px;
      border-radius: 50%;
      transition: all .2s;

      @media only screen and (max-width: $xsm-and-down) {
        width: 12px;
        height: 12px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        width: 8px;
        height: 8px;
      }
    }
  }
}
