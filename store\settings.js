const defaultCourseItem = () => ({
  id: null,
  name: '',
  length: 60,
  lessons: 10,
  price: '',
  shortDescription: '',
  introductionToCourse: '',
  courseStructure: '',
  youtube: '',
  image: null,
  languageId: null,
  isPublish: false,
})

export const state = () => ({
  basicInfoItem: null,
  languagesItem: null,
  summaryItem: null,
  backgroundItem: null,
  aboutMeItem: null,
  pricingTableItem: null,
  courseItems: [],
  preferenceItems: [],
  specialitiesItem: null,
  teachingQualificationItems: [],
  invoiceItem: null,
  notificationCalendarItem: null,
  lessonCountItems: [
    { id: 1, name: 1 },
    { id: 2, name: 5 },
    { id: 3, name: 10 },
    { id: 4, name: 20 },
  ],
  lessonLengthItems: [
    { id: 1, name: 30 },
    { id: 2, name: 60 },
    { id: 3, name: 90 },
    { id: 4, name: 120 },
  ],
  lessonPrices: [
    { id: null, length: 30, lessons: 1, price: '' },
    { id: null, length: 30, lessons: 5, price: '' },
    { id: null, length: 30, lessons: 10, price: '' },
    { id: null, length: 30, lessons: 20, price: '' },
    { id: null, length: 60, lessons: 1, price: '' },
    { id: null, length: 60, lessons: 5, price: '' },
    { id: null, length: 60, lessons: 10, price: '' },
    { id: null, length: 60, lessons: 20, price: '' },
    { id: null, length: 90, lessons: 1, price: '' },
    { id: null, length: 90, lessons: 5, price: '' },
    { id: null, length: 90, lessons: 10, price: '' },
    { id: null, length: 90, lessons: 20, price: '' },
    { id: null, length: 120, lessons: 1, price: '' },
    { id: null, length: 120, lessons: 5, price: '' },
    { id: null, length: 120, lessons: 10, price: '' },
    { id: null, length: 120, lessons: 20, price: '' },
  ],
  illustrationItems: [
    { id: 1, image: 'illustration-1', name: 'Illustration 1' },
    { id: 2, image: 'illustration-2', name: 'Illustration 2' },
    { id: 3, image: 'illustration-3', name: 'Illustration 3' },
    { id: 4, image: 'illustration-4', name: 'Illustration 4' },
    { id: 5, image: 'illustration-5', name: 'Illustration 5' },
    { id: 6, image: 'illustration-6', name: 'Illustration 6' },
    { id: 7, image: 'illustration-7', name: 'Illustration 7' },
    { id: 8, image: 'illustration-8', name: 'Illustration 8' },
    { id: 9, image: 'illustration-9', name: 'Illustration 9' },
    { id: 10, image: 'illustration-10', name: 'Illustration 10' },
    { id: 11, image: 'illustration-11', name: 'Illustration 11' },
    { id: 12, image: 'illustration-12', name: 'Illustration 12' },
    { id: 13, image: 'illustration-13', name: 'Illustration 13' },
    { id: 14, image: 'illustration-14', name: 'Illustration 14' },
    { id: 15, image: 'illustration-15', name: 'Illustration 15' },
    { id: 16, image: 'illustration-16', name: 'Illustration 16' },
    { id: 17, image: 'illustration-17', name: 'Illustration 17' },
    { id: 18, image: 'illustration-18', name: 'Illustration 18' },
    { id: 19, image: 'illustration-19', name: 'Illustration 19' },
    { id: 20, image: 'illustration-20', name: 'Illustration 20' },
    { id: 21, image: 'illustration-21', name: 'Illustration 21' },
    { id: 22, image: 'illustration-22', name: 'Illustration 22' },
  ],
})

export const mutations = {
  SET_BASIC_INFO_ITEM(state, payload) {
    state.basicInfoItem = payload
  },
  UPDATE_BASIC_INFO_ITEM(state, payload) {
    state.basicInfoItem = Object.assign({}, state.basicInfoItem, payload)
  },
  SET_LANGUAGES_ITEM(state, payload) {
    state.languagesItem = payload
  },
  UPDATE_LANGUAGES_ITEM(state, payload) {
    state.languagesItem = Object.assign({}, state.languagesItem, payload)
  },
  SET_SUMMARY_ITEM(state, payload) {
    state.summaryItem = payload
  },
  UPDATE_SUMMARY_ITEM(state, payload) {
    state.summaryItem = Object.assign({}, state.summaryItem, payload)
  },
  SET_BACKGROUND_ITEM(state, payload) {
    state.backgroundItem = payload
  },
  UPDATE_BACKGROUND_ITEM(state, payload) {
    state.backgroundItem = Object.assign({}, state.backgroundItem, payload)
  },
  SET_ABOUT_ME_ITEM(state, payload) {
    state.aboutMeItem = payload
  },
  UPDATE_ABOUT_ME_ITEM(state, payload) {
    state.aboutMeItem = Object.assign({}, state.aboutMeItem, payload)
  },
  SET_PRICING_TABLE_ITEM(state, payload) {
    state.pricingTableItem = payload
  },
  UPDATE_PRICING_TABLE_ITEM(state, payload) {
    state.pricingTableItem = Object.assign({}, state.pricingTableItem, payload)
  },
  UPDATE_LESSON_PRICES(state, payload) {
    state.lessonPrices = state.lessonPrices.map((item) => {
      const [existItem] = payload.filter(
        (el) => el.length === item.length && el.lessons === item.lessons
      )

      return existItem
        ? { ...item, ...existItem, price: existItem.price.toFixed(2) }
        : item
    })
  },
  UPDATE_LESSON_PRICE(state, { value, length, lessons }) {
    state.lessonPrices.forEach((item) => {
      if (item.length === length && item.lessons === lessons) {
        item.price = value
      }
    })
  },
  SET_COURSE_ITEMS(state, payload) {
    state.courseItems = payload
  },
  ADD_COURSE_ITEM(state, payload) {
    const item = { ...defaultCourseItem(), uid: payload }

    if (state.languagesItem?.languagesTaught.length) {
      item.languageId = state.languagesItem.languagesTaught[0].id
    }

    state.courseItems.push(item)
  },
  UPDATE_COURSE_ITEM(state, payload) {
    state.courseItems.forEach((item, i) => {
      if (
        (item.id && item.id === payload.id) ||
        (item.uid && item.uid === payload.uid)
      ) {
        state.courseItems.splice(i, 1, { ...item, ...payload })
      }
    })
  },
  UPDATE_COURSE_ITEM_ID(state, { id, uid }) {
    state.courseItems.forEach((item, i) => {
      if (item.uid && item.uid === uid) {
        delete item.uid

        state.courseItems.splice(i, 1, { ...item, id })
      }
    })
  },
  UPDATE_COURSE_ITEM_TITLE(state, { id, name }) {
    const course = state.courseItems.find((item) => item.id === id)

    if (course) course.name = name
  },
  REMOVE_COURSE_ITEM(state, payload) {
    state.courseItems = state.courseItems.filter(
      (item) =>
        (item.id && item.id !== payload.id) ||
        (item.uid && item.uid !== payload.uid)
    )
  },
  SET_PREFERENCE_ITEMS(state, payload) {
    state.preferenceItems = payload
  },
  UPDATE_TEACHING_PREFERENCE_ITEMS(state, payload) {
    const selectedIds = payload.map((item) => item.id)

    state.preferenceItems = state.preferenceItems.map((item) => ({
      ...item,
      isSelected: selectedIds.includes(item.id),
    }))
  },
  UPDATE_LEARNING_PREFERENCE_ITEMS(state, id) {
    state.preferenceItems = state.preferenceItems.map((item) => ({
      ...item,
      isSelected: id === item.id,
    }))
  },
  SET_SPECIALITIES_ITEM(state, payload) {
    state.specialitiesItem = payload
  },
  UPDATE_AVAILABLE_SPECIALIZATIONS(state, payload) {
    state.specialitiesItem.availableSpecializations = payload
  },
  UPDATE_TEACHER_SPECIALITIES(state, payload) {
    state.specialitiesItem.teacherSpecialities = payload.map((item, i) => ({
      ...item,
      priority: i + 1,
    }))
  },
  SET_TEACHING_QUALIFICATION_ITEMS(state, payload) {
    state.teachingQualificationItems = payload
  },
  ADD_TEACHING_QUALIFICATION_ITEM(state, payload) {
    state.teachingQualificationItems.push(payload)
  },
  REMOVE_TEACHING_QUALIFICATION_ITEM(state, payload) {
    state.teachingQualificationItems = state.teachingQualificationItems.filter(
      (item) =>
        (item.id && item.id !== payload.id) ||
        (item.uid && item.uid !== payload.uid)
    )
  },
  SET_INVOICE_ITEM(state, payload) {
    state.invoiceItem = payload
  },
  UPDATE_INVOICE_ITEM(state, payload) {
    state.invoiceItem = Object.assign({}, state.invoiceItem, payload)
  },
  SET_NOTIFICATION_CALENDAR_ITEM(state, payload) {
    state.notificationCalendarItem = payload
  },
  UPDATE_NOTIFICATION_CALENDAR_ITEM(state, payload) {
    state.notificationCalendarItem = Object.assign(
      {},
      state.notificationCalendarItem,
      payload
    )
  },
}

export const getters = {
  availableSpecializations: (state) =>
    state.specialitiesItem?.availableSpecializations,
  teacherSpecialities: (state) =>
    (state.specialitiesItem?.teacherSpecialities
      ? [...state.specialitiesItem.teacherSpecialities]
      : []
    ).sort((a, b) => a.priority - b.priority),
}

export const actions = {
  getBasicInfo({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/settings/basic-info`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_BASIC_INFO_ITEM', data)
      })
  },
  updateBasicInfo({ state, dispatch }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/settings/basic-info`
    const data = JSON.stringify({
      firstName: state.basicInfoItem.firstName,
      lastName: state.basicInfoItem.lastName,
      uiLanguage: state.basicInfoItem.uiLanguage,
      timezone: state.basicInfoItem.timezone,
      taxCountryId: state.basicInfoItem?.taxCountry?.id ?? null,
    })

    return this.$axios
      .put(url, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  updateAvatar({ commit, dispatch }, file) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/settings/basic-info-avatar`
    const formData = new FormData()

    formData.append('file', file)

    return this.$axios
      .post(url, formData, {
        header: { 'Content-Type': 'multipart/form-data' },
      })
      .then((response) => response.data)
      .then((data) => {
        commit('UPDATE_BASIC_INFO_ITEM', { avatars: data })
        commit(
          'user/UPDATE_USER',
          { userPicture: data.user_thumb_248x248 },
          { root: true }
        )
        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  getLanguages({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/settings/languages`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_LANGUAGES_ITEM', data)
      })
  },
  updateLanguages({ state, commit, dispatch }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/settings/languages`
    const data = JSON.stringify({
      languagesSpoken: Array.isArray(state.languagesItem.languagesSpoken)
        ? state.languagesItem.languagesSpoken.map((item) => item.id)
        : [],
      languagesTaught: Array.isArray(state.languagesItem.languagesTaught)
        ? state.languagesItem.languagesTaught.map((item) => item.id)
        : [],
      nativeLanguages: Array.isArray(state.languagesItem.nativeLanguages)
        ? state.languagesItem.nativeLanguages.map((item) => item.id)
        : [],
      languageToLearn: state.languagesItem.languageToLearn,
    })

    return this.$axios
      .put(url, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        const languageToLearn = state.languagesItem.languages?.find(
          (item) => item.id === state.languagesItem.languageToLearn
        )

        if (languageToLearn) {
          commit('user/UPDATE_USER', { languageToLearn }, { root: true })
        }

        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  getSummary({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/summary`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_SUMMARY_ITEM', data)
      })
  },
  updateSummary({ state, dispatch }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/summary`
    const data = JSON.stringify({
      shortSummary: state.summaryItem.shortSummary || '',
      longerSummary: state.summaryItem.longSummary || '',
      interestingFacts: state.summaryItem.factsAbout || '',
    })

    return this.$axios
      .put(url, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  getBackground({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/background`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_BACKGROUND_ITEM', data)
      })
  },
  updateBackground({ state, dispatch }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/background`
    const data = JSON.stringify({
      teachingBackground: state.backgroundItem.teachingBackground || '',
      generalBackground: state.backgroundItem.generalBackground || '',
    })

    return this.$axios
      .put(url, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  getAboutMe({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/about-me`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_ABOUT_ME_ITEM', data)
      })
  },
  updateAboutMe({ state, dispatch }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/about-me`
    const data = JSON.stringify({
      expects: state.aboutMeItem.whatToExpect || '',
      linkedin: state.aboutMeItem.linkedinUrl || '',
      youtube: state.aboutMeItem.youtubeUrl || '',
    })

    return this.$axios
      .put(url, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  uploadCV({ commit, dispatch }, file) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/about-me-cv`
    const formData = new FormData()

    formData.append('file', file)

    return this.$axios
      .post(url, formData, {
        header: { 'Content-Type': 'multipart/form-data' },
      })
      .then((response) => response.data)
      .then((data) => {
        if (data[0]) {
          commit('UPDATE_ABOUT_ME_ITEM', {
            cvUrl: data[0],
          })
        }

        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  getPricingTable({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/prcing-table`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_PRICING_TABLE_ITEM', data)
        commit('UPDATE_LESSON_PRICES', data.lessonPricing || [])
      })
  },
  updatePricingTable({ state, commit, dispatch }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/prcing-table`
    const data = JSON.stringify({
      isFreeTrialLesson: state.pricingTableItem.freeTrialLesson,
      isTrialLesson: state.pricingTableItem.trialLesson,
      priceTrialLesson: state.pricingTableItem.priceTrialLesson || 0,
      acceptNewStudents: state.pricingTableItem.acceptNewStudents,
      lessonPricing: state.lessonPrices
        .filter((item) => item.id || item.price)
        .map((item) => ({ ...item, price: item.price || null })),
    })

    return this.$axios
      .put(url, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        if (!state.pricingTableItem.trialLesson) {
          commit('UPDATE_PRICING_TABLE_ITEM', {
            priceTrialLesson: 0,
          })
        }

        commit('UPDATE_LESSON_PRICES', data)
        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  getCourses({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/courses`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_COURSE_ITEMS', data)
      })
  },
  addCourse({ state, commit, dispatch }, data) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/courses`
    const uid = data.uid
    const data_ = { ...data }

    delete data_.uid

    return this.$axios
      .post(url, JSON.stringify(data_), {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then((data) => {
        const { id } = data.data[0]

        dispatch('snackbar/success', null, { root: true })
        commit('UPDATE_COURSE_ITEM_ID', { id, uid })
        commit('UPDATE_COURSE_ITEM_TITLE', { id, name: data_.name })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  updateCourse({ state, commit, dispatch }, data) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/courses`
    const data_ = { ...data }

    delete data_.isConfirmed

    return this.$axios
      .put(url, JSON.stringify(data_), {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        dispatch('snackbar/success', null, { root: true })
        commit('UPDATE_COURSE_ITEM_TITLE', { id: data.id, name: data.name })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  removeCourse({ dispatch }, id) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/courses`

    return this.$axios
      .delete(url, {
        headers: {
          'Content-Type': 'application/json',
        },
        data: JSON.stringify({ id }),
      })
      .then(() => {
        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  getTeachingPreferences({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/preferenses`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_PREFERENCE_ITEMS', data)
      })
  },
  updateTeachingPreferences({ state, dispatch }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/preferenses`
    const data = JSON.stringify({
      preferences: state.preferenceItems
        .filter((item) => item.isSelected)
        .map((item) => item.id),
    })

    return this.$axios
      .put(url, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  getLearningPreferences({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/students/settings/learning-preferences`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_PREFERENCE_ITEMS', data)
      })
  },
  updateLearningPreferences({ state, dispatch }) {
    const url = `${process.env.NUXT_ENV_API_URL}/students/settings/learning-preferences`
    const [data] = state.preferenceItems.filter((item) => item.isSelected)

    return this.$axios
      .put(url, JSON.stringify(data), {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  getSpecialities({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/specialities`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_SPECIALITIES_ITEM', data)
      })
  },
  updateSpecialities({ getters, dispatch }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/specialities`
    const data = JSON.stringify({
      specialities: getters.teacherSpecialities.map((item) => ({
        id: item.id,
        priority: item.priority,
      })),
    })

    return this.$axios
      .put(url, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  getTeachingQualifications({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/qualifications`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_TEACHING_QUALIFICATION_ITEMS', data)
      })
  },
  addTeachingQualification({ dispatch }, data) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/qualifications`
    const formData = new FormData()

    formData.append('name', data.name)
    formData.append('file', data.file)

    return this.$axios
      .post(url, formData, {
        header: { 'Content-Type': 'multipart/form-data' },
      })
      .then((data) => {
        dispatch('snackbar/success', null, { root: true })

        return data.data
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  removeTeachingQualification({ dispatch }, id) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers/settings/qualifications`

    return this.$axios
      .delete(url, {
        headers: {
          'Content-Type': 'application/json',
        },
        data: JSON.stringify({ id }),
      })
      .then((data) => {
        dispatch('snackbar/success', null, { root: true })

        return data
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  getInvoice({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/students/settings/invoice-info`

    return this.$axios
      .get(url)
      .then((response) => response.data)
      .then((data) => {
        commit('SET_INVOICE_ITEM', data)
      })
  },
  updateInvoice({ state, dispatch }) {
    const url = `${process.env.NUXT_ENV_API_URL}/students/settings/invoice-info`
    const data = {
      ...state.invoiceItem,
      invoiceInfo: state.invoiceItem.text,
    }

    delete data.text

    return this.$axios
      .put(url, JSON.stringify(data), {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
  getNotificationCalendar({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/settings/notification-calendar`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_NOTIFICATION_CALENDAR_ITEM', data)
      })
  },
  updateNotificationCalendar({ state, dispatch }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/settings/notification-calendar`
    const data = {
      ...state.notificationCalendarItem,
      isEnabled: state.notificationCalendarItem.enabled,
    }

    if (!data.isEnabled) {
      data.email = ''
    }

    delete data.enabled

    return this.$axios
      .put(url, JSON.stringify(data), {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(() => {
        dispatch('snackbar/success', null, { root: true })
      })
      .catch((e) => {
        dispatch('snackbar/error', null, { root: true })

        console.info(e)
      })
  },
}
