<template>
  <section class="about">
    <v-container class="py-0">
      <v-row>
        <v-col class="col-12 py-0">
          <div class="section-head section-head--decorated">
            <h3
              class="section-head-title"
              style="color: #262626; -webkit-text-fill-color: #262626"
            >
              {{ $t('home_page.about_section_title') }}
            </h3>
          </div>
        </v-col>
      </v-row>
    </v-container>
    <div class="about-content">
      <v-container class="py-0">
        <v-row no-gutters>
          <v-col class="col-xl-10 offset-xl-1 about-wrap">
            <div class="about-bg d-none d-md-block">
              <v-img
                :src="require('~/assets/images/homepage/about-bg.png')"
                :options="{ rootMargin: '50%' }"
                contain
                height="100%"
              ></v-img>
            </div>
            <div class="about-bg d-md-none">
              <v-img
                :src="require('~/assets/images/homepage/about-m-bg.svg')"
                :options="{ rootMargin: '50%' }"
                contain
                height="100%"
              ></v-img>
            </div>
            <v-row>
              <v-col class="col-12 col-md-4 py-0">
                <div class="about-item about-item-i1">
                  <div class="about-item-image">
                    <v-img
                      :src="require('~/assets/images/homepage/about-1.svg')"
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <div class="about-item-title">
                    {{ $t('home_page.about_1_title') }}
                  </div>
                  <div class="about-item-text">
                    {{ $t('home_page.about_1_text') }}
                  </div>
                  <div class="about-item-more">
                    <nuxt-link
                      to="/teacher-listing/1/motivation,2;speciality,22"
                      class="link-more"
                    >
                      {{ $t('learn_more') }}
                    </nuxt-link>
                  </div>
                </div>
              </v-col>

              <v-col class="col-12 col-md-4 offset-md-4 py-0">
                <div class="about-item about-item-i2">
                  <div class="about-item-image">
                    <v-img
                      :src="require('~/assets/images/homepage/about-2.svg')"
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <div class="about-item-title">
                    {{ $t('home_page.about_2_title') }}
                  </div>
                  <div class="about-item-text">
                    {{ $t('home_page.about_2_text') }}
                  </div>
                  <div class="about-item-more">
                    <nuxt-link to="/education" class="link-more">
                      {{ $t('learn_more') }}
                    </nuxt-link>
                  </div>
                </div>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="order-md-2 col-12 col-md-4 offset-md-8 py-0">
                <div class="about-item about-item-i3">
                  <div class="about-item-image">
                    <v-img
                      :src="require('~/assets/images/homepage/about-3.svg')"
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <div class="about-item-title">
                    {{ $t('home_page.about_3_title') }}
                  </div>
                  <div class="about-item-text">
                    {{ $t('home_page.about_3_text') }}
                  </div>
                  <div class="about-item-more">
                    <nuxt-link
                      to="/teacher-listing/1/motivation,3"
                      class="link-more"
                    >
                      {{ $t('learn_more') }}
                    </nuxt-link>
                  </div>
                </div>
              </v-col>
              <v-col
                class="order-md-1 col-12 col-md-4 offset-md-1 offset-lg-4 py-0"
              >
                <div class="about-item about-item-i4">
                  <div class="about-item-image">
                    <v-img
                      :src="require('~/assets/images/homepage/about-4.svg')"
                      contain
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <div class="about-item-title">
                    {{ $t('home_page.about_4_title') }}
                  </div>
                  <div class="about-item-text">
                    {{ $t('home_page.about_4_text') }}
                  </div>
                  <div class="about-item-more">
                    <nuxt-link
                      to="/teacher-listing/1/motivation,1"
                      class="link-more"
                    >
                      {{ $t('learn_more') }}
                    </nuxt-link>
                  </div>
                </div>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="col-12 py-0">
                <div class="about-item about-item-i5">
                  <div class="about-item-image">
                    <v-img
                      :src="require('~/assets/images/homepage/about-5.svg')"
                      contain
                      class="type-1"
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                    <v-img
                      :src="require('~/assets/images/homepage/about-5-m.svg')"
                      contain
                      max-width="371"
                      class="type-2"
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <div class="about-item-title">
                    {{ $t('home_page.about_5_title') }}
                  </div>
                  <div class="about-item-text">
                    {{ $t('home_page.about_5_text') }}
                  </div>
                  <div class="about-item-more">
                    <nuxt-link to="/business" class="link-more">
                      {{ $t('learn_more') }}
                    </nuxt-link>
                  </div>
                </div>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </section>
</template>

<script>
export default {
  name: 'AboutSection',
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.about {
  padding-top: 80px;

  @media #{map-get($display-breakpoints, 'md-and-up')} {
    overflow: hidden;
  }

  .section-head {
    margin-bottom: 130px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin-bottom: 80px;
    }
  }

  &-wrap {
    position: relative;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding-bottom: 62px !important;
    }

    .about-bg {
      position: absolute;
      left: 58%;
      top: 25px;
      width: 90%;
      height: 100%;
      transform: translateX(-50%);

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        top: 12px;
        left: 68%;
        height: 104%;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        width: 100%;
        height: 100%;
        top: 135px;
        left: 0;
        transform: translateX(0);
      }
    }
  }

  &-item {
    position: relative;
    max-width: 330px;
    padding-bottom: 70px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: 380px;
      margin: 0 auto;
    }

    &-image {
      margin-bottom: 5px;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        display: flex;
        justify-content: center;
        margin-bottom: 18px;
        margin-left: auto;
        margin-right: auto;
      }
    }

    &-more {
      margin-top: 12px;
    }

    &-i1 {
      margin-top: 25px;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 0;
      }

      .about-item-image {
        max-width: 280px;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          max-width: 304px;
        }
      }
    }

    &-i2 {
      .about-item-image {
        max-width: 242px;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          max-width: 293px;
        }
      }
    }

    &-i3 {
      top: -240px;
      left: 30px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        top: 0;
        left: 20px;
        margin-top: -590px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        top: auto;
        left: auto;
        margin-top: 0;
      }

      .about-item-image {
        max-width: 248px;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          max-width: 280px;
        }
      }
    }

    &-i4 {
      top: -30px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        top: -140px;
        margin-top: 127px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        top: auto;
        margin-top: 0;
      }

      .about-item-image {
        max-width: 255px;
      }
    }

    &-i5 {
      top: -250px;
      max-width: 620px;
      padding-bottom: 0;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        top: -160px;
        max-width: 575px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        top: 15px;
        margin-left: 30px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        max-width: 371px;
        top: -50px;
        margin-left: 0;
        margin-top: 80px;
        padding-top: 130px;
      }

      .about-item {
        &-image {
          position: absolute;
          top: 50%;
          left: 0;
          width: 100%;
          transform: translateY(-50%);

          @media only screen and (max-width: $xxs-and-down) {
            top: 0;
            left: -30px;
            transform: none;

            .type-1 {
              display: none;
            }
          }

          .type-2 {
            display: none;

            @media only screen and (max-width: $xxs-and-down) {
              display: block;
            }
          }
        }

        &-title {
          font-size: 20px;

          @media only screen and (max-width: $xsm-and-down) {
            font-size: 18px;
          }
        }

        &-text {
          font-size: 16px;
        }

        &-title,
        &-text,
        &-more {
          padding-left: 200px;

          @media only screen and (max-width: $xsm-and-down) {
            padding-left: 32%;
          }

          @media only screen and (max-width: $xxs-and-down) {
            padding-left: 0;
            padding-right: 30px;
          }
        }
      }
    }
  }
}
</style>
