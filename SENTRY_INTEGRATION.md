# Sentry Integration - langu-frontend-7b

## Overview

Sentry has been successfully integrated into the langu-frontend-7b project for error tracking and performance monitoring. This document outlines the configuration, features, and usage.

## Configuration Details

### Project Information
- **Project Name**: `langu-frontend-7b`
- **DSN**: `https://<EMAIL>/4509759109529681`
- **Organization**: Sentry.io
- **Environment**: Configurable via `NUXT_ENV_SENTRY_ENVIRONMENT`

### Files Modified

1. **nuxt.config.js**
   - Updated Sentry configuration with new DSN
   - Added project-specific settings
   - Configured environment-based filtering
   - Added performance monitoring

2. **Environment Files**
   - `.env.sample`
   - `.env.dist2`
   - `.env.prod`
   - All updated with new DSN

3. **New Files Created**
   - `plugins/sentry.client.js` - Client-side Sentry configuration
   - `pages/sentry-test.vue` - Test page for Sentry integration
   - `SENTRY_INTEGRATION.md` - This documentation

4. **Modified Files**
   - `layouts/default.vue` - Added global error handling

## Features Enabled

### Error Tracking
- ✅ JavaScript errors and exceptions
- ✅ Unhandled promise rejections
- ✅ Vue.js component errors
- ✅ Network request failures
- ✅ Custom error messages

### Performance Monitoring
- ✅ Page load times
- ✅ Navigation performance
- ✅ Custom performance metrics
- ✅ Sample rate: 10% in production, 100% in development

### Context Information
- ✅ User information (when logged in)
- ✅ Browser and device information
- ✅ Page/route context
- ✅ Custom tags and metadata
- ✅ Breadcrumb navigation tracking

### Environment Configuration
- ✅ Development: Limited events, console logging
- ✅ Production: Full error tracking
- ✅ Staging: Configurable via environment variables

## Usage

### Automatic Error Capture
Sentry automatically captures:
- Uncaught JavaScript errors
- Unhandled promise rejections
- Vue component errors
- Network failures

### Manual Error Reporting
```javascript
// Capture a message
this.$sentry.captureMessage('Custom message', 'info')

// Capture an exception
try {
  // risky code
} catch (error) {
  this.$sentry.captureException(error)
}

// Add context
this.$sentry.setContext('custom', {
  key: 'value'
})

// Add breadcrumb
this.$sentry.addBreadcrumb({
  message: 'User clicked button',
  category: 'ui',
  level: 'info'
})
```

### Testing Integration

1. **Visit Test Page**: Navigate to `/sentry-test` in your browser
2. **Run Tests**: Use the test buttons to verify different event types
3. **Check Console**: In development, events are logged to console
4. **Verify Sentry**: Check your Sentry dashboard for events

### Development vs Production

**Development Mode:**
- Only critical errors and manual tests are sent
- All events are logged to console
- Full debugging information available

**Production Mode:**
- All errors are sent to Sentry
- Performance monitoring enabled
- User context automatically captured

## Environment Variables

```bash
# Required
NUXT_ENV_SENTRY_DSN='https://<EMAIL>/4509759109529681'

# Optional
NUXT_ENV_SENTRY_ENVIRONMENT='production' # or 'development', 'staging'
```

## Troubleshooting

### Common Issues

1. **Events not appearing in Sentry**
   - Check DSN configuration
   - Verify environment settings
   - Check browser console for errors

2. **Too many events in development**
   - Events are filtered in development mode
   - Only critical errors and manual tests are sent

3. **Missing user context**
   - User context is set when user is logged in
   - Check `$store.state.user.item` availability

### Debug Commands

```javascript
// Test Sentry in browser console
window.testSentry() // Available in development mode

// Check Sentry status
console.log(this.$sentry) // Should not be null/undefined

// Manual test
this.$sentry.captureMessage('Test from console', 'info')
```

## Monitoring and Alerts

### Recommended Alerts
- Error rate threshold: > 5% in 5 minutes
- New error types
- Performance degradation
- High volume of errors from specific users/pages

### Key Metrics to Monitor
- Error frequency and trends
- Performance metrics
- User impact
- Browser/device distribution of errors

## Security Considerations

- DSN is public but safe to expose
- Sensitive data is filtered before sending
- User PII is handled according to privacy settings
- Source maps can be uploaded for better debugging

## Next Steps

1. **Set up Alerts**: Configure Sentry alerts for critical errors
2. **Source Maps**: Upload source maps for better error tracking
3. **Performance Monitoring**: Fine-tune performance thresholds
4. **Custom Dashboards**: Create project-specific dashboards
5. **Integration**: Connect with issue tracking systems

## Support

For issues with Sentry integration:
1. Check this documentation
2. Visit the test page at `/sentry-test`
3. Check browser console for debug information
4. Review Sentry dashboard for event details
