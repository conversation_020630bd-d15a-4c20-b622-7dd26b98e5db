<template>
  <l-dialog
    v-if="dialog"
    :dialog="dialog"
    custom-class="lesson-evaluation-dialog"
    :hide-close-button="!closeButton"
    :persistent="persistent"
    max-width="725"
    @close-dialog="closeClickHandler"
  >
    <div class="mb-2 mb-md-3">
      <div class="lesson-evaluation-dialog-title text--gradient">
        {{
          $t(
            $vuetify.breakpoint.smAndUp
              ? 'please_evaluate_your_most_recent_lesson'
              : 'please_evaluate_your_lesson'
          )
        }}
      </div>
      <div class="lesson-evaluation-dialog-subtitle font-weight-medium">
        <span class="text-capitalize">{{ item.language.name }}</span>
        {{ $t('lesson_with') }} {{ item.teacherFullName }},
        {{ $dayjs(item.lessonTime).tz(timeZone).format('D MMMM') }},
        {{ $dayjs(item.lessonTime).tz(timeZone).format('LT') }}
      </div>
    </div>
    <div class="lesson-evaluation-dialog-body">
      <div class="evaluation-form-rating">
        <div class="item d-flex align-center body-1">
          <div class="item-title font-weight-medium">
            {{ $t('lesson_rating') }}:
          </div>
          <div>
            <form-rate
              :item="feedback"
              property="lessonRating"
              @update-value="updateValue"
            ></form-rate>
          </div>
        </div>
        <div class="item d-flex align-center body-1">
          <div class="item-title font-weight-medium">
            {{ $t('teacher_rating') }}:
          </div>
          <div>
            <form-rate
              :item="feedback"
              property="teacherRating"
              @update-value="updateValue"
            ></form-rate>
          </div>
        </div>
        <div class="item d-flex align-center body-1">
          <div class="item-title font-weight-medium">
            {{ $t('langu_rating') }}:
          </div>
          <div>
            <form-rate
              :item="feedback"
              property="languRating"
              @update-value="updateValue"
            ></form-rate>
          </div>
        </div>
      </div>
      <div v-if="item.tags && item.tags.length" class="evaluation-form-tags">
        <div class="item body-1">
          <div class="item-title font-weight-medium">
            {{ $t('my_teacher_was_choose_up_to_5') }}:
            <span class="greyDark--text font-weight-thin">
              ({{ $t('optional') }})
            </span>
          </div>
          <div class="item-value">
            <div class="tags-list d-flex flex-wrap">
              <l-chip
                v-for="tag in item.tags"
                :key="tag.id"
                :item-id="tag.id"
                class="mt-1"
                :label="tag.name"
                :close-btn="false"
                transparent
                clickable
                :selected-ids="feedback.tag"
                @click.native="clickTag(tag.id)"
              ></l-chip>
            </div>
          </div>
        </div>
      </div>
      <div class="evaluation-form-review">
        <div class="item body-1">
          <div class="item-title font-weight-medium">
            {{
              $t(
                $vuetify.breakpoint.smAndUp
                  ? 'help_other_students_choose_teacher_write_review_that_will_appear_on_their_profile'
                  : 'write_review_that_will_appear_on_teachers_profile'
              )
            }}:
            <span class="greyDark--text font-weight-thin">
              ({{ $t('optional') }})
            </span>
          </div>
          <div class="item-value mt-1">
            <v-textarea
              v-model="feedback.description"
              class="l-textarea"
              no-resize
              height="106"
              solo
              dense
              :counter="reviewCounter"
              :rules="reviewRules"
              :placeholder="
                $t(
                  'write_something_that_will_help_other_students_select_teacher'
                )
              "
            ></v-textarea>
          </div>
        </div>
      </div>
    </div>
    <div
      class="lesson-evaluation-dialog-actions pt-2 pt-sm-5 pt-lg-8 d-flex justify-space-between align-center"
    >
      <v-btn
        v-if="!closeButton"
        color="orange"
        text
        class="font-weight-medium"
        @click="skipRatingClickHandler"
      >
        {{ $t('skip_rating') }}
      </v-btn>
      <v-spacer></v-spacer>
      <v-btn
        color="primary"
        class="font-weight-medium"
        :disabled="isInvalid"
        @click="submitClickHandler"
      >
        {{ $t('submit') }}
      </v-btn>
    </div>
  </l-dialog>
</template>

<script>
import LDialog from '@/components/LDialog'
import FormRate from '@/components/form/FormRate'
import LChip from '@/components/LChip'

const PREVIEW_MIN_LENGTH = 1000
const feedbackDefault = () => ({
  lessonRating: null,
  teacherRating: null,
  languRating: null,
  tag: [],
  description: '',
  skip: 0,
})

export default {
  name: 'LessonEvaluationDialog',
  components: { LDialog, FormRate, LChip },
  props: {
    dialog: {
      type: Boolean,
      required: true,
    },
    item: {
      type: Object,
      required: true,
    },
    closeButton: {
      type: Boolean,
      default: false,
    },
    persistent: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      feedback: feedbackDefault(),
      reviewCounter: PREVIEW_MIN_LENGTH,
      reviewRules: [
        (v) =>
          v.length === 0 ||
          v.length <= PREVIEW_MIN_LENGTH ||
          this.$t('please_keep_your_review_less_than_count_characters', {
            value: PREVIEW_MIN_LENGTH,
          }),
      ],
    }
  },
  computed: {
    timeZone() {
      return this.$store.getters['user/timeZone']
    },
    isInvalid() {
      return (
        !this.feedback.lessonRating ||
        !this.feedback.teacherRating ||
        !this.feedback.languRating ||
        this.feedback.description?.length > PREVIEW_MIN_LENGTH
      )
    },
  },
  watch: {
    'item.lessonId'() {
      this.feedback.tag = this.item?.lastTagsWithThisTeacher?.map(
        (tag) => tag.id
      )

      if (this.item?.feedbackStarsModel) {
        this.feedback = {
          ...this.feedback,
          ...this.item.feedbackStarsModel,
        }
      }
    },
  },
  methods: {
    updateValue(property, value) {
      this.feedback[property] = value
    },
    clickTag(id) {
      if (this.feedback.tag.includes(id)) {
        this.feedback.tag = this.feedback.tag.filter((el) => el !== id)
      } else {
        if (this.feedback.tag.length === 5) {
          this.feedback.tag.pop()
        }

        this.feedback.tag.push(id)
      }
    },
    closeClickHandler() {
      this.$emit('close')
    },
    skipRatingClickHandler() {
      this.sendData({
        ...feedbackDefault(),
        lessonId: this.item.lessonId,
        skip: 1,
      })
    },
    submitClickHandler() {
      const lessonId = this.item.lessonId

      this.sendData({
        ...this.feedback,
        lessonId,
      }).then(() => {
        this.$store.dispatch('snackbar/success', {
          successMessage: 'feedback_sent',
        })
        this.$store.commit('lesson/UPDATE_LESSON', {
          lessonId,
          isSkippedFeedback: false,
        })
      })
    },
    sendData(data) {
      return new Promise((resolve) => {
        this.$store
          .dispatch('lesson/sendFeedback', data)
          .then(() => {
            this.closeClickHandler()

            this.feedback = feedbackDefault()

            resolve()
          })
          .catch(() => {
            this.$store.dispatch('snackbar/error', {
              errorMessage: 'sending_error',
            })
          })
      })
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.v-application .v-dialog.lesson-evaluation-dialog {
  & > .v-card {
    padding: 32px 30px 24px !important;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      padding: 22px 18px 24px !important;
    }

    @media only screen and (max-width: $xxs-and-down) {
      padding: 22px 10px 16px !important;
    }
  }
}

.lesson-evaluation-dialog {
  &-title {
    margin-bottom: 4px;
    font-size: 20px;
    font-weight: 700;
    line-height: 1.1;

    @media only screen and (max-width: $xsm-and-down) {
      text-align: center;
    }
  }

  &-subtitle {
    position: relative;
    padding-bottom: 6px;
    font-size: 16px;
    line-height: 1.2;

    @media only screen and (max-width: $xsm-and-down) {
      text-align: center;
    }

    &::after {
      left: 0;
      bottom: 0;
      width: 100%;
      height: 1px;
      content: '';
      position: absolute;
      background: linear-gradient(
        126.15deg,
        var(--v-success-base) 0%,
        var(--v-primary-base) 102.93%
      );
    }
  }

  &-body {
    .evaluation-form {
      &-rating {
        @media only screen and (max-width: $xsm-and-down) {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 8px 12px;
          background: linear-gradient(
            126.15deg,
            rgba(#80b622, 0.15) 0%,
            rgba(#3c87f8, 0.15) 102.93%
          );
          border-radius: 15px;
        }

        & > div {
          align-items: center;

          @media only screen and (max-width: $xxs-and-down) {
            width: 100%;
          }

          & > * {
            &:first-child {
              min-width: 160px;
              line-height: 1.1;

              @media only screen and (max-width: $xsm-and-down) {
                min-width: 120px;
              }

              @media only screen and (max-width: $xxs-and-down) {
                margin-right: auto;
                font-size: 14px !important;
              }
            }

            &:last-child {
              margin-left: 8px;
            }
          }

          &:not(:last-child) {
            margin-bottom: 10px;
          }
        }
      }

      &-tags {
        margin-top: 32px;

        @media only screen and (max-width: $xsm-and-down) {
          order: 3;
        }

        .tags-list {
          .chip {
            margin-right: 12px;
            cursor: pointer;

            @media only screen and (max-width: $xsm-and-down) {
              margin-right: 8px;
            }

            @media only screen and (max-width: $xxs-and-down) {
              margin-right: 4px;
            }
          }
        }
      }

      &-review {
        margin-top: 32px;

        @media only screen and (max-width: $xsm-and-down) {
          order: 2;
          margin-top: 18px;
        }
      }
    }

    .icon {
      border-radius: 50%;
      overflow: hidden;
    }
  }

  &-actions {
    @media only screen and (max-width: $xsm-and-down) {
      position: relative;
      margin-top: 32px;

      &::after {
        left: -18px;
        top: 0;
        width: calc(100% + 36px);
        height: 1px;
        content: '';
        position: absolute;
        background: linear-gradient(
          126.15deg,
          var(--v-success-base) 0%,
          var(--v-primary-base) 102.93%
        );
        opacity: 0.25;
      }
    }

    @media only screen and (max-width: $xxs-and-down) {
      &::after {
        left: -10px;
        width: calc(100% + 20px);
      }
    }
  }
}
</style>
