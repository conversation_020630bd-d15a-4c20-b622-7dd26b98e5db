<!--
  TOKBOX VIDEO PROVIDER (OPTION B) - COMMENTED OUT
  This component is no longer used. Whereby is now the default and only video provider.
  This file is kept for reference but should not be imported or used.
-->
<template>
  <div>
    <classroom-container :asset="file" :hover-enabled="false">
      <div
        id="video-window"
        :class="[
          'tokbox-component cursor-before-grab',
          { 'video-window--is-fullscreen': settings.isFullscreenEnabled },
        ]"
      >
        <div id="tokbox-local-stream-placeholder" class="local-stream"></div>
        <div id="tokbox-remote-stream-placeholder" class="remote-stream"></div>

        <video-actions
          :is-joined="isJoined"
          :settings="{ ...settings, isMuted, isVideoEnabled }"
          :is-screen-share-disabled="
            isRemoteScreenShareEnabled || screenSharingNotSupported
          "
          :type="file.asset.type"
          @switch-video-player="switchVideoPlayer"
          @toggle-video="toggleVideo"
          @toggle-audio="toggleAudio"
          @toggle-full-screen="toggleFullScreen"
          @toggle-screen-share="toggleScreenShare"
        ></video-actions>
      </div>
    </classroom-container>

    <classroom-container
      v-show="isLocalScreenShareEnabled || isRemoteScreenShareEnabled"
      :asset="screenShareAsset"
      :hover-enabled="false"
    >
      <div class="tokbox-component screenshare-component cursor-before-grab">
        <div class="user-name">
          <template v-if="isLocalScreenShareEnabled">
            {{ $t('my_screen') }}
          </template>
          <template v-if="isRemoteScreenShareEnabled">
            {{
              $t('classroom_user_screen', {
                username: zoomOtherAsset.asset.username,
              })
            }}
          </template>
        </div>
        <div
          id="tokbox-remote-screenshare-placeholder"
          class="remote-stream"
        ></div>

        <div
          id="tokbox-local-screenshare-placeholder"
          class="remote-stream"
        ></div>

        <div
          v-if="isLocalScreenShareEnabled"
          class="stream-controls stream-controls--screenshare"
        >
          <div class="video-window-buttons-wrap">
            <div class="stream-controls-wrapper cursor-auto">
              <div class="toolbar-button-wrapper">
                <button
                  class="toolbar-button-item cursor-pointer"
                  data-stream-stop-screen-share
                  type="button"
                  @click="toggleScreenShare"
                >
                  <svg
                    class="toolbar-button-icon"
                    width="38"
                    height="35"
                    viewBox="0 0 38 35"
                  >
                    <use
                      :xlink:href="`${require('~/assets/images/classroom/not_share.svg')}#not_share`"
                    ></use>
                  </svg>
                </button>
                <div class="hover-btn-info">
                  {{ $t('stop_screenshare') }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </classroom-container>
  </div>
</template>

<script>
import { switchFullScreen } from '~/helpers'

import ClassroomContainer from '~/components/classroom/ClassroomContainer'
import VideoActions from '~/components/classroom/video/VideoActions'

export default {
  name: 'Tokbox',
  components: { ClassroomContainer, VideoActions },
  props: {
    file: {
      type: Object,
      required: true,
    },
    screenShareAsset: {
      type: Object,
      required: true,
    },
    zoomOtherAsset: {
      type: Object,
      required: true,
    },
  },
  data: () => ({
    OT: {},
    isJoined: false,
    isLocalScreenShareEnabled: false,
    isRemoteScreenShareEnabled: false,
    settings: {
      isScreenShareEnabled: false,
      isFullscreenEnabled: false,
    },
    session: null,
    publisher: null,
    screenSharingPublisher: null,
    screenSharingNotSupported: false,
    isMediaChanging: false,
  }),
  computed: {
    apiKey() {
      return this.$store.getters['classroom/tokboxApiKey']
    },
    sessionId() {
      return this.$store.getters['classroom/tokboxSessionId']
    },
    token() {
      return this.$store.getters['classroom/tokboxToken']
    },
    role() {
      return this.$store.getters['classroom/role']
    },
    pubOptions() {
      return {
        insertMode: 'append',
        width: '100%',
        height: '100%',
        usePreviousDeviceSelection: true,
        name: this.$store.getters['classroom/userName'],
        style: {
          nameDisplayMode: 'off',
        },
        fitMode: 'contain',
        showControls: false,
        frameRate: 15,
      }
    },
    isVideoEnabled() {
      return this.file.asset?.settings?.[this.role]?.isVideoEnabled ?? true
    },
    isMuted() {
      return this.file.asset?.settings?.[this.role]?.isMuted ?? false
    },
    maxIndex() {
      return this.$store.state.classroom.maxIndex
    },
  },
  async beforeMount() {
    this.OT = await require('@opentok/client')

    await this.init()

    window.addEventListener('beforeunload', this.closeStream)
    window.addEventListener('pagehide', this.closeStream)
    document.addEventListener('fullscreenchange', this.fullscreenChangeHandler)
  },
  beforeDestroy() {
    document.removeEventListener(
      'fullscreenchange',
      this.fullscreenChangeHandler
    )
    this.closeStream()
  },
  methods: {
    init() {
      return new Promise((resolve) => {
        if (!this.OT.checkSystemRequirements()) {
          return alert(
            'Sorry. Your browser does not support our streaming protocols. Please try latest version of Google Chrome or Mozilla Firefox.'
          )
        }

        this.OT.checkScreenSharingCapability((response) => {
          if (!response.supported || response.extensionRegistered === false) {
            this.screenSharingNotSupported = true
          }
        })

        this.session = this.OT.initSession(this.apiKey, this.sessionId)

        this.session.connect(this.token, (err) => {
          if (err) {
            return this.errorHandler(err)
          }

          this.isJoined = true
          this.publisher = this.OT.initPublisher(
            'tokbox-local-stream-placeholder',
            {
              ...this.pubOptions,
              publishAudio: !this.isMuted,
              publishVideo: this.isVideoEnabled,
            }
          )
          this.session.publish(this.publisher, this.errorHandler)

          this.publisher.on({
            streamDestroyed: (event) => {
              event.preventDefault()

              if (this.session && this.publisher) {
                this.session.disconnect()
                this.session.unpublish(this.publisher)
                this.session.off()
              }
            },
          })

          this.session.on({
            streamCreated: (event) => {
              let el = 'tokbox-remote-stream-placeholder'

              if (event.stream.videoType === 'screen') {
                el = 'tokbox-remote-screenshare-placeholder'
                this.isRemoteScreenShareEnabled = true

                this.updateData(this.screenShareAsset.id, {
                  index: this.maxIndex + 1,
                })
              }

              this.session.subscribe(
                event.stream,
                el,
                {
                  insertMode: 'append',
                  fitMode: 'contain',
                  style: {
                    audioLevelDisplayMode: 'off',
                    buttonDisplayMode: 'off',
                    nameDisplayMode: 'off',
                  },
                  mirror: false,
                },
                (error) => {
                  if (error) {
                    alert(
                      `Sorry, we encountered an error trying to connect you to the remote stream. Please report to Langu Team. Error code: ${error.code}, details: ${error.message}`
                    )
                  }
                }
              )
            },
            streamPropertyChanged: (event) => {
              const asset = {
                settings: {
                  ...this.file.asset.settings,
                  [this.role]: {
                    isVideoEnabled: this.isVideoEnabled,
                    isMuted: this.isMuted,
                  },
                },
              }

              if (
                this.isMediaChanging &&
                (event.changedProperty === 'hasVideo' ||
                  event.changedProperty === 'hasAudio')
              ) {
                if (event.changedProperty === 'hasVideo') {
                  asset.settings[this.role].isVideoEnabled = event.newValue
                }

                if (event.changedProperty === 'hasAudio') {
                  asset.settings[this.role].isMuted = !event.newValue
                }

                this.updateData(this.file.id, asset)
              }
            },
            streamDestroyed: (event) => {
              if (event.stream.videoType === 'screen') {
                this.isRemoteScreenShareEnabled = false
              }
            },
          })
        })

        resolve()
      })
    },
    errorHandler(e) {
      if (e) {
        this.isJoined = false

        const message =
          e.code === 1500
            ? 'We are unable to access your video camera. Either the browser has not been given permissions or the camera is in use by another program.'
            : e.message

        alert(message)
      }
    },
    toggleVideo() {
      this.isMediaChanging = true
      this.publisher.publishVideo(!this.isVideoEnabled)
    },
    toggleAudio() {
      this.isMediaChanging = true
      this.publisher.publishAudio(this.isMuted)
    },
    toggleFullScreen() {
      this.settings.isFullscreenEnabled = !this.settings.isFullscreenEnabled

      switchFullScreen(this.settings.isFullscreenEnabled)
    },
    toggleScreenShare() {
      this.settings.isScreenShareEnabled = !this.settings.isScreenShareEnabled
      this.isLocalScreenShareEnabled = !this.isLocalScreenShareEnabled

      if (this.settings.isScreenShareEnabled) {
        this.screenSharingPublisher = this.OT.initPublisher(
          'tokbox-local-screenshare-placeholder',
          {
            insertMode: 'append',
            videoSource: 'screen',
            showControls: false,
            resolution: '640x480',
            frameRate: 7,
          }
        )

        this.session.publish(this.screenSharingPublisher, this.errorHandler)

        this.screenSharingPublisher.on({
          streamDestroyed: (event) => {
            event.preventDefault()

            this.screenSharingPublisher.disconnect()
            this.session.unpublish(this.screenSharingPublisher)
          },
        })
      } else {
        this.screenSharingPublisher.destroy()

        this.screenSharingPublisher = null
      }
    },
    async closeStream() {
      if (this.publisher) {
        await this.publisher.destroy()
      }

      if (this.screenSharingPublisher) {
        await this.screenSharingPublisher.destroy()
      }
    },
    switchVideoPlayer(type) {
      this.$store.dispatch('classroom/deleteAsset', this.file)
      this.$store.dispatch('classroom/createAsset', {
        ...this.file.asset,
        type,
      })
    },
    fullscreenChangeHandler() {
      if (!document.fullscreenElement) {
        this.settings.isFullscreenEnabled = false
      }
    },
    updateData(id, asset) {
      this.$store.commit('classroom/moveAsset', {
        id,
        asset,
      })
      this.$store
        .dispatch('classroom/moveAsset', {
          id,
          lessonId: this.file.lessonId,
          asset,
        })
        .finally(() => (this.isMediaChanging = false))
    },
  },
}
</script>
