<template>
  <div class="steps">
    <div class="steps-wrap">
      <div id="steps-helper" class="steps-helper">
        <div class="steps-list">
          <div
            v-for="step in steps"
            :id="`step-${step.id}`"
            :key="step.id"
            :class="[
              'step-item',
              { 'step-item--active': step.id === activeItemId },
              { 'step-item--link': itemLink.id === step.id },
            ]"
          >
            <div class="step-item-helper">
              <div class="step-item-number">
                <span>{{ step.id }}.</span>
              </div>
              <div class="step-item-title">{{ $t(step.title) }}</div>
              <!--              <div class="step-item-text">-->
              <!--                {{ $t(step.text) }}-->
              <!--              </div>-->
            </div>

            <nuxt-link
              v-if="itemLink.id === step.id"
              :to="itemLink.path"
            ></nuxt-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Steps',
  props: {
    activeItemId: {
      type: Number,
      default: 1,
    },
    itemLink: {
      type: Object,
      default: () => ({}),
    },
  },
  mounted() {
    const el = document.getElementById('steps-helper')
    const activeEl = document.getElementById(`step-${this.activeItemId}`)

    if (this.$vuetify.breakpoint.smAndDown && el && activeEl) {
      const x = activeEl.getBoundingClientRect().left - 15

      el.scrollTo({ left: x, behavior: 'smooth' })
    }
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.steps {
  @media #{map-get($display-breakpoints, 'md-and-up')} {
    margin-bottom: 20px;
    //padding-bottom: 28px;
    //border-bottom: 1px solid #eceef3;
  }

  &-wrap {
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: calc(100% + 30px);
      height: 50px;
      margin-left: -15px;
      margin-bottom: 24px;
      overflow: hidden;
    }

    //@media only screen and (max-width: $xsm-and-down) {
    //  height: 110px;
    //}
  }

  &-helper {
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding-bottom: 17px;
      overflow-x: auto;
      overflow-y: hidden;
      box-sizing: content-box;
    }
  }

  &-list {
    display: flex;
    justify-content: space-between;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      height: 50px;
    }

    //@media only screen and (max-width: $xsm-and-down) {
    //  height: 110px;
    //}
  }

  .step-item {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 312px;
    margin-right: 20px;
    padding: 10px 22px;
    letter-spacing: 0.3px;

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      height: 52px;

      &:last-child {
        margin-right: 0;
      }
    }

    @media #{map-get($display-breakpoints, 'md-only')} {
      margin-right: 10px;
      padding: 10px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      flex: 1 0 220px;
      width: 220px;
      margin: 0 0 0 15px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      flex: 1 0 280px;
      width: 280px;
      padding: 10px 5px 10px 12px;
    }

    a {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2;
    }

    &-helper {
      position: relative;
      padding-left: 48px;

      @media only screen and (max-width: $xsm-and-down) {
        padding-left: 45px;
      }
    }

    &-number {
      position: absolute;
      top: 50%;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 33px;
      height: 33px;
      padding: 0 0 3px 2px;
      border-radius: 50%;
      background: linear-gradient(
        126.15deg,
        rgba(128, 182, 34, 0.72) 0%,
        rgba(60, 135, 248, 0.72) 102.93%
      );
      transform: translateY(-50%);

      span {
        position: relative;
        display: inline-block;
        font-size: 16px;
        font-weight: 700;
      }
    }

    &-title {
      //margin-bottom: 5px;
      font-size: 14px;
      font-weight: 700;
      line-height: 1.28;
    }

    &-text {
      font-size: 12px;
      line-height: 1.5;
    }

    &:not(.step-item--active) {
      .step-item-number {
        span {
          color: var(--v-success-base);
          background: linear-gradient(
            -75deg,
            var(--v-success-base),
            var(--v-primary-base)
          );
          background: -moz-linear-gradient(
            -75deg,
            var(--v-success-base),
            var(--v-primary-base)
          );
          background: -webkit-linear-gradient(
            -75deg,
            var(--v-success-base),
            var(--v-primary-base)
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        &::before {
          content: '';
          position: absolute;
          top: 1px;
          left: 1px;
          width: calc(100% - 2px);
          height: calc(100% - 2px);
          background-color: var(--v-greyBg-base);
          border-radius: 50%;
        }
      }
    }

    &--active,
    &--link:hover {
      background-image: url('~assets/images/step-bg.svg');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-position: center center;

      .step-item-number {
        color: #fff;
      }
    }
  }
}
</style>
