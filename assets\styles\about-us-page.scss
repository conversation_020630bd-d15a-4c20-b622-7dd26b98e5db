@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.about-us-page {
  font-size: 16px;

  @media only screen and (max-width: $mac-13-and-down) {
    font-size: 14px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    font-size: 12px;
  }

  @media only screen and (max-width: $xxs-and-down) {
    font-size: 10px;
  }

  .heading {
    max-width: 1920px;
    height: 64vh;
    max-height: 758px;
    min-height: 450px;
    background-color: var(--v-dark-base);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top center;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      min-height: 350px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-height: 450px;
      min-height: 300px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      max-height: 300px;
      min-height: 250px;
    }

    &-text {
      padding: 0.75em 15px;
      font-size: 1.75em;
      line-height: 1.3;
      background-color: rgba(0, 0, 0, 0.6);
    }
  }

  &-intro {
    max-width: 786px;
    padding: 0 0 3.5rem;
    font-size: 1.75em;
    line-height: 1.25;
  }

  &-content {
    max-width: 1100px;
    padding: 3.5rem 0 2rem;
    background-color: #ffffff;

    .button-container {
      margin: 0 0 6em;
    }

    .boxes {
      margin: 4rem 0 2rem;

      @media only screen and (min-width: $xsm-and-up) {
        margin: 4rem 0;
      }

      @media only screen and (min-width: $xsm-and-up) {
        display: flex;
        justify-content: space-between;
        margin: 4em 0;
      }

      .box {
        padding: 0.5rem 1.25rem 1.5rem;
        font-size: 1.2em;
        line-height: 1.15;

        @media only screen and (min-width: $xsm-and-up) {
          width: 33.3333%;
          padding: 0 1.25rem;
        }

        .icon {
          height: 3em;
        }

        .headline {
          font-size: inherit !important;
        }
      }
    }

    .members {
      max-width: 950px;
      padding: 4.2em 0 6em;

      .member {
        .image {
          width: 200px;
          max-width: 200px;
          height: 200px;
          margin-bottom: 15px;

          @media #{map-get($display-breakpoints, 'sm-and-up')} {
            width: 250px;
            max-width: 250px;
            height: 250px;
            margin-bottom: 0;
          }

          .v-skeleton-loader__image {
            height: 100%;
          }
        }

        &-content {
          flex: 0 1 100%;
        }

        .top {
          font-size: 1.2em;
          color: inherit;
          line-height: 1.15;

          & > .name {
            @media (min-width: 768px) {
              height: 35px;
              font-size: 28px;
              overflow: hidden;
            }
          }

          & > .occupation {
            @media #{map-get($display-breakpoints, 'sm-and-up')} {
              height: 25px;
              font-size: 19px !important;
              overflow: hidden;
            }
          }

          @media #{map-get($display-breakpoints, 'sm-and-up')} {
            height: 60px;
            line-height: 1;
          }
        }

        .bio {
          width: 100%;
          padding: 25px 40px 25px 25px;
          line-height: 1.15;
          background-color: #eeeeee;

          @media only screen and (max-width: $xxs-and-down) {
            padding: 20px 15px;
          }

          &-inner {
            margin: 0;
            font-size: 1.2em;
          }

          @media #{map-get($display-breakpoints, 'sm-and-up')} {
            height: 190px;
            padding: 18px 40px 18px 10px;

            &-inner {
              max-height: 100%;
              font-size: 1.2em;
              overflow: hidden;
            }
          }
        }
      }
    }

    .section {
      @media only screen and (min-width: $xxs-and-up) {
        max-width: 800px;
      }

      .headline {
        position: relative;
        line-height: 1;
        font-size: 1em !important;
        color: inherit;
        text-transform: none;

        &::after {
          content: '';
          display: block;
          width: 100%;
          height: 4px;
          margin: 2px auto 0;
          background-color: var(--v-orange-base);
        }

        .regular {
          padding: 0 0.12em;
          font-size: 3.4em;
          font-weight: bold;
        }

        .super {
          position: absolute;
          top: 0;
          left: 1.6em;
          color: var(--v-orange-base);
          font-size: 1.105em;
          font-weight: bold;
          text-transform: uppercase;
        }
      }

      .text {
        margin: 2rem 0 0;
        font-size: 1.2em;
        line-height: 1.15;
      }

      &-team {
        &.en {
          .headline {
            .regular {
              padding-left: 0.75em;
            }

            .super {
              left: 0.3em;
            }
          }
        }

        &.pl {
          .headline {
            .regular {
              padding-left: 0.4em;
            }

            .super {
              left: 0.3em;
            }
          }
        }
      }

      &-teachers {
        &.pl {
          .headline {
            .super {
              left: 3.25em;
            }
          }
        }
      }

      &-methodology {
        &.pl {
          .headline {
            .regular {
              padding-left: 0.75em;
            }

            .super {
              left: 0.3em;
            }
          }
        }

        &.es {
          .headline {
            .super {
              left: 0;
              top: -0.2em;
            }
          }
        }
      }
    }
  }
}
