<template>
  <l-dialog
    :dialog="isShownTimePickerDialog"
    max-width="1030"
    custom-class="time-picker"
    persistent
    :fullscreen="$vuetify.breakpoint.smAndDown"
    v-on="$listeners"
  >
    <div class="time-picker-header text--gradient">
      {{ $t('schedule_your_lessons') }}:
    </div>
    <div class="wrap">
      <div class="time-picker-body">
        <div class="time-picker-selects mb-md-2">
          <div class="selects">
            <div class="selects-language">
              <div class="selects-lesson-value d-flex align-center">
                <div class="icon icon-flag elevation-2">
                  <v-img
                    v-if="language.isoCode"
                    :src="
                      require(`~/assets/images/flags/${language.isoCode}.svg`)
                    "
                    width="18"
                    height="18"
                  ></v-img>
                </div>
                {{ language.name }}
              </div>
            </div>

            <div v-if="course" class="selects-course">
              {{ course }}
            </div>

            <div class="selects-lesson">
              <div class="selects-lesson-value d-flex align-center">
                <div class="icon">
                  <v-img
                    :src="require('~/assets/images/clock-gradient.svg')"
                    width="18"
                    height="18"
                  ></v-img>
                </div>

                {{ $tc('lessons_count', countLessons) }}
              </div>
            </div>

            <div class="selects-duration">
              <div class="selects-lesson-value d-flex align-center">
                <div class="icon">
                  <v-img
                    :src="require('~/assets/images/clock-gradient.svg')"
                    width="18"
                    height="18"
                  ></v-img>
                </div>

                {{ $tc('minutes_count', lessonLength) }}
              </div>
            </div>
          </div>
        </div>
        <time-picker
          :username="username"
          :lesson-length="lessonLength"
          :quantity-lessons="countLessons"
          :current-time="currentTime"
          :is-shown-time-picker-dialog="isShownTimePickerDialog"
          v-on="$listeners"
        ></time-picker>
      </div>
    </div>

    <div class="time-picker-footer d-flex justify-md-end">
      <v-btn
        small
        color="primary"
        :disabled="isNotValid"
        @click="scheduleLessons"
      >
        {{ $t('schedule_now') }}
      </v-btn>
    </div>
  </l-dialog>
</template>

<script>
import LDialog from '~/components/LDialog'
import TimePicker from '~/components/TimePicker'

export default {
  name: 'TimePickerDialog',
  components: { LDialog, TimePicker },
  props: {
    isShownTimePickerDialog: {
      type: Boolean,
      required: true,
    },
    username: {
      type: String,
      required: true,
    },
    language: {
      type: Object,
      required: true,
    },
    currentTime: {
      type: Object,
      required: true,
    },
    lessonLength: {
      type: Number,
      required: true,
    },
    countLessons: {
      type: Number,
      required: true,
    },
    purchaseId: {
      type: Number,
      required: true,
    },
    course: {
      type: String,
      required: true,
    },
  },
  computed: {
    selectedSlots() {
      return this.$store.state.teacher_profile.selectedSlots || []
    },
    isNotValid() {
      return this.selectedSlots.length === 0
    },
  },
  methods: {
    scheduleLessons() {
      const slots = []

      if (this.selectedSlots.length) {
        this.selectedSlots.forEach((item) => {
          item.forEach((el) => slots.push(el.id))
        })
      }

      if (slots.length) {
        this.$store
          .dispatch('lesson/scheduleLesson', {
            purchaseId: this.purchaseId,
            slots,
          })
          .then(() => {
            this.$store.commit('user/DECREASE_UNSCHEDULED_LESSONS_NUMBER')
            this.$emit('close-dialog')
            this.$store.commit('teacher_profile/RESET_SELECTED_SLOTS')
            this.$router.push({ path: '/user/lessons' })
          })
          .catch((e) => {
            this.$store.dispatch('snackbar/error')

            console.info(e)
          })
      }
    },
  },
}
</script>

<style scoped lang="scss">
.selects-language {
  width: auto !important;
  min-width: 120px;
}
</style>
