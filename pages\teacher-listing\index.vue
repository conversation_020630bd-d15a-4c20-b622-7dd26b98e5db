<template>
  <v-row>
    <teacher-listing
      :teachers="teachers"
      :faq-items="faqItems"
    ></teacher-listing>

    <check-email-dialog
      :show-check-email-dialog="showCheckEmailDialog"
      @close="showCheckEmailDialog = false"
    ></check-email-dialog>

    <set-password-dialog
      :show-set-password-dialog="showSetPasswordDialog"
    ></set-password-dialog>
  </v-row>
</template>

<script>
import TeacherListing from '~/components/teacher-listing/TeacherListing'
import CheckEmailDialog from '~/components/CheckEmailDialog'
import SetPasswordDialog from '~/components/SetPasswordDialog'

export default {
  name: 'TeacherListingPage',
  components: { TeacherListing, CheckEmailDialog, SetPasswordDialog },
  async asyncData({ store, query }) {
    let filters

    await store
      .dispatch('teacher_filter/getFilters')
      .then((data) => (filters = data))
    await store.dispatch('teacher_filter/resetSorting')
    await store.dispatch('teacher_filter/resetFilters')

    const currentCurrency = store.state.currency.item
    const selectedSorting = store.state.teacher_filter.selectedSorting
    const showCheckEmailDialog = Boolean(+query.checkEmail)
    const searchQuery = query?.search

    let params = `currency,${currentCurrency.id};sortOption,${selectedSorting.id}`

    if (store.getters['user/isStudent']) {
      const userLanguage = store.getters['user/language']
      const presetLanguage = filters?.languages.find(
        (item) => item.id === userLanguage?.id
      )

      if (presetLanguage) {
        store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
          language: presetLanguage,
        })

        params += `;language,${presetLanguage.id}`
      }
    }

    await store.dispatch('teacher/getTeachers', {
      page: 1,
      perPage: process.env.NUXT_ENV_PER_PAGE,
      params,
      searchQuery,
    })

    if (searchQuery) {
      store.commit('teacher_filter/SET_SEARCH_QUERY', {
        searchQuery,
        updateActiveFilters: true,
      })
    }

    return { filters, showCheckEmailDialog }
  },
  head() {
    return {
      title: this.$t('teacher_listing_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('teacher_listing_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} teacher-listing-page`,
      },
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    teachers() {
      return this.$store.state.teacher.items
    },
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
    userLanguage() {
      return this.$store.getters['user/language']
    },
    showSetPasswordDialog() {
      return !!this.$store.state.auth.passwordTokenItem
    },
    currentCurrency() {
      return this.$store.state.currency.item
    },
    selectedSorting() {
      return this.$store.state.teacher_filter.selectedSorting
    },
    faqItems() {
      return this.$store.state.faq.teacherListItems
    },
  },
  watch: {
    async isUserLogged(newValue, oldValue) {
      if (newValue) {
        let params = `currency,${this.currentCurrency.id};sortOption,${this.selectedSorting.id}`

        if (this.isStudent && this.userLanguage) {
          await this.$store
            .dispatch('teacher_filter/getFilters')
            .then((data) => (this.filters = data))

          const userLanguage = this.$store.getters['user/language']
          const presetLanguage = this.filters?.languages.find(
            (item) => item.id === userLanguage?.id
          )

          if (presetLanguage) {
            this.$store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
              language: presetLanguage,
            })

            params += `;language,${presetLanguage.id}`
          }
        }

        await this.$store.dispatch('teacher/getTeachers', {
          page: 1,
          perPage: process.env.NUXT_ENV_PER_PAGE,
          params,
        })
        await this.$store.dispatch('teacher_filter/updateCurrencyActiveFilter')
      }
    },
  },
  watchQuery: true,
  async beforeMount() {
    if (!this.faqItems.length) {
      await this.$store.dispatch('loadingAllow', false)
      this.$store
        .dispatch('faq/getTeacherListPageFaqs')
        .finally(() => this.$store.dispatch('loadingAllow', true))
    }
  },
}
</script>

<style lang="scss">
@import './assets/styles/teacher-listing.scss';
</style>
