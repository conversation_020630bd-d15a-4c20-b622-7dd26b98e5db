<template>
  <div
    :class="[
      'score',
      {
        'score--large': large,
        'score--medium': medium,
        'score--stars-only': hideRating,
      },
    ]"
  >
    <span v-if="!hideRating">{{ value_.toFixed(1) }}</span>

    <div>
      <svg
        v-for="i in stars"
        :key="i"
        :width="width"
        :height="height"
        viewBox="0 0 12 12"
      >
        <use :xlink:href="iconFilledStar"></use>
      </svg>
      <svg v-if="isHasHalf" :width="width" :height="height" viewBox="0 0 12 12">
        <use :xlink:href="iconFilledHalfStar"></use>
      </svg>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StarRating',
  props: {
    value: {
      type: Number,
      required: true,
    },
    large: {
      type: Boolean,
      required: false,
    },
    medium: {
      type: Boolean,
      required: false,
    },
    hideRating: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      iconFilledStar: `${require('~/assets/images/icon-sprite.svg')}#filledStar`,
      iconFilledHalfStar: `${require('~/assets/images/icon-sprite.svg')}#filledHalfStar`,
    }
  },
  computed: {
    width() {
      return this.large ? 20 : this.medium ? 14 : 12
    },
    height() {
      return this.large ? 20 : this.medium ? 14 : 12
    },
    value_() {
      return Math.round(this.value * 10) / 10
    },
    isRoundToLess() {
      const rest = Math.round((this.value_ % 1) * 10)

      return rest <= 5 && rest !== 0
    },
    roundToLessHalf() {
      return this.isRoundToLess
        ? Math.floor(this.value_ * 2) / 2
        : Math.ceil(this.value_ * 2) / 2
    },
    stars() {
      return this.isRoundToLess
        ? Math.floor(this.roundToLessHalf)
        : Math.ceil(this.roundToLessHalf)
    },
    isHasHalf() {
      return (this.isRoundToLess && this.value_ !== 5) || this.value_ < 0.5
    },
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.score {
  display: flex;
  align-items: center;
  height: 18px;
  font-size: 12px;
  line-height: 0.8;
  font-weight: 700;
  letter-spacing: 0.1px;
  color: var(--v-orange-base);

  @media only screen and (max-width: $mac-13-and-down) {
    justify-content: flex-end;
  }

  & > div {
    width: 65px;
    display: flex;
    margin-left: 2px;

    @media only screen and (max-width: $mac-13-and-down) {
      width: auto;
    }
  }

  svg:not(:first-child) {
    margin-left: 1px;
  }
  &--medium {
    font-size: 14px;
  }
  &--large {
    font-size: 18px;

    @media only screen and (max-width: $mac-13-and-down) {
      font-size: 16px;
    }

    & > div {
      width: 112px;
      margin-left: 8px;

      @media only screen and (max-width: $mac-13-and-down) {
        width: 84px;
      }
    }

    svg {
      &:not(:first-child) {
        margin-left: 3px;

        @media only screen and (max-width: $mac-13-and-down) {
          margin-left: 1px;
        }
      }

      @media only screen and (max-width: $mac-13-and-down) {
        width: 16px !important;
        height: 16px !important;
      }
    }
  }

  &--stars-only {
    & > div {
      margin-left: 0;
    }

    &.score--large > div {
      margin-left: 0;
    }
  }
}
</style>
