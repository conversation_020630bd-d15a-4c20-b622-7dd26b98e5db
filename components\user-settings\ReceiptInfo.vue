<template>
  <user-setting-template
    :title="$t('receipt_information')"
    :submit-func="submitData"
  >
    <v-row>
      <v-col class="col-12">
        <div class="input-wrap-title body-1 font-weight-medium mb-2">
          {{
            $t(
              'provide_purchaser_information_you_would_like_to_appear_in_receipt'
            )
          }}:
        </div>
      </v-col>
      <v-col class="col-12 col-md-10">
        <div class="input-wrap">
          <div>
            <v-textarea
              :value="itemText"
              class="l-textarea"
              no-resize
              height="186"
              solo
              dense
              @input="updateValue($event, 'text')"
            ></v-textarea>
          </div>
        </div>
      </v-col>
    </v-row>
  </user-setting-template>
</template>

<script>
import UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'

export default {
  name: 'ReceiptInfo',
  components: { UserSettingTemplate },
  data() {
    return {
      isShownSpecialitiesDialog: false,
    }
  },
  computed: {
    itemText() {
      return this.$store.state.settings.invoiceItem?.text ?? ''
    },
  },
  beforeCreate() {
    this.$store.dispatch('settings/getInvoice')
  },
  methods: {
    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_INVOICE_ITEM', {
        [property]: value,
      })
    },
    submitData() {
      this.$store.dispatch('settings/updateInvoice')
    },
  },
}
</script>

<style scoped lang="scss">
.checkbox:not(:last-child) {
  margin-bottom: 20px;
}
</style>
