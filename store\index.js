const localeDomains = require('~~/locale-domains').default

export const state = () => ({
  isLoading: 0,
  loadingAllow: true,
  appTitle: process.env.NUXT_ENV_APP_TITLE,
  locale: 'en',
  locales: Object.keys(localeDomains).map((item) => ({
    ...localeDomains[item],
    code: item,
  })),
  subdomain: 'en',
  isShownLoginSidebar: false,
  isShownTeacherFilter: false,
  isPasswordLinkExpired: false,
  // authCookie: null,
  systemLanguage: null,
  systemCurrency: null,
  isCookieAllowed: true,
  allowedUtmTags: [
    'utm_source',
    'utm_campaign',
    'utm_ad',
    'utm_medium',
    'utm_term',
    'utm_content',
    'latest_utm_source',
  ],
  utmTags: null,
})

export const mutations = {
  SET_LOCALE(state, payload) {
    state.locale = payload
  },
  SET_SUBDOMAIN(state, payload) {
    state.subdomain = payload
  },
  SET_IS_LOGIN_SIDEBAR(state, payload) {
    state.isShownLoginSidebar = payload
  },
  SET_IS_TEACHER_FILTER(state, payload) {
    state.isShownTeacherFilter = payload
  },
  SET_IS_PASSWORD_LINK_EXPIRED(state, payload) {
    state.isPasswordLinkExpired = payload
  },
  // SET_AUTH_COOKIE(state, value) {
  //   state.authCookie = value
  // },
  SET_SYSTEM_LANGUAGE(state, value) {
    state.systemLanguage = value
  },
  SET_SYSTEM_CURRENCY(state, value) {
    state.systemCurrency = value
  },
  LOADING_START(state) {
    if (state.loadingAllow) {
      state.isLoading += 1
    }
  },
  LOADING_STOP(state) {
    state.loadingAllow = true

    if (state.isLoading > 0) {
      state.isLoading -= 1
    }
  },
  LOADING_ALLOW(state, value) {
    state.loadingAllow = value
  },
  SET_IS_COOKIE_ALLOWED(state, value) {
    state.isCookieAllowed = value
  },
  SET_UTM_TAGS(state, value) {
    state.utmTags = value
  },
}

export const getters = {
  isLoading: (state) => state.isLoading !== 0,
}

export const actions = {
  loadingStart({ commit }) {
    commit('LOADING_START')
  },
  loadingStop({ commit }) {
    commit('LOADING_STOP')
  },
  loadingAllow({ commit }, value) {
    commit('LOADING_ALLOW', value)
  },
  setCookieAllowed({ commit }, value) {
    commit('SET_IS_COOKIE_ALLOWED', !!value)

    if (value) {
      const date = new Date()
      date.setDate(date.getDate() + 9999)

      this.$cookiz.set('cookies_accepted', 1, {
        expires: date,
        domain: process.env.NUXT_ENV_COOKIE_DOMAIN,
      })
    }
  },
  async nuxtServerInit({ state, getters, commit, dispatch }, { app, req }) {
    // auth cookie
    // const L2SESSID = app.$cookiz.get('L2SESSID')
    const language = req.headers['accept-language']
      ?.split(';')[0]
      ?.split(',')[0]
      ?.toLowerCase()
    const cookiesAccepted = app.$cookiz.get('cookies_accepted')
    const host = req.headers.host
    const subdomain = host.split('.')[2] ? host.split('.')[0] : 'en'
    const currencies = getters['currency/items']

    if (!cookiesAccepted) {
      dispatch('setCookieAllowed', 0)
    }

    commit('SET_SUBDOMAIN', subdomain)
    // commit('SET_AUTH_COOKIE', L2SESSID)

    await dispatch('user/getUserStatus')

    let languageId = 1

    switch (language) {
      case 'en-gb':
      case 'en_gb':
        languageId = 2
        break
      case 'en-us':
      case 'en_us':
        languageId = 3
        break
      case 'pl':
      case 'pl-pl':
      case 'pl_pl':
        languageId = 4
        break
    }

    if (!getters['user/isUserLogged']) {
      commit('SET_LOCALE', subdomain)

      if (subdomain !== app.i18n.locale) {
        await app.i18n.setLocaleCookie(subdomain)
      }

      const presetCurrency = app.$cookiz.get('currency')
      const currency = presetCurrency || languageId

      commit(
        'SET_SYSTEM_CURRENCY',
        currencies.find((item) => item.id === languageId)
      )
      await dispatch('currency/setItem', {
        item: currencies.find((item) => item.id === currency),
      })
    } else {
      const userCurrencyId = state.user?.item?.currency?.id

      if (userCurrencyId) {
        commit(
          'SET_SYSTEM_CURRENCY',
          currencies.find((item) => item.id === userCurrencyId)
        )
      }
    }

    commit('SET_SYSTEM_LANGUAGE', language)
  },
}
