<template>
  <div
    v-ripple="clickable && !closeBtn ? { class: `text--gradient` } : false"
    :class="[
      'chip',
      {
        'chip--icon': icon,
        'chip--light text--gradient': light,
        'chip--close-btn': closeBtn,
        'chip--selected': isSelected,
        'chip--transparent': transparent,
        unselected: clickable || closeBtn,
      },
    ]"
  >
    <div>
      <div v-if="icon" class="chip-icon">
        <svg width="16" height="16" viewBox="0 0 16 16">
          <use
            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#${icon}`"
          ></use>
        </svg>
      </div>
      <span :class="['chip-label', { 'text--gradient': light || transparent }]">
        {{ label }}
      </span>

      <div
        v-if="closeBtn"
        class="chip-close"
        @click.prevent.stop="$emit('click:close', $event)"
      >
        <svg width="21" height="20" viewBox="0 0 21 20">
          <use :xlink:href="closeIcon"></use>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LChip',
  props: {
    itemId: {
      type: Number,
      default: 0,
    },
    light: {
      type: Boolean,
      default: false,
    },
    transparent: {
      type: Boolean,
      default: false,
    },
    clickable: {
      type: Boolean,
      default: false,
    },
    icon: {
      type: String,
      default: null,
    },
    label: {
      type: String,
      required: true,
    },
    closeBtn: {
      type: Boolean,
      default: true,
    },
    selectedIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      closeIcon: `${require('~/assets/images/icon-sprite.svg')}#close`,
    }
  },
  computed: {
    isSelected() {
      return this.selectedIds.includes(this.itemId)
    },
  },
}
</script>

<style lang="scss" scoped>
.chip {
  position: relative;
  display: inline-flex;
  align-items: center;
  min-height: 32px;
  color: #fff;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 0.3px;
  border-radius: 16px;
  border: 1px solid #314869;
  transition: border-color 0.3s;

  &::before {
    border-radius: inherit;
  }

  & > div {
    position: relative;
    padding: 4px 12px;
  }

  &:not(.chip--transparent):not(.unselected) {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        480.93deg,
        var(--v-success-base) 0%,
        var(--v-primary-base) 102.93%
      );
      opacity: 0.2;
      box-shadow: 0px 3px 16px -5px rgba(0, 0, 0, 0.73);
      border: none !important;
    }
  }

  &-label {
    position: relative;
    z-index: 2;
    -webkit-text-fill-color: -webkit-linear-gradient(
      -75deg,
      var(--v-success-base),
      var(--v-primary-base)
    );
  }

  &-icon,
  &-close {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }

  &-icon {
    position: absolute;
    left: 12px;
  }

  &-close {
    right: 10px;
    width: 20px;
    height: 20px;
    cursor: pointer;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &--icon {
    display: flex;
    align-items: center;

    & > div {
      padding-left: 36px;
    }
  }

  &--light {
    font-weight: 600;
    border: 1px solid #b9d5eb;

    &::before {
      background: linear-gradient(
        126.15deg,
        rgba(128, 182, 34, 0.2) 0%,
        rgba(60, 135, 248, 0.2) 102.93%
      );
      opacity: 1;
    }
  }

  &--close-btn {
    & > div {
      padding-right: 40px;
    }
  }

  &--transparent {
    border: 1px solid #b1cffc;
  }

  &--selected {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        126.15deg,
        var(--v-success-base) 0%,
        var(--v-primary-base) 102.93%
      );
      opacity: 0.2;
    }
  }

  &.unselected:not(.chip--close-btn):not(.chip--selected) {
    &::before {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      width: calc(100% + 2px);
      height: calc(100% + 2px);
      border-style: none;
      padding: 1px;
      background: linear-gradient(
        126.15deg,
        var(--v-success-base) 0%,
        var(--v-primary-base) 102.93%
      );
      -webkit-mask: linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: destination-out;
      mask-composite: exclude;
      transform: none;
      transition: opacity 0.3s;
      opacity: 1 !important;
    }

    &:hover {
      border-color: transparent !important;

      &::before {
        opacity: 1 !important;
      }
    }
  }
}
</style>
