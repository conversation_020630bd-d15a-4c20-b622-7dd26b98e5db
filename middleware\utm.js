// middleware/utm.js
export default function ({ route }) {
  console.log(process.client, 'client')
  //   if (process.client) {
  //     console.log('hererere')
  //     const allowedUtmTags = [
  //       'utm_source',
  //       'utm_campaign',
  //       'utm_ad',
  //       'utm_medium',
  //       'utm_term',
  //       'utm_content',
  //       'latest_utm_source',
  //     ]
  //     const utm = {}

  //     console.log(route.query, 'route.query')
  //     allowedUtmTags.forEach((param) => {
  //       if (route.query[param]) {
  //         utm[param] = route.query[param]
  //       }
  //     })

  //     if (Object.keys(utm).length > 0) {
  //       const storedUTM = JSON.parse(localStorage.getItem('utm') || '{}')
  //       const updatedUTM = { ...storedUTM, ...utm }
  //       console.log(updatedUTM, 'updatedUTM')
  //       localStorage.setItem('utm', JSON.stringify(updatedUTM))
  //     }
  //   }
}
