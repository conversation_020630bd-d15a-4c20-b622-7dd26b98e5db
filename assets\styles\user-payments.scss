@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media screen and (max-width: 768px) {
  .mobile-only {
    display: block;
    margin: 10px auto;
  }

  .desktop-only {
    display: none;
  }
}

.user-payments {
  --sidebar-width: 330px;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    --sidebar-width: 325px;
  }

  &-mobile-summary {
    display: none;
    
    @media screen and (max-width: 768px) {
      display: block;
      background: #2D2D2D;
      border-radius: 12px;
      padding: 24px;
      margin: 10px auto;
      color: #fff;

      .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .amount-info {
          .amount {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
          }
        }

        .payout-button {
          .v-btn {
            background: linear-gradient(to right, #95ce32, #3C87F8) !important;
            border-radius: 20px;
            height: 40px;
            padding: 0 24px;
            text-transform: none;
            font-weight: 500;
            color: white;
          }
        }
      }
    }
  }

  &-wrap {
    max-width: 1360px;
    padding-bottom: 25px;
  }

  &-header {
    @media only screen and (min-width: $mac-13-and-up) {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    @media only screen and (max-width: $mac-13-and-down) {
      flex-wrap: wrap;

      & > div {
        width: 100%;
      }
    }
  }

  &-title {
    position: relative;

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      margin-right: 24px;
    }

    @media only screen and (max-width: $mac-13-and-down) {
      margin-right: 0;
    }

    h1 {
      white-space: nowrap;
      font-size: 24px;
      line-height: 1.333;

      @media only screen and (max-width: $xxs-and-down) {
        font-size: 20px;
      }
    }
  }

  &-controls {
    justify-content: space-between;
    align-items: center;
    flex-grow: 1;

    @media only screen and (max-width: $mac-13-and-down) {
      margin-top: 18px;
    }

    @media only screen and (min-width: $mac-13-and-up) {
      max-width: 970px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: 100%;
      flex-wrap: wrap;
    }
  }
  
  &-search {
    width: 100%;

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      min-width: 240px;
      flex-basis: 380px;
    }
  }

  &-nav {
    min-width: 400px;
    margin-left: 18px;
    padding: 4px;
    background-color: #fff;
    box-shadow: 0 4px 14px rgba(217, 225, 236, 0.47);
    border-radius: 16px;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      min-width: 350px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: 100%;
      min-width: auto;
      margin: 12px 0 0;
    }

    & > a:not(:last-child) {
      margin-right: 4px;
    }

    .v-btn {
      &.nav-btn {
        flex-grow: 1;
        border-radius: 14px;
        background-color: transparent !important;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          width: 50%;
          min-width: 70px !important;
          text-align: center;
        }

        @media only screen and (max-width: $xsm-and-down) {
          font-size: 13px !important;
          font-weight: 400 !important;
        }
      }

      &::before {
        background-color: transparent;
      }

      &:not(.v-btn--active) {
        color: var(--v-greyDark-base);
      }

      &--active,
      &--active:hover {
        &::before {
          background: linear-gradient(
              126.15deg,
              rgba(128, 182, 34, 0.16) 0%,
              rgba(60, 135, 248, 0.16) 102.93%
          );
          opacity: 1;
        }
      }
    }
  }

  &-body {
    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      display: flex;
    }
  }

  &-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: calc(100% - var(--sidebar-width));

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: 100%;
    }

    .payment-day-group {
      margin-bottom: 32px;
    }

    .payment-date {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 16px;
      color: var(--v-dark-base);
    }

    .payment-time {
      font-size: 14px;
      font-weight: 400;
      margin-bottom: 8px;
      color: var(--v-greyDark-base);
    }

    .payment-items {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 14px rgba(217, 225, 236, 0.47);
      overflow: hidden;
    }

    .payment-item {

      &:last-child {
        border-bottom: none;
      }

      .payment-header {
        margin-bottom: 8px;
      }

      .payment-user {
        font-size: 16px;
        font-weight: 500;
        color: var(--v-dark-base);
      }

      .payment-details {
        margin-bottom: 8px;
      }

      .payment-description {
        font-size: 14px;
        color: var(--v-greyDark-base);
        margin-bottom: 4px;
      }

      .payment-meta {
        font-size: 12px;
        color: var(--v-greyLight-base);

        span {
          display: inline-block;
          margin-right: 12px;
        }
      }

      .payment-value {
        font-size: 16px;
        font-weight: 500;
        color: var(--v-dark-base);
        text-align: right;
      }
    }

    .payout-section {
      margin-top: 32px;

      .available-payout,
      .scheduled-value {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 14px rgba(217, 225, 236, 0.47);
        padding: 16px;
        margin-bottom: 16px;

        h3 {
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 12px;
        }
      }

      .available-payout {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .payout-btn {
          border-radius: 8px;
          text-transform: none;
          font-weight: 500;
        }
      }
    }
  }

  &-sidebar {
    width: var(--sidebar-width);
    height: 100%;
    background-color: #2D2D2D;
    border-radius: 12px;
    padding: 24px;
    color: #fff;
    margin-left: 24px;
    max-height: 270px;
    min-height: 250px;
    @media screen and (max-width: 768px) {
      display: none;
    }

    .available-amount {
      margin-bottom: 20px;

      .amount {
        font-size: 24px;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .label {
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        margin-bottom: 16px;
      }

      .v-btn {
        background: linear-gradient(to right, #95ce32, #3C87F8) !important;
        border-radius: 20px;
        height: 40px;
        width: 159px;
        text-transform: none;
        font-weight: 500;
        color: white;
      }
    }

    .scheduled-amount {
      .amount {
        font-size: 24px;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .label {
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
      }
    }
  }
}





