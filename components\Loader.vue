<template>
  <div
    v-if="isLoading"
    :class="['ajax-loader', { 'ajax-loader--absolute': absolute }]"
  >
    <!-- <clip-loader :loading="true" color="#333" size="60px"></clip-loader> -->
  </div>
</template>

<script>
// import ClipLoader from 'vue-spinner/src/ClipLoader.vue'

export default {
  name: 'Loader',
  components: {
    // ClipLoader,
  },
  props: {
    isLoading: {
      type: Boolean,
      required: true,
    },
    absolute: {
      type: Boolean,
      default: false,
    },
  },
}
</script>

<style lang="scss">
.ajax-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  opacity: 0.75;
  z-index: 9999999;

  &--absolute {
    position: absolute;
  }
}

.v-clip {
  position: absolute;
  top: calc(50% - 30px);
  left: calc(50% - 30px);
}
</style>
