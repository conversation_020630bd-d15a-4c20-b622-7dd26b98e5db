<template>
  <div @drop.prevent="drop" @dragleave.prevent="dragleave">
    <div v-show="isDragging" class="popup-load-files-drop-wrap">
      <div class="drop-area--wrapper">
        <img
          :src="require('~/assets/images/classroom/dropfiles.svg')"
          class="drop-area--wrapper__dropbox-img"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script>
import UploadFiles from '~/mixins/UploadFiles'

export default {
  name: 'DropFileArea',
  mixins: [UploadFiles],
  props: {
    viewportWidth: {
      type: Number,
      required: true,
    },
  },
  computed: {
    isDragging() {
      return this.$store.state.classroom.isDragging
    },
  },
  mounted() {
    document.addEventListener('dragover', this.dragover)
  },
  beforeDestroy() {
    document.removeEventListener('dragover', this.dragover)
  },
  methods: {
    dragover(e) {
      e.preventDefault()

      this.$store.commit('classroom/isDraggingTrigger', true)
    },
    dragleave() {
      this.$store.commit('classroom/isDraggingTrigger', false)
    },
    drop(e) {
      this.$store.commit('classroom/isDraggingTrigger', false)
      this.uploadFiles(e.dataTransfer.files)
    },
  },
}
</script>

<style scoped lang="scss">
.popup-load-files-drop-wrap {
  position: relative !important;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh !important;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 999999;
  pointer-events: none;

  .drop-area--wrapper {
    padding: 30px;
  }

  .drop-area--wrapper__dropbox-img {
    width: 100%;
    height: auto;
  }
}
</style>
