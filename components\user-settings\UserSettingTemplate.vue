<template>
  <v-form
    ref="form"
    :value="formValid"
    @validate="validate"
    @submit.prevent="submit"
    @input="formValid = $event"
  >
    <div class="user-settings-panel">
      <div class="panel">
        <div
          v-if="$vuetify.breakpoint.smAndUp"
          class="panel-head d-none d-sm-block"
        >
          <div class="panel-head-title font-weight-medium mb-2 mb-sm-3 mb-md-5">
            {{ title }}
          </div>
        </div>
        <div class="panel-body">
          <slot></slot>
        </div>
        <div
          v-if="!hideFooter"
          class="panel-footer d-flex justify-center justify-sm-end"
        >
          <v-btn
            color="primary"
            class="font-weight-medium"
            type="submit"
            :disabled="!valid"
          >
            <svg class="mr-1" width="18" height="18" viewBox="0 0 18 18">
              <use
                :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#save-icon`"
              ></use>
            </svg>
            {{ $t('save_changes') }}
          </v-btn>
        </div>
      </div>
    </div>
  </v-form>
</template>

<script>
export default {
  name: 'UserSettingTemplate',
  props: {
    title: {
      type: String,
      required: true,
    },
    hideFooter: {
      type: Boolean,
      default: false,
    },
    customValid: {
      type: Boolean,
      default: true,
    },
    submitFunc: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      formValid: true,
    }
  },
  computed: {
    valid() {
      return this.formValid && this.customValid
    },
  },
  mounted() {
    this.validate()
  },
  methods: {
    validate() {
      this.$refs.form.validate()
    },
    submit() {
      if (!this.valid) return

      this.submitFunc()
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.user-settings-panel {
  padding: 44px;
  border-radius: 20px;
  background-color: #fff;
  box-shadow: 0 8px 17px rgba(17, 46, 90, 0.1);

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    padding: 24px;
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    padding: 0;
    box-shadow: none;
  }

  .row {
    margin: 0 -14px !important;
  }

  .col {
    padding: 0 14px !important;
  }

  .panel {
    color: var(--v-greyDark-base);

    &-head {
      &-title {
        font-size: 24px;
        line-height: 1.333;
        color: var(--v-darkLight-base);

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          font-size: 20px;
        }
      }
    }

    &-body {
      .chips {
        & > div {
          margin-top: 6px;
        }
      }

      .price-input
        .v-text-field--outlined.v-input--dense.v-text-field--outlined
        > .v-input__control
        > .v-input__slot {
        min-height: 32px !important;
      }

      .theme--light.v-data-table
        > .v-data-table__wrapper
        > table
        > thead
        > tr
        > th {
        color: var(--v-dark-base);
      }

      .theme--light.v-data-table
        > .v-data-table__wrapper
        > table
        > thead
        > tr:last-child
        > th,
      .theme--light.v-data-table
        > .v-data-table__wrapper
        > table
        > tbody
        > tr:not(:last-child)
        > td:not(.v-data-table__mobile-row),
      .theme--light.v-data-table
        > .v-data-table__wrapper
        > table
        > tbody
        > tr:not(:last-child)
        > th:not(.v-data-table__mobile-row) {
        border: none !important;
      }

      .theme--light.v-data-table
        > .v-data-table__wrapper
        > table
        > tbody
        > tr:hover:not(.v-data-table__expanded__content):not(.v-data-table__empty-wrapper) {
        background: none;
      }

      .v-data-table > .v-data-table__wrapper > table > tbody > tr > td,
      .v-data-table > .v-data-table__wrapper > table > thead > tr > td,
      .v-data-table > .v-data-table__wrapper > table > tfoot > tr > td {
        height: 38px;
        color: var(--v-greyDark-base);
      }
    }

    &-footer {
      margin-top: 115px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-top: 56px;
      }

      .v-btn {
        letter-spacing: 0.1px;

        @media only screen and (max-width: $xxs-and-down) {
          width: 100% !important;
        }
      }
    }
  }

  .l-checkbox .v-input--selection-controls__input {
    width: 16px;
    height: 16px;
    margin-top: 2px;
  }
}
</style>
