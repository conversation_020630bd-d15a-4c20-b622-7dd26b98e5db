<template>
  <div>
    <div class="prices-title mb-2">
      {{ $t('price_per_lesson') }}
    </div>

    <div v-if="trialPackage.lessons" class="prices-trial">
      {{
        $t('trial_lesson_minute', {
          value: trialPackage.length,
        })
      }}:
      <span>
        <template v-if="trialPackage.isFreeTrialLesson">
          {{ $t('free') }}
        </template>
        <template v-else>
          {{ currentCurrencySymbol }}{{ trialPackage.price.toFixed(2) }}
        </template>
      </span>
    </div>

    <div class="prices-lesson-length">
      <div class="prices-lesson-length-title">
        <svg width="15" height="15" viewBox="0 0 15 15">
          <use
            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#clock`"
          ></use>
        </svg>
        {{ $t('lesson_length') }}:
      </div>
      <div class="prices-lesson-length-content">
        <v-row no-gutters>
          <v-col class="col-12">
            <v-radio-group v-model="selectedLessonLength" hide-details>
              <v-row no-gutters>
                <v-col
                  v-for="item in lessonLengthPackages"
                  :key="item.id"
                  :class="['col-6', { 'col-12': item.isTrial }]"
                >
                  <div class="radiobutton">
                    <v-radio
                      class="l-radio-button l-radio-button--type-2 l-radio-button--active-gradient"
                      color="success"
                      :ripple="false"
                      :value="item"
                    >
                      <template #label>
                        <div>
                          <template v-if="item.isTrial">
                            {{
                              `${$t('trial')} - ${$tc(
                                'minutes_count',
                                item.length
                              )}`
                            }}
                          </template>
                          <template v-else>
                            {{ $tc('minutes_count', item.length) }}
                          </template>
                        </div>
                      </template>
                    </v-radio>
                  </div>
                </v-col>
              </v-row>
            </v-radio-group>
          </v-col>
        </v-row>
      </div>
    </div>
    <div
      v-if="!isSelectedTrial && packages && packages.length"
      class="prices-lesson-price"
    >
      <div class="prices-lesson-length-title">
        <svg width="15" height="15" viewBox="0 0 15 15">
          <use
            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#clock`"
          ></use>
        </svg>
        {{ $t('number_of_lessons') }}:
      </div>
      <v-radio-group v-model="selectedCourse" hide-details>
        <div
          v-for="item in packages"
          :key="item.id"
          class="d-flex justify-space-between"
        >
          <div>
            <div class="radiobutton">
              <v-radio
                class="l-radio-button l-radio-button--type-2 l-radio-button--active-gradient"
                color="success"
                :ripple="false"
                :value="item"
              >
                <template #label>
                  <div>
                    {{ $tc('lessons_count', item.lessons) }}
                  </div>
                </template>
              </v-radio>
            </div>
          </div>
          <div>{{ currentCurrencySymbol }}{{ item.price.toFixed(2) }}</div>
        </div>
      </v-radio-group>
    </div>

    <template v-if="!hasFreeSlots">
      <div class="prices-attention-message caption mt-2">
        {{ $t('teacher_has_no_availability_right_now') }}
      </div>
    </template>
    <template v-else>
      <div
        v-if="!acceptNewStudents && !studentHasLessonsWithTeacher"
        class="prices-attention-message caption mt-2"
      >
        {{ $t('teacher_is_very_busy_right_now') }}
      </div>
    </template>

    <div class="prices-buttons">
      <template
        v-if="
          !hasFreeSlots || (!acceptNewStudents && !studentHasLessonsWithTeacher)
        "
      >
        <template v-if="languagesTaught.length">
          <find-more-teachers-button
            v-for="(lt, idx) in languagesTaught"
            :key="idx"
            :language="lt"
            :class="{ 'mt-2': idx === 1 }"
          ></find-more-teachers-button>
        </template>
      </template>
      <template v-else>
        <v-btn
          class="order font-weight-medium"
          width="100%"
          color="primary"
          @click="$emit('schedule-lessons')"
        >
          <template v-if="trialPackage.lessons && isSelectedTrial">
            {{ $t('book_trial_lesson') }}:&#160;
            <template v-if="trialPackage.isFreeTrialLesson">
              {{ $t('free') }}
            </template>
            <template v-else>
              {{ currentCurrencySymbol }}{{ trialPackage.price.toFixed(2) }}
            </template>
          </template>
          <template v-else>
            <template v-if="selectedCourse.lessons === 1">
              {{ $t('book_lesson') }}:
            </template>
            <template v-else> {{ $t('purchase_package') }}: </template>
            &#160;{{ currentCurrencySymbol }}{{ totalPrice }}
          </template>
        </v-btn>
      </template>

      <v-btn
        class="gradient font-weight-medium mt-2"
        width="100%"
        @click="$emit('send-message')"
      >
        <div class="text--gradient">
          {{ $t('send_me_message') }}
        </div>
      </v-btn>
    </div>
  </div>
</template>

<script>
import FindMoreTeachersButton from '~/components/teacher-profile/FindMoreTeachersButton'

export default {
  name: 'PricePerLesson',
  components: { FindMoreTeachersButton },
  props: {
    hasFreeSlots: {
      type: Boolean,
      required: true,
    },
    acceptNewStudents: {
      type: Boolean,
      required: true,
    },
    studentHasLessonsWithTeacher: {
      type: Boolean,
      required: true,
    },
    languagesTaught: {
      type: Array,
      required: true,
    },
  },
  computed: {
    trialPackage() {
      return this.$store.getters['teacher_profile/trialPackage']
    },
    isSelectedTrial() {
      return this.$store.state.teacher_profile.isSelectedTrial
    },
    lessonLengthPackages() {
      return this.$store.getters['teacher_profile/lessonLengthPackages'].filter(
        (item) => !item.isCourse
      )
    },
    packages() {
      return this.$store.getters['teacher_profile/packages'].filter(
        (item) => !item.isCourse
      )
    },
    selectedLessonLength: {
      get() {
        return this.$store.state.teacher_profile.selectedLessonLength
      },
      set(value) {
        this.$store.dispatch('teacher_profile/setSelectedLessonLength', value)
      },
    },
    selectedCourse: {
      get() {
        return this.$store.state.teacher_profile.selectedCourse
      },
      set(value) {
        this.$store.commit('teacher_profile/SET_SELECTED_COURSE', value || {})
      },
    },
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
    totalPrice() {
      return this.$store.getters['teacher_profile/totalPrice']
    },
  },
}
</script>

<style scoped lang="scss">
.prices {
  &-title {
    font-size: 20px;
    font-weight: 700;
    line-height: 1.3;
  }

  &-trial {
    letter-spacing: 0.57px;

    span {
      display: inline-block;
      margin-left: 5px;
      color: var(--v-success-base);
      font-weight: 700;
      letter-spacing: 0.052px;
    }
  }

  &-lesson-length {
    margin-top: 20px;

    &-title {
      position: relative;
      padding-left: 23px;
      font-weight: 700;
      font-size: 14px;
      letter-spacing: 0.57561px;
      margin-bottom: 8px;

      svg {
        position: absolute;
        left: 0;
        top: 4px;
      }
    }
  }

  &-lesson-price {
    margin-top: 16px;
    font-size: 14px;
    font-weight: 500;

    & > div > div:last-child {
      font-weight: 600;
    }
  }

  &-lesson-price,
  &-lesson-length {
    .v-input--selection-controls {
      padding-top: 0 !important;
    }

    .radiobutton {
      margin-bottom: 12px;
    }
  }

  &-attention-message {
    padding: 8px;
    color: #969696;
    background: linear-gradient(
      122.42deg,
      rgba(214, 123, 127, 0.04) 0%,
      rgba(249, 193, 118, 0.04) 100%
    );
    border-radius: 8px;
    border: 1px solid #e69c7b;
  }

  &-info-message {
    color: #969696;
  }

  &-buttons {
    margin-top: 20px;

    .v-btn.order {
      letter-spacing: 0.1px;
    }
  }
}
</style>
