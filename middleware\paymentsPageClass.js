export default function ({ store, route, app }) {
  // This middleware will be applied to all payment pages
  // It adds the body class needed for the background color
  if (process.client) {
    // Add the class to the body element
    document.body.classList.add(`${app.i18n.locale}`, 'user-payments-page')

    // Clean up when navigating away
    return () => {
      if (document.body.classList.contains('user-payments-page')) {
        document.body.classList.remove('user-payments-page')
      }
    }
  }
}
