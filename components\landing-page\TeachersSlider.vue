<template>
  <div
    v-if="data.length"
    :class="['lp-teachers-slider', { 'lp-teachers-slider--dark': dark }]"
  >
    <client-only>
      <VueSlickCarousel v-bind="sliderSettings">
        <div v-for="(item, index) in data" :key="index">
          <div class="slider">
            <div class="slider-card">
              <nuxt-link
                :to="{ path: `/teacher/${item.username}` }"
                target="_blank"
              >
                <v-img
                  class="slider-card__image"
                  :src="getSrcAvatar(item.picture)"
                  position="50% 30%"
                ></v-img>
              </nuxt-link>
              <div
                v-if="item.languagesTaught.length"
                class="flags-area d-flex justify-end"
              >
                <div
                  v-for="languageTaught in item.languagesTaught"
                  :key="languageTaught.isoCode"
                  class="flags-item ma-1 elevation-2 rounded overflow-hidden"
                >
                  <v-img
                    :src="
                      require(`~/assets/images/flags/${languageTaught.isoCode}.svg`)
                    "
                    width="40"
                    height="30"
                    contain
                    :options="{ rootMargin: '50%' }"
                  ></v-img>
                </div>
              </div>
              <div class="slider-card__content">
                <div class="d-flex justify-space-between align-center">
                  <nuxt-link
                    :to="{ path: `/teacher/${item.username}` }"
                    target="_blank"
                    class="slider-card__content-name text-uppercase"
                  >
                    {{ item.firstName }} {{ item.lastName }}
                  </nuxt-link>
                  <div class="slider-card__content-star">
                    <star-rating
                      class="mr-1 mr-sm-2 mr-md-0"
                      :value="item.averageRatings"
                    ></star-rating>
                    <p class="mb-0">
                      ({{ $tc('review', item.countFeedbacks) }})
                    </p>
                  </div>
                </div>

                <div class="slider-card__content-text">
                  {{ item.shortSummary }}
                </div>

                <table>
                  <tr>
                    <td
                      :class="[
                        'slider-card__content-tlabel',
                        { 'dark--text': !dark },
                      ]"
                    >
                      {{ $t('teaches') }}:
                    </td>
                    <td
                      :class="[
                        'slider-card__content-ttext',
                        { 'dark--text': !dark },
                      ]"
                    >
                      {{ item.languagesTaught.map((i) => i.name).join(', ') }}
                    </td>
                  </tr>
                  <tr>
                    <td
                      :class="[
                        'slider-card__content-tlabel',
                        { 'dark--text': !dark },
                      ]"
                    >
                      {{ $t('specialities') }}:
                    </td>
                    <td
                      :class="[
                        'slider-card__content-ttext',
                        { 'dark--text': !dark },
                      ]"
                    >
                      {{ item.specialities }}
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
        </div>
      </VueSlickCarousel>
    </client-only>
  </div>
</template>

<script>
import VueSlickCarousel from 'vue-slick-carousel'
import 'vue-slick-carousel/dist/vue-slick-carousel.css'
import StarRating from '~/components/StarRating'

export default {
  name: 'TeachersSlider',
  components: { VueSlickCarousel, StarRating },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    dark: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      slider: null,
      sliderSettings: {
        dots: false,
        focusOnSelect: true,
        infinite: true,
        speed: 800,
        slidesToShow: 1,
        slidesToScroll: 1,
        responsive: [
          {
            breakpoint: 900,
            settings: {
              arrows: false,
              dots: true,
            },
          },
          {
            breakpoint: 850,
            settings: {
              arrows: false,
              dots: true,
              centerMode: true,
              centerPadding: '100px',
            },
          },
          {
            breakpoint: 639,
            settings: {
              arrows: false,
              dots: true,
              centerMode: true,
              centerPadding: '45px',
            },
          },
          {
            breakpoint: 479,
            settings: {
              arrows: false,
              dots: true,
              centerMode: true,
              centerPadding: '30px',
            },
          },
        ],
      },
    }
  },
  methods: {
    getSrcAvatar(image, defaultImage = 'avatar.png') {
      return image || require(`~/assets/images/homepage/${defaultImage}`)
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.lp-teachers-slider {
  @media (max-width: 850px) {
    .slick-slide {
      padding: 0 15px;
      transition: all 0.5s ease-in;

      @media only screen and (max-width: $xsm-and-down) {
        padding: 0;
      }

      &:not(.slick-center) {
        transform: translate3d(0, 0, 0) scale(0.8) !important;
        opacity: 0.7;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          transform: translate3d(0, 0, 0) scale(0.9) !important;
          opacity: 0.6;
        }
      }
    }
  }

  .slider {
    padding: 15px 30px;
    margin-top: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .slider-card {
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
    position: relative;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &__image {
      width: 100%;
      height: 430px;
      max-height: 330px;
    }

    &__img-point {
      position: absolute;
      top: 20px;
      right: 20px;
    }

    &__content {
      color: white;
      padding: 20px;
      background: linear-gradient(
        180.39deg,
        rgba(171, 19, 92, 0.8) -80.41%,
        rgba(247, 173, 72, 0.8) 86.01%
      );

      &-star {
        color: #fff;

        p {
          font-size: 14px;
        }

        #text {
          display: inline;
        }
      }

      &-name {
        font-size: 20px;
        font-weight: bold;
        line-height: 1.3;
        color: #fff !important;
        text-decoration: none;
      }

      &-text {
        font-size: 18px;
        line-height: 20px;
        font-weight: 300;
        margin: 15px 0;

        @media only screen and (max-width: $xsm-and-down) {
          font-size: 16px;
        }
      }

      &-tlabel {
        color: white;
        font-size: 18px;

        @media only screen and (max-width: $xsm-and-down) {
          font-size: 16px;
        }

        @media only screen and (max-width: $xxs-and-down) {
          font-size: 14px;
        }
      }

      &-ttext {
        color: white;
        font-size: 18px;
        font-weight: 300;
        padding-left: 10px;

        @media only screen and (max-width: $xsm-and-down) {
          font-size: 16px;
        }

        @media only screen and (max-width: $xxs-and-down) {
          font-size: 14px;
        }
      }
    }

    td {
      padding-top: 10px;
      vertical-align: baseline;
    }
  }

  .slider-card .flags-area {
    position: absolute;
    right: 0;
    text-align: right;
    top: 0;
    width: 100%;
  }

  .slider-card .flag-icon {
    display: inline-block;
    font-size: 30px;
    margin: 10px;
  }

  @media (max-width: 1439px) {
    .slider {
      display: flex;
      justify-content: center;
      padding: 15px;

      &-card {
        max-width: 480px;
        margin: 0 auto;

        &__content-text {
          line-height: 20px;
        }
      }
    }
  }

  @media (max-width: 1170px) {
    .slider-card {
      max-width: 400px;

      &__image {
        width: inherit;
        background-position: 0 15%;
      }
    }
  }

  @media (max-width: 900px) {
    .slider-card {
      max-width: 480px;
    }
  }

  @media only screen and (max-width: $xsm-and-down) {
    .slider {
      flex-direction: column;
      padding: 0;
    }
  }

  @media (max-width: 480px) {
    .slider-card__image {
      background-position: center center;
    }
  }

  .slick-arrow {
    position: absolute;
    top: 50%;
    background-color: #000;
    transform: translateY(-50%);

    &.slick-next {
      right: 30px;
    }

    &.slick-prev {
      left: 30px;
    }
  }

  .slick-dots {
    margin-top: 10px !important;
  }

  &--dark {
    .slider-card {
      &__content {
        color: white;
        background: var(--v-darkLight-base);

        &-star {
          color: var(--v-orangeLight-base);
        }
      }
    }
  }
}
</style>
