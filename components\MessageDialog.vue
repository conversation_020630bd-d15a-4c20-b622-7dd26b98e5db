<template>
  <l-dialog
    :dialog="isShownMessageDialog"
    max-width="725"
    custom-class="message-dialog"
    persistent
    :fullscreen="$vuetify.breakpoint.xsOnly"
    v-on="$listeners"
  >
    <v-form @submit.prevent="submitHandler">
      <div class="message-dialog-header text--gradient">
        {{
          $t(
            $vuetify.breakpoint.xsOnly
              ? 'ask_question'
              : 'questions_ask_teacher_directly'
          )
        }}
      </div>
      <div
        :class="[
          'message-dialog-body pt-0 pt-sm-3',
          {
            'l-scroll l-scroll--grey l-scroll--large':
              $vuetify.breakpoint.xsOnly,
          },
        ]"
      >
        <v-row no-gutters>
          <v-col class="col-12 col-sm-5 col-md-6">
            <div class="message-dialog-text pr-0 pr-sm-2">
              <div v-show="$vuetify.breakpoint.xsOnly">
                <span class="text-capitalize">{{ $t('teacher') }}</span
                >: {{ recipientName }}<br />
                <br />
              </div>
              <div v-html="$t('message_dialog_text', { recipientName })"></div>
            </div>
          </v-col>
          <v-col class="col-12 col-sm-7 col-md-6 mt-3 mt-sm-0">
            <editor
              :value="newMessage"
              :limit="6000"
              @update="newMessage = $event"
              @validation="isMessageValid = $event"
            ></editor>
          </v-col>
        </v-row>
      </div>
      <div
        class="message-dialog-footer d-flex justify-space-between align-center"
      >
        <div class="prev-button body-1" @click="$emit('close-dialog')">
          <svg class="mr-1" width="17" height="12" viewBox="0 0 17 12">
            <use
              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#arrow-prev`"
            ></use></svg
          >{{ $t('go_back') }}
        </div>
        <div>
          <v-btn small color="primary" type="submit">
            {{ $t('send_message') }}
          </v-btn>
        </div>
      </div>
    </v-form>
  </l-dialog>
</template>

<script>
import LDialog from '~/components/LDialog'
import Editor from '~/components/form/Editor'

export default {
  name: 'MessageDialog',
  components: { LDialog, Editor },
  props: {
    recipientId: {
      type: Number,
      required: true,
    },
    recipientName: {
      type: String,
      required: true,
    },
    isShownMessageDialog: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      newMessage: '',
      isMessageValid: false,
    }
  },
  beforeDestroy() {
    this.newMessage = ''
    this.isMessageValid = false
  },
  methods: {
    submitHandler() {
      if (this.isMessageValid) {
        this.$store
          .dispatch('message/sendNewMessage', {
            recipientId: this.recipientId,
            message: this.newMessage,
          })
          .then((res) => {
            this.newMessage = ''

            this.$router.push({ path: `/user/messages/${res.id}/view` })
          })
          .finally(() => this.$emit('close-dialog'))
      }
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.v-application .v-dialog.message-dialog {
  & > .v-card {
    padding: 32px 40px !important;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding: 50px 18px 74px !important;

      .dialog-content,
      .v-form,
      .message-dialog-body {
        height: 100%;
      }

      .message-dialog-body {
        overflow-y: auto;
      }
    }
  }

  .message-dialog {
    &-header {
      display: inline-block;
      padding-right: 60px;
      font-size: 20px;
      font-weight: 700;
      line-height: 1.1;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 50px;
        display: flex;
        align-items: center;
        padding-left: 18px;
        font-size: 18px;
      }
    }

    &-body {
      .row {
        .col:first-child {
          padding-right: 20px;
        }

        .col:last-child {
          padding-left: 20px;
        }
      }
    }

    &-text {
      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        div:first-child {
          font-size: 16px;
          font-weight: 600;
        }
      }

      ul {
        padding-left: 20px;
      }
    }

    &-footer {
      @media #{map-get($display-breakpoints, 'md-and-up')} {
        margin-top: 28px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 74px;
        padding: 0 18px;
      }

      .prev-button {
        color: var(--v-orange-base);
        cursor: pointer;
      }
    }
  }

  .text-editor .ProseMirror {
    min-height: 248px;
  }

  .text-editor-buttons {
    left: 8px;
    right: auto;
  }
}
</style>
