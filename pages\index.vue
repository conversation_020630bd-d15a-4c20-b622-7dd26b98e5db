<template>
  <v-row v-resize="onResize">
    <v-col class="col-12 px-0">
      <intro-section :language-items="languageItems"></intro-section>
      <stat-section></stat-section>

      <section class="features">
        <v-container>
          <v-row>
            <v-col class="col-xl-10 offset-xl-1 px-2 px-lg-4">
              <v-row>
                <v-col class="col-12 col-sm-6 features-item features-item-i1">
                  <div class="features-item-wrap">
                    <div class="features-item-image">
                      <v-img
                        :src="require('~/assets/images/homepage/trophy.svg')"
                        width="70"
                        :options="{ rootMargin: '30%' }"
                      ></v-img>
                    </div>
                    <div class="features-item-title">
                      {{ $t('home_page.feature_1_title') }}
                    </div>
                    <div class="features-item-text">
                      {{ $t('home_page.feature_1_text') }}
                    </div>
                  </div>
                </v-col>
                <v-col
                  class="col-12 col-sm-6 pr-md-0 features-item features-item-i2"
                >
                  <div class="features-item-wrap">
                    <div class="features-item-image">
                      <v-img
                        :src="
                          require('~/assets/images/homepage/data-management.svg')
                        "
                        width="80"
                        :options="{ rootMargin: '30%' }"
                      ></v-img>
                    </div>
                    <div class="features-item-title">
                      {{ $t('home_page.feature_2_title') }}
                    </div>
                    <div class="features-item-text">
                      {{ $t('home_page.feature_2_text') }}
                    </div>
                  </div>
                </v-col>
                <v-col class="col-12 col-sm-6 features-item features-item-i3">
                  <div class="features-item-wrap">
                    <div class="features-item-image">
                      <v-img
                        :src="require('~/assets/images/homepage/calendar.svg')"
                        width="80"
                        :options="{ rootMargin: '30%' }"
                      ></v-img>
                    </div>
                    <div class="features-item-title">
                      {{ $t('home_page.feature_3_title') }}
                    </div>
                    <div class="features-item-text">
                      {{ $t('home_page.feature_3_text') }}
                    </div>
                  </div>
                </v-col>
                <v-col
                  class="col-12 col-sm-6 pr-md-0 features-item features-item-i4"
                >
                  <div class="features-item-wrap">
                    <div class="features-item-image">
                      <v-img
                        :src="require('~/assets/images/homepage/puzzle.svg')"
                        width="82"
                        :options="{ rootMargin: '30%' }"
                      ></v-img>
                    </div>
                    <div class="features-item-title">
                      {{ $t('home_page.feature_4_title') }}
                    </div>
                    <div class="features-item-text">
                      {{ $t('home_page.feature_4_text') }}
                    </div>
                  </div>
                </v-col>
                <v-col class="col-12 py-0 d-sm-none">
                  <div class="features-button">
                    <v-btn
                      :to="localePath({ name: 'teacher-listing' })"
                      large
                      color="primary"
                    >
                      {{ $t('get_started') }}
                    </v-btn>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-container>
      </section>

      <how-works-section></how-works-section>
      <about-section></about-section>

      <section class="find-teacher">
        <div class="section-bg">
          <v-img
            :src="require('~/assets/images/homepage/find-teacher-bg.png')"
            :options="{ rootMargin: '50%' }"
          ></v-img>
        </div>
        <v-container>
          <v-row>
            <v-col class="col-12">
              <div class="find-teacher-content text-center">
                <div class="find-teacher-images">
                  <div class="find-teacher-images-item">
                    <v-img
                      :src="require('~/assets/images/homepage/user-icon-1.svg')"
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <div class="find-teacher-images-item">
                    <v-img
                      :src="require('~/assets/images/homepage/user-icon-2.svg')"
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <div class="find-teacher-images-item">
                    <v-img
                      :src="require('~/assets/images/homepage/user-icon-3.svg')"
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                </div>
                <div class="find-teacher-title">
                  {{ $t('home_page.find_teacher_title') }}
                </div>
                <div class="find-teacher-text">
                  {{ $t('home_page.find_teacher_text') }}
                </div>
                <div class="find-teacher-button">
                  <v-btn
                    :to="
                      localePath({
                        name: 'teacher-listing',
                        params: { utm_source: 'homepage', utm_medium: 'intro' },
                      })
                    "
                    large
                    color="primary"
                    outlined
                  >
                    {{ $t('find_teacher') }}
                  </v-btn>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </section>

      <languages-section></languages-section>

      <tutors-section
        v-if="teacherItems && teacherItems.length"
        :teachers="teacherItems"
      ></tutors-section>

      <section class="virtual-classroom">
        <div class="section-bg">
          <v-img
            :src="require('~/assets/images/homepage/virtual-classroom-bg.png')"
            :options="{ rootMargin: '50%' }"
          ></v-img>
        </div>
        <v-container class="py-0">
          <v-row>
            <v-col class="col-12 py-0">
              <div class="section-head section-head--decorated">
                <h3
                  class="section-head-title"
                  style="color: #ffffff; -webkit-text-fill-color: #ffffff"
                >
                  {{ $t('home_page.classroom_section_title') }}
                </h3>
              </div>
            </v-col>
          </v-row>
          <v-row>
            <v-col class="col-12 col-md-7">
              <div class="virtual-classroom-image">
                <v-img
                  :src="require('~/assets/images/homepage/circle.svg')"
                  class="circle"
                  contain
                  position="left center"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
                <div>
                  <div class="laptop">
                    <v-img
                      :src="
                        require('~/assets/images/homepage/classroom-laptop.png')
                      "
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <div class="whiteboard">
                    <v-img
                      :src="
                        require('~/assets/images/homepage/classroom-whiteboard.png')
                      "
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <div class="stream">
                    <v-img
                      :src="
                        require('~/assets/images/homepage/classroom-stream.png')
                      "
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <div class="exercise">
                    <v-img
                      :src="
                        require('~/assets/images/homepage/classroom-exercise.png')
                      "
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <v-img
                    :src="require('~/assets/images/homepage/mobile.png')"
                    max-width="261"
                    class="mobile"
                    :options="{ rootMargin: '50%' }"
                  ></v-img>
                </div>
              </div>
            </v-col>
            <v-col class="col-12 col-md-5">
              <div class="virtual-classroom-content">
                <div class="virtual-classroom-video-1">
                  <v-img
                    :src="require('~/assets/images/homepage/video-1.png')"
                    max-width="404"
                    :options="{ rootMargin: '50%' }"
                  ></v-img>
                </div>
                <div
                  class="virtual-classroom-title"
                  v-html="$t('home_page.classroom_title')"
                ></div>
                <div
                  class="virtual-classroom-text"
                  v-html="$t('home_page.classroom_text')"
                ></div>
                <div class="virtual-classroom-video-2">
                  <v-img
                    :src="require('~/assets/images/homepage/video-2.png')"
                    max-width="468"
                    :options="{ rootMargin: '50%' }"
                  ></v-img>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </section>

      <review-section
        v-if="reviewItems && reviewItems.length"
        :reviews="reviewItems"
      ></review-section>

      <section class="company">
        <v-container class="py-0">
          <v-row>
            <v-col class="col-12 py-0">
              <div class="section-head section-head--decorated">
                <h3
                  class="section-head-title"
                  style="color: #262626; -webkit-text-fill-color: #262626"
                >
                  {{ $t('home_page.company_section_title') }}
                </h3>
              </div>
            </v-col>
          </v-row>
          <v-row>
            <v-col class="col-12">
              <div class="company-list">
                <div class="company-item">
                  <v-img
                    :src="
                      require('~/assets/images/homepage/partners/oxford.svg')
                    "
                    contain
                    :options="{ rootMargin: '50%' }"
                  ></v-img>
                </div>
                <div class="company-item">
                  <v-img
                    :src="require('~/assets/images/homepage/partners/ucl.svg')"
                    contain
                    :options="{ rootMargin: '50%' }"
                  ></v-img>
                </div>
                <div class="company-item">
                  <v-img
                    :src="
                      require('~/assets/images/homepage/partners/huffington-post.svg')
                    "
                    contain
                    :options="{ rootMargin: '50%' }"
                  ></v-img>
                </div>
                <div class="company-item">
                  <v-img
                    :src="require('~/assets/images/homepage/partners/et.svg')"
                    contain
                    :options="{ rootMargin: '50%' }"
                  ></v-img>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </section>
      <section class="start">
        <div class="section-bg">
          <v-img
            :src="require('~/assets/images/homepage/start-bg.png')"
            :options="{ rootMargin: '50%' }"
          ></v-img>
        </div>
        <v-container fill-height class="py-0 d-block d-sm-flex">
          <v-row>
            <v-col class="col-xl-10 offset-xl-1">
              <v-row>
                <v-col class="col-12 col-sm-6">
                  <div class="start-title--mobile">
                    {{ $t('home_page.start_text') }}
                  </div>
                  <div class="start-image">
                    <v-img
                      :src="require('~/assets/images/homepage/start-img.svg')"
                      contain
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                </v-col>
                <v-col class="col-12 col-sm-6 d-sm-flex align-center">
                  <div class="start-content">
                    <div class="start-text">
                      {{ $t('home_page.start_text') }}
                    </div>
                    <div class="start-button">
                      <v-btn
                        :to="
                          localePath({
                            name: 'teacher-listing',
                            params: {
                              utm_source: 'homepage',
                              utm_medium: 'bofu',
                            },
                          })
                        "
                        large
                        color="primary"
                      >
                        {{ $t('lets_get_started') }}
                      </v-btn>
                    </div>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-container>
      </section>

      <faq-section
        v-if="faqItems && faqItems.length"
        :items="faqItems"
        background-image
      ></faq-section>

      <thinking-section></thinking-section>
    </v-col>
  </v-row>
</template>

<script>
import IntroSection from '~/components/homepage/IntroSection'
import StatSection from '~/components/homepage/StatSection'
import HowWorksSection from '~/components/homepage/HowWorksSection'
import AboutSection from '~/components/homepage/AboutSection'
import LanguagesSection from '~/components/homepage/LanguagesSection'
import TutorsSection from '~/components/homepage/TutorsSection'
import ReviewSection from '~/components/homepage/ReviewSection'
import FaqSection from '~/components/homepage/FaqSection'
import ThinkingSection from '~/components/homepage/ThinkingSection'

export default {
  name: 'HomePage',
  components: {
    IntroSection,
    StatSection,
    HowWorksSection,
    AboutSection,
    LanguagesSection,
    TutorsSection,
    ReviewSection,
    FaqSection,
    ThinkingSection,
  },
  async asyncData({ store }) {
    let faqItems, reviewItems, languageItems, teacherItems

    await Promise.allSettled([
      store.dispatch('faq/getHomePageFaqs'),
      store.dispatch('language/getHomePageLanguages'),
      store.dispatch('review/getHomePageReviews'),
      store.dispatch('teacher/getHomePageTeachers'),
    ]).then((res) => {
      faqItems = res[0]?.value
      languageItems = res[1]?.value
      reviewItems = res[2]?.value
      teacherItems = res[3]?.value
    })

    return {
      faqItems,
      reviewItems,
      teacherItems,
      languageItems,
    }
  },
  data() {
    return {
      tutorEls: null,
      reviewEls: null,
    }
  },
  head() {
    return {
      title: this.$t('home_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('home_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('home_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('home_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} home-page`,
      },
    }
  },
  computed: {
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
    // userCurrency() {
    //   return this.$store.getters['user/currency']
    // },
    locale() {
      return this.$i18n.locale
    },
  },
  watch: {
    isUserLogged(newValue, oldValue) {
      this.$store
        .dispatch('language/getHomePageLanguages')
        .then((data) => (this.languageItems = data))
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.tutorEls = document.getElementsByClassName('tutors-carousel-item')
      this.reviewEls = document.getElementsByClassName(
        'home-page-reviews-carousel-item'
      )

      this.setCarouselItemHeight(this.tutorEls)
      this.setCarouselItemHeight(this.reviewEls)
    })
  },
  methods: {
    onResize() {
      if (this.tutorEls) {
        this.setCarouselItemHeight(this.tutorEls)
      }

      if (this.reviewEls) {
        this.setCarouselItemHeight(this.reviewEls)
      }
    },
    setCarouselItemHeight(els) {
      let maxHeight = 0

      Array.prototype.forEach.call(els, (el) => {
        if (el.offsetHeight > maxHeight) {
          maxHeight = el.offsetHeight
        }
      })

      Array.prototype.forEach.call(els, (el) => {
        el.style.height = '1px'
        el.style.minHeight = `${maxHeight}px`
      })
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/home-page.scss';
</style>
