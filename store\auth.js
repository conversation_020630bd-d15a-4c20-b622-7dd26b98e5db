export const state = () => ({
  passwordTokenItem: null,
})

export const mutations = {
  SET_PASSWORD_TOKEN_ITEM: (state, payload) => {
    state.passwordTokenItem = payload
  },
}

export const getters = {
  getPasswordTokenItem: (state) => state.passwordTokenItem,
}

export const actions = {
  login(ctx, data) {
    const url = `${process.env.NUXT_ENV_API_URL}/users-login`

    return this.$axios.post(url, data).then((response) => response.data)
  },
  logout({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/logout`

    commit('LOADING_START', null, { root: true })

    return this.$axios.post(url).then(() => {
      this.$cookiz.remove('L2SESSID')

      // commit('SET_AUTH_COOKIE', '', { root: true })
      commit('user/SET_USER', null, { root: true })
      commit('user/SET_TIDIO_DATA', null, { root: true })
    })
  },
  forgotPassword(ctx, data) {
    const url = `${process.env.NUXT_ENV_URL}/user/forgot`

    return this.$axios
      .post(url, data)
      .then((response) => response.status)
      .catch(() => {
        throw new Error('Invalid e-mail')
      })
  },
  checkPasswordToken({ commit }, token) {
    const url = `${process.env.NUXT_ENV_API_URL}/users-confirm-password-token`

    return this.$axios
      .post(url, JSON.stringify({ token }), {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then((response) => response.data)
      .then((data) => {
        commit('SET_PASSWORD_TOKEN_ITEM', {
          token,
          ...data,
        })
      })
      .catch(() => {
        commit('SET_PASSWORD_TOKEN_ITEM', {
          token: null,
        })
      })
  },
  setPassword(ctx, data) {
    const url = `${process.env.NUXT_ENV_API_URL}/users-set-password`

    return this.$axios
      .post(url, JSON.stringify(data), {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then((response) => response)
  },
  confirmRegistration() {
    const url = `${process.env.NUXT_ENV_API_URL}/users/confirm-registration`

    return this.$axios.post(url)
  },
}
