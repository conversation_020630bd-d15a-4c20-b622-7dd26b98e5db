<!-- eslint-disable vue/no-v-for-template-key -->
<!-- eslint-disable vue/attributes-order -->
<template>
  <v-col class="col-12 px-0">
    <div class="user-payments">
      <payout-modal
        :show="showPayoutModal"
        @close="showPayoutModal = false"
        @option-selected="handlePayoutOptionSelected"
      />
      <v-container fluid class="pa-0">
        <v-row no-gutters>
          <v-col class="col-12">
            <div class="user-payments-wrap mx-auto">
              <div class="user-payments-header mb-2 mb-md-4 mb-lg-5">
                <div class="user-payments-title">
                  <h1 class="font-weight-medium">{{ $t('payments') }} 🏦</h1>
                </div>
                <div class="user-payments-controls d-flex">
                  <div class="user-payments-search">
                    <v-form @submit.prevent="handleSearch">
                      <v-text-field
                        v-model="searchQuery"
                        :placeholder="
                          $t(
                            isLessonsTab
                              ? 'search_for_lesson'
                              : 'search_for_payout'
                          )
                        "
                        dense
                        hide-details
                        class="custom-search-input"
                        color="transparent"
                        background-color="#fff"
                        solo
                        flat
                      >
                        <template #append>
                          <div style="cursor: pointer" @click="handleSearch">
                            <v-img
                              :src="require('~/assets/images/search-icon.svg')"
                            ></v-img>
                          </div>
                        </template>
                      </v-text-field>
                    </v-form>
                  </div>
                  <div class="user-payments-nav d-flex">
                    <v-btn
                      :to="localePath('/user/payments/lessons')"
                      class="nav-btn font-weight-medium"
                      :class="{ 'v-btn--active': isLessonsTab }"
                      height="48"
                    >
                      {{ $t('payment_lessons') }}
                    </v-btn>
                    <v-btn
                      :to="localePath('/user/payments/payouts')"
                      class="nav-btn font-weight-medium"
                      :class="{ 'v-btn--active': isPayoutsTab }"
                      height="48"
                    >
                      {{ $t('payment_payouts') }}
                    </v-btn>
                  </div>
                </div>
                <div class="user-payments-mobile-summary mobile-only">
                  <div class="summary-row">
                    <div class="amount-info">
                      <div class="amount">
                        {{ currentCurrencySymbol }}
                        {{ availableAmount }}
                      </div>
                      <div class="label">Available to pay out</div>
                    </div>
                    <div class="payout-button">
                      <v-btn
                        :disabled="isPayoutDisabled"
                        @click="handlePayoutNow"
                      >
                        {{ $t('pay_out_now') }}
                      </v-btn>
                    </div>
                  </div>
                  <div
                    v-if="isPayoutDisabled"
                    class="payout-limitation-info mobile-only"
                  >
                    <div class="limitation-message">
                      {{ $t('limited_payouts') }}
                    </div>
                  </div>
                  <div class="summary-row">
                    <div class="amount-info">
                      <div class="amount">
                        {{ currentCurrencySymbol }} {{ scheduledAmount }}
                      </div>
                      <div class="label">Scheduled lesson value</div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="user-payments-body">
                <div class="user-payments-content">
                  <template v-if="filteredPayments.length">
                    <div class="payments-list">
                      <template v-for="payment in filteredPayments">
                        <component
                          :is="paymentComponents[type]"
                          :key="payment.id"
                          v-bind="{ item: payment }"
                        ></component>
                      </template>
                    </div>
                    <div class="mt-3 mt-md-5 text-center">
                      <pagination
                        :current-page="Number(page)"
                        :total-pages="Number(totalPages)"
                        :route="localePath(`/user/payments/${type}`)"
                      ></pagination>
                    </div>
                  </template>
                  <template v-else>
                    <div class="payments-list-empty">
                      {{ noResultsMessage }}
                    </div>
                  </template>
                </div>

                <aside
                  v-if="$vuetify.breakpoint.mdAndUp"
                  class="user-payments-sidebar desktop-only"
                >
                  <div class="available-amount">
                    <div class="amount">
                      {{ currentCurrencySymbol }}{{ availableAmount }}
                    </div>
                    <div class="label">Available to pay out</div>
                    <v-btn
                      class="order font-weight-medium"
                      width="159"
                      color="primary"
                      :disabled="isPayoutDisabled"
                      :style="{
                        background:
                          'linear-gradient(to right, #95ce32, #3C87F8)',
                        borderRadius: '20px',
                        textTransform: 'none',
                        height: '40px',
                      }"
                      @click="handlePayoutNow"
                    >
                      {{ $t('pay_out_now') }}
                    </v-btn>
                    <div
                      v-if="isPayoutDisabled"
                      class="payout-limitation-info desktop-only mt-1"
                    >
                      <div class="limitation-message">
                        Only 1 payout is permitted in any 7-day period.
                      </div>
                    </div>
                  </div>
                  <div class="scheduled-amount">
                    <div class="amount">
                      {{ currentCurrencySymbol }}{{ scheduledAmount }}
                    </div>
                    <div class="label">Scheduled lesson value</div>
                  </div>
                </aside>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </v-col>
</template>

<script>
import PayoutModal from './PayoutModal'
import PaymentLesson from '~/components/payments/PaymentLesson'
import PaymentPayout from '~/components/payments/PaymentPayout'
import Pagination from '~/components/Pagination'
import { formatCurrencyLocale } from '~/helpers'

export default {
  name: 'PaymentsPage',
  components: {
    Pagination,
    PayoutModal,
    PaymentLesson,
    PaymentPayout,
  },
  props: {
    type: {
      type: String,
      required: true,
    },
    page: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      searchQuery: '',
      paymentComponents: {
        lessons: PaymentLesson,
        payouts: PaymentPayout,
      },
      showPayoutModal: false,
      initialRedirectDone: false,
      isDataFetching: false,
    }
  },
  computed: {
    isLessonsTab() {
      return this.type === 'lessons' || this.$route.path === '/user/payments'
    },
    isPayoutsTab() {
      return this.type === 'payouts'
    },
    payments() {
      return this.$store.getters['payments/payments'](this.type)
    },
    totalPages() {
      return this.$store.getters['payments/totalPages'](this.type)
    },
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
    currentCurrency() {
      // Get current currency info from store
      return this.$store.state.currency.item
    },
    userLocale() {
      // Get user's UI language/locale, fallback to browser locale or 'en'
      return this.$store.getters['user/isUserLogged']
        ? this.$store.state.user.item?.uiLanguage || this.$i18n.locale
        : this.$i18n.locale || 'en'
    },
    // Check if payout is disabled due to 6-day limitation
    isPayoutDisabled() {
      const payouts = this.$store.getters['payments/payments']('payouts')
      if (!payouts || payouts.length === 0) {
        return false
      }

      // Find the most recent payout
      const mostRecentPayout = payouts.reduce((latest, current) => {
        const currentDate = new Date(current.date)
        const latestDate = new Date(latest.date)
        return currentDate > latestDate ? current : latest
      })

      if (!mostRecentPayout || !mostRecentPayout.date) {
        return false
      }

      // Calculate the difference in hours
      const payoutDate = new Date(mostRecentPayout.date)
      const currentDate = new Date()
      const hoursDifference = (currentDate - payoutDate) / (1000 * 60 * 60)

      // Return true if less than 144 hours (6 days) have passed or if available amount is 0.00
      return (
        hoursDifference < 144 ||
        this.availableAmount === '0.00' ||
        this.availableAmount === '0,00'
      )
    },
    availableAmount() {
      const balance =
        this.$store.getters['payments/earningsBalance'].balance || '0'
      const currencyCode = this.currentCurrency?.isoCode || 'EUR'
      return formatCurrencyLocale(balance, currencyCode, this.userLocale, false)
    },
    scheduledAmount() {
      const futureIncome =
        this.$store.getters['payments/earningsBalance'].futureIncome || '0'
      const currencyCode = this.currentCurrency?.isoCode || 'EUR'
      return formatCurrencyLocale(
        futureIncome,
        currencyCode,
        this.userLocale,
        false
      )
    },
    searchPlaceholder() {
      return this.isLessonsTab ? 'search_for_lesson' : 'search_for_payout'
    },
    noResultsMessage() {
      return this.isLessonsTab
        ? this.$t('no_lessons_found')
        : this.$t('no_payouts_found')
    },
    filteredPayments() {
      // If no payments data, return empty array
      if (!this.payments) return []

      // If no search query, return all payments
      if (!this.searchQuery || this.searchQuery.trim() === '')
        return this.payments

      // Enhanced search implementation
      const query = this.searchQuery.toLowerCase().trim()

      // Use a try-catch to prevent any errors from breaking the UI
      try {
        return this.payments.filter((payment) => {
          if (!payment) return false

          // Helper function to safely check if a field contains the query
          const fieldContains = (field) => {
            return field && field.toString().toLowerCase().includes(query)
          }

          // For lessons tab - search across multiple fields
          if (this.isLessonsTab) {
            return (
              fieldContains(payment.student) || // Student name
              fieldContains(payment.date) || // Lesson date
              fieldContains(payment.time) || // Lesson time
              fieldContains(payment.invoiceNo) || // Invoice number
              fieldContains(payment.lessonNo) || // Lesson number
              fieldContains(payment.value) || // Lesson value
              fieldContains(payment.status) || // Lesson status
              fieldContains(payment.lessonType) // Lesson type
            )
          }
          // For payouts tab - search across multiple fields
          else {
            return (
              fieldContains(payment.date) || // Payout date
              fieldContains(payment.time) || // Payout time
              fieldContains(payment.amount) || // Payout amount/value
              fieldContains(payment.counterPartyType) || // Payout method
              fieldContains(payment.status) || // Payout status
              fieldContains(payment.currency) // Payout currency
            )
          }
        })
      } catch (e) {
        // If any error occurs, return the original payments
        return this.payments
      }
    },
  },
  watch: {
    $route: {
      immediate: true,
      handler(newRoute) {
        // Handle root payments redirect only once
        if (newRoute.path === '/user/payments' && !this.initialRedirectDone) {
          this.initialRedirectDone = true
          this.$router.push(this.localePath('/user/payments/lessons'))
          return
        }

        // Handle page changes without duplicate fetches
        const pageParam = parseInt(newRoute.params.page) || 1
        if (pageParam !== this.page && !this.isDataFetching) {
          this.fetchPaymentData(pageParam)
        }
      },
    },
    // Watch for page prop changes
    page: {
      handler(newPage, oldPage) {
        if (newPage !== oldPage && !this.isDataFetching) {
          this.fetchPaymentData(newPage)
        }
      },
    },
    // Watch for type changes to ensure correct tab is selected
    type: {
      immediate: true,
      handler(newType) {
        // Ensure the correct tab is selected based on the type prop
        this.$nextTick(() => {
          if (
            newType === 'lessons' &&
            !this.$route.path.includes('/user/payments/lessons')
          ) {
            this.$router.push(this.localePath('/user/payments/lessons'))
          } else if (
            newType === 'payouts' &&
            !this.$route.path.includes('/user/payments/payouts')
          ) {
            this.$router.push(this.localePath('/user/payments/payouts'))
          }
        })
      },
    },
  },
  async created() {
    this.searchQuery = this.$route.query?.search || ''
    // Always initialize data when component is created
    await this.initializeData()
  },
  methods: {
    async initializeData() {
      if (this.isDataFetching) return

      try {
        this.isDataFetching = true
        await Promise.all([
          this.$store.dispatch('payments/fetchEarningsBalance'),
          this.fetchPaymentData(),
          // Always fetch payouts data to check for payout limitations
          this.$store.dispatch('payments/fetchPayouts', {
            page: 1,
            itemsPerPage: 5,
          }),
        ])
      } finally {
        this.isDataFetching = false
      }
    },
    handleSearch() {
      // Just trigger a re-render, no need to update URL or make API calls
      // The filteredPayments computed property will handle the filtering
    },
    async fetchPaymentData(pageNumber = null) {
      if (this.isDataFetching) return

      try {
        this.isDataFetching = true
        const page = pageNumber || this.page

        const params = {
          page,
          itemsPerPage: 20, // Set to 5 items per page
          searchQuery: this.searchQuery,
        }

        if (this.type === 'payouts') {
          await this.$store.dispatch('payments/fetchPayouts', params)
        } else if (this.type === 'lessons') {
          await this.$store.dispatch('payments/fetchLessons', params)
        }

        // Update the URL if needed (only for direct method calls, not from route watchers)
        if (pageNumber && parseInt(this.$route.params.page || '1') !== page) {
          // Construct the new URL
          let newPath = `/user/payments/${this.type}`
          if (page > 1) {
            newPath += `/${page}`
          }

          // Update the URL without triggering the route watcher
          this.$router.push({
            path: this.localePath(newPath),
            query: this.$route.query,
          })
        }
      } finally {
        this.isDataFetching = false
      }
    },
    handlePayoutNow() {
      this.showPayoutModal = true
    },
    handlePayoutOptionSelected(option) {
      // Handle the selected payout option
      this.$router.push(option.route)
    },
    // Reset search
    async resetSearch() {
      this.searchQuery = ''
      await this.$router.push({
        query: {
          ...this.$route.query,
          search: undefined,
          page: '1',
        },
      })
      await this.fetchPaymentData()
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/user-payments.scss';
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.user-payments-nav {
  .v-text-field.v-text-field--enclosed:not(.v-text-field--rounded)
    > .v-input__control
    > .v-input__slot {
    padding: 0 10px 10px 0;
  }
  .nav-btn {
    color: var(--v-grey-base);
    text-transform: none;
    border-radius: 20px;

    &:hover {
      color: var(--v-dark-base) !important;
    }

    &.v-btn--active {
      background: linear-gradient(
        126.15deg,
        rgba(128, 182, 34, 0.1) 0%,
        rgba(60, 135, 248, 0.1) 102.93%
      );
      color: var(--v-dark-base) !important;
    }
    &:not(:last-child) {
      margin-right: 8px;
    }
  }
}

// Search input styles
.custom-search-input {
  .v-input__control {
    min-height: 56px !important;
    height: 100% !important;
    .v-input__slot {
      background-color: #fff !important;
      border-radius: 40px !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: none !important;

      .v-text-field__slot {
        input {
          font-size: 16px;
          padding: 0 0 0 4px;

          &::placeholder {
            color: #757575;
            font-weight: normal;
          }
        }
      }
    }
  }

  .v-input__append-inner {
    .v-image {
      width: 26px !important;
      height: 26px !important;
    }
  }

  &.v-input--is-focused {
    .v-input__slot {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    }

    // Remove focus outline
    fieldset {
      border-color: transparent !important;
    }
  }

  // Remove all borders and outlines in all states
  fieldset {
    border: none !important;
  }

  .v-text-field--outlined fieldset,
  .v-text-field--solo .v-input__control .v-input__slot {
    border: none !important;
    border-color: transparent !important;
    outline: none !important;
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    .v-input__control {
      min-height: 56px;
    }

    .v-input__append-inner {
      .v-image {
        width: 20px !important;
        height: 20px !important;
      }
    }

    .v-text-field__slot {
      input {
        padding-top: 10px;
      }
    }
  }
}

// Payout limitation info styles
.payout-limitation-info {
  .limitation-message {
    font-size: 12px;
    color: #ff474c;
    text-align: center;
    font-style: italic;
    line-height: 1.4;
  }

  &.mobile-only {
    padding: 8px 16px;
    margin-top: 8px;
  }

  &.desktop-only {
    .limitation-message {
      font-size: 11px;
      text-align: left;
    }
  }
}
</style>
