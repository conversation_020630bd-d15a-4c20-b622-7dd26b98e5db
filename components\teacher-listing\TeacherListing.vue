<template>
  <v-col class="col-12 px-0">
    <div class="teacher-listing">
      <v-container fluid class="py-0 px-md-12 px-8">
        <v-row>
          <v-col class="col-12">
            <div class="teacher-listing-wrap">
              <div class="teacher-listing-content">
                <v-navigation-drawer
                  v-if="$vuetify.breakpoint.smAndDown"
                  id="teacher-filters"
                  v-model="drawer"
                  hide-overlay
                  fixed
                  color="darkLight"
                  width="375"
                >
                  <teacher-filter
                    @filters-loaded="filtersLoaded"
                  ></teacher-filter>
                </v-navigation-drawer>

                <!-- <div
                  class="filter-button d-md-none"
                  @click="openFilterClickHandler"
                >
                  {{ $t('filters') }}
                  <span v-if="quantityActiveFilters"
                    >({{ quantityActiveFilters }})</span
                  >
                  <div class="filter-button-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                      <use
                        :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#funnel`"
                      ></use>
                    </svg>
                  </div>
                </div> -->

                <div class="teacher-listing-header-title">
                  <div class="teacher-listing-header-content">
                    <div class="teacher-listing-header-title-text">
                      {{ dynamicTitle }}
                    </div>

                    <!-- Subtitle -->
                    <div class="teacher-listing-header-subtitle">
                      {{ dynamicSubtitle }}
                    </div>

                    <!-- Ratings -->
                    <div class="teacher-listing-header-ratings">
                      <span class="rating-score">5.0</span>
                      <div class="rating-stars">
                        <star-rating :value="5" hide-rating></star-rating>
                      </div>
                      <span class="rating-text"
                        >(300,000+ {{ $t('teacher_ratings_text') }})</span
                      >
                    </div>

                    <!-- 4 Column Features -->
                    <div class="teacher-listing-features">
                      <div class="feature-item">
                        <v-btn
                          color="primary"
                          large
                          class="get-started-btn"
                          @click="scrollToFirstTeacher"
                        >
                          {{ $t('lets_get_started_button') }}
                        </v-btn>
                      </div>

                      <div class="feature-item mt-1">
                        <div class="feature-icon">
                          <v-img
                            :src="
                              require('~/assets/images/banners/advanced.svg')
                            "
                            width="26"
                            height="26"
                          ></v-img>
                        </div>
                        <div
                          class="feature-text beginner-advanced gradient-text"
                        >
                          <div class="beginner-text">
                            {{ $t('beginner_to') }}
                          </div>
                          <div class="advanced-text">{{ $t('advanced') }}</div>
                        </div>
                      </div>

                      <div class="feature-item mt-1">
                        <div class="feature-icon">
                          <v-img
                            :src="
                              require('~/assets/images/banners/teacher-star.svg')
                            "
                            width="27"
                            height="27"
                          ></v-img>
                        </div>
                        <div class="feature-text rating-feature">
                          <div class="rating-number gradient-text">98%</div>
                          <div class="rating-subtitle">
                            <div class="rating-line-1">
                              {{ $t('lessons_rated') }}
                            </div>
                            <div class="rating-line-2">{{ $t('5_stars') }}</div>
                          </div>
                        </div>
                      </div>

                      <div class="feature-item mt-1">
                        <div class="feature-icon">
                          <v-img
                            :src="
                              require('~/assets/images/banners/private-lessons.svg')
                            "
                            width="37"
                            height="26"
                          ></v-img>
                        </div>
                        <div class="feature-text private-lessons gradient-text">
                          <div class="private-line-1">
                            {{ $t('live_private') }}
                          </div>
                          <div class="private-line-2">
                            {{ $t('lessons_text') }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="teacher-listing-banner-container">
                    <span v-if="getBannerVisibility"
                      ><teacher-listing-banner></teacher-listing-banner
                    ></span>
                  </div>
                </div>

                <teacher-filter-new></teacher-filter-new>

                <teacher-listing-header></teacher-listing-header>

                <section v-if="teachers.length" class="teacher-listing-result">
                  <div class="teacher-listing-result-list">
                    <div
                      v-for="teacher in teachers"
                      :key="teacher.id"
                      class="teacher-listing-result-item"
                    >
                      <teacher-card :teacher="teacher"></teacher-card>
                    </div>
                  </div>

                  <div v-if="totalPages > 1" class="mt-3 mt-md-5 text-center">
                    <pagination
                      :current-page="page"
                      :total-pages="totalPages"
                      route="/teacher-listing"
                      :params="params"
                    ></pagination>
                  </div>
                </section>

                <div
                  v-if="!teachers.length && quantityActiveFilters"
                  class="teacher-listing-result--empty text-center"
                  v-html="
                    $t('empty_teacher_list', {
                      email: '<EMAIL>',
                    })
                  "
                ></div>
                <section
                  v-if="faqItems.length"
                  id="teacher-listing-page-faq-section"
                  ref="questions"
                  class="questions teacher-listing-page-faq-section"
                >
                  <div
                    v-if="backgroundImage"
                    ref="questionsSectionBg"
                    class="section-bg"
                  >
                    <v-img
                      :src="
                        require('~/assets/images/homepage/questions-bg.png')
                      "
                      position="center top"
                      :options="{ rootMargin: '50%' }"
                    ></v-img>
                  </div>
                  <v-container class="py-0 faq-custom-wrapper">
                    <v-row>
                      <v-col class="col-12 py-0">
                        <div class="section-head section-head--decorated">
                          <h3 :class="['section-head-title', titleClass]">
                            {{ $t('home_page.questions_section_title') }}
                          </h3>
                        </div>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col class="col-12 py-0">
                        <div class="questions-content">
                          <l-expansion-panels
                            :items="faqItems"
                            flat
                          ></l-expansion-panels>

                          <div class="questions-button">
                            <v-btn to="/faq" large color="primary">
                              {{ $t('see_our_full_faqs') }}
                            </v-btn>
                          </div>
                        </div>
                      </v-col>
                    </v-row>
                  </v-container>
                </section>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </v-col>
</template>

<script>
import TeacherFilterNew from '~/components/TeacherFilterNew'
import TeacherListingHeader from '~/components/teacher-listing/TeacherListingHeader'
import TeacherListingBanner from '~/components/teacher-listing/TeacherListingBanner'
import TeacherCard from '~/components/TeacherCard'
import Pagination from '~/components/Pagination'
import LExpansionPanels from '~/components/LExpansionPanels'

export default {
  name: 'TeacherListingPage',
  components: {
    TeacherFilterNew,
    TeacherListingHeader,
    TeacherListingBanner,
    TeacherCard,
    Pagination,
    LExpansionPanels,
  },
  props: {
    teachers: {
      type: Array,
      required: true,
    },
    faqItems: {
      type: Array,
      required: true,
    },
    page: {
      type: Number,
      default: 1,
    },
    params: {
      type: String,
      default: '',
    },
    backgroundImage: {
      type: Boolean,
      default: false,
    },
    titleClass: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      scrollContainer: null,
      isCustomBreakpoint: false,
    }
  },
  computed: {
    quantityActiveFilters() {
      return this.$store.getters['teacher_filter/quantityActiveFilters']
    },
    totalPages() {
      return this.$store.getters['teacher/totalPages']
    },
    drawer: {
      get() {
        return this.$store.state.isShownTeacherFilter
      },
      set(value) {
        this.$store.commit('SET_IS_TEACHER_FILTER', value)
      },
    },
    breakpoint() {
      return this.isCustomBreakpoint
        ? this.$vuetify.breakpoint
        : { mdAndUp: true }
    },
    filterScroll: {
      get() {
        if (process.client && typeof window !== 'undefined') {
          return window.sessionStorage.getItem('filter-scroll')
        }
        return null
      },
      set(value) {
        if (process.client && typeof window !== 'undefined') {
          window.sessionStorage.setItem('filter-scroll', value)
        }
      },
    },
    getBannerVisibility() {
      return this.$vuetify.breakpoint.mdAndUp
    },
    dynamicTitle() {
      const selectedLanguage = this.$store.state.teacher_filter.selectedLanguage
      const selectedSpecialities = this.$store.state.teacher_filter
        .selectedSpecialities

      // Get language name from URL slug or language object
      let languageName = null
      if (selectedLanguage && selectedLanguage.name) {
        // Create URL-friendly slug from language name
        const languageSlug = selectedLanguage.name
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[()]/g, '')
          .replace(/,/g, '')

        // Get the translated language name
        languageName = this.$t(`${languageSlug}`) || selectedLanguage.name
      }

      // Get specialty name (use first selected specialty)
      let specialtyName = null
      if (selectedSpecialities && selectedSpecialities.length > 0) {
        const specialty = selectedSpecialities[0]
        // Get translated specialty name
        const currentLocale = this.$i18n.locale
        const translation = specialty.speciality?.translations?.find(
          (t) => t.locale === currentLocale && t.field === 'name'
        )
        const rawSpecialtyName =
          translation?.value || specialty.speciality?.name || specialty.name

        // Try to get a mapped specialty name, otherwise use the raw name
        if (rawSpecialtyName && typeof rawSpecialtyName === 'string') {
          const specialtySlug = rawSpecialtyName
            .toLowerCase()
            .replace(/\s+/g, '')
            .replace(/[()]/g, '')
            .replace(/,/g, '')

          specialtyName =
            this.$t(`specialty_names.${specialtySlug}`) !==
            `specialty_names.${specialtySlug}`
              ? this.$t(`specialty_names.${specialtySlug}`)
              : rawSpecialtyName
        }
      }

      // Determine which title template to use
      if (languageName && specialtyName) {
        return this.$t('teacher_listing_language_specialty_title', {
          language: languageName,
          specialty: specialtyName,
        })
      } else if (languageName) {
        return this.$t('teacher_listing_language_only_title', {
          language: languageName,
        })
      } else if (specialtyName) {
        return this.$t('teacher_listing_specialty_only_title', {
          specialty: specialtyName,
        })
      } else {
        return this.$t('teacher_listing_default_title')
      }
    },
    dynamicSubtitle() {
      const selectedLanguage = this.$store.getters[
        'teacher_filter/selectedLanguage'
      ]
      const selectedSpecialities = this.$store.getters[
        'teacher_filter/selectedSpecialities'
      ]

      if (selectedSpecialities && selectedSpecialities.length > 0) {
        const specialty = selectedSpecialities[0]

        const specialtyDescription = this.getTranslatedSpecialityDescription(
          specialty.speciality || specialty
        )
        if (specialtyDescription) {
          return specialtyDescription
        }
      }

      if (selectedLanguage) {
        let languageName = selectedLanguage.name

        if (this.$i18n.locale === 'pl') {
          const languageGenitiveMap = {
            English: 'angielskiego',
            Ukrainian: 'ukraińskiego',
            Swedish: 'szwedzkiego',
            Spanish: 'hiszpańskiego',
            Russian: 'rosyjskiego',
            'Portuguese (Portugal)': 'portugalskiego (Portugalia)',
            'Portuguese (Brazil)': 'portugalskiego (Brazylia)',
            Portuguese: 'portugalskiego',
            Polish: 'polskiego',
            'Persian (Farsi)': 'perskiego (farsi)',
            Norwegian: 'norweskiego',
            Japanese: 'japońskiego',
            Korean: 'koreańskiego',
            Italian: 'włoskiego',
            Hungarian: 'węgierskiego',
            Greek: 'greckiego',
            German: 'niemieckiego',
            French: 'francuskiego',
            Dutch: 'niderlandzkiego',
            Danish: 'duńskiego',
            Czech: 'czeskiego',
            Croatian: 'chorwackiego',
            'Chinese (Mandarin)': 'chińskiego (mandaryńskiego)',
            'Chinese (Cantonese)': 'chińskiego (kantońskiego)',
            Catalan: 'katalońskiego',
            Bulgarian: 'bułgarskiego',
            Bosnian: 'bośniackiego',
            Arabic: 'arabskiego',
            'Ancient Greek': 'starogreckiego',
            Mathematics: 'matematyki',
            Belarusian: 'białoruskiego',
            Turkish: 'tureckiego',
            Thai: 'tajskiego',
            Slovak: 'słowackiego',
            Welsh: 'walijskiego',
            Afrikaans: 'afrykanerskiego (Afrikaans)',
            Serbian: 'serbskiego',
          }
          languageName =
            languageGenitiveMap[selectedLanguage.name] ||
            selectedLanguage.name.toLowerCase()
        }

        return this.$t('teacher_listing_header_subtitle_with_language', {
          language: languageName,
        })
      } else {
        // No language selected - show generic subtitle
        return this.$t('teacher_listing_header_subtitle_no_language')
      }
    },
  },
  mounted() {
    this.scrollContainer = document
      ?.getElementById('teacher-filters')
      ?.getElementsByClassName('v-navigation-drawer__content')[0]
    this.isCustomBreakpoint = true

    if (this.scrollContainer) {
      this.$nextTick(() => {
        this.scrollContainer.addEventListener('scroll', this.onScroll)
      })
    }
  },
  beforeDestroy() {
    if (this.scrollContainer) {
      this.scrollContainer.removeEventListener('scroll', this.onScroll)
    }
  },
  methods: {
    getTranslatedSpecialityDescription(speciality) {
      if (!speciality) return null

      const currentLocale = this.$i18n.locale
      const translation = speciality.translations?.find(
        (t) => t.locale === currentLocale && t.field === 'description'
      )
      return translation ? translation.content : speciality.description
    },
    openFilterClickHandler() {
      this.$store.commit('SET_IS_TEACHER_FILTER', true)
    },
    filtersLoaded() {
      if (
        this.$vuetify.breakpoint.smAndDown &&
        this.filterScroll &&
        this.scrollContainer
      ) {
        this.scrollContainer.scroll({
          top: this.filterScroll,
          behavior: 'instant',
        })
      }
    },
    onScroll() {
      this.filterScroll = this.scrollContainer.scrollTop
    },
    scrollToFirstTeacher() {
      // Scroll to the first teacher card
      const firstTeacherCard = document.querySelector('.teacher-listing-header')
      if (firstTeacherCard) {
        firstTeacherCard.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        })
      }
    },
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.banner {
  position: relative;
  display: flex;
  justify-content: space-between;
  min-height: 125px;
  padding: 5px 8px 0 32px;
  line-height: 1.333;
  margin-top: -10px;

  @media #{map-get($display-breakpoints, 'md-only')} {
    padding: 5px 15px 0 20px;
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    flex-direction: column;
  }

  @media only screen and (max-width: $xsm-and-down) {
    padding: 16px 16px 0 16px;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // background: linear-gradient(97.6deg, #80b622 4.6%, #3c87f8 37.97%), #c4c4c4;
    opacity: 0.1;
    border-radius: 16px;
  }

  &-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 15px 10px 20px 0;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      max-width: 600px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      padding: 0 0 15px 0;
    }
  }

  &-title {
    margin-bottom: 8px;
    font-size: 24px;
    font-weight: 700;

    @media #{map-get($display-breakpoints, 'md-only')} {
      font-size: 22px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      font-size: 20px;
    }
  }

  &-text {
    font-weight: 300;
    font-size: 14px;
    letter-spacing: -0.002em;
  }

  &-image {
    display: flex;
    align-items: flex-end;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      justify-content: center;

      .v-image {
        max-height: 90px !important;
      }
    }

    &-helper {
      width: 362px;
    }
  }
}

.teacher-listing-header-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 15px;

  @media screen and (max-width: 768px) {
    flex-direction: column;
    gap: 30px;
  }
}

.teacher-listing-header-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.teacher-listing-header-title-text {
  font-size: 36px;
  font-weight: 900;
  line-height: 1.2;
  color: #000;

  @media screen and (max-width: 768px) {
    font-size: 32px;
  }
}

.teacher-listing-header-subtitle {
  font-size: 16px;
  line-height: 1.5;
  color: #000000;
  max-width: 800px;

  @media screen and (max-width: 768px) {
    display: none;
  }
}

.teacher-listing-header-ratings {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;

  @media screen and (max-width: 768px) {
    display: none;
  }

  .rating-score {
    font-weight: 800;
    font-style: bold;
    color: #000;
  }

  .rating-stars {
    display: flex;
    gap: 2px;
  }

  .rating-text {
    color: #000;
  }
}

.teacher-listing-features {
  display: flex;
  align-items: flex-start;
  gap: 40px;
  flex-wrap: wrap;
  max-width: max-content;
  margin-top: 10px;

  @media screen and (max-width: 768px) {
    display: none;
  }

  .feature-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;

    .feature-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .feature-text {
      font-family: 'Lato', sans-serif;
      color: #000;

      &.beginner-advanced {
        display: flex;
        flex-direction: column;
        line-height: 1.2;

        .beginner-text,
        .advanced-text {
          font-size: 22px;
          font-weight: 900;
        }
      }

      &.rating-feature {
        display: flex;
        flex-direction: column;
        line-height: 1.2;

        .rating-number {
          font-size: 22px;
          font-weight: 900;
          line-height: 120%;
          letter-spacing: 0%;
        }

        .rating-subtitle {
          .rating-line-1,
          .rating-line-2 {
            font-size: 18px;
            font-weight: 400;
            line-height: 120%;
            letter-spacing: 0%;
            color: #000;
          }
        }
      }

      &.private-lessons {
        display: flex;
        flex-direction: column;
        line-height: 1.2;

        .private-line-1,
        .private-line-2 {
          font-size: 22px;
          font-weight: 900;
          line-height: 120%;
          letter-spacing: 0%;
        }
      }
    }

    .get-started-btn {
      font-weight: 700;
      text-transform: none;
      border-radius: 24px;
      padding: 5px 50px;
    }
  }
}

.teacher-listing-banner-container {
  flex-shrink: 0;
  max-width: 400px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;

  @media screen and (max-width: 768px) {
    display: none;
  }
}

.teacher-listing-content {
  width: 100%;
}
</style>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';
.questions {
  position: relative;
  margin: 138px 0 82px;

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    margin: 95px 0 82px;
  }

  .section-bg {
    top: 72px;
  }

  .section-head {
    margin-bottom: 118px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin-bottom: 70px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-bottom: 40px;
    }
  }

  &-content {
    max-width: 920px;
    margin: 0 auto;

    @media only screen and (max-width: $xxs-and-down) {
      .v-expansion-panel-content__wrap {
        padding: 0 16px 20px !important;
      }
    }
  }

  &-button {
    display: flex;
    justify-content: center;
    margin-top: 45px;

    .v-btn {
      min-width: 202px !important;

      @media only screen and (max-width: $xxs-and-down) {
        min-width: 100% !important;
        width: 100% !important;
      }
    }
  }
}

.faq-custom-wrapper {
  display: grid;
  justify-content: center;
}

.section-head--decorated h3 {
  color: var(--v-success-base);
  background: -webkit-linear-gradient(
    -75deg,
    var(--v-success-base),
    var(--v-primary-base)
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.teacher-listing-page-faq-section {
  padding-top: 50px;
  margin-top: 50px;
  padding-bottom: 70px;
}

.questions-content div div::before {
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2),
    0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);
  border-bottom: 1px solid #dadada;
  border-radius: 0px !important;
}

.questions-content svg {
  fill: #ef5a6f !important;
}

.teacher-listing-page-faq-section h3 {
  color: var(--v-success-base);
  background: -webkit-linear-gradient(
    -75deg,
    var(--v-success-base),
    var(--v-primary-base)
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.teacher-listing-page-faq-section
  .v-expansion-panels
  .v-expansion-panel::before {
  box-shadow: none !important;
}

.teacher-listing-page-faq-section .v-expansion-panels .v-expansion-panel {
  background-color: transparent !important;
  margin-bottom: 0px !important;
}
.teacher-listing-header-image {
  background-image: url('~/assets/images/banners/default.svg');
  width: 250px;
  height: 120px;
  background-position: center center;
}

// Common gradient text class
.gradient-text {
  background: linear-gradient(126.15deg, #80b622 0%, #3c87f8 102.93%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
