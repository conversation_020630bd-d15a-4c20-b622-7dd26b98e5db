<template>
  <l-dialog
    :dialog="show"
    max-width="800"
    custom-class="wise-transfer-modal"
    @close-dialog="$emit('close')"
  >
    <v-card class="pa-2" flat>
      <div class="d-flex justify-space-between align-center mb-2">
        <h2 class="text-h6 font-weight-medium">Wise Transfer</h2>
        <!-- <v-btn icon small @click="$emit('close')">
          <v-icon>mdi-close</v-icon>
        </v-btn> -->
      </div>

      <div class="wise-transfer-info mb-2">
        Wise payouts are processed each Monday. You will receive a link by email
        from <PERSON>, and you will enter your banking details directly with them.
        Please enter your email address and your full name below.
      </div>

      <v-form ref="form" @submit.prevent="handleSubmit">
        <v-row no-gutters class="wise-modal">
          <v-col cols="6" class="pr-2">
            <div class="input-label mb-1">Email address:</div>
            <text-input
              :value="form.email"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required, rules.email]"
              @input="updateValue($event, 'email')"
            />
          </v-col>
          <v-col cols="6" class="pl-2">
            <div class="input-label mb-1">Full name:</div>
            <text-input
              :value="form.fullName"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required]"
              @input="updateValue($event, 'fullName')"
            />
          </v-col>
        </v-row>

        <div class="currency-info mb-2">
          In what currency would you like to receive the transfer? Please enter
          the 3-letter currency code (i.e. AUD, ZAR, PEN, etc.).
        </div>

        <v-row no-gutters>
          <v-col cols="6" class="wise-modal">
            <div class="input-label mb-1">Currency:</div>
            <text-input
              :value="form.currency"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required, rules.currencyCode]"
              @input="updateValue($event, 'currency')"
            />
          </v-col>
        </v-row>

        <div class="text-right">
          <v-btn
            color="primary"
            large
            class="px-12"
            type="submit"
            :loading="loading"
          >
            Confirm payout
          </v-btn>
        </div>
      </v-form>
    </v-card>
  </l-dialog>
</template>

<script>
import LDialog from '~/components/LDialog'
import TextInput from '~/components/form/TextInput'
export default {
  name: 'WiseTransferModal',
  components: {
    LDialog,
    TextInput,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      form: {
        email: '',
        fullName: '',
        currency: '',
      },
      rules: {
        required: (v) => !!v || 'This field is required',
        email: (v) => /.+@.+\..+/.test(v) || 'Please enter a valid email',
        currencyCode: (v) =>
          !v ||
          /^[A-Z]{3}$/.test(v) ||
          'Please enter a valid 3-letter currency code',
      },
    }
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.resetForm()
      }
    },
  },
  methods: {
    updateValue(value, field) {
      this.form[field] = value
    },
    resetForm() {
      this.form = {
        email: '',
        fullName: '',
        currency: '',
      }
      if (this.$refs.form) {
        this.$refs.form.resetValidation()
      }
    },
    async handleSubmit() {
      if (!this.loading && this.$refs.form.validate()) {
        this.loading = true
        try {
          // Call the Wise transfer API
          const result = await this.$store.dispatch(
            'payments/requestWiseTransfer',
            this.form
          )

          // Show toast notification based on the result
          if (result.success) {
            this.$store.dispatch(
              'snackbar/success',
              { successMessage: 'Form submitted successfully' },
              { root: true }
            )
            this.$emit('submit', this.form)
            this.$emit('close')
          } else {
            this.$store.dispatch(
              'snackbar/error',
              { errorMessage: 'Something went wrong' },
              { root: true }
            )
          }
        } catch (error) {
          // Show generic error message if the store action throws
          // alert('An unexpected error occurred. Please try again.')
        } finally {
          this.loading = false
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.wise-transfer-modal {
  ::v-deep .v-card {
    box-shadow: none !important;
  }
  .v-text-field .v-input .v-input__control .v-text-field--outlined {
    border-radius: 16px !important;
  }
  .input-label {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.87);
  }

  .wise-transfer-info,
  .currency-info {
    color: rgba(0, 0, 0, 0.6);
    font-size: 14px;
    line-height: 1.4;
  }

  /* New custom input class with stronger specificity */

  @media (max-width: 768px) {
    .v-card {
      padding: 16px !important;
    }

    .v-row {
      margin: 0;
    }

    .v-col {
      padding: 0;
      margin-bottom: 2px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .text-right {
      display: flex;
      justify-content: flex-end;

      .v-btn {
        width: max-content !important;
      }
    }

    .wise-transfer-info,
    .currency-info {
      margin-bottom: 2px;
    }
    .wise-modal {
      flex-direction: column;
      margin-bottom: 2px;
      width: 100%;
    }
  }
}
</style>
