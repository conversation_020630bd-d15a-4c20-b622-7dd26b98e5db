<template>
  <lesson-item :item="item" :user-statuses="userStatuses">
    <template #lessonAdditionalActionsTop>
      <div>
        <nuxt-link to="/user/settings#calendar">
          <template v-if="item.isSyncedWithCalendar">
            <v-img
              :src="require('~/assets/images/check-gradient.svg')"
              width="12"
              height="13"
            ></v-img>
            {{ $t('synced_with_my_calendar') }}
          </template>
          <template v-else>
            <v-img
              :src="require('~/assets/images/gear-icon-gradient.svg')"
              width="13"
              height="14"
            ></v-img>
            {{ $t('configure_calendar_sync') }}
          </template>
        </nuxt-link>
      </div>
      <div>
        <a :href="`/lesson/${item.lessonId}/icalendar/get`">
          <v-img
            :src="require('~/assets/images/download-icon-gradient.svg')"
            width="14"
            height="14"
          ></v-img>
          {{ $t('download_ics_calendar_file') }}
        </a>
      </div>
    </template>
  </lesson-item>
</template>

<script>
import LessonItem from '~/components/user-lessons/LessonItem'

export default {
  name: 'UpcomingLesson',
  components: { LessonItem },
  props: {
    item: {
      type: Object,
      required: true,
    },
    userStatuses: {
      type: Object,
      default: () => ({}),
    },
  },
}
</script>
