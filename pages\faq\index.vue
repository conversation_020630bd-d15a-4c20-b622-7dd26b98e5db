<template>
  <v-row>
    <v-col class="col-12 px-0">
      <v-container fluid>
        <v-row>
          <v-col class="col-12">
            <div class="faq-page-wrap mt-1 mt-sm-2 mt-md-4 mx-auto">
              <div class="d-flex flex-column flex-md-row">
                <div class="faq-page-content">
                  <div class="section-head">
                    <h1 class="faq-page-title font-weight-regular mb-4">
                      {{ $t('faq_page.page_title') }}
                    </h1>
                    <youtube
                      video-link="https://www.youtube.com/watch?v=ZCJzw3T9pXg&pp=ygUdaGV5bGFuZ3UgaG93IHRvIGJvb2sgYSBsZXNzb24%3D"
                    ></youtube>
                  </div>
                  <template v-if="generalQuestions.items.length">
                    <h2 class="font-weight-regular mb-1 text--gradient">
                      {{ $t('faq_page.general_questions.title') }}
                    </h2>
                    <l-expansion-panels
                      :items="generalQuestions.items"
                      :panels="generalQuestions.activeItems"
                      link
                    ></l-expansion-panels>
                    <v-divider class="mt-3 mb-2 mt-md-4 mb-md-3"></v-divider>
                  </template>

                  <template v-if="languageLessons.items.length">
                    <h2 class="font-weight-regular mb-1 text--gradient">
                      {{ $t('faq_page.language_lessons.title') }}
                    </h2>
                    <l-expansion-panels
                      :items="languageLessons.items"
                      :panels="languageLessons.activeItems"
                      link
                    ></l-expansion-panels>
                    <v-divider class="mt-3 mb-2 mt-md-4 mb-md-3"></v-divider>
                  </template>

                  <template v-if="languTeachers.items.length">
                    <h2 class="font-weight-regular mb-1 text--gradient">
                      {{ $t('faq_page.langu_teachers.title') }}
                    </h2>
                    <l-expansion-panels
                      :items="languTeachers.items"
                      :panels="languTeachers.activeItems"
                      link
                    ></l-expansion-panels>
                    <v-divider class="mt-3 mb-2 mt-md-4 mb-md-3"></v-divider>
                  </template>

                  <template v-if="costsAndPayments.items.length">
                    <h2 class="font-weight-regular mb-1 text--gradient">
                      {{ $t('faq_page.costs_and_payments.title') }}
                    </h2>
                    <l-expansion-panels
                      :items="costsAndPayments.items"
                      :panels="costsAndPayments.activeItems"
                      link
                    ></l-expansion-panels>
                    <v-divider class="mt-3 mb-2 mt-md-4 mb-md-3"></v-divider>
                  </template>

                  <template v-if="technicalQuestions.items.length">
                    <h2 class="font-weight-regular mb-1 text--gradient">
                      {{ $t('faq_page.technical_questions.title') }}
                    </h2>
                    <l-expansion-panels
                      :items="technicalQuestions.items"
                      :panels="technicalQuestions.activeItems"
                      link
                    ></l-expansion-panels>
                    <v-divider class="mt-3 mb-2 mt-md-4 mb-md-3"></v-divider>
                  </template>

                  <template v-if="faqsForBusiness.items.length">
                    <h2 class="font-weight-regular mb-1 text--gradient">
                      {{ $t('faq_page.faqs_for_business.title') }}
                    </h2>
                    <l-expansion-panels
                      :items="faqsForBusiness.items"
                      :panels="faqsForBusiness.activeItems"
                      link
                    ></l-expansion-panels>
                    <v-divider class="mt-3 mb-2 mt-md-4 mb-md-3"></v-divider>
                  </template>

                  <template v-if="faqsForTeachers.items.length">
                    <h2 class="font-weight-regular mb-1 text--gradient">
                      {{ $t('faq_page.faqs_for_teachers.title') }}
                    </h2>
                    <l-expansion-panels
                      :items="faqsForTeachers.items"
                      :panels="faqsForTeachers.activeItems"
                      link
                    ></l-expansion-panels>
                  </template>
                </div>
                <aside class="faq-page-sidebar mt-3 mt-md-0">
                  <div class="faq-page-sidebar-sticky">
                    <div class="faq-page-sidebar-item">
                      <div class="item-title mb-1">
                        {{ $t('faq_page.welcome_to_langu') }}
                      </div>
                      <div class="item-content body-1 text-md-body-2">
                        <v-img
                          class="float-left mr-1"
                          :src="
                            require('~/assets/images/faq-page/dark_square_rounded-1x.png')
                          "
                          :srcset="`${require('~/assets/images/faq-page/dark_square_rounded-1x.png')}, ${require('~/assets/images/faq-page/dark_square_rounded-2x.png')} 2x`"
                          width="70"
                          height="70"
                        >
                          <template #placeholder>
                            <v-skeleton-loader
                              class="rounded"
                              width="70"
                              height="70"
                              type="image"
                            ></v-skeleton-loader>
                          </template>
                        </v-img>
                        {{ $t('faq_page.description') }}
                      </div>
                    </div>
                    <v-divider class="my-2 my-md-3"></v-divider>
                    <div class="faq-page-sidebar-item">
                      <div class="item-title mb-1">
                        {{ $t('faq_page.get_in_touch') }}
                      </div>
                      <div class="item-content body-1 d-flex flex-column">
                        <div>
                          <a
                            href="mailto:<EMAIL>"
                            class="d-inline-flex align-center mb-1"
                            target="_blank"
                          >
                            <div class="d-flex align-center mr-2">
                              <svg width="26" height="26" viewBox="0 0 20 20">
                                <use
                                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#email-icon`"
                                ></use>
                              </svg>
                            </div>
                            <EMAIL></a
                          >
                        </div>
                        <div>
                          <a
                            href="https://www.facebook.com/heylangu"
                            class="d-inline-flex mb-1"
                            target="_blank"
                            rel="noopener"
                          >
                            <div class="d-flex align-center mr-2">
                              <svg width="26" height="26" viewBox="0 0 32 32">
                                <use
                                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#facebook`"
                                ></use>
                              </svg>
                            </div>

                            Facebook</a
                          >
                        </div>
                        <div>
                          <a
                            href="https://www.linkedin.com/company/langu/"
                            class="d-inline-flex align-center mb-1"
                            target="_blank"
                            rel="noopener"
                          >
                            <div class="d-flex align-center mr-2">
                              <svg width="26" height="26" viewBox="0 0 32 32">
                                <use
                                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#linkedin`"
                                ></use>
                              </svg>
                            </div>

                            LinkedIn</a
                          >
                        </div>
                        <div>
                          <a
                            href="https://www.instagram.com/heylangu/"
                            class="d-inline-flex align-center"
                            target="_blank"
                            rel="noopener"
                          >
                            <div class="d-flex align-center mr-2">
                              <svg width="26" height="26" viewBox="0 0 32 32">
                                <use
                                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#instagram`"
                                ></use>
                              </svg>
                            </div>
                            Instagram</a
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </aside>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </v-col>
  </v-row>
</template>

<script>
import LExpansionPanels from '~/components/LExpansionPanels'
import Youtube from '~/components/Youtube'
export default {
  name: 'FaqPage',
  components: {
    LExpansionPanels,
    Youtube,
  },
  data() {
    return {
      id: 1,
      objectId: {
        id: null,
        property: null,
      },
      activeProperty: null,
      generalQuestions: {
        items: [],
        activeItems: [],
      },
      languageLessons: {
        items: [],
        activeItems: [],
      },
      languTeachers: {
        items: [],
        activeItems: [],
      },
      costsAndPayments: {
        items: [],
        activeItems: [],
      },
      technicalQuestions: {
        items: [],
        activeItems: [],
      },
      faqsForTeachers: {
        items: [],
        activeItems: [],
      },
      faqsForBusiness: {
        items: [],
        activeItems: [],
      },
    }
  },
  head() {
    return {
      title: this.$t('faq_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('faq_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('faq_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('faq_page.seo_description'),
        },
        { hid: 'og:image', property: 'og:image', content: this.previewImage },
        { hid: 'og:image:width', property: 'og:image:width', content: 800 },
        { hid: 'og:image:height', property: 'og:image:height', content: 396 },
        {
          hid: 'og:image:type',
          property: 'og:image:type',
          content: 'image/jpeg',
        },
      ],
      bodyAttrs: {
        class: 'faq-page',
      },
    }
  },
  created() {
    const regex = /#faq\d+/gm
    const { hash } = this.$route

    if (hash.match(regex)) {
      this.objectId.id = +hash.replace('#faq', '')
    }

    this.generateData('general_questions', 2, this.generalQuestions)
    this.generateData('language_lessons', 8, this.languageLessons)
    this.generateData('langu_teachers', 7, this.languTeachers)
    this.generateData('costs_and_payments', 11, this.costsAndPayments)
    this.generateData('technical_questions', 5, this.technicalQuestions)
    this.generateData('faqs_for_business', 10, this.faqsForBusiness)
    this.generateData(
      'faqs_for_teachers',
      this.$i18n.locale === 'es' ? 12 : this.$i18n.locale === 'pl' ? 14 : 15,
      this.faqsForTeachers
    )
  },
  mounted() {
    this.$nextTick(() => {
      const el = document.getElementById(
        `${this.objectId.property}_${this.objectId.id}`
      )

      if (el) {
        this.$vuetify.goTo(el, {
          duration: 0,
          offset: 10,
          easing: 'linear',
        })
      }
    })
  },
  methods: {
    generateData(property, qt, entity) {
      const arr = []

      for (let i = 1; i <= qt; i++) {
        arr.push({
          id: this.id,
          selectorId: `${property}_${this.id}`,
          title: this.$t(`faq_page.${property}.question_${i}`),
          description: this.$t(`faq_page.${property}.answer_${i}`),
        })

        if (this.id === this.objectId.id) {
          this.objectId.property = property

          entity.activeItems.push(i - 1)
        }

        this.id++
      }

      entity.items = arr
    },
  },
}
</script>

<style lang="scss">
@import '~assets/styles/faq-page.scss';
</style>
