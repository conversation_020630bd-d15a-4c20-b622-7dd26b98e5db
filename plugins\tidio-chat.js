import uniqid from 'uniqid'

export const setTidioProperties = (user, $dayjs) => {
  window.tidioChatApi.setContactProperties({
    amount_of_langu_credit: user.amountOfLanguCredit,
    date_of_most_recent_lesson: user.dateOfMostRecentLesson,
    date_registered_on_langu: $dayjs(user.userCreatedDate).format('ll'),
    first_name: user.firstName,
    has_booked_trial: user.isBookedAnyTrialLesson ? 'Yes' : 'No',
    has_student_booked_normal_lessons: user.isHasBookedNotTrialLesson
      ? 'Yes'
      : 'No',
    has_student_finished_a_trial: user.isFinishedAnyTrialLesson ? 'Yes' : 'No',
    last_name: user.lastName,
    number_unscheduled_lessons_student: user.numbersOfUnscheduledLessons,
    number_upcoming_lessons_teacher: user.numberUpcomingLessons,
    student_company_tag: user.studentTags,
    student_has_upcoming_lessons: user.isHasUpcomingLessons ? 'Yes' : 'No',
    students_most_recent_teachers_first_name: user.firstNameLast<PERSON>oughtTeacher,
    students_most_recent_teachers_link:
      user.teacherProfileUrlOfLastPurchasedLesson,
    students_studied_language: user.chosenLanguageToLearn,
    students_studied_language_link: user.teacherListingLanguageURL,
    teacher_wallet_balance: user.withdraw,
    total_GBP_spent_on_langu: user.amountSpentForLessonsGBP,
    total_lessons_purchased: user.numberOfLessonsPurchased,
    ui_language: user.uiLanguage,
    user_currency: user.currency,
    user_type:
      user.userType === 1
        ? 'Student'
        : user.userType === 2
        ? 'Teacher'
        : 'unknown',
    username: user.username,
  })
}

export const setVisitorData = (item) => {
  window.tidioChatApi.setVisitorData(item)
}

export default ({ store, $dayjs, $cookiz }) => {
  const userTidioData = store.state.user.tidioData
  const isUserLogged = store.getters['user/isUserLogged']
  let tidioId = $cookiz.get('tidio_id')

  if (!tidioId) {
    const id = uniqid('tidio-')

    $cookiz.set('tidio_id', id, {
      maxAge: 60 * 60 * 24 * 999,
      domain: process.env.NUXT_ENV_COOKIE_DOMAIN,
    })

    tidioId = id
  }

  const visitorData = {
    distinct_id: tidioId,
  }

  ;(function () {
    function onTidioChatApiReady() {
      if (isUserLogged && userTidioData) {
        setVisitorData({
          distinct_id: userTidioData.userId,
          email: userTidioData.email,
          name: userTidioData.fullName,
        })
        setTidioProperties(userTidioData, $dayjs)
      }
    }

    if (window.tidioChatApi) {
      window.tidioChatApi.on('ready', onTidioChatApiReady)
    } else {
      document.addEventListener('tidioChat-ready', onTidioChatApiReady)
    }
  })()

  setTimeout(function () {
    const d = document
    const s = d.createElement('script')
    s.src = '//code.tidio.co/8hsdoehmiu8dsesxaxgw63j3kpxict2u.js'

    s.async = 1
    d.getElementsByTagName('body')[0].appendChild(s)
  }, 3000)

  document.tidioChatLang = store.state.locale
  document.tidioIdentify = visitorData
}
