<template>
  <section class="home-page-reviews">
    <div class="section-bg">
      <v-img
        :src="require('~/assets/images/homepage/reviews-bg.png')"
        position="center bottom"
        :options="{ rootMargin: '50%' }"
      ></v-img>
    </div>
    <v-container>
      <v-row>
        <v-col class="col-12 py-0">
          <div class="section-head section-head--decorated">
            <h3
              class="section-head-title"
              style="color: #262626; -webkit-text-fill-color: #262626"
            >
              {{ $t('home_page.reviews_section_title') }}
            </h3>
            <div
              class="section-head-subtitle"
              style="max-width: 900px"
              v-html="$t('home_page.reviews_section_subtitle')"
            ></div>
          </div>
        </v-col>
      </v-row>
      <v-row>
        <v-col class="col-12 py-0 d-md-flex justify-end">
          <div class="home-page-reviews-carousel">
            <client-only>
              <VueSlickCarousel v-bind="reviewsCarouselSettings">
                <div
                  v-for="(r, idx) in reviews"
                  :key="idx"
                  class="home-page-reviews-carousel-item"
                >
                  <nuxt-link
                    :to="r.teacherProfileUrl"
                    class="home-page-reviews-carousel-item-helper"
                  >
                    <div class="home-page-reviews-carousel-item-info">
                      <v-avatar width="87" height="87">
                        <v-img
                          :src="getSrcAvatar(r.avatars, 'user_thumb_87x87')"
                          :srcset="
                            getSrcSetAvatar(
                              r.avatars,
                              'user_thumb_87x87',
                              'user_thumb_174x174'
                            )
                          "
                          :options="{ rootMargin: '50%' }"
                        ></v-img>
                      </v-avatar>
                      <div class="flag">
                        <v-img
                          :src="
                            require(`~/assets/images/flags/${r.language.isoCode}.svg`)
                          "
                          width="36"
                          contain
                          :options="{ rootMargin: '50%' }"
                        ></v-img>
                      </div>
                      <div class="rating">
                        <v-img
                          :src="require('~/assets/images/homepage/stars.svg')"
                          width="100%"
                          contain
                          :options="{ rootMargin: '50%' }"
                        ></v-img>
                      </div>
                    </div>
                    <div class="home-page-reviews-carousel-item-text">
                      “{{ r.description | reviewText }}”
                    </div>
                    <div class="home-page-reviews-carousel-item-bottom">
                      <div
                        class="home-page-reviews-carousel-item-bottom-helper"
                      >
                        <v-img
                          :src="
                            require('~/assets/images/homepage/user-icon-4.svg')
                          "
                          width="45"
                          :options="{ rootMargin: '50%' }"
                        ></v-img>
                        {{ r.studentFirstName }},
                        {{
                          $tc('review_lessons_count', r.countFinishedLesson, {
                            language: r.language.name,
                          })
                        }}
                      </div>
                    </div>
                  </nuxt-link>
                </div>
              </VueSlickCarousel>
            </client-only>
          </div>
        </v-col>
      </v-row>
    </v-container>
  </section>
</template>

<script>
import VueSlickCarousel from 'vue-slick-carousel'
import 'vue-slick-carousel/dist/vue-slick-carousel.css'

import Avatars from '~/mixins/Avatars.vue'

export default {
  name: 'ReviewSection',
  components: {
    VueSlickCarousel,
  },
  filters: {
    reviewText(val) {
      let str = val

      if (str.length > 322) {
        const arr = str.substring(0, 322).split(' ')

        arr.pop()

        str = arr.join(' ') + ' ...'
      }

      return str
    },
  },
  mixins: [Avatars],
  props: {
    reviews: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      reviewsCarouselSettings: {
        dots: false,
        focusOnSelect: true,
        infinite: true,
        slidesToShow: 3,
        slidesToScroll: 1,
        vertical: true,
        verticalSwiping: true,
        speed: 800,
        responsive: [
          {
            breakpoint: 992,
            settings: {
              vertical: false,
              verticalSwiping: false,
              slidesToShow: 1,
            },
          },
        ],
      },
    }
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.home-page-reviews {
  position: relative;
  margin-top: 157px;
  padding-bottom: 45px;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    padding-bottom: 110px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    margin-top: 130px;
    padding-bottom: 100px;

    .section-bg {
      top: 50%;
      height: 84%;
      transform: translateY(-50%);

      .v-image__image {
        background-position: 85% bottom !important;
      }
    }
  }

  @media only screen and (max-width: $xxs-and-down) {
    margin-top: 80px;
  }

  &-carousel {
    max-width: 898px;
    margin-top: 30px;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      max-width: 100%;
      padding-left: 90px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: 620px;
      margin-left: auto;
      margin-right: auto;
      padding-left: 0;
    }

    @media only screen and (max-width: $xsm-and-down) {
      margin-top: 15px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      width: calc(100% + 15px);
    }

    &-item {
      position: relative;
      max-width: 680px;
      padding: 16px 0 38px 23px;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        padding: 16px 15px 0;
      }

      @media only screen and (max-width: $xsm-and-down) {
        max-width: 100%;
        padding-top: 25px;

        &::before {
          content: '';
          position: absolute;
          top: 43px;
          right: 48px;
          width: 40px;
          height: 36px;
          background-size: cover;
          background-repeat: no-repeat;
          background-image: url('~assets/images/quotes.svg');
          opacity: 0.3;
        }
      }

      @media only screen and (min-width: $xsm-and-up) {
        &::before {
          display: none;
        }
      }

      &-helper {
        position: relative;
        display: block;
        height: 100%;
        padding: 24px 36px 105px 105px;
        background-color: var(--v-darkLight-base);
        border-radius: 24px;
        color: #fff !important;
        text-decoration: none;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding: 95px 32px 105px;
          box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);

          &::before {
            content: '';
            position: absolute;
            top: 18px;
            right: 32px;
            width: 40px;
            height: 36px;
            background-size: cover;
            background-repeat: no-repeat;
            background-image: url('~assets/images/quotes.svg');
            opacity: 0.3;
          }
        }

        @media only screen and (max-width: $xxs-and-down) {
          padding: 90px 15px 90px;
          box-shadow: none;

          &::before {
            right: 18px;
          }
        }
      }

      &-info {
        position: absolute;
        left: -15px;
        top: -15px;

        .flag {
          position: absolute;
          bottom: 4px;
          right: -16px;
          border-radius: 6px;
          overflow: hidden;
        }

        .rating {
          position: absolute;
          left: 30px;
          bottom: -18px;
          width: 60px;

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            left: 102px;
            bottom: 38px;
            width: 88px;
          }
        }
      }

      &-text {
        min-height: 80px;
        font-size: 16px;
        font-weight: 300;
        line-height: 1.6;
      }

      &-bottom {
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 40px;
        padding: 0 36px 0 105px;
        font-size: 20px;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding: 0 32px;
        }

        @media only screen and (max-width: $xxs-and-down) {
          padding: 0 15px;
        }

        &-helper {
          position: relative;
          padding: 0 50px 0 60px;

          @media only screen and (max-width: $xxs-and-down) {
            padding: 0 0 0 60px;
            font-size: 14px;
          }

          &::before {
            content: '';
            position: absolute;
            top: -16px;
            right: 0;
            width: 40px;
            height: 36px;
            background-size: cover;
            background-repeat: no-repeat;
            background-image: url('~assets/images/quotes.svg');
            opacity: 0.3;

            @media #{map-get($display-breakpoints, 'sm-and-down')} {
              display: none;
            }
          }

          .v-image {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }
  }

  .slick-slide:nth-child(odd) > div {
    display: flex !important;
    justify-content: flex-end;
  }

  .slick-arrow {
    position: absolute;
    top: 27%;
    transform: translateY(-50%);

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      transform: translateX(-50%);
    }
  }

  .slick-prev,
  .slick-next {
    left: calc(898px - 1594px);

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      left: calc(898px - 1090px);
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      left: -115px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      top: auto;
      bottom: -100px;
    }
  }

  .slick-prev {
    @media #{map-get($display-breakpoints, 'md-and-up')} {
      transform: translateY(calc(-50% - 45px));
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      left: calc(50% - 50px);
    }

    &::before {
      top: 50%;
      left: calc(50% - 2px);
      transform: translate(-50%, -50%);

      @media #{map-get($display-breakpoints, 'md-and-up')} {
        top: calc(50% - 2px);
        left: 50%;
        transform: translate(-50%, -50%) rotate(90deg);
      }
    }
  }

  .slick-next {
    @media #{map-get($display-breakpoints, 'md-and-up')} {
      transform: translateY(calc(-50% + 45px));
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      left: calc(50% + 50px);
    }

    &::before {
      top: 50%;
      left: calc(50% + 2px);
      transform: translate(-50%, -50%) rotate(180deg);

      @media #{map-get($display-breakpoints, 'md-and-up')} {
        top: calc(50% + 2px);
        left: 50%;
        transform: translate(-50%, -50%) rotate(270deg);
      }
    }
  }
}
</style>
