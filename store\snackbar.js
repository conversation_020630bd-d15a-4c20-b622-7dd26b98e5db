const defaultItem = () => ({
  timeout: 2000,
  successMessage: 'your_settings_have_been_updated',
  errorMessage: 'data_is_incorrect',
})

export const state = () => ({
  item: defaultItem(),
  isShown: false,
})

export const mutations = {
  SET_ITEM(state, item = {}) {
    state.item = { ...state.item, ...item }
  },
  SET_IS_SHOWN(state, payload) {
    state.isShown = payload

    if (!payload) {
      state.item = defaultItem()
    }
  },
}

export const actions = {
  success({ commit }, payload) {
    let data = { type: 'success' }

    if (payload) {
      data = { ...payload, ...data }
    }

    commit('SET_ITEM', data)
    commit('SET_IS_SHOWN', true)
  },
  error({ commit }, payload) {
    let data = { type: 'error' }

    if (payload) {
      data = { ...payload, ...data }
    }

    commit('SET_ITEM', data)
    commit('SET_IS_SHOWN', true)
  },
}
