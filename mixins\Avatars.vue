<script>
export default {
  methods: {
    getSrcAvatar(images, property, defaultImage = 'avatar.png') {
      return images?.[property]
        ? images[property]
        : require(`~/assets/images/homepage/${defaultImage}`)
    },
    getSrcSetAvatar(images, property1, property2) {
      return images?.[property1] && images?.[property2]
        ? `
            ${images[property1]} 1x,
            ${images[property2]} 2x,
          `
        : ''
    },
  },
}
</script>
