export const state = () => ({
  items: [],
})

export const mutations = {
  SET_ITEMS: (state, payload) => {
    state.items = payload
  },
}

export const getters = {}

export const actions = {
  getHomePageLanguages({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/get-publish-languages`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        if (Array.isArray(data)) {
          const arr = data.map((item) => item.language)

          commit('SET_ITEMS', arr)

          return arr
        }

        return []
      })
      .catch((e) => console.log(e))
  },
}
