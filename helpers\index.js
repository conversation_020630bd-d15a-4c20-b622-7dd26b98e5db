const getSlugByString = (str) => {
  return str
    .toLowerCase()
    .replace(/[àáâãäåą]/g, 'a')
    .replace(/[çć]/g, 'c')
    .replace(/[èéêëę]/g, 'e')
    .replace(/[ìíî<PERSON>]/g, 'i')
    .replace(/[ñń]/g, 'n')
    .replace(/[òóôõö]/g, 'o')
    .replace(/[ùúûü]/g, 'u')
    .replace(/[ýÿ]/g, 'y')
    .replace(/ł/g, 'l')
    .replace(/ś/g, 's')
    .replace(/[źż]/g, 'z')
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
}

const getFileExtension = (file) => {
  return /[.]/.exec(file) ? /[^.]+$/.exec(file)[0].toLowerCase() : undefined
}

const debounce = (fn, delay) => {
  let timeoutID = null

  return function () {
    clearTimeout(timeoutID)

    const args = arguments
    const that = this

    timeoutID = setTimeout(function () {
      fn.apply(that, args)
    }, delay)
  }
}

const switchFullScreen = (isEnabled, elementId = 'video-window') => {
  if (isEnabled) {
    let elem = document.getElementById(elementId)

    if (elem.requestFullscreen) {
      elem.requestFullscreen()
    } else if (elem.mozRequestFullScreen) {
      /* Firefox */
      elem.mozRequestFullScreen()
    } else if (elem.webkitRequestFullscreen) {
      /* Chrome, Safari & Opera */
      elem.webkitRequestFullscreen()
    } else if (elem.msRequestFullscreen) {
      /* IE/Edge */
      elem = window.top.document.body // To break out of frame in IE
      elem.msRequestFullscreen()
    }
  } else if (document.exitFullscreen) {
    document.exitFullscreen()
  } else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen()
  } else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen()
  } else if (document.msExitFullscreen) {
    window.top.document.msExitFullscreen()
  }
}

const setStyleVariable = (property, value, selectors = ':root') => {
  const el = document.querySelector(selectors)

  el.style.setProperty(property, value)
}

const getPrice = (val) => {
  let v = val

  if (!v || typeof v === 'string') {
    v = parseInt(v, 10)
  }

  return isNaN(v) ? '0.00' : v.toFixed(2)
}

function toInitialCase(strValue) {
  // Check if the argument is a string
  if (typeof strValue !== 'string') {
    throw new TypeError(
      'Failed to convert string to Initial Case. Check method "toInitialCase".'
    )
  }

  // Check if the string is empty
  if (strValue.trim() === '') {
    return '' // Return an empty string if input is empty or contains only spaces
  }

  return strValue
    .toLowerCase() // Convert the entire string to lowercase
    .split(' ') // Split the string into words by spaces
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word
    .join(' ') // Join the words back together with spaces
}

function getMinimumPrice(currencyCode, lesseonLength) {
  const pricingMap = {
    EUR: { 30: 7, 60: 11, 90: 16, 120: 21 },
    GBP: { 30: 6, 60: 10, 90: 15, 120: 20 },
    PLN: { 30: 30, 60: 50, 90: 70, 120: 85 },
    USD: { 30: 8, 60: 12, 90: 17, 120: 22 },
    AUD: { 30: 12, 60: 20, 90: 28, 120: 36 },
    CAD: { 30: 11, 60: 18, 90: 25, 120: 32 },
  }

  return pricingMap[`${currencyCode}`][`${lesseonLength}`] || 10
}

/**
 * Format currency value according to locale conventions
 * @param {number|string} amount - The amount to format
 * @param {string} currencyCode - Currency code (EUR, USD, GBP, PLN, etc.)
 * @param {string} locale - Locale code (en, es, pl, etc.)
 * @param {boolean} includeSymbol - Whether to include currency symbol (default: true)
 * @returns {string} Formatted currency string
 */
function formatCurrencyLocale(
  amount,
  currencyCode = 'EUR',
  locale = 'en',
  includeSymbol = true
) {
  // Convert amount to number
  const numericAmount = Number(amount)
  if (isNaN(numericAmount)) {
    return includeSymbol ? `${getCurrencySymbol(currencyCode)}0.00` : '0.00'
  }

  // Map locale codes to proper Intl locale identifiers
  const localeMap = {
    en: 'en-US',
    es: 'es-ES',
    pl: 'pl-PL',
  }

  const intlLocale = localeMap[locale] || locale || 'en-US'

  try {
    // Format number according to locale without currency symbol
    const formatter = new Intl.NumberFormat(intlLocale, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })

    const formattedNumber = formatter.format(numericAmount)

    if (!includeSymbol) {
      return formattedNumber
    }

    // Get currency symbol
    const symbol = getCurrencySymbol(currencyCode)

    // Position symbol according to currency conventions
    if (currencyCode === 'PLN') {
      return `${formattedNumber} ${symbol}`
    } else {
      return `${symbol}${formattedNumber}`
    }
  } catch (error) {
    // Fallback to basic formatting if Intl fails
    const fallbackFormatted = numericAmount.toFixed(2)
    if (!includeSymbol) {
      return fallbackFormatted
    }

    const symbol = getCurrencySymbol(currencyCode)
    return currencyCode === 'PLN'
      ? `${fallbackFormatted} ${symbol}`
      : `${symbol}${fallbackFormatted}`
  }
}

/**
 * Get currency symbol for a given currency code
 * @param {string} currencyCode - Currency code (EUR, USD, GBP, PLN, etc.)
 * @returns {string} Currency symbol
 */
function getCurrencySymbol(currencyCode) {
  const currencySymbols = {
    EUR: '€',
    USD: '$',
    GBP: '£',
    PLN: 'zł',
    CAD: 'C$',
    AUD: 'A$',
  }
  return currencySymbols[currencyCode] || currencyCode
}

export {
  getSlugByString,
  getFileExtension,
  debounce,
  switchFullScreen,
  setStyleVariable,
  getPrice,
  toInitialCase,
  getMinimumPrice,
  formatCurrencyLocale,
  getCurrencySymbol,
}
