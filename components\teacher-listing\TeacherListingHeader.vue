<template>
  <div>
    <div class="desktop-only">
      <div class="teacher-listing-header">
        <!-- <div class="search-result">
          <template>
            <div class="teachers-quantity">
              {{ $tc('teachers_count', teachersQuantity) }}
            </div>
            <div class="reset-all" @click="resetAllClickHandler">
              {{ $t('clear_all') }}
            </div>
          </template>
        </div> -->
        <div v-if="activeFilters.length" class="active-filters">
          <div class="chips">
            <l-chip
              v-if="languageChip"
              :clickable="true"
              :label="languageChip.name"
              light
              @click:close="resetLanguage"
            ></l-chip>
            <l-chip
              v-if="motivationChip"
              :clickable="true"
              :icon="motivationChip.icon"
              :label="motivationChip.motivationName"
              light
              @click:close="resetMotivation"
            ></l-chip>
            <template v-if="specialityChips.length > 0">
              <l-chip
                v-for="speciality in specialityChips"
                :key="`s-${speciality.id}`"
                :clickable="true"
                light
                :label="speciality.name"
                @click:close="resetSpeciality(speciality)"
              ></l-chip>
            </template>
            <l-chip
              v-if="proficiencyLevelChip"
              :clickable="true"
              :label="proficiencyLevelChip.name"
              light
              @click:close="resetProficiencyLevel"
            ></l-chip>
            <l-chip
              v-if="teacherPreferenceChip && teacherPreferenceChip.id === 1"
              :clickable="true"
              :label="$t('native_speaker')"
              light
              @click:close="resetTeacherPreference"
            ></l-chip>
            <l-chip
              v-if="teacherMatchLanguageChip"
              :clickable="true"
              :label="`${$t('also_speaks')} ${teacherMatchLanguageChip.name}`"
              light
              @click:close="resetTeacherPreference"
            ></l-chip>
            <template v-if="dateChips.length > 0">
              <l-chip
                v-for="date in dateChips"
                :key="`d-${date.id}`"
                :clickable="true"
                light
                :label="$t(date.name)"
                @click:close="resetDay(date)"
              ></l-chip>
            </template>
            <template v-if="timeChips.length > 0">
              <l-chip
                v-for="time in timeChips"
                :key="`t-${time.id}`"
                :clickable="true"
                light
                :label="$t(time.name)"
                @click:close="resetTime(time)"
              ></l-chip>
            </template>
            <l-chip
              v-if="currencyChip"
              :clickable="true"
              :label="currencyChip.isoCode"
              light
              @click:close="resetCurrency"
            ></l-chip>
            <l-chip
              v-if="searchChip"
              :clickable="true"
              :label="searchChip.name"
              light
              @click:close="resetSearchQuery"
            ></l-chip>
            <l-chip
              v-if="tagsChip"
              :clickable="true"
              :label="tagsChip.name"
              light
              @click:close="resetSearchQuery"
            ></l-chip>
          </div>
        </div>
        <!-- <div v-if="feedbackTags.length > 0" class="tag-filters">
          <div class="tag-filters-title font-weight-medium">
            {{ $t('show_teachers_who_are') }}
          </div>
          <div class="tag-filters-list d-flex flex-wrap">
            <l-chip
              :clickable="true"
              v-for="tag in feedbackTags"
              :key="tag.id"
              :label="tag.name"
              light
              :close-btn="selectedFeedbackTag.id === tag.id"
              transparent
              clickable
              :item-id="tag.id"
              :selected-ids="[selectedFeedbackTag.id]"
              @click:close="resetFeedbackTag"
              @click.native="feedbackTagClickHandler(tag)"
            ></l-chip>
          </div>
        </div> -->

        <div class="teacher-listing-header-top d-flex">
          <div class="search-wrap">
            <search-input
              v-model.trim="searchQuery_"
              placeholder="search_for_names_or_keywords"
              class="search-input--inner-border"
              @submit="submitSearchForm"
            ></search-input>
            <div v-if="hasFiltersApplied" class="clear-all-wrapper">
              <a
                href="#"
                class="clear-all-link"
                @click.prevent="clearAllFilters"
              >
                {{ $t('clear_all') }}
              </a>
            </div>
          </div>
          <div class="teachers-sorting">
            <div class="d-flex align-center">
              <span>{{ $t('sort_by') }}:</span>
              <div
                class="teachers-sorting-select"
                :style="{
                  maxWidth: sortingSelectWidth,
                  minWidth: sortingSelectWidth,
                }"
              >
                <select-input
                  v-model="selectedSorting"
                  :items="sortByItems"
                  height="auto"
                  class="sort-by-select"
                  :hide-selected="hasSelectedFeedbackTag"
                  :dropdown-class="'custom-class-999'"
                  :menu-props="{ contentClass: 'sort-by-dropdown-menu' }"
                >
                </select-input>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mobile-only">
      <div class="teacher-listing-header">
        <!-- <div class="search-result">
          <template>
            <div class="teachers-quantity">
              {{ $tc('teachers_count', teachersQuantity) }}
            </div>
            <div class="reset-all" @click="resetAllClickHandler">
              {{ $t('clear_all') }}
            </div>
          </template>
        </div> -->
        <div v-if="activeFilters.length" class="active-filters">
          <div class="chips">
            <l-chip
              v-if="languageChip"
              :clickable="true"
              :label="languageChip.name"
              light
              @click:close="resetLanguage"
            ></l-chip>
            <l-chip
              v-if="motivationChip"
              :clickable="true"
              :icon="motivationChip.icon"
              :label="motivationChip.motivationName"
              light
              @click:close="resetMotivation"
            ></l-chip>
            <template v-if="specialityChips.length > 0">
              <l-chip
                v-for="speciality in specialityChips"
                :key="`s-${speciality.id}`"
                :clickable="true"
                light
                :label="speciality.name"
                @click:close="resetSpeciality(speciality)"
              ></l-chip>
            </template>
            <l-chip
              v-if="proficiencyLevelChip"
              :clickable="true"
              :label="proficiencyLevelChip.name"
              light
              @click:close="resetProficiencyLevel"
            ></l-chip>
            <l-chip
              v-if="teacherPreferenceChip && teacherPreferenceChip.id === 1"
              :clickable="true"
              :label="$t('native_speaker')"
              light
              @click:close="resetTeacherPreference"
            ></l-chip>
            <l-chip
              v-if="teacherMatchLanguageChip"
              :clickable="true"
              :label="`${$t('also_speaks')} ${teacherMatchLanguageChip.name}`"
              light
              @click:close="resetTeacherPreference"
            ></l-chip>
            <template v-if="dateChips.length > 0">
              <l-chip
                v-for="date in dateChips"
                :key="`d-${date.id}`"
                :clickable="true"
                light
                :label="$t(date.name)"
                @click:close="resetDay(date)"
              ></l-chip>
            </template>
            <template v-if="timeChips.length > 0">
              <l-chip
                v-for="time in timeChips"
                :key="`t-${time.id}`"
                :clickable="true"
                light
                :label="$t(time.name)"
                @click:close="resetTime(time)"
              ></l-chip>
            </template>
            <l-chip
              v-if="currencyChip"
              :clickable="true"
              :label="currencyChip.isoCode"
              light
              @click:close="resetCurrency"
            ></l-chip>
            <l-chip
              v-if="searchChip"
              :clickable="true"
              :label="searchChip.name"
              light
              @click:close="resetSearchQuery"
            ></l-chip>
            <l-chip
              v-if="tagsChip"
              :clickable="true"
              :label="tagsChip.name"
              light
              @click:close="resetSearchQuery"
            ></l-chip>
          </div>
        </div>

        <div class="teacher-listing-header-top d-flex">
          <div class="teachers-sorting">
            <div class="d-flex align-center">
              <span>{{ $t('sort_by') }}:</span>
              <div
                class="teachers-sorting-select"
                :style="{
                  maxWidth: sortingSelectWidth,
                  minWidth: sortingSelectWidth,
                }"
              >
                <select-input
                  v-model="selectedSorting"
                  :items="sortByItems"
                  height="auto"
                  class="sort-by-select"
                  :hide-selected="hasSelectedFeedbackTag"
                >
                </select-input>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LChip from '~/components/LChip'
import SearchInput from '~/components/form/SearchInput'
import SelectInput from '~/components/form/SelectInput'

export default {
  name: 'TeacherListingResultHeader',
  components: { LChip, SearchInput, SelectInput },
  data() {
    return {
      chevronIcon: `${require('~/assets/images/icon-sprite.svg')}#chevron-down`,
      searchQuery_: null,
    }
  },
  computed: {
    sortByItems() {
      return this.$store.getters['teacher_filter/sortByItems']
    },
    activeFilters() {
      return this.$store.state.teacher_filter.activeFilters
    },
    teachersQuantity() {
      return this.$store.state.teacher.totalQuantity
    },
    feedbackTags() {
      return this.$store.getters['teacher_filter/feedbackTags']
    },
    languageChip() {
      return this.$store.getters['teacher_filter/languageChip']
    },
    motivationChip() {
      return this.$store.getters['teacher_filter/motivationChip']
    },
    specialityChips() {
      return this.$store.getters['teacher_filter/specialityChips']
    },
    proficiencyLevelChip() {
      return this.$store.getters['teacher_filter/proficiencyLevelChip']
    },
    teacherPreferenceChip() {
      return this.$store.getters['teacher_filter/teacherPreferenceChip']
    },
    teacherMatchLanguageChip() {
      return this.$store.getters['teacher_filter/teacherMatchLanguageChip']
    },
    dateChips() {
      return this.$store.getters['teacher_filter/dateChips']
    },
    timeChips() {
      return this.$store.getters['teacher_filter/timeChips']
    },
    currencyChip() {
      return this.$store.getters['teacher_filter/currencyChip']
      // return this.$store.getters['teacher_filter/currencyChip']?.isoCode ===
      // 'EUR'
      // ? false
      // : this.$store.getters['teacher_filter/currencyChip']
    },
    searchChip() {
      return this.$store.getters['teacher_filter/searchChip']
    },
    tagsChip() {
      return this.$store.getters['teacher_filter/selectedFeedbackTag']
    },
    selectedSorting: {
      get() {
        return this.$store.getters['teacher_filter/selectedSorting']
      },
      set(item) {
        this.$store.commit('teacher_filter/SET_SELECTED_SORTING', item)
        this.fetchData()
      },
    },
    selectedFeedbackTag: {
      get() {
        return this.$store.getters['teacher_filter/selectedFeedbackTag'] || {}
      },
      set(item) {
        this.$store.commit('teacher_filter/SET_SELECTED_FEEDBACK_TAG', item)
      },
    },
    hasSelectedFeedbackTag() {
      return this.$store.getters['teacher_filter/hasSelectedFeedbackTag']
    },
    searchQuery: {
      get() {
        return this.$store.getters['teacher_filter/searchQuery']
      },
      set(value) {
        this.$store.commit('teacher_filter/SET_SEARCH_QUERY', {
          searchQuery: value,
        })
      },
    },
    sortingSelectWidth() {
      if (!this.selectedSorting) return '180px'

      const selectedText = this.$t(this.selectedSorting.name)
      const baseWidth = 140
      const charWidth = this.$i18n.locale === 'es' ? 8 : 10 // Spanish has longer text
      const calculatedWidth = baseWidth + selectedText.length * charWidth

      // Set min and max bounds based on language
      const minWidth = this.$i18n.locale === 'es' ? 180 : 140
      const maxWidth = this.$i18n.locale === 'es' ? 260 : 200

      return Math.min(Math.max(calculatedWidth, minWidth), maxWidth) + 'px'
    },
    hasFiltersApplied() {
      // Check if there are any active filters
      const hasActiveFilters =
        this.activeFilters && this.activeFilters.length > 0

      // Check if there's a search query
      const hasSearchQuery = this.searchQuery && this.searchQuery.trim() !== ''

      // Check if language filter is applied (not "All Languages")
      const hasLanguageFilter =
        this.$store.state.teacher_filter.selectedLanguage !== null

      // Check if any other filters are applied
      const hasMotivation =
        this.$store.state.teacher_filter.selectedMotivation !== null
      const hasSpecialities =
        this.$store.state.teacher_filter.selectedSpecialities.length > 0
      const hasProficiencyLevel =
        this.$store.state.teacher_filter.selectedProficiencyLevel !== null
      const hasTeacherPreference =
        this.$store.state.teacher_filter.selectedTeacherPreference !== null
      const hasDays = this.$store.state.teacher_filter.selectedDays.length > 0
      const hasTimes = this.$store.state.teacher_filter.selectedTimes.length > 0
      const hasFeedbackTag =
        this.$store.state.teacher_filter.selectedFeedbackTag !== null

      return (
        hasActiveFilters ||
        hasSearchQuery ||
        hasLanguageFilter ||
        hasMotivation ||
        hasSpecialities ||
        hasProficiencyLevel ||
        hasTeacherPreference ||
        hasDays ||
        hasTimes ||
        hasFeedbackTag
      )
    },
  },
  beforeMount() {
    this.searchQuery_ = this.searchQuery
  },
  methods: {
    resetLanguage() {
      this.$store.commit('teacher_filter/RESET_SELECTED_LANGUAGE')
      if (process.client && typeof window !== 'undefined') {
        window.sessionStorage.setItem('isLanguageFilterRemoved', 'true')
      }
      this.fetchData()
    },
    resetSpeciality(item) {
      this.$store.commit('teacher_filter/UPDATE_SELECTED_SPECIALITIES', item)
      this.fetchData()
    },
    resetMotivation() {
      this.$store.commit('teacher_filter/RESET_SELECTED_MOTIVATION')
      this.fetchData()
    },
    resetProficiencyLevel() {
      this.$store.commit('teacher_filter/RESET_SELECTED_PROFICIENCY_LEVEL')
      this.fetchData()
    },
    resetTeacherPreference() {
      this.$store.commit('teacher_filter/RESET_SELECTED_TEACHER_PREFERENCE')
      this.fetchData()
    },
    resetDay(item) {
      this.$store.commit('teacher_filter/UPDATE_SELECTED_DAYS', item)
      this.fetchData()
    },
    resetTime(item) {
      this.$store.commit('teacher_filter/UPDATE_SELECTED_TIMES', item)
      this.fetchData()
    },
    resetCurrency() {
      this.$store.dispatch('teacher_filter/resetCurrency')
      this.$store.dispatch('teacher_filter/setCurrencyByUser', {
        setByUser: false,
      })
      this.fetchData()
    },
    resetFeedbackTag() {
      this.$store.commit('teacher_filter/RESET_SELECTED_FEEDBACK_TAG')
      this.fetchData()
    },
    feedbackTagClickHandler(tag) {
      this.selectedFeedbackTag = tag

      this.fetchData()
    },
    submitSearchForm() {
      this.searchQuery = this.searchQuery_

      this.fetchData()
    },
    resetSearchQuery() {
      this.searchQuery = null
      this.searchQuery_ = null

      this.fetchData()
    },
    resetAllClickHandler() {
      if (process.client && typeof window !== 'undefined') {
        window.sessionStorage.setItem('active-filter-panel', '0')
      }

      this.$router.push({
        path: '/teacher-listing',
        params: {},
        query: {},
      })
    },
    fetchData() {
      this.$store.commit('teacher_filter/SET_NEED_UPDATE_TEACHERS', true)
    },
    clearAllFilters() {
      // Reset all filters to original state
      this.$store.commit('teacher_filter/RESET_SELECTED_LANGUAGE')
      this.$store.commit('teacher_filter/RESET_SELECTED_MOTIVATION')
      this.$store.commit('teacher_filter/RESET_SELECTED_SPECIALITIES')
      this.$store.commit('teacher_filter/RESET_SELECTED_PROFICIENCY_LEVEL')
      this.$store.commit('teacher_filter/RESET_SELECTED_TEACHER_PREFERENCE')
      this.$store.commit('teacher_filter/RESET_SELECTED_DAYS')
      this.$store.commit('teacher_filter/RESET_SELECTED_TIMES')
      this.$store.commit('teacher_filter/RESET_SELECTED_FEEDBACK_TAG')

      // Reset search query
      this.searchQuery = null
      this.searchQuery_ = null

      // Reset currency to default
      this.$store.dispatch('teacher_filter/resetCurrency')
      this.$store.dispatch('teacher_filter/setCurrencyByUser', {
        setByUser: false,
      })

      // Mark that language filter was removed so English doesn't auto-select
      if (process.client && typeof window !== 'undefined') {
        window.sessionStorage.setItem('isLanguageFilterRemoved', 'true')
      }

      // Navigate to clean URL and fetch data
      this.$router.push({
        path: '/teacher-listing',
        params: {},
        query: {},
      })
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.teacher-listing-header {
  margin-top: 30px;

  @media #{map-get($display-breakpoints, 'xs-only')} {
    margin-top: 28px;
  }

  &-top {
    align-items: center;
    justify-content: space-between;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      flex-wrap: wrap;
    }

    @media only screen and (max-width: $xsm-and-down) {
      flex-direction: column;
      align-items: flex-start !important;
    }

    .search-wrap {
      padding-right: 24px;

      @media only screen and (min-width: $xsm-and-up) {
        flex: 1 0 auto;
        display: flex;
        align-items: center;
      }

      @media only screen and (max-width: $mac-13-and-down) {
        padding-right: 18px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        padding-right: 0;
      }

      @media only screen and (max-width: $xsm-and-down) {
        width: 100%;
      }

      .v-form {
        width: 100%;
        max-width: 580px;
        min-width: 310px;

        @media only screen and (max-width: $xsm-and-down) {
          max-width: 100%;
          min-width: auto;
        }
      }
    }
  }

  .search-result {
    flex: 1 0 auto;
    min-width: 152px;
    font-size: 24px;
    display: flex;

    .reset-all {
      display: flex;
      justify-content: center;
      place-items: center;
      color: var(--v-error-darken1) !important;
      margin-top: 5px;
      margin-left: 20px;
    }

    @media only screen and (max-width: $mac-13-and-down) {
      padding-left: 12px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      padding-left: 20px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 0;
    }

    .reset-all {
      margin-right: 32px;
      font-size: 14px;
      line-height: 1.2;
      font-weight: 700;
      letter-spacing: 0.3px;
      color: var(--v-orange-base);
      cursor: pointer;

      @media only screen and (max-width: $xsm-and-down) {
        margin-right: 15px;
      }
    }

    .teachers-quantity {
      font-weight: 600;

      @media #{map-get($display-breakpoints, 'xs-only')} {
        font-size: 17px;
      }

      @media only screen and (max-width: $xsm-and-down) {
        font-size: 18px;
      }
    }
  }

  & > .search-result {
    margin-bottom: 14px;
  }

  .teachers-sorting {
    display: flex;
    align-items: center;
    color: linear-gradient(
      -75deg,
      var(--v-success-base),
      var(--v-primary-base)
    );
    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-top: 14px;
    }

    & > div > span {
      display: inline-block;
      padding-right: 11px;
      font-size: 18px;
      letter-spacing: 0.3px;
      line-height: 1.1;
      font-weight: bold;

      @media #{map-get($display-breakpoints, 'xs-only')} {
        max-width: unset !important;
        font-weight: 600;
      }
    }

    &-select {
      max-width: 180px;
      min-width: 140px;

      .v-select {
        .v-select__selections {
          font-size: 18px !important;
          font-weight: 700 !important;
          color: var(--v-success-base);
          background: linear-gradient(
            -75deg,
            var(--v-success-base),
            var(--v-primary-base)
          );
          background: -webkit-linear-gradient(
            -75deg,
            var(--v-success-base),
            var(--v-primary-base)
          );
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          letter-spacing: 0.3px;
          font-weight: bold;
          padding-right: 8px !important;
        }

        .v-input__append-inner {
          color: var(--v-orange-base) !important;
          margin-left: 0 !important;
        }

        .v-input__control {
          min-height: auto !important;
        }
      }
    }
  }

  .active-filters {
    .chips {
      display: flex;
      flex-wrap: wrap;

      .chip {
        margin: 0 24px 24px 0;
        border: none;
      }
    }
  }

  .tag-filters {
    margin-top: 22px;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-top: 10px;
    }

    &-title {
      font-size: 16px;

      @media #{map-get($display-breakpoints, 'xs-only')} {
        font-size: 14px;
      }
    }

    &-list {
      margin-top: -6px;

      & > div {
        margin: 12px 12px 0 0;

        &.unselected {
          cursor: pointer;
        }
      }
    }
  }
}

.pl {
  .teacher-listing-header .teachers-sorting {
    &-select {
      max-width: 170px;
      min-width: 130px;
    }
  }
}

.es {
  .teacher-listing-header .teachers-sorting {
    &-select {
      max-width: 240px;
      min-width: 180px;

      .v-select .v-select__selections {
        font-size: 16px !important;
        padding-right: 6px !important;
      }
    }

    span {
      font-size: 16px;
      padding-right: 8px;
    }
  }
}

.teacher-listing-header-top {
  margin-top: 10px;
}

.teachers-sorting {
  font-size: 20px;
}

.search-wrap {
  position: relative;
  display: flex;
  align-items: center;
  gap: 15px;
}

.clear-all-wrapper {
  flex-shrink: 0;
}

.clear-all-link {
  color: var(--v-orange-base) !important;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  transition: color 0.2s ease;

  &:hover {
    color: var(--v-orange-base);
    text-decoration: underline;
  }

  @media screen and (max-width: 768px) {
    font-size: 13px;
  }
}

.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media screen and (max-width: 768px) {
  .mobile-only {
    display: block;
    margin-top: 20px;
  }

  .desktop-only {
    display: none;
  }
}
@media only screen and (max-width: 767px) {
  .search-input .v-input {
    border-radius: 50px !important;
  }
  .teacher-listing-header-title {
    border: none !important;
  }
}

.sort-by-dropdown-menu {
  margin-top: 0px !important;
}
</style>
