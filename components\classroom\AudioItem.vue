<template>
  <classroom-container
    :asset="file"
    :child-header-height="80"
    :handles="['mr', 'ml']"
  >
    <classroom-container-header :file="file" :title="file.asset.displayName">
      <div :id="`audio-${file.id}`" ref="childComponent">
        <audio :id="`player-${file.id}`" crossorigin playsinline>
          <source :src="audioUrl" :type="mimeType" />
        </audio>
      </div>
    </classroom-container-header>
  </classroom-container>
</template>

<script>
import Plyr from 'plyr'
import { getFileExtension } from '~/helpers'
import { defaultWidth } from '~/helpers/constants'
import ClassroomContainer from '~/components/classroom/ClassroomContainer'
import ClassroomContainerHeader from '~/components/classroom/ClassroomContainerHeader'

export default {
  name: 'AudioItem',
  components: { ClassroomContainer, ClassroomContainerHeader },
  props: {
    file: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      currentTime: 0,
      changedBySocket: false,
      player: {
        config: {
          title: '',
        },
      },
    }
  },
  computed: {
    width() {
      return this.file.asset?.width ?? 0
    },
    height() {
      return this.file.asset?.height ?? 0
    },
    zoomIndex() {
      return this.$store.getters['classroom/zoomAsset']?.asset?.zoomIndex ?? 1
    },
    isSocketConnected() {
      return this.$store.state.socket.isConnected
    },
    audioUrl() {
      return `${process.env.NUXT_ENV_URL}${this.file.asset.path}`
    },
    mimeType() {
      const ext = getFileExtension(this.file.asset.path)

      let type = 'audio/mpeg'

      if (ext === 'wav') {
        type = 'audio/wav'
      }

      return type
    },
    isUserInteracted() {
      return this.$store.state.classroom.isUserInteracted
    },
  },
  mounted() {
    const options = {
      controls: [
        'play',
        'progress',
        'current-time',
        'mute',
        'volume',
        'settings',
      ],
      hideControls: false,
      resetOnEnd: true,
      volume: this.file.asset.volume || 1,
      muted: this.file.asset.muted || false,
      storage: {
        enabled: false,
      },
      speed: {
        selected: this.file.asset.speed || 1,
        options: [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
      },
      i18n: {
        speed: this.$t('speed'),
        normal: this.$t('normal'),
      },
    }

    this.player = new Plyr(`#player-${this.file.id}`, options)

    this.player.source = {
      type: 'audio',
      title: this.file.asset.displayName,
    }

    this.player.on('ready', () => {
      this.player.on('play', this.playAudio)
      this.player.on('pause', this.pauseAudio)
      this.player.on('ratechange', this.changeSpeed)
      this.player.on('timeupdate', this.timeupdate)

      this.player.pause()
      this.resize()
    })

    this.player.on('loadeddata', () => {
      this.currentTime = this.file.asset.currentTime || 0
      this.player.currentTime = this.currentTime
    })

    new ResizeObserver(this.resize).observe(this.$refs.childComponent)
  },
  beforeDestroy() {
    this.player.destroy()
  },
  methods: {
    resize() {
      if (!this.width) {
        const width =
          this.$refs.childComponent.getBoundingClientRect().width /
          this.zoomIndex
        const height =
          this.$refs.childComponent.getBoundingClientRect().height /
          this.zoomIndex
        const asset = { width, height, originalWidth: defaultWidth }

        this.$store.commit('classroom/moveAsset', {
          id: this.file.id,
          asset,
        })

        this.$store.dispatch('classroom/moveAsset', {
          id: this.file.id,
          lessonId: this.file.lessonId,
          asset,
        })
      }
    },
    playAudio(e) {
      this.onPlayerChange({
        currentTime: e.detail.plyr.media.currentTime,
        play: true,
      })
    },
    pauseAudio(e) {
      this.onPlayerChange({
        currentTime: e.detail.plyr.media.currentTime,
        play: false,
      })
    },
    timeupdate(e) {
      this.currentTime = this.player.currentTime
    },
    changeSpeed(e) {
      this.onPlayerChange({
        speed: e.detail.plyr.config.speed.selected,
      })
    },
    onPlayerChange(asset) {
      if (!this.changedBySocket) {
        const data = {
          id: this.file.id,
          lessonId: this.file.lessonId,
          asset,
        }

        if (this.isSocketConnected) {
          this.$socket.emit('audio-updated', data)
        }

        this.$store.dispatch('classroom/updateAssetWithoutSync', data)
      } else {
        this.changedBySocket = false
      }
    },
  },
  sockets: {
    'audio-updated'(data) {
      if (data.id === this.file.id) {
        this.changedBySocket = true

        this.$store.commit('classroom/updateAsset', data)

        if (data.asset.speed) {
          this.player.speed = data.asset.speed
        }

        if (data.asset.currentTime) {
          this.player.currentTime = data.asset.currentTime
        }

        if ('play' in data.asset) {
          if (data.asset.play) {
            if (!this.isUserInteracted) {
              this.player.muted = true
            }

            this.player.play()
          } else {
            this.player.pause()
          }
        }
      }
    },
  },
}
</script>

<style lang="scss">
.plyr--audio .plyr__controls {
  position: relative;
  width: 100%;
  color: #fff;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 1px;
    width: calc(100% - 2px);
    height: 1px;
    background-color: rgba(0, 0, 0, 0.3);
  }
}
</style>
