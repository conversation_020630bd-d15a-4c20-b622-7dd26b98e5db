<template>
  <payments-page :type="'lessons'" :page="1"></payments-page>
</template>

<script>
import PaymentsPage from '~/components/payments/PaymentsPage'

export default {
  name: 'PaymentsLessons',
  components: { PaymentsPage },
  middleware: ['authenticated', 'paymentsPageClass'],
  async asyncData({ store, query }) {
    // Always fetch data on initial load
    await Promise.all([
      store.dispatch('payments/fetchLessons', {
        page: 1,
        itemsPerPage: 20,
      }),
      store.dispatch('payments/fetchEarningsBalance'),
    ])

    const type = 'lessons'
    const searchQuery = query?.search

    return { type, searchQuery }
  },
  head() {
    return {
      title: this.$t('teacher_payments_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('teacher_payments_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('teacher_payments_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('teacher_payments_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} user-payments-page`,
      },
    }
  },
}
</script>
