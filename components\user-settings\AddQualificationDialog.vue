<template>
  <l-dialog
    :dialog="isShownQualificationDialog"
    max-width="844"
    custom-class="qualification-dialog"
    v-on="$listeners"
  >
    <v-form v-model="valid">
      <div class="mt-3 mt-sm-4">
        <v-row>
          <v-col class="col-12 col-sm-6 pt-0 d-sm-flex align-end">
            <div class="input-wrap">
              <div
                class="input-wrap-label font-weight-medium mb-1 mb-sm-2 mb-md-3"
              >
                {{ $t('what_is_name_of_teaching_qualification') }}
              </div>
              <text-input
                v-model="item.name"
                type-class="border-gradient"
                height="44"
                hide-details
                :rules="rules.name"
              ></text-input>
            </div>
          </v-col>
          <v-col class="col-12 col-sm-6 pt-0 d-flex align-end">
            <div class="input-wrap">
              <div
                class="input-wrap-label font-weight-medium mt-1 mt-sm-0 mb-1 mb-sm-2 mb-md-3"
              >
                {{ $t('please_upload_copy_of_qualification_certificate') }}
              </div>
              <div class="upload-file">
                <div class="d-flex">
                  <v-btn
                    class="gradient font-weight-medium"
                    @click="
                      $refs.fileQualification.$el.querySelector('input').click()
                    "
                  >
                    <div>
                      <v-img
                        class="mr-1"
                        :src="
                          require('~/assets/images/upload-icon-gradient.svg')
                        "
                        width="20"
                        height="20"
                      ></v-img>
                    </div>
                    <div class="text--gradient">
                      {{ $t('choose_file') }}
                    </div>
                  </v-btn>
                  <div class="d-flex align-center ml-2 body-2">
                    <template v-if="!item.file">
                      {{ $t('no_file_chosen') }}
                    </template>
                    <template v-else>
                      <div class="upload-file-name">
                        {{ item.file.name }}
                        <v-btn
                          class="file-remove-btn"
                          width="18"
                          height="18"
                          icon
                          @click="item.file = null"
                        >
                          <v-img
                            :src="require('~/assets/images/close-gradient.svg')"
                            width="15"
                            height="15"
                          ></v-img>
                        </v-btn>
                      </div>
                    </template>
                  </div>
                </div>
                <div class="qualification-dialog-input">
                  <v-file-input
                    ref="fileQualification"
                    v-model="item.file"
                    class="l-file-input l-file-input--input-hidden mt-0"
                    :rules="rules.file"
                    prepend-icon=""
                    accept="image/png, image/jpeg, image/bmp, application/pdf"
                  ></v-file-input>
                </div>
              </div>
            </div>
          </v-col>
        </v-row>
      </div>
    </v-form>

    <template #footer>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="font-weight-medium"
          :disabled="!valid"
          @click="add"
        >
          <svg class="mr-1" width="18" height="18" viewBox="0 0 18 18">
            <use
              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#save-icon`"
            ></use>
          </svg>
          {{ $t('save_changes') }}
        </v-btn>
      </div>
    </template>
  </l-dialog>
</template>

<script>
import TextInput from '@/components/form/TextInput'
import LDialog from '@/components/LDialog'

const defaultItem = () => ({
  name: '',
  file: null,
  verified: null,
})

export default {
  name: 'AddQualificationDialog',
  components: { LDialog, TextInput },
  props: {
    isShownQualificationDialog: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      valid: true,
      rules: {
        name: [(v) => !!v && v.length > 1],
        file: [
          (v) => !!v,
          (v) =>
            !v ||
            v.size < 6000000 ||
            this.$t('file_size_should_be_less_than', { value: '6 MB' }),
        ],
      },
      item: defaultItem(),
    }
  },
  methods: {
    add() {
      this.$store
        .dispatch('settings/addTeachingQualification', this.item)
        .then((data) => {
          this.$store.commit('settings/ADD_TEACHING_QUALIFICATION_ITEM', {
            ...this.item,
            ...data,
          })

          this.item = defaultItem()

          this.$emit('qualification-added')
        })
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';

.qualification-dialog {
  .v-card {
    @media #{map-get($display-breakpoints, 'md-and-down')} {
      padding: 48px 28px 32px 28px;
    }
  }

  .upload-file {
    position: relative;

    &-name {
      position: relative;
      padding-right: 32px;

      .file-remove-btn {
        position: absolute;
        right: 0;
        top: 2px;
      }
    }
  }

  &-input {
    position: absolute;
    left: 0;
    bottom: -26px;
  }
}
</style>
