<template>
  <l-dialog
    v-if="isShownConfirmDialog"
    :dialog="isShownConfirmDialog"
    hide-close-button
    max-width="418"
    custom-class="remove-illustration text-center"
    v-on="$listeners"
  >
    <div>
      <div class="remove-illustration-title font-weight-medium">
        {{ $t('are_you_sure') }}
      </div>
      <div class="mt-2">
        <slot></slot>
      </div>
      <div
        class="d-flex justify-space-around justify-sm-space-between flex-wrap mt-2"
      >
        <v-btn
          class="gradient font-weight-medium my-1"
          @click="$emit('close-dialog')"
        >
          <div class="text--gradient">
            {{ $t(cancelTextButton) }}
          </div>
        </v-btn>
        <v-btn
          class="font-weight-medium my-1"
          color="primary"
          @click="$emit('confirm')"
        >
          {{ $t(confirmTextButton) }}
        </v-btn>
      </div>
    </div>
  </l-dialog>
</template>

<script>
export default {
  name: 'ConfirmDialog',
  props: {
    isShownConfirmDialog: {
      type: Boolean,
      required: true,
    },
    cancelTextButton: {
      type: String,
      default: 'close',
    },
    confirmTextButton: {
      type: String,
      default: 'confirm',
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/vars';

.remove-illustration {
  &-title {
    font-size: 20px;
  }
}
</style>
