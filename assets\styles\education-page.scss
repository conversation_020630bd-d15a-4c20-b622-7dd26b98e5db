@import '~vuetify/src/styles/settings/_variables';

.row-education {
  max-width: 1560px;
  margin: 0 auto;
  padding: 0 15px;
}

.flex-lg-row-reverse {
  display: flex;
  flex-direction: row-reverse;
}

@media (max-width: 1170px) {
  .flex-lg-row-reverse {
    flex-wrap: wrap;
    justify-content: center;
  }
}

.section-1 {
  padding: 220px 10% 250px;
  position: relative;
  z-index: 2 !important;
}

@media (max-width: 1750px) {
  .section-1 {
    padding: 190px 6% 200px;
  }
}

@media(max-width: 1440px) {
  .section-1__img {
    width: 65%;
    object-fit: contain;
    position: absolute;
    top: 0;
    right: 2px;
    z-index: -1;
  }

  .section-1__label {
    font-size: 37px;
    margin-bottom: 20px;
  }

  .section-1 {
    padding: 224px 6% 160px;
  }

  .section-1__sublabel {
    margin: 0 0 25px;
    font-size: 20px;
    line-height: 28px;
  }
}

@media (max-width: 1250px) {
  .section-1 {
    padding: 120px 4% 160px;
  }
}

@media (max-width: 991px) {
  .section-1 {
    padding: 120px 15px 160px;
  }
}

@media (max-width: 650px) {
  .section-1 {
    padding: 120px 15px 100px;
  }
}

@media (max-width: 440px) {
  .section-1 {
    padding: 224px 85px 259px;
  }
}

.section-1__content {
  width: 660px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

@media (max-width: 1250px) {
  .section-1__content {
    width: 40%;
  }
}

@media (max-width: 1100px) {
  .section-1__content {
    width: 55%;
  }

  .section-1 .section-1__sublabel {
    max-width: 450px !important;
  }
}

@media (max-width: 1270px) {
  .type-content__list {
    transform: scale(0.9);
  }
}

@media (max-width: 600px) {
  .section-1__content {
    width: 75%;
  }
}

.section1__img {
  width: 58%;
  object-fit: contain;
  position: absolute;
  top: -21px;
  right: -10px;
  z-index: -1;
}

@media (max-width: 800px) {
  .section1__img {
    width: 100%;
    right: -30%;
  }
}

.section-1__label {
  max-width: 673px;
  font-size: 48px;
  font-weight: 700;
  margin-top: 0;
  margin-bottom: 0;
  line-height: 1.125;
  background: #AB135C;
  background: -webkit-linear-gradient(to bottom right, #AB135C 0%, #FBB03B 100%);
  background: -moz-linear-gradient(to bottom right, #AB135C 0%, #FBB03B 100%);
  background: linear-gradient(to bottom right, #AB135C 0%, #FBB03B 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (max-width: 1100px) {
  .section-1__label {
    width: 68%;
    font-size: 23px;
    line-height: 1.5;
  }
}

.section-1__sublabel {
  color: #2D2D2D;
  font-size: 20px;
  margin: 24px 0 36px;
  font-weight: 300;
  line-height: 1.4;
}

@media (max-width: 1250px) {
  .section-1__label {
    font-size: 33px;
  }

  .section-1__sublabel {
    font-size: 20px;
    line-height: 28px;
  }
}

@media (max-width: 1100px) {
  .section-1__sublabel {
    font-size: 20px;
  }
}

@media(max-width: 900px) {
  .section-1__content {
    width: 80%;
  }
}

@media(max-width: 679px) {
  .section-1 {
    padding: 113px 15px 77px;
  }

  .section-1__content {
    width: 100%;
  }

  .section-1__label {
    font-size: 23px;
    line-height: 30px;
    width: 70%;
  }

  .section-1__sublabel {
    font-size: 15px;
    line-height: 23px;
    color: #2D2D2D;
    opacity: 0.75;
    width: 70%;
  }
}

.business-label {
  width: 100%;
  display: flex;
  align-items: center;
  margin-left: 20px;
  font-weight: 500;
  font-size: 32px;
}

.business-label__point {
  width: 10px;
  height: 55px;
  background: #ffb202;
  border-radius: 10px;
  margin-right: 25px;
}

.number {
  width: 33.3333%;
  position: relative;
  min-height: 144px;
  margin-bottom: 87px;
}

.number-li {
  width: 78px;
  height: 100%;
  background: linear-gradient(179.61deg, rgba(171, 19, 92, 0.1) -22.73%, rgba(247, 173, 72, 0.1) 72.1%);
  border-radius: 10px;
  position: absolute;
  top: 0;
  left: 13px;
}

.number-helper {
  position: relative;
  padding: 0 30px 0 0;
}

.number-helper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 82px;
  height: 144px;
  background: linear-gradient(179.61deg, rgba(171, 19, 92, 0.1) -22.73%, rgba(247, 173, 72, 0.1) 72.1%);
  border-radius: 10px;
}

.number-label {
  color: #C91460;
  font-size: 62px;
  font-weight: 800;
  line-height: 1.1;
  padding: 16px 0 0 16px;
}

.number-text {
  max-width: 230px;
  color: #262626;
  margin-top: 15px;
  padding-left: 16px;
  font-size: 18px;
  letter-spacing: 0.1px;
}

@media (max-width: 850px) {
  .number-label {
    font-size: 38px;
  }

  .number-wrap {
    margin: auto auto !important;
  }

  .number-text {
    font-size: 14px;
    opacity: 0.6;
    line-height: 30px;
  }
}

@media (max-width: 479px) {
  .number-helper {
    padding: 0 15px 0 0;
  }

  .number-label {
    top: -8px !important;
    padding-left: 10px;
    font-size: 36px;
  }

  .number-text {
    max-width: 100%;
    padding-left: 10px;
  }
}

.card {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  display: flex;
  padding: 50px;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

.card {
  padding: 82px 65px;
}

.card-label {
  text-align: center;
  font-size: 20px;
  font-weight: 700;
  line-height: 28px;
  margin: 27px 0 15px;
}

.card-text {
  font-size: 17px;
  line-height: 25px;
}

.section-2 {
  position: relative;
  margin-bottom: 102px;
  padding-top: 45px;

  @media (max-width: 1170px) {
    margin-bottom: 60px;
  }

  .business-label {
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin-bottom: 45px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-bottom: 30px;
    }
  }
}

.cards-cont {
  padding: 15px !important;
}

@media (max-width: 750px) {
  .section-1__label {
    margin-top: 50px;
  }
}

.section-2__bottom {
  display: flex;
  justify-content: flex-end;

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.section-2__more {
  width: 100%;
  right: 100px;
  min-height: 175px;
  max-width: 810px;
  padding: 44px 43px;
  display: flex;
  align-items: center;
  background: linear-gradient(179.59deg, rgba(171, 19, 92, 0.24) -27.72%, rgba(247, 173, 72, 0.36) 68.88%);
  border-radius: 20px;
}

.section-2__more-img {
  margin-right: 20px;
  width: 73px;
}

.section-2__more-label {
  font-weight: 700;
  font-size: 22px;
}

.section-2__more-text {
  font-size: 17px;
  line-height: 25px;
}

.section-2__img {
  width: 76%;
  object-fit: contain;
  position: absolute;
  top: -380px;
  left: -10px;
  z-index: -1;
}

@media (max-width: 950px) {
  .section-2__img {
    width: 80%;
  }
}

@media (max-width: 750px) {
  .section-2__img {
  }
}

.cards-cont {
  .card {
    height: 100%;
  }
}

@media #{map-get($display-breakpoints, 'md-and-up')} {
  .cards-cont:nth-child(1) {
    margin-top: 130px;
  }

  .cards-cont:nth-child(2) {
    margin-top: 55px;

    .card {
      height: calc(100% - 75px);
    }
  }

  .cards-cont:nth-child(3) {
    margin-top: -20px;

    .card {
      height: calc(100% - 150px);
    }
  }

  .cards-cont:nth-child(5) {
    margin-top: -75px;

    .card {
      height: calc(100% - 75px);
    }
  }

  .cards-cont:nth-child(6) {
    margin-top: -150px;

    .card {
      height: calc(100% - 150px);
    }
  }
}

.number-li {
  width: 78px;
  height: 100%;
  background: linear-gradient(179.61deg, rgba(171, 19, 92, 0.1) -22.73%, rgba(247, 173, 72, 0.1) 72.1%);
  border-radius: 10px;
  position: absolute;
  top: 0;
  left: 13px;
}

.number-label {
  color: #c91460;
  font-size: 60px;
  font-weight: 800;
}

.number-text {
  color: #262626;
  font-size: 17px;
  line-height: 25.4px;
  opacity: 0.6;
}

@media(max-width: 1440px) {
  .business-label {
    font-size: 30px;
  }

  .card {
    padding: 65px 53px;
  }

  .card-label {
    font-size: 20px;
    line-height: 25px;
  }

  .card-text {
    font-size: 17px;
  }

  .section-2__more {
    padding: 35px;
  }

  .section-2__more-img {
    width: 60px;
  }
}

@media(max-width: 679px) {
  .business-label {
    font-size: 26px;
    margin-bottom: 40px;
  }

  .business-label__point {
    margin-right: 10px;
  }

  .card {
    padding: 45px 35px 41px !important;
    margin-bottom: 30px !important;
  }

  .card-label {
    margin: 19px 0 10px !important;
  }

  .section-2__more {
    position: relative;
    max-width: 414px;
    min-height: 180px;
    right: -20px;
    padding: 32px 15px 32px 35px;
    border-radius: 20px 0 0 20px;
  }

  .section-2__more-label {
    font-size: 20px;
    line-height: 21px;
    margin-bottom: 10px;
  }
}

@media (max-width: 479px) {
  .section-2 {
    margin-bottom: 75px;
  }

  .section-2__more {
    min-height: 150px;
    padding: 10px 15px;
  }

  .section-2__more-label {
    font-size: 18px;
  }

  .section-2__more-text {
    font-size: 15px;
  }
}

@media (max-width: 340px) {
  .mobile-padding-right {
    padding-right: 100px;
  }
}

.section-3 {
  position: relative;
}

@media (max-width: 1250px) {
  .section-3 {
    padding: 40px 0 30px;
  }
}

@media (max-width: 650px) {
  .section-3 {
    padding: 20px 0;
  }
}

@media (max-width: 440px) {
  .section-3 {
    padding: 20px 0;
  }
}

@media (max-width: 750px) {
  .section-3 {
    padding: 0;
  }
}

.section-3__img {
  position: absolute;
  bottom: -480px;
  right: 0;
}

@media (min-width: 849px) {
  .section-3__img {
    width: 70%;
  }
}

.number-wrap {
  position: relative;
  width: 100%;
  max-width: 1000px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-left: 120px;
  padding-top: 80px;
  z-index: 2;
}

@media(max-width: 1440px) {
  .number-wrap {
    margin-top: 4px;
    margin-left: 15px;
    max-width: 1064px;
  }
}

@media (max-width: 650px) {
  .number {
    width: 50%;
    margin-bottom: 30px;
  }

  .number:nth-child(3) {
    width: 100%;
    padding-left: 22%;
  }

  .number-wrap {
    max-width: 435px;
    padding-top: 60px;
  }
}

.section-4 {
  position: relative;
  padding-top: 45px;
}

.section-4 .business-label {
  margin-bottom: 60px;
}

.cards-cont-4 {
  display: flex;
  justify-content: space-between;
}

.section-4 .card {
  padding: 100px 38px
}

@media(min-width: 968px) {
  .col-card {
    width: 32% !important;
  }
}

.col-card .card {
  height: 100%;
}

.col-card .card-text {
  font-size: 15px;
}

.col-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30px 0;
}

@media(max-width: 1440px) {
  .section-4 {
    padding-top: 56px;
  }

  .section-4 .business-label {
    margin-bottom: 55px;
  }

  .section-5 .business-label {
    margin-bottom: 30px;
  }

  .section-4 .card {
    padding: 50px 30px;
  }

  .section-4 .card-label {
    font-size: 18px;
  }

  .section-4 .card-text {
    font-size: 15px;
  }
}

@media(max-width: 967px) {
  .section-4 {
    padding-top: 15px;
  }

  .section-4 .business-label {
    margin-bottom: 40px;
  }

  .cards-cont-4 {
    flex-wrap: wrap;
    justify-content: center;
  }

  .col-card {
    margin-bottom: 10px;
  }
}

.section-5 {
  padding-top: 100px;
  position: relative;
  margin-bottom: 100px;

  .business-label {
    margin-bottom: 30px;
  }
}

.section-5-row {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
}

.section-5-row table {
  color: white;
  margin-bottom: 20px;
}

@media (max-width: 750px) {
  .section-5 {
    padding-top: 0;
  }
}

.teacher {
  display: flex;
  align-items: center;
  margin: 30px 0;
}

.teacher-img {
  margin-right: 25px;
}

.teacher-content__label {
  color: #262626;
  font-size: 30px;
  font-weight: bold;
}

.teacher-content__text {
  color: #262626;
  font-size: 20px;
  width: 350px;
  line-height: 26.1px;
}

.skills-cont {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

@media(max-width: 1440px) {
  .section-5 {
    padding-top: 95px;
  }
}

@media(max-width: 1080px) {
  .section-5 {
    padding-top: 60px;
  }

  .section-3__img {
    bottom: -400px;
  }

  .section-5-row {
    flex-direction: column;
  }
}

@media(max-width: 1200px) {
  .teacher-content__text {
    width: 100%;
  }
}

@media (max-width: 850px) {
  .number {
    min-height: 90px;
    margin-bottom: 60px;
  }

  .number-label {
    padding: 10px 0 0 15px;
    font-size: 38px;
    line-height: 36px;
  }

  .number-text {
    margin-top: 0;
    padding-left: 15px;
    line-height: 15px;
    font-size: 14px;
  }

  .section-3__img {
    bottom: -200%;
  }

  .number-helper::before {
    width: 55px;
    height: 90px;
  }
}

@media(max-width: 676px) {
  .section-5 {
    padding-top: 120px;
  }

  .teacher-img {
    width: 72px;
    height: 72px;
  }

  .teacher-content__label {
    font-size: 20px;
  }

  .teacher-content__text {
    font-size: 17px;
  }

  .swiper-n2 .swiper-slide {
    width: 80% !important;
    margin: 25px 10%;
  }
}

.section-6 {
  position: relative;
  margin-top: 60px;
}

.section-6__imgl {
  position: absolute;
  left: -167px;
  bottom: -400px;
}
.section-6__imgr {
  position: absolute;
  right: -167px;
  top: -500px;
}

@media(max-width: 1440px) {
  .section-6__imgl {
    left: -75px;
  }

  .section-6__imgr {
    right: -75px;
  }
}

.type {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin: 30px 0;
}

@media (max-width: 850px) {
  .type {
    flex-direction: column;
  }
}

@media (max-width: 850px) {
  .type.reverse {
    flex-direction: column-reverse;
  }
}

.type img {
  margin: 30px;
  width: 100%;
  max-width: 410px;
}

.type-content {
  width: 100%;
  max-width: 555px;
  margin-left: 40px;
}

.type-content__label {
  font-size: 25px;
  font-weight: bold;
}

.type-content__list {
  font-size: 20px;
  margin-left: -2em;
}

.type-content__list li {
  margin: 19px 0;
  line-height: 25px;
}

.type-content__list li::marker {
  content: "•";
  color: #FBB03B;
  font-size: 28px;
}

.type-content__list li p {
  margin-left: 15px;
}

.section-7 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 2;

  .row-education {
    position: relative;
  }
}

.section-7__label {
  position: relative;
  margin-top: 40px;
  font-size: 32px;
  font-weight: 500;

  @media (max-width: 1440px) {
    font-size: 30px;
  }

  @media (max-width: 679px) {
    font-size: 26px;
  }
}

.section-7__label-point {
  position: absolute;
  right: -10px;
  top: 0;
  background: linear-gradient(179.61deg, rgba(171, 19, 92, 0.1) -22.73%, rgba(247, 173, 72, 0.1) 72.1%);
  border-radius: 10px;
  height: 100%;
  width: 50%;
}

@media (max-width: 1080px) {
  .type img {
    margin: 0;
    width: 50%;
  }
}

@media (max-width: 850px) {
  .type img {
    margin: 30px;
    width: 100%;
  }
}

.section-7__image {
  display: block;
  width: 100%;
  margin: 75px 0;
}

@media (max-width: 950px) {
  .section-7__image {
    display: none;
  }
}

.section-7__list {
  margin-top: 40px;
  margin-bottom: 40px;
}

@media (max-width: 450px) {
  .section-7__list {
    display: flex;
  }
}

.section-7__item {
  position: absolute;
  max-width: 284px;
  width: 25%;
  font-size: 17px;
  line-height: 1.2;
  font-weight: 400;

  @media (max-width: 1170px) {
    max-width: 195px;
  }
}

.section-7__item img {
  margin-right: 20px;
}

.section-7__item-1 {
  top: 23%;
  left: 12%;
}

.section-7__item-2 {
  top: 61%;
  left: 28%;
}

.section-7__item-3 {
  top: 21%;
  left: 60%;
}

.section-7__item-4 {
  top: 56%;
  left: 79%;
}

@media(max-width: 1440px) {
  .section-7__item {
    font-size: 15px;
  }
}

@media(max-width: 950px) {
  .section-7__item {
    display: none;
  }
}

@media(max-width: 679px) {
  .col-card .card {
    height: 95%;
  }
}

.form-image {
  min-width: 350px;
  max-width: 700px;
  margin: 30px 0;
  position: relative;
  display: none;

  img {
    width: 100%;
    display: block;
  }
}

.form-image__text-wrap {
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  top: 0;
  bottom: 0;
}

.form-image__text {
  font-size: 15px;
  padding: 20px 5px 20px 30%;
  line-height: 1.3;
}

.form-image__text.left {
  padding-left: 5px;
  padding-right: 30%;
  text-align: right;
}

@media(max-width: 950px) {
  .form-image {
    display: block;
  }
}

@media (max-width: 1100px) {
  .section-1 .section-1__label {
    max-width: 364px;
    font-size: 25px;
  }

  .section-1 .section-1__sublabel {
    margin: 0 0 50px;
    font-size: 18px;
  }

  .cards-cont-4 {
    margin-bottom: 25px;
  }
}

@media(max-width: 679px) {
  .section-5 {
    padding-top: 50px;
    margin-bottom: 50px;
  }

  .section-6 {
    margin-top: 0;
  }

  .col-12.col-btn {
    transform: scale(1.1) !important;
  }
}

@media (max-width: 425px) {
  .section-1 {
    padding-top: 80px;
  }

  .section1__img {
    top: -5px;
  }
}

.type.reverse .type-content {
  max-width: 416px;
}

@media (max-width: 600px) {
  .section-1__label {
    margin-bottom: 35px;
  }

  .section-1 .section-1__sublabel {
    margin: 0 0 60px;
  }
}

@media (max-width: 480px) {
  .section1__img {
    width: 95%;
  }

  .section-1__label {
    margin-top: -10px;
    width: 75%;
  }

  .section-1 .section-1__label {
    font-size: 23px;
  }

  .section-1 .section-1__sublabel {
    font-size: 16px;
  }

  .section-1 .section-1__sublabel {
    margin: 0 0 50px;
  }
}

@media (max-width: 425px) {
  .section1__img {
    width: 90%;
  }

  .section-1__label {
    margin-top: 5px;
    width: 88%
  }

  .section-1 .section-1__sublabel {
    width: 88%
  }
}

@media (max-width: 360px) {
  .section-1 .section-1__label {
    font-size: 21px;
  }

  .section-1__label {
    width: 97%;
    margin-top: -15px;
  }

  .section1__img {
    width: 85%
  }
}
