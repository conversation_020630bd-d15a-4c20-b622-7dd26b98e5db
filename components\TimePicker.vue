<template>
  <div class="time-picker unselected">
    <div class="time-picker-toggle">
      <div
        :class="['btn btn-prev', { 'btn--disabled': isPrevButtonDisabled }]"
        @click="!isPrevButtonDisabled ? toggleWeek(-7) : false"
      >
        <v-icon color="greyDark">{{ mdiChevronLeft }}</v-icon>
      </div>
      <div class="period text-center">
        {{ firstDayOfWeek.format('D MMM') }} -
        {{ lastDayOfWeek.format('D MMM') }}
      </div>
      <div class="btn btn-next" @click="toggleWeek(7)">
        <v-icon color="greyDark">{{ mdiChevronRight }}</v-icon>
      </div>
    </div>
    <div class="time-picker-top-bar">
      <div
        ref="timePickerTopBarHelper"
        class="time-picker-top-bar-helper mx-auto"
      >
        <div v-for="i in 7" :key="i" class="item">
          {{ getDayOfWeek(i) | dayFormat('ddd,') }}
          <br v-if="$vuetify.breakpoint.xsOnly" class="d-sm-none" />
          {{ getDayOfWeek(i) | dayFormat('MMM D') }}
        </div>
      </div>
    </div>
    <div
      ref="timePickerWrap"
      class="time-picker-wrap l-scroll l-scroll--grey l-scroll--large"
    >
      <div class="time-picker-wrap-helper">
        <div class="time-picker-left-bar">
          <template v-if="$i18n.locale === 'en'">
            <div class="item">12 AM</div>
            <div v-for="i in 11" :key="`am-${i}`" class="item">{{ i }} AM</div>
            <div class="item">12 PM</div>
            <div v-for="i in 11" :key="`pm-${i}`" class="item">{{ i }} PM</div>
          </template>
          <template v-else>
            <div v-for="i in 24" :key="i" class="item">{{ i - 1 }}:00</div>
          </template>
        </div>
        <div ref="timePickerGraph" :key="key" class="time-picker-graph">
          <div v-for="(day, idx) in calendar" :key="`d-${idx}`" class="day">
            <client-only>
              <time-picker-item
                v-for="(item, index) in day"
                :key="`${idx}-${index}`"
                id-defined
                :item="item"
                :allowed-to-select="allowedToSelect"
                :class="index % 2 ? '' : 'first-half'"
                :active-items="activeItems"
                @mouseover-item="mouseoverItem($event)"
                @mouseleave-item="mouseleaveItem"
                @click-item="clickItem($event)"
              >
              </time-picker-item>
            </client-only>
          </div>
        </div>
        <div class="time-picker-right-bar">
          <template v-if="$i18n.locale === 'en'">
            <div class="item">12 AM</div>
            <div v-for="i in 11" :key="`am-${i}`" class="item">{{ i }} AM</div>
            <div class="item">12 PM</div>
            <div v-for="i in 11" :key="`pm-${i}`" class="item">{{ i }} PM</div>
          </template>
          <template v-else>
            <div v-for="i in 24" :key="i" class="item">{{ i - 1 }}:00</div>
          </template>
        </div>
      </div>
    </div>

    <loader :is-loading="isLoading" absolute></loader>
  </div>
</template>

<script>
import { mdiChevronLeft, mdiChevronRight } from '@mdi/js'
import TimePickerItem from '~/components/TimePickerItem'
import Loader from '~/components/Loader'

const STATUS_FREE = 0
const STATUS_RESERVED = 1
const STATUS_OCCUPIED = 2
// const STATUS_SOME_AVAILABILITY = 4

export default {
  name: 'TimePicker',
  components: { TimePickerItem, Loader },
  filters: {
    dayFormat(time, format = 'HH:mm') {
      return time.format(format)
    },
  },
  props: {
    username: {
      type: String,
      required: true,
    },
    lessonLength: {
      type: Number,
      required: true,
    },
    quantityLessons: {
      type: Number,
      required: true,
    },
    currentTime: {
      type: Object,
      required: true,
    },
    isShownTimePickerDialog: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      mdiChevronLeft,
      mdiChevronRight,
      key: 1,
      now: this.$dayjs(),
      items: [],
      activeItems: [],
      isLoading: false,
      mounted: false,
    }
  },
  computed: {
    firstDayOfWeek() {
      return this.currentTime.day(1)
    },
    lastDayOfWeek() {
      return this.currentTime.day(7)
    },
    slots() {
      return this.$store.state.teacher_profile.slots
    },
    selectedSlots() {
      return this.$store.state.teacher_profile.selectedSlots || []
    },
    isPrevButtonDisabled() {
      return this.now.day(1).isSameOrAfter(this.firstDayOfWeek, 'day')
    },
    quantityItemsPerLesson() {
      return this.lessonLength / 30
    },
    allowedToSelect() {
      return (
        this.selectedSlots.length < this.quantityLessons ||
        this.quantityLessons === 1
      )
    },
    calendar() {
      const result = []

      for (let i = 0; i < 7; i++) {
        result.push(this.items.slice(i * 48, 48 * (i + 1)))
      }

      return result
    },
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
    timeZone() {
      return this.$store.getters['user/timeZone']
    },
  },
  watch: {
    lessonLength() {
      this.reset()
    },
    quantityLessons() {
      this.reset()
    },
    slots: {
      handler() {
        if (!this.isShownTimePickerDialog) {
          setTimeout(this.getItems)
        }
      },
      deep: true,
    },
    isShownTimePickerDialog(newValue, oldValue) {
      if (this.mounted && newValue) {
        this.scroll()
      }
    },
  },
  async mounted() {
    const selectedSlots = window.localStorage.getItem('selected-slots')
      ? JSON.parse(window.localStorage.getItem('selected-slots'))
      : null

    if (selectedSlots) {
      this.$store.commit('teacher_profile/SET_SELECTED_SLOTS', selectedSlots)

      window.localStorage.removeItem('selected-slots')
    }

    await this.getItems()

    this.scroll()

    this.mounted = true
  },
  methods: {
    getItems() {
      const result = []

      let selectedSlots = []

      this.selectedSlots.forEach(
        (item) =>
          (selectedSlots = selectedSlots.concat(item.map((el) => el.date)))
      )

      for (let d = 1; d <= 7; d++) {
        for (let h = 0; h < 48; h++) {
          const date = this.getDayOfWeek(d)
            .hour(Math.floor(h / 2))
            .minute(h % 2 ? 30 : 0)
            .second(0)
          const dateOffset = date.tz(this.timeZone).utcOffset()

          let sameItem

          for (let s = 0; s < this.slots.length; s++) {
            const dateObj = this.$dayjs(this.slots[s].date)

            if (date.isSame(dateObj.add(dateOffset, 'minute'), 'minute')) {
              sameItem = this.slots[s]

              break
            }
          }

          const dateByUTC = date
            .add(this.$dayjs(date).tz(this.timeZone).utcOffset() * -1, 'minute')
            .format()

          result.push({
            id: sameItem?.id,
            status: sameItem?.status,
            date: dateByUTC,
            isSelected: selectedSlots.includes(dateByUTC),
            isAvailable:
              sameItem?.status === STATUS_FREE &&
              !this.now.add(1, 'day').isSameOrAfter(date, 'minute'),
            isUnavailable:
              sameItem?.status === STATUS_OCCUPIED ||
              sameItem?.status === STATUS_RESERVED ||
              (sameItem?.status === STATUS_FREE &&
                this.now.add(1, 'day').isSameOrAfter(date, 'minute')),
          })
        }
      }

      this.items = result
    },
    async toggleWeek(day) {
      const date = this.firstDayOfWeek.add(day, 'day')

      await this.$store.dispatch('loadingAllow', false)

      this.isLoading = true

      this.$store
        .dispatch('teacher_profile/getSlots', {
          slug: this.username,
          date,
        })
        .then(() => {
          this.$emit('update-current-time', date)

          this.$nextTick(this.getItems)
        })
        .then(() => {
          this.scroll()
        })
        .finally(() => {
          this.isLoading = false

          this.$store.dispatch('loadingAllow', true)
        })
    },
    getDayOfWeek(day) {
      return this.currentTime.day(day)
    },
    checkDirection(item, index) {
      let direction = 0

      for (let i = 1; i < this.quantityItemsPerLesson; i++) {
        if (
          this.items[index + i].isAvailable &&
          !this.items[index + i].isSelected
        ) {
          direction = 1
        } else {
          direction = 0

          break
        }
      }

      if (direction === 0) {
        for (let i = 1; i < this.quantityItemsPerLesson; i++) {
          if (
            this.items[index - i].isAvailable &&
            !this.items[index - i].isSelected
          ) {
            direction = -1
          } else {
            direction = 0

            break
          }
        }
      }

      return direction
    },
    clickItem(item) {
      if (!item.isSelected && this.allowedToSelect) {
        const arr = [item]

        if (this.quantityItemsPerLesson === 1 && this.quantityLessons === 1) {
          this.activeItems = []

          this.resetSelectedSlots()
        }

        if (this.quantityItemsPerLesson > 1) {
          const index = this.items.indexOf(item)
          const direction = this.checkDirection(item, index)

          if (direction) {
            if (this.quantityLessons === 1) {
              this.resetSelectedSlots()
            }

            for (let i = 1; i < this.quantityItemsPerLesson; i++) {
              arr.push(this.items[index + i * direction])
            }
          } else {
            return
          }
        }

        arr.forEach((item) => (item.isSelected = true))

        this.$store.commit('teacher_profile/ADD_SELECTED_SLOT', arr)
      } else {
        for (let i = 0; i < this.selectedSlots.length; i++) {
          const ids = this.selectedSlots[i]?.map((el) => el.id)

          if (ids.includes(item.id)) {
            this.$store.commit('teacher_profile/REMOVE_SELECTED_SLOT', i)

            this.items.forEach((arr) => {
              if (ids.includes(arr.id)) {
                arr.isSelected = false
              }
            })

            break
          }
        }
      }

      if (!this.isUserLogged) {
        window.setTimeout(() => {
          this.$emit('next-step')
        }, 300)
      }
    },
    mouseoverItem(item) {
      if (this.quantityItemsPerLesson === 1) {
        this.activeItems.push(item)

        return
      }

      if (this.quantityItemsPerLesson > 1) {
        const index = this.items.indexOf(item)
        const direction = this.checkDirection(item, index)

        if (direction) {
          this.activeItems.push(item)

          for (let i = 1; i < this.quantityItemsPerLesson; i++) {
            this.activeItems.push(this.items[index + i * direction])
          }
        }
      }
    },
    mouseleaveItem() {
      this.activeItems = []
    },
    resetSelectedSlots() {
      this.$store.commit('teacher_profile/RESET_SELECTED_SLOTS')

      this.items.forEach((arr) => {
        arr.isSelected = false
      })
    },
    reset() {
      this.activeItems = []

      this.resetSelectedSlots()
      this.key++
    },
    scroll() {
      const options = {
        top: 560,
        behavior: 'instant',
      }

      if (this.selectedSlots.length) {
        const [earliestTime] = this.selectedSlots
          .flat()
          .map((slot) => {
            const dateObj = this.$dayjs(slot.date)

            return dateObj
              .add(dateObj.tz(this.timeZone).utcOffset(), 'minute')
              .format()
          })
          .filter((date) =>
            this.$dayjs(date).isBetween(this.firstDayOfWeek, this.lastDayOfWeek)
          )
          .map((date) => this.$dayjs(date).format('HH-mm'))
          .sort()
        const el = document.getElementById(`h-${earliestTime}`)

        if (el) {
          options.top = el.offsetTop - 84
        }
      }

      setTimeout(() => {
        this.$refs.timePickerWrap.scroll(options)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.time-picker {
  --timepicker-sidebar-width: 48px;

  @media #{map-get($display-breakpoints, 'xs-only')} {
    --timepicker-sidebar-width: 30px;
  }

  position: relative;
  display: flex;
  flex-direction: column;
  flex-grow: 1;

  &-toggle,
  &-helper {
    display: flex;
  }

  &-toggle {
    justify-content: center;
    align-items: center;
    margin-bottom: 12px;

    .btn {
      margin: 0 10px;
      cursor: pointer;

      &--disabled {
        cursor: auto;
        opacity: 0.4;
      }
    }

    .period {
      min-width: 120px;
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
    }
  }

  &-wrap {
    flex-grow: 1;
    height: 132px;
    overflow-y: auto;
    overflow-x: hidden;

    &-helper {
      display: flex;
    }
  }

  &-left-bar,
  &-right-bar,
  &-top-bar {
    .item {
      font-size: 12px;
      color: #575757;
      text-align: center;
      line-height: 1.333;
      white-space: nowrap;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 10px;
      }
    }
  }

  &-left-bar,
  &-right-bar {
    width: var(--timepicker-sidebar-width);

    .item {
      height: 64px;
    }
  }

  &-top-bar {
    padding-right: 8px;

    &-helper {
      display: flex;
      justify-content: space-around;
      width: calc(100% - var(--timepicker-sidebar-width) * 2 - 2px);
    }

    .item {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      flex-basis: 14.2857%;
      height: 32px;

      @media only screen and (max-width: $xxs-and-down) {
        font-size: 10px;
      }
    }
  }

  &-graph {
    width: calc(100% - 48px);
    display: flex;
    border-style: solid;
    border-color: #e0e0e0;
    border-width: 1px 0 0 1px;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      width: calc(100% - 24px);
    }

    .day {
      flex-grow: 1;
    }
  }
}
</style>
