function slavicPluralization(choice, choicesLength) {
  const teen = choice > 10 && choice < 20

  if (choice === 0 || teen) {
    return 0
  }

  if (choice === 1) {
    return 1
  }

  const endsWithTwo = choice % 10 === 2
  const endsWithThree = choice % 10 === 3
  const endsWithFour = choice % 10 === 4

  if (!teen && (endsWithTwo || endsWithThree || endsWithFour)) {
    return 2
  }

  return 0
}

export default ({ app }) => {
  app.i18n.pluralizationRules = {
    pl: slavicPluralization,
  }
}
