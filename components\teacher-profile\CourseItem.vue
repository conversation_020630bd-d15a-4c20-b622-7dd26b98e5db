<template>
  <div :class="['course-item', { 'course-item--open': isOpened }]">
    <div class="course-item-left d-flex flex-column justify-space-between">
      <div class="d-flex justify-space-between">
        <div class="course-item-left--top">
          <div class="course-item-title d-flex justify-space-between mb-2">
            <h4 class="text--gradient">
              <!--                          <h4-->
              <!--                            class="item-title text&#45;&#45;gradient item-title&#45;&#45;favorite mb-2"-->
              <!--                          >-->
              <!--                          <svg width="18" height="18" viewBox="0 0 12 12">-->
              <!--                            <use-->
              <!--                              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#filledStar`"-->
              <!--                            ></use>-->
              <!--                          </svg>-->
              <a
                :href="localePath(`/teacher/${username}#${item.slug}`)"
                @click.prevent.stop="$emit('go-to-course', item.slug)"
              >
                {{ item.name }}
              </a>
            </h4>
            <div class="course-item-features">
              <div>
                <div class="mr-1">
                  <v-img
                    class="flag"
                    :src="
                      require(`~/assets/images/flags/${item.language.isoCode}.svg`)
                    "
                    width="18"
                    height="18"
                  ></v-img>
                </div>

                {{ item.language.name }}
              </div>
              <div>
                <v-img
                  :src="require('~/assets/images/clock-gradient.svg')"
                  width="18"
                  height="18"
                ></v-img>
                {{ $tc('lessons_count', item.lessons) }}
              </div>
              <div>
                <v-img
                  :src="require('~/assets/images/clock-gradient.svg')"
                  width="18"
                  height="18"
                ></v-img>
                {{ $tc('minutes_count', item.length) }}
              </div>
              <div>
                <v-img
                  :src="require('~/assets/images/dollar-coin-gradient.svg')"
                  width="18"
                  height="18"
                ></v-img>
                {{ $t('total_price') }}:
                <span
                  v-if="currentCurrencyHtmlSymbol"
                  v-html="currentCurrencyHtmlSymbol"
                ></span
                >{{ (item.lessons * item.price).toFixed(2) }}
              </div>
            </div>
          </div>
          <div class="course-item-description">
            <div>{{ item.shortDescription }}</div>
          </div>
        </div>
        <div class="course-item-features">
          <div>
            <div class="mr-1">
              <v-img
                class="flag"
                :src="
                  require(`~/assets/images/flags/${item.language.isoCode}.svg`)
                "
                width="18"
                height="18"
              ></v-img>
            </div>

            {{ item.language.name }}
          </div>
          <div>
            <v-img
              :src="require('~/assets/images/clock-gradient.svg')"
              width="18"
              height="18"
            ></v-img>
            {{ $tc('lessons_count', item.lessons) }}
          </div>
          <div>
            <v-img
              :src="require('~/assets/images/clock-gradient.svg')"
              width="18"
              height="18"
            ></v-img>
            {{ $tc('minutes_count', item.length) }}
          </div>
          <div>
            <v-img
              :src="require('~/assets/images/dollar-coin-gradient.svg')"
              width="18"
              height="18"
            ></v-img>
            {{ $t('total_price') }}:
            <span
              v-if="currentCurrencyHtmlSymbol"
              v-html="currentCurrencyHtmlSymbol"
            ></span
            >{{ (item.lessons * item.price).toFixed(2) }}
          </div>
        </div>
        <div v-if="$vuetify.breakpoint.smAndDown" class="course-item-image">
          <v-img
            eager
            :src="
              require(`~/assets/images/course-illustrations/${item.image}.svg`)
            "
          ></v-img>
        </div>
      </div>
      <div v-show="isOpened" class="course-item-details">
        <youtube
          v-if="item.youtube"
          :video-link="item.youtube"
          class="mt-0 mt-md-3 mt-lg-4 mb-3 mb-lg-4"
        ></youtube>
        <div v-if="item.introductionToCourse" class="mb-3 mb-lg-4">
          <h5 class="text--gradient">Description:</h5>
          <div v-html="item.introductionToCourse"></div>
        </div>
        <div
          v-if="item.courseStructure"
          class="course-item-structure mb-3 mb-lg-4"
        >
          <h5 class="text--gradient">
            Here’s how the course will be structured:
          </h5>

          <div v-html="item.courseStructure"></div>
        </div>
      </div>
      <div class="course-item-footer">
        <v-btn
          v-if="hasFreeSlots && acceptNewStudents"
          class="gradient"
          @click="chooseCourse"
        >
          <div class="text--gradient">{{ $t('book_course') }}</div>
        </v-btn>
        <div
          class="course-item-footer-show"
          @click="$emit('toggle-show-course', index)"
        >
          <span class="mr-1">
            {{ $t(isOpened ? 'show_less' : 'show_more') }}
          </span>
          <svg width="12" height="12" viewBox="0 0 12 12">
            <use
              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#chevron-down`"
            ></use>
          </svg>
        </div>
      </div>
    </div>
    <div
      v-if="$vuetify.breakpoint.mdAndUp"
      class="course-item-right d-none d-md-block"
    >
      <div class="course-item-image">
        <v-img
          v-if="item.image"
          eager
          :src="
            require(`~/assets/images/course-illustrations/${item.image}.svg`)
          "
        ></v-img>
      </div>
    </div>
  </div>
</template>

<script>
import Youtube from '~/components/Youtube'

export default {
  name: 'CourseItem',
  components: { Youtube },
  props: {
    item: {
      type: Object,
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
    openCourses: {
      type: Array,
      required: true,
    },
    username: {
      type: String,
      required: true,
    },
  },
  computed: {
    isOpened() {
      return this.openCourses.includes(this.index)
    },
    currentCurrencyHtmlSymbol() {
      return this.$store.getters['currency/currentCurrencyHtmlSymbol']
    },
    hasFreeSlots() {
      return this.$store.state.teacher_profile.item?.hasFreeSlots
    },
    acceptNewStudents() {
      return this.$store.state.teacher_profile.item?.acceptNewStudents
    },
  },
  methods: {
    chooseCourse() {
      this.$store.dispatch('teacher_profile/setSelectedCourse', this.item)
      this.$emit('show-time-picker-dialog')
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.course-item {
  position: relative;
  display: flex;
  min-height: 222px;
  margin-bottom: 16px;
  padding: 28px 0 28px 40px;
  border-radius: 16px;
  color: var(--v-greyDark-base);
  overflow: hidden;

  @media only screen and (max-width: $mac-13-and-down) {
    padding: 28px 0 28px 22px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    margin-bottom: 24px;
    padding: 24px 16px;
  }

  &:nth-child(3n + 1)::before {
    background: linear-gradient(
        118.56deg,
        var(--v-success-base) 3.04%,
        var(--v-primary-base) 27.45%
      ),
      #c4c4c4;
    opacity: 0.1;
  }

  &:nth-child(3n + 2)::before {
    background: linear-gradient(122.42deg, #d67b7f 0%, #f9c176 100%);
    opacity: 0.16;
  }

  &:nth-child(3n + 3)::before {
    background: linear-gradient(126.15deg, #80b622 0%, #fbb03b 102.93%), #c4c4c4;
    opacity: 0.12;
  }

  &-left {
    position: relative;
    width: calc(100% - 170px);

    @media only screen and (max-width: $mac-13-and-down) {
      width: calc(100% - 130px);
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: 100%;
    }

    &--top {
      width: 100%;
    }
  }

  &-right {
    position: relative;
    flex: 0 0 190px;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  &:last-child {
    margin-bottom: 0;
  }

  &-title {
    @media only screen and (max-width: $mac-13-and-down) {
      flex-direction: column;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding-right: 140px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      padding-right: 100px;
    }

    h4 {
      font-size: 20px;
      font-weight: 700;
      line-height: 1.2;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 18px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        font-size: 16px;
      }
    }

    &--favorite {
      position: relative;
      padding-left: 24px;

      svg {
        position: absolute;
        left: 0;
        top: 2px;
      }
    }

    .course-item-features {
      display: none;

      @media only screen and (max-width: $mac-13-and-down) {
        display: block;
      }
    }
  }

  &-language {
    font-size: 16px;
    line-height: 1.2;
    margin-top: 3px;

    @media only screen and (min-width: $mac-13-and-up) {
      padding-left: 12px;
    }

    @media only screen and (max-width: $mac-13-and-down) {
      margin-top: 18px;
      font-size: 14px;
    }

    .flag {
      border-radius: 50%;
      overflow: hidden;
    }
  }

  &-features {
    display: none;

    @media only screen and (min-width: $mac-13-and-up) {
      display: block;
      flex: 0 0 210px;
      margin: 4px 0 0 20px;
    }

    @media only screen and (max-width: $mac-13-and-down) {
      margin: 15px 0 0 0;
    }

    & > div {
      position: relative;
      margin-bottom: 15px;
      padding-left: 28px;
      font-size: 16px;
      line-height: 1.2;
      font-weight: 500;

      &:last-child {
        margin-bottom: 0;
      }

      @media only screen and (max-width: $mac-13-and-down) {
        padding-left: 24px;
        font-size: 14px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        margin-bottom: 12px;
      }

      .v-image {
        position: absolute;
        left: 0;
        top: 1px;

        @media only screen and (max-width: $mac-13-and-down) {
          top: -1px;
        }

        &.flag {
          height: 18px;
          border-radius: 50%;
          overflow: hidden;
        }
      }
    }
  }

  &-description {
    display: flex;
    margin-bottom: 18px;
    font-size: 17px;
    font-weight: 300;
    line-height: 1.5;
    letter-spacing: -0.002em;

    @media only screen and (min-width: $mac-13-and-up) {
      justify-content: space-between;
      min-height: 102px;
    }

    @media only screen and (max-width: $mac-13-and-down) {
      flex-direction: column-reverse;
      margin-bottom: 24px;
      font-size: 14px;
    }
  }

  &-details {
    height: 0;
    font-size: 17px;
    font-weight: 300;
    line-height: 1.57;
    letter-spacing: -0.002em;
    overflow: hidden;

    @media only screen and (max-width: $mac-13-and-down) {
      font-size: 14px;
    }

    h5 {
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 700;
      line-height: 1.3333;

      @media only screen and (max-width: $mac-13-and-down) {
        margin-bottom: 12px;
        font-size: 16px;
      }
    }

    p {
      margin-bottom: 12px !important;

      @media only screen and (max-width: $mac-13-and-down) {
        margin-bottom: 8px !important;
      }
    }
  }

  &-image {
    max-width: 200px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      position: absolute;
      right: 0;
      top: 0;
      max-width: 140px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      max-width: 100px;
    }

    img {
      display: block;
      width: 100%;
    }
  }

  &-structure {
    strong {
      font-weight: 700 !important;
    }
  }

  &-footer {
    display: flex;
    align-items: center;

    .v-btn {
      min-width: 132px !important;
    }

    &-show {
      display: flex;
      align-items: center;
      margin-left: 24px;
      color: var(--v-orange-base);
      font-size: 14px;
      font-weight: 900;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: var(--v-dark-base);
      }
    }
  }

  &--open {
    height: auto;

    .course-item {
      &-description {
        margin-bottom: 24px;

        @media only screen and (max-width: $xxs-and-down) {
          margin-bottom: 24px;
        }
      }

      &-details {
        height: auto;
      }

      &-footer {
        padding-top: 8px;

        &-show {
          svg {
            transform: rotate(180deg);
          }
        }
      }
    }
  }
}
</style>
