<template>
  <div
    :class="[
      'conversation-item',
      { 'conversation-item--mine': isCurrentUser },
      { 'conversation-item--other': !isCurrentUser },
    ]"
  >
    <div>
      <div class="conversation-item-header">
        <div class="conversation-item-header-avatar">
          <v-avatar width="52" height="52">
            <v-img
              :src="getSrcAvatar(avatars, 'user_thumb_52x52')"
              :srcset="
                getSrcSetAvatar(
                  avatars,
                  'user_thumb_52x52',
                  'user_thumb_104x104'
                )
              "
              :options="{ rootMargin: '50%' }"
            ></v-img>
          </v-avatar>
          <user-status
            :user-id="item.authorId"
            :user-statuses="userStatuses"
          ></user-status>
        </div>
        <div>
          <template v-if="isCurrentUser">
            {{ $t('sent_by_me_on') }}
          </template>
          <template v-else>
            {{
              $t('sent_by_somebody_on', {
                username: recipient<PERSON><PERSON>,
              })
            }}
          </template>
          {{ $dayjs(item.createDate).tz(timeZone).format('ll, LT') }}
        </div>
      </div>
      <div
        :class="[
          'conversation-item-body',
          { 'conversation-item-body--file': item.isFile },
        ]"
      >
        <template v-if="!item.isFile">
          <div v-html="item.text"></div>
        </template>
        <template v-else>
          <a :href="fileUrl" download @click.prevent="downloadClickHandler">
            <span class="mr-1">📎</span>{{ item.text }}
          </a>
        </template>
      </div>
      <div class="conversation-item-footer">
        <template v-if="item.readDate"
          >{{ $t('seen') }}
          {{ $dayjs(item.readDate).tz(timeZone).format('ll, LT') }}</template
        >
        <template v-else>{{ $t('not_yet_seen') }}</template>
        <v-btn
          v-if="isCurrentUser"
          text
          x-small
          height="16"
          color="grey"
          @click="isShownMessageConfirmDialog = true"
        >
          (<span class="text-decoration-underline">{{
            $t('delete_message')
          }}</span
          >)</v-btn
        >
      </div>
    </div>

    <confirm-dialog
      :is-shown-confirm-dialog="isShownMessageConfirmDialog"
      cancel-text-button="no"
      confirm-text-button="yes"
      @confirm="removeMessage"
      @close-dialog="isShownMessageConfirmDialog = false"
    >
      {{ $t('are_you_sure_you_want_to_delete_this_message_permanently') }}
    </confirm-dialog>
  </div>
</template>

<script>
import ConfirmDialog from '@/components/ConfirmDialog'
import Avatars from '~/mixins/Avatars'
import UserStatus from '~/components/UserStatus'

export default {
  name: 'ConversationItem',
  components: { UserStatus, ConfirmDialog },
  mixins: [Avatars],
  props: {
    item: {
      type: Object,
      required: true,
    },
    threadId: {
      type: Number,
      required: true,
    },
    recipientName: {
      type: String,
      required: true,
    },
    recipientAvatars: {
      type: Object,
      required: true,
    },
    userAvatars: {
      type: Object,
      required: true,
    },
    userStatuses: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isShownMessageConfirmDialog: false,
    }
  },
  computed: {
    userId() {
      return this.$store.state.user.item?.id
    },
    isCurrentUser() {
      return this.userId === this.item.authorId
    },
    avatars() {
      return this.isCurrentUser ? this.userAvatars : this.recipientAvatars
    },
    fileUrl() {
      return `${process.env.NUXT_ENV_URL}/messages/file/${this.item.id}`
    },
    timeZone() {
      return this.$store.getters['user/timeZone']
    },
  },
  methods: {
    async downloadClickHandler() {
      await this.$axios({
        url: this.fileUrl,
        method: 'GET',
        responseType: 'blob',
      })
        .then((response) => {
          const url = window.URL.createObjectURL(new Blob([response.data]))
          const link = document.createElement('a')

          link.href = url
          link.setAttribute('download', this.item.text)
          document.body.appendChild(link)
          link.click()
        })
        .catch(() => console.info('Download error'))
    },
    removeMessage() {
      this.isShownMessageConfirmDialog = false

      this.$store.dispatch('message/removeMessage', this.item.id)
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.conversation-item {
  display: flex;
  width: 100%;

  & > div {
    width: calc(100% - 60px);
    max-width: 462px;

    @media only screen and (max-width: $xsm-and-down) {
      width: calc(100% - 45px);
    }

    @media only screen and (max-width: $xxs-and-down) {
      width: calc(100% - 20px);
    }
  }

  &-header {
    display: flex;
    align-items: flex-end;
    margin-bottom: 2px;
    font-size: 13px;
    color: var(--v-greyLight-darken2);
    line-height: 1.23;

    @media only screen and (max-width: $xxs-and-down) {
      font-size: 12px;
    }

    &-avatar {
      position: relative;
      margin-bottom: 2px;
      filter: drop-shadow(0px 4px 5px rgba(0, 0, 0, 0.2));

      @media only screen and (max-width: $xxs-and-down) {
        .v-avatar {
          width: 42px !important;
          min-width: 42px !important;
          height: 42px !important;
        }
      }
    }

    & > div:not(.conversation-item-header-avatar) {
      margin-left: 10px;
    }
  }

  &-body {
    padding: 16px 12px;
    font-size: 14px;
    line-height: 1.4;
    border-radius: 16px;
    color: var(--v-greyLight-darken4);
    background: linear-gradient(
      126.15deg,
      rgba(128, 182, 34, 0.18) 0%,
      rgba(60, 135, 248, 0.18) 102.93%
    );

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      padding: 14px 12px;
    }

    a {
      color: var(--v-greyLight-darken4) !important;
      transition: color 0.3s;

      span {
        display: inline-block;
        font-size: 20px;
      }

      &:hover {
        color: var(--v-success-base) !important;
      }
    }

    &.conversation-item-body--file {
      a {
        text-decoration: none;
      }
    }

    ul {
      padding-left: 20px;

      li {
        margin-bottom: 0;

        p {
          min-height: 16px;
          margin-bottom: 0;
        }
      }
    }

    & > div {
      word-wrap: break-word;

      & > * {
        min-height: 16px;
        margin-bottom: 0;

        &:last-child {
          min-height: 0;
        }
      }
    }
  }

  &-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 2px;
    padding-right: 10px;
    font-size: 13px;
    color: var(--v-greyLight-darken2);
    line-height: 1.23;

    @media only screen and (max-width: $xxs-and-down) {
      font-size: 12px;
    }
  }

  &--mine {
    justify-content: flex-end;

    .conversation-item-header {
      flex-direction: row-reverse;

      &-avatar {
        margin-right: 0;
        margin-left: 12px;
      }
    }

    .conversation-item-footer {
      padding-right: 0;
    }

    & + .conversation-item--mine {
      margin-top: 10px;

      @media only screen and (max-width: $xxs-and-down) {
        margin-top: 6px;
      }

      .conversation-item-header-avatar {
        display: none;
      }
    }
  }

  &--other {
    & + .conversation-item--other {
      margin-top: 10px;

      @media only screen and (max-width: $xxs-and-down) {
        margin-top: 6px;
      }

      .conversation-item-header-avatar {
        display: none;
      }
    }
  }
}
</style>
