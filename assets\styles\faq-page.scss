@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.faq-page {
  --sidebar-width: 100%;

  @media #{map-get($display-breakpoints, 'md-and-up')} {
    --sidebar-width: 260px;
  }


  &-wrap {
    max-width: 1030px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: 720px;
    }
  }

  &-title {
    font-size: 32px;
    line-height: 1.333;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 26px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      font-size: 24px;
    }
  }

  &-content {
    width: 100%;

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      width: calc(100% - var(--sidebar-width));
      padding-right: 36px;
    }

    h2 {
      font-size: 26px;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 22px;
      }

      @media only screen and (max-width: $xsm-and-down) {
        font-size: 20px;
      }
    }

    .v-expansion-panel-content__wrap {
      p {
        margin-bottom: 16px !important;
      }

      a {
        color: var(--v-orange-base);
        text-decoration: none;
      }

      img {
        max-width: 100%;
        margin-top: 20px;
      }

      .iframe-wrapper {
        position: relative;
        max-width: 560px;
        margin-left: auto;
        margin-right: auto;

        &::before {
          content: '';
          position: relative;
          display: block;
          width: 100%;
          padding-bottom: 56.25%;
        }

        iframe {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  &-sidebar {
    width: var(--sidebar-width);

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      &-sticky {
        position: sticky;
        top: 70px;
      }
    }

    &-item {
      &::after {
        content: '';
        display: table;
        clear: both;
      }

      .item-title {
        font-size: 24px;

        @media #{map-get($display-breakpoints, 'md-and-up')} {
          font-size: 22px;
        }

        @media only screen and (max-width: $xsm-and-down) {
          font-size: 20px;
        }
      }

      .item-content {
        a {
          color: var(--v-darkLight-base);
          text-decoration: none;

          &:hover {
            color: var(--v-orange-base);
            transition: color .2s;
          }
        }
      }
    }


  }
}
