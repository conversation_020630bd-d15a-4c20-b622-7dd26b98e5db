<template>
  <v-app light>
    <div class="error-page">
      <v-container fluid class="fill-height">
        <v-row align="center" justify="center" class="fill-height">
          <v-col cols="12" class="text-center">
            <!-- 404 Illustration -->
            <div class="error-illustration">
              <v-img
                :src="require('~/assets/images/404-Error-page-01.svg')"
                alt="404 Error"
                max-width="500"
                contain
                class="mx-auto"
              ></v-img>
            </div>

            <!-- Error Content -->
            <div class="error-content">
              <!-- <template v-if="error.statusCode === 404"> -->
              <h1 class="error-title">{{ $t('error_404_title') }}</h1>
              <h2 class="error-subtitle">
                {{ $t('error_404_subtitle') }}
              </h2>
              <p
                class="error-description"
                v-html="$t('error_404_description')"
              ></p>
              <nuxt-link
                id="book-a-trail-button-header"
                :to="localePath({ path: '/teacher-listing' })"
                class="take-home-btn"
              >
                {{ $t('error_404_button') }}
              </nuxt-link>
              <!-- </template> -->

              <!-- <template v-else>
                <h1 class="error-title mb-4">
                  {{ $t('error_general_title') }}
                </h1>
                <h2 class="error-subtitle mb-6">
                  {{ $t('error_general_subtitle') }}
                </h2>
                <p
                  class="error-description mb-8"
                  v-html="$t('error_general_description')"
                ></p>
                <v-btn
                  :to="localePath('/')"
                  color="primary"
                  large
                  rounded
                  class="take-home-btn"
                  elevation="0"
                >
                  {{ $t('error_general_button') }}
                </v-btn>
              </template> -->
            </div>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </v-app>
</template>

<script>
export default {
  layout: 'empty',
  props: {
    error: {
      type: Object,
      default: null,
    },
  },
  head() {
    const title = this.error.statusCode === 404 ? 'Page Not Found' : 'Error'
    return {
      title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content:
            this.error.statusCode === 404
              ? 'The page you are looking for could not be found.'
              : 'An error occurred while processing your request.',
        },
      ],
    }
  },
}
</script>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.error-illustration {
  .v-image {
    max-width: 100%;
    height: auto;
    margin-top: -70px;
  }
}

.error-content {
  max-width: 600px;
  margin: 0 auto;
}

.error-title {
  font-size: 42px;
  font-weight: 700;
  color: #000000;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }

  @media (max-width: 480px) {
    font-size: 2rem;
  }
}

.error-subtitle {
  font-size: 1.75rem;
  font-weight: 600;
  color: #000000;
  line-height: 1.3;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }

  @media (max-width: 480px) {
    font-size: 1.25rem;
  }
}

.error-description {
  font-size: 14px;
  color: #000000;
  line-height: 1.6;
  margin-top: 10px;
  margin-bottom: 20px;
  @media (max-width: 768px) {
    font-size: 1rem;
  }

  @media (max-width: 480px) {
    font-size: 0.9rem;
  }
}

.take-home-btn {
  font-weight: 600;
  font-size: 16px;
  padding: 12px 32px;
  text-transform: none;
  letter-spacing: 0.5px;
  border-radius: 50px;
  background-color: #fbb03b !important;
  color: white !important;
  text-decoration-line: none;
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);
  }

  @media (max-width: 480px) {
    font-size: 1rem;
    padding: 10px 24px;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .error-page {
    padding: 15px;
  }

  .error-illustration {
    margin-bottom: 1.5rem;

    .v-image {
      max-width: 400px;
    }
  }
}

@media (max-width: 480px) {
  .error-page {
    padding: 10px;
  }

  .error-illustration {
    margin-bottom: 1rem;

    .v-image {
      max-width: 300px;
    }
  }

  .error-content {
    padding: 0 10px;
  }
}

// Animation for the illustration
.error-illustration {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}
</style>
