export const state = () => ({
  item: null,
  reviews: [],
  services: [],
  isSelectedTrial: true,
  selectedLessonLength: null,
  selectedLanguage: {},
  selectedSlots: [],
  selectedCourse: {},
  currentNumberLesson: null,
  slots: [],
})

export const mutations = {
  SET_ITEM(state, payload) {
    state.item = payload
  },
  SET_REVIEWS(state, payload) {
    state.reviews = payload
  },
  SET_SERVICES(state, payload) {
    state.services = payload
  },
  SET_IS_SELECTED_TRIAL(state, payload) {
    state.isSelectedTrial = payload
  },
  SET_SELECTED_LESSON_LENGTH(state, payload) {
    state.selectedLessonLength = payload
  },
  SET_SELECTED_COURSE(state, payload) {
    state.selectedCourse = payload

    if (!state.isSelectedTrial) {
      state.currentNumberLesson = payload?.lessons
    }
  },
  SET_CURRENT_NUMBER_LESSON(state, payload) {
    state.currentNumberLesson = payload
  },
  RESET_CURRENT_NUMBER_LESSON(state) {
    state.currentNumberLesson = null
  },
  SET_SELECTED_LANGUAGE(state, payload) {
    state.selectedLanguage = payload
  },
  SET_SELECTED_SLOTS(state, payload) {
    state.selectedSlots = payload
  },
  ADD_SELECTED_SLOT(state, payload) {
    state.selectedSlots.push(payload)
  },
  REMOVE_SELECTED_SLOT(state, payload) {
    state.selectedSlots.splice(payload, 1)
  },
  RESET_SELECTED_SLOTS(state) {
    state.selectedSlots = []
  },
  SET_SLOTS(state, payload) {
    state.slots = payload
  },
}

export const getters = {
  lessonLengthPackages: (state) => {
    const obj = {}
    const arr = []

    state.services.forEach((item) => {
      if (item.isTrialLesson || item.isFreeTrialLesson) {
        arr.push({
          isTrial: true,
          length: item.length,
          packages: [item],
        })

        return
      }

      if (item.isCourse) {
        arr.push({
          isCourse: true,
          length: item.length,
          packages: [item],
        })

        return
      }

      obj[item.length] = true
    })

    Object.keys(obj).forEach((key) => {
      const packages = state.services.filter(
        (item) =>
          item.length === +key && !item.isTrialLesson && !item.isFreeTrialLesson
      )

      if (packages.length) {
        arr.push({
          isTrial: false,
          length: +key,
          packages,
        })
      }
    })

    return arr.sort((a, b) =>
      a.isTrial === b.isTrial ? 0 : a.isTrial ? -1 : 1 || a.length - b.length
    )
  },
  trialPackage: (state, getters) => {
    const [trialPackage] = getters.lessonLengthPackages.filter(
      (item) => item.isTrial
    )

    return trialPackage?.packages ? trialPackage.packages[0] : {}
  },
  packages: (state) => state.selectedLessonLength?.packages,
  totalPrice: (state) =>
    state.selectedCourse?.price && state.selectedCourse?.lessons
      ? (state.selectedCourse.price * state.selectedCourse.lessons).toFixed(2)
      : 0,
  courses: (state) => state.services.filter((item) => item.isCourse),
  isSelectedDefaultCourse: (state) => !state.selectedCourse?.isCourse,
  isSelectedTrial: (state) =>
    state.isSelectedTrial && !state.selectedCourse?.isCourse,
}

export const actions = {
  setSelectedCourse({ state, commit, getters, dispatch }, payload) {
    if (payload.isDefault) {
      const [item] = getters.lessonLengthPackages.filter(
        (item) => !item.isCourse && item.length === 60
      )

      dispatch('setSelectedLessonLength', item)
    } else {
      commit('SET_SELECTED_COURSE', payload)

      if (payload.language) {
        commit('SET_SELECTED_LANGUAGE', payload.language)
      }
    }
  },
  setSelectedLessonLength({ state, commit, getters }, payload) {
    commit('SET_SELECTED_LESSON_LENGTH', payload)

    if (payload.isTrial) {
      commit('SET_IS_SELECTED_TRIAL', true)
      commit('SET_SELECTED_COURSE', payload.packages[0])
    } else {
      commit('SET_IS_SELECTED_TRIAL', false)

      const packages = getters.packages.filter((item) => !item.isCourse)
      const value = packages
        .map((item) => item.lessons)
        .includes(state.currentNumberLesson || 10)
        ? state.currentNumberLesson || 10
        : packages[packages.length - 1].lessons
      const [selectedCourse] = packages.filter((item) => item.lessons === value)

      commit('SET_SELECTED_COURSE', selectedCourse)
    }
  },
  getItem({ commit }, slug) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/profile/${slug}`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_ITEM', data)
      })
  },
  getReviews({ commit }, slug) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/profile/reviews/${slug}/30`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_REVIEWS', data)
      })
  },
  getServices({ commit }, { slug, currencyIsoCode }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/profile/services/${slug}?currency=${currencyIsoCode}`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_SERVICES', data)
      })
  },
  getSlots({ commit }, { slug, date }) {
    date = date.add(-1, 'day').format('YYYY-MM-DD')

    const url = `${process.env.NUXT_ENV_API_URL}/users/slots/${slug}?date=${date}&addDays=8`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_SLOTS', data)
      })
  },
}
