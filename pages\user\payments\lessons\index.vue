<template>
  <payments-page :type="'lessons'" :page="page"></payments-page>
</template>

<script>
import PaymentsPage from '~/components/payments/PaymentsPage'

export default {
  name: 'PaymentsLessons',
  components: { PaymentsPage },
  middleware: ['authenticated', 'paymentsPageClass'],

  async asyncData({ store, query }) {
    const page = parseInt(query.page) || 1
    // Always fetch data for the current page
    await Promise.all([
      store.dispatch('payments/fetchLessons', {
        page,
        itemsPerPage: 20,
      }),
      store.dispatch('payments/fetchEarningsBalance'),
    ])

    // Set current page in store
    store.commit('payments/SET_CURRENT_PAGE', page)

    return {
      type: 'lessons',
      searchQuery: query?.search,
      page,
    }
  },

  head() {
    return {
      title: this.$t('teacher_payments_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('teacher_payments_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('teacher_payments_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('teacher_payments_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} user-payments-page`,
      },
    }
  },

  computed: {
    locale() {
      return this.$i18n.locale
    },
  },

  watchQuery: ['page', 'search'],
}
</script>
