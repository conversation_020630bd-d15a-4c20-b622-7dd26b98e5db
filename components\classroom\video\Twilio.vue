<!--
  TWILIO VIDEO PROVIDER (OPTION A) - COMMENTED OUT
  This component is no longer used. Whereby is now the default and only video provider.
  This file is kept for reference but should not be imported or used.
-->
<template>
  <div class="twilio-stream">
    <classroom-container :asset="file" :hover-enabled="false">
      <div
        id="video-window"
        :class="[
          'twilio-component cursor-before-grab',
          { 'video-window--is-fullscreen': settings.isFullscreenEnabled },
        ]"
      >
        <div
          id="twilio-local-stream-placeholder"
          class="local-stream hr-flip"
        ></div>
        <div id="twilio-remote-stream-placeholder" class="remote-stream"></div>

        <video-actions
          :is-joined="isJoined"
          :settings="{ ...settings, isMuted, isVideoEnabled }"
          :is-screen-share-disabled="
            isRemoteScreenShareEnabled || screenSharingNotSupported
          "
          :type="file.asset.type"
          @switch-video-player="switchVideoPlayer"
          @toggle-video="toggleVideo"
          @toggle-audio="toggleAudio"
          @toggle-full-screen="toggleFullScreen"
          @toggle-screen-share="toggleScreenShare"
        ></video-actions>
      </div>
    </classroom-container>

    <classroom-container
      v-show="isLocalScreenShareEnabled || isRemoteScreenShareEnabled"
      :asset="screenShareAsset"
      :hover-enabled="false"
    >
      <div class="tokbox-component screenshare-component cursor-before-grab">
        <div class="user-name">
          <template v-if="isLocalScreenShareEnabled">
            {{ $t('my_screen') }}
          </template>
          <template v-if="isRemoteScreenShareEnabled">
            {{
              $t('classroom_user_screen', {
                username: zoomOtherAsset.asset.username,
              })
            }}
          </template>
        </div>
        <div id="twilio-screenshare-placeholder" class="remote-stream"></div>

        <div
          v-if="isLocalScreenShareEnabled"
          class="stream-controls stream-controls--screenshare"
        >
          <div class="video-window-buttons-wrap">
            <div class="stream-controls-wrapper cursor-auto">
              <div class="toolbar-button-wrapper">
                <button
                  class="toolbar-button-item cursor-pointer"
                  data-stream-stop-screen-share
                  type="button"
                  @click="toggleScreenShare"
                >
                  <svg
                    class="toolbar-button-icon"
                    width="38"
                    height="35"
                    viewBox="0 0 38 35"
                  >
                    <use
                      :xlink:href="`${require('~/assets/images/classroom/not_share.svg')}#not_share`"
                    ></use>
                  </svg>
                </button>
                <div class="hover-btn-info">
                  {{ $t('stop_screenshare') }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </classroom-container>
  </div>
</template>

<script>
import {
  connect,
  isSupported,
  LocalVideoTrack,
  createLocalVideoTrack,
} from 'twilio-video'
import { switchFullScreen } from '~/helpers'
import { isSupportedScreenShare } from '~/helpers/check_device'

import ClassroomContainer from '~/components/classroom/ClassroomContainer'
import VideoActions from '~/components/classroom/video/VideoActions'

export default {
  name: 'Twilio',
  components: {
    ClassroomContainer,
    VideoActions,
  },
  props: {
    file: {
      type: Object,
      required: true,
    },
    screenShareAsset: {
      type: Object,
      required: true,
    },
    zoomOtherAsset: {
      type: Object,
      required: true,
    },
  },
  data: () => ({
    room: null,
    localStreamContainer: null,
    remoteStreamContainer: null,
    screenShareStreamContainer: null,
    screenShareTrack: null,
    isLocalScreenShareEnabled: false,
    isRemoteScreenShareEnabled: false,
    screenSharingNotSupported: !isSupportedScreenShare(),
    isJoined: false,
    settings: {
      isScreenShareEnabled: false,
      isFullscreenEnabled: false,
    },
  }),
  computed: {
    twilioAccessToken() {
      return this.$store.getters['classroom/twilioAccessToken']
    },
    twilioRoomName() {
      return this.$store.getters['classroom/twilioRoomName']
    },
    role() {
      return this.$store.getters['classroom/role']
    },
    isVideoEnabled() {
      return this.file.asset?.settings?.[this.role]?.isVideoEnabled ?? true
    },
    isMuted() {
      return this.file.asset?.settings?.[this.role]?.isMuted ?? false
    },
    maxIndex() {
      return this.$store.state.classroom.maxIndex
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.localStreamContainer = document.getElementById(
        'twilio-local-stream-placeholder'
      )
      this.remoteStreamContainer = document.getElementById(
        'twilio-remote-stream-placeholder'
      )
      this.screenShareStreamContainer = document.getElementById(
        'twilio-screenshare-placeholder'
      )

      if (isSupported) {
        connect(this.twilioAccessToken, {
          name: this.twilioRoomName,
          audio: true,
        })
          .then(
            (room) => {
              this.room = room
              this.isJoined = true

              room.participants.forEach((participant) => {
                // console.log(
                //   `Participant "${participant.identity}" is connected to the Room`
                // )

                this.attachRemoteParticipant(participant)
              })

              room.on('participantConnected', (participant) => {
                // console.log(
                //   `Participant "${participant.identity}" has connected to the Room`
                // )

                this.attachRemoteParticipant(participant)
              })

              // room.once('participantDisconnected', (participant) => {
              //   console.log(
              //     `Participant "${participant.identity}" has disconnected from the Room`
              //   )
              // })

              room.on('disconnected', (room, error) => {
                this.stopLocalVideo()

                room.localParticipant.tracks.forEach((publication) => {
                  const attachedElements = publication.track.detach()

                  // console.log(`Participant "${publication}" disconnected`)
                  publication.track.stop()
                  publication.unpublish()
                  attachedElements.forEach((element) => element.remove())
                })

                this.isLocalScreenShareEnabled = false
                this.isRemoteScreenShareEnabled = false
                this.localStreamContainer.innerHTML = ''
                this.screenShareStreamContainer.innerHTML = ''
                this.screenShareTrack = null

                if (error) {
                  console.log(
                    'You were disconnected from the Room:',
                    error.code,
                    error.message
                  )
                }
              })

              if (this.isVideoEnabled) {
                this.startLocalVideo()
              }

              if (this.isMuted) {
                this.stopLocalAudio()
              }
            },
            (error) => this.handleMediaError(error)
          )
          .catch(this.handleMediaError)
      } else {
        this.switchVideoPlayer('tokbox')
      }
    })

    window.addEventListener('beforeunload', this.closeStream)
    window.addEventListener('pagehide', this.closeStream)

    document.addEventListener('fullscreenchange', this.fullscreenChangeHandler)
  },
  beforeDestroy() {
    document.removeEventListener(
      'fullscreenchange',
      this.fullscreenChangeHandler
    )
    this.closeStream()
  },
  methods: {
    startLocalAudio() {
      this.room.localParticipant.audioTracks.forEach((publication) => {
        publication.track.enable()
      })
    },
    stopLocalAudio() {
      this.room.localParticipant.audioTracks.forEach((publication) => {
        publication.track.disable()
      })
    },
    startLocalVideo() {
      createLocalVideoTrack({ name: 'camera' })
        .then((localTrack) => {
          this.room.localParticipant.publishTrack(localTrack)
          this.localStreamContainer.appendChild(localTrack.attach())
        })
        .catch(this.handleMediaError)
    },
    stopLocalVideo() {
      this.room.localParticipant.videoTracks.forEach((publication) => {
        if (publication.track.name === 'camera') {
          publication.track.stop()
          publication.unpublish()

          this.localStreamContainer.innerHTML = ''
        }
      })
    },
    attachRemoteParticipant(participant) {
      participant.tracks.forEach((publication) => {
        if (publication.isSubscribed) {
          const track = publication.track

          this.remoteStreamContainer.appendChild(track.attach())
        }
      })

      participant.on('trackSubscribed', (track) => {
        if (track.name === 'screenshare') {
          this.screenShareStreamContainer.appendChild(track.attach())

          this.isRemoteScreenShareEnabled = true
        } else {
          this.remoteStreamContainer.appendChild(track.attach())
        }
      })

      participant.on('trackUnsubscribed', (track) => {
        if (track.name === 'screenshare') {
          this.isRemoteScreenShareEnabled = false
          this.screenShareStreamContainer.innerHTML = ''
        }

        if (track.name === 'camera' && !this.hasActiveVideoStream()) {
          this.remoteStreamContainer.innerHTML = ''
        }
      })
    },
    toggleVideo() {
      this.updateData(this.file.id, {
        settings: {
          ...this.file.asset.settings,
          [this.role]: {
            isVideoEnabled: !this.isVideoEnabled,
            isMuted: this.isMuted,
          },
        },
      })

      this.isVideoEnabled ? this.startLocalVideo() : this.stopLocalVideo()
    },
    toggleAudio() {
      this.updateData(this.file.id, {
        settings: {
          ...this.file.asset.settings,
          [this.role]: {
            isVideoEnabled: this.isVideoEnabled,
            isMuted: !this.isMuted,
          },
        },
      })

      this.isMuted ? this.stopLocalAudio() : this.startLocalAudio()
    },
    toggleFullScreen() {
      this.settings.isFullscreenEnabled = !this.settings.isFullscreenEnabled

      switchFullScreen(this.settings.isFullscreenEnabled)
    },
    toggleScreenShare() {
      this.settings.isScreenShareEnabled = !this.settings.isScreenShareEnabled

      if (this.screenShareTrack && !this.settings.isScreenShareEnabled) {
        this.isLocalScreenShareEnabled = false

        this.room.localParticipant.unpublishTrack(this.screenShareTrack)
        this.screenShareTrack.stop()

        this.screenShareTrack = null
        this.screenShareStreamContainer.innerHTML = ''
      } else {
        navigator.mediaDevices
          .getDisplayMedia()
          .then((stream) => {
            this.screenShareTrack = LocalVideoTrack(stream.getTracks()[0], {
              name: 'screenshare',
            })
            this.room.localParticipant.publishTrack(this.screenShareTrack)
            this.screenShareStreamContainer.appendChild(
              this.screenShareTrack.attach()
            )
            this.updateData(this.screenShareAsset.id, {
              index: this.maxIndex + 1,
            })

            this.isLocalScreenShareEnabled = true
          })
          .catch(() => {
            console.error('Could not share the screen')
            alert('Could not share the screen')
          })
      }
    },
    switchVideoPlayer(type) {
      this.$store.dispatch('classroom/deleteAsset', this.file)
      this.$store.dispatch('classroom/createAsset', {
        ...this.file.asset,
        type,
      })
    },
    fullscreenChangeHandler() {
      if (!document.fullscreenElement) {
        this.settings.isFullscreenEnabled = false
      }
    },
    updateData(id, asset) {
      this.$store.commit('classroom/moveAsset', {
        id,
        asset,
      })
      this.$store.dispatch('classroom/moveAsset', {
        id,
        lessonId: this.file.lessonId,
        asset,
      })
    },
    hasActiveVideoStream() {
      let r = false

      this.room.participants.forEach((participant) =>
        participant.videoTracks.forEach((videoTrack) => {
          if (videoTrack.isSubscribed) {
            r = true
          }
        })
      )

      return r
    },
    closeStream() {
      if (this.room) {
        this.room.disconnect()
      }
    },
    handleMediaError(error) {
      this.isJoined = false

      switch (error.name) {
        case 'NotAllowedError':
        case 'NotReadableError':
          alert(
            'We are unable to access your video camera. Either the browser has not been given permissions or the camera is in use by another program.'
          )
          break
        case 'NotFoundError':
          alert(
            `1. Permission denied by system \n2. The object cannot be found here \n3. Requested device not found`
          )
          break
        default:
          alert(
            `Sorry, we encountered an error trying to connect you to the remote stream. Please report to Langu Team. Error code: ${error.code}, details: ${error.message}`
          )
      }
    },
  },
}
</script>

<style lang="scss">
.twilio-stream {
  .local-stream,
  .remote-stream {
    video {
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      height: 100%;
      transform: translateY(-50%);
    }
  }

  .remote-stream {
    position: relative;
  }
}
</style>
