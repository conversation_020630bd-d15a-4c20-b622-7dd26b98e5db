// plugins/router.js
export default ({ app }) => {
  if (process.client) {
    const allowedUtmTags = [
      'utm_source',
      'utm_campaign',
      'utm_ad',
      'utm_medium',
      'utm_term',
      'utm_content',
      'latest_utm_source',
    ]
    app.router.beforeEach((to, from, next) => {
      if (typeof localStorage !== 'undefined') {
        if (to?.query && Object.keys(to?.query).length) {
          let areUtmTagsInQuery = false
          const utmTagsFromQuery = {}
          Object.keys(to?.query).forEach((key) => {
            if (allowedUtmTags.includes(key)) {
              areUtmTagsInQuery = true
              utmTagsFromQuery[key] = to?.query[key]
            }
          })
          if (areUtmTagsInQuery) {
            localStorage.setItem('utm', JSON.stringify(utmTagsFromQuery))
          }
        } else {
          const storedUTM = JSON.parse(localStorage.getItem('utm') || '{}')
          if (Object.keys(storedUTM).length > 0) {
            const updatedQuery = { ...to.query, ...storedUTM }
            const queryString = new URLSearchParams(updatedQuery).toString()

            if (queryString !== new URLSearchParams(to.query).toString()) {
              return next({ ...to, query: updatedQuery })
            }
          }
        }
      }
      next()
    })
  } else {
    console.log('Router plugin running on server side')
  }
}
