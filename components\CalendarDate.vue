<template>
  <div class="calendar-date" :style="styles" @click="clickHandler">
    <div class="v-btn">{{ $dayjs(date).format('D') }}</div>
    <div
      :class="[
        'calendar-date-marker',
        { 'calendar-date-marker--free': isFree },
        { 'calendar-date-marker--some-free': isSomeFree },
        { 'calendar-date-marker--occupied': isOccupied },
      ]"
    ></div>
  </div>
</template>

<script>
const STATUS_PAST = -1
const STATUS_FREE = 0
const STATUS_OCCUPIED = 2
const STATUS_EMPTY = 3
const STATUS_SOME_FREE = 4

export default {
  name: 'CalendarDate',
  props: {
    date: {
      type: String,
      required: true,
    },
    item: {
      type: Object,
      default: () => ({}),
    },
    type: {
      type: String,
      required: true,
    },
  },
  computed: {
    isPast() {
      return this.item.status === STATUS_PAST
    },
    isFree() {
      return this.item.status === STATUS_FREE
    },
    isOccupied() {
      return this.item.status === STATUS_OCCUPIED
    },
    isEmpty() {
      return this.item.status === STATUS_EMPTY
    },
    isSomeFree() {
      return this.item.status === STATUS_SOME_FREE
    },
    hasAction() {
      return (this.isOccupied || this.isSomeFree) && this.type === 'upcoming'
    },
    styles() {
      return { cursor: this.hasAction ? 'pointer' : 'auto' }
    },
  },
  methods: {
    clickHandler() {
      if (this.hasAction) {
        this.$emit('click-date', this.item.date)
      }
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.calendar-date {
  position: relative;

  &-marker {
    position: absolute;
    left: 50%;
    bottom: 3px;
    width: 4px;
    height: 4px;
    margin-left: -2px;
    border-radius: 2px;
    background-color: transparent;

    &.calendar-date-marker {
      &--free {
        background-color: var(--v-green-base);
      }

      &--some-free {
        background-color: var(--v-primary-base);
      }

      &--occupied {
        background-color: var(--v-orange-base);
      }
    }
  }
}
</style>
