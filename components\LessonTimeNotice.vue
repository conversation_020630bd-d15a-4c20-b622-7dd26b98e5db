<template>
  <div
    v-if="currentTime"
    :class="['time-notice', { 'time-notice--dark': dark }]"
  >
    {{ $t('lesson_times_displayed_based_on_your_current_local_time') }}:
    {{ currentTime.format('LT') }} ({{ currentTime.format('z') }}).
    <template v-if="!isUserLogged">
      <br v-if="!oneLine" />
      <span
        :class="{ 'text--gradient': !dark }"
        @click.stop.prevent="showLoginSidebarClickHandler"
      >
        {{ $t('log_in') }}
      </span>
      {{ $t('to_change_your_time_zone') }}.
    </template>
  </div>
</template>

<script>
export default {
  name: 'LessonTimeNotice',
  props: {
    dark: {
      type: Boolean,
      default: false,
    },
    oneLine: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentTime: null,
      intervalId: null,
    }
  },
  computed: {
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
    timezone() {
      return this.$store.getters['user/timeZone']
    },
  },
  created() {
    this.setCurrentTime()

    this.intervalId = setInterval(() => {
      this.setCurrentTime()
    }, 10000)
  },
  beforeDestroy() {
    window.clearInterval(this.intervalId)
  },
  methods: {
    setCurrentTime() {
      this.currentTime = this.$dayjs().tz(this.timezone)
    },
    showLoginSidebarClickHandler() {
      this.$emit('show-login-sidebar')
      this.$store.commit('SET_IS_LOGIN_SIDEBAR', true)
    },
  },
}
</script>

<style lang="scss" scoped>
.time-notice {
  padding-bottom: 1px;

  span {
    display: inline-block;
    cursor: pointer;
    transition: color 0.3s;

    &.text--gradient {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 1px;
        left: 0;
        bottom: -1px;
        background: linear-gradient(
          75deg,
          var(--v-success-base),
          var(--v-primary-base)
        );
      }
    }
  }

  &--dark {
    span {
      color: #fff;

      &:hover {
        color: var(--v-success-base);
      }
    }
  }
}
</style>
