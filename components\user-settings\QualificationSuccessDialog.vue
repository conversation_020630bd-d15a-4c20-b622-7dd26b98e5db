<template>
  <l-dialog
    :dialog="isShownDialog"
    max-width="418"
    custom-class="qualification-added text-center"
    v-on="$listeners"
  >
    <div>
      <v-img
        :src="require('~/assets/images/success-icon-gradient.svg')"
        width="56"
        height="56"
        class="mx-auto mb-3"
      ></v-img>
      <div class="qualification-added-text">
        {{
          $t(
            'thank_you_for_saving_your_qualification_it_will_shortly_be_verified_by_langu_admin'
          )
        }}
      </div>
    </div>
  </l-dialog>
</template>

<script>
export default {
  name: 'QualificationSuccessDialog',
  props: {
    isShownDialog: {
      type: Boolean,
      required: true,
    },
  },
}
</script>
