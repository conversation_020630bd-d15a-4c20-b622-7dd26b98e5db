<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
  <path d="M7.99996 12.5619C7.7132 12.5619 7.42649 12.4524 7.20786 12.2339L0.328226 5.35414C-0.109409 4.91651 -0.109409 4.20696 0.328226 3.7695C0.765684 3.33204 1.47509 3.33204 1.91276 3.7695L7.99996 9.85705L14.0872 3.76971C14.5248 3.33225 15.2342 3.33225 15.6716 3.76971C16.1094 4.20717 16.1094 4.91672 15.6716 5.35435L8.79205 12.2341C8.57332 12.4526 8.2866 12.5619 7.99996 12.5619Z" fill="url(#paint0_linear)"/>
</g>
<defs>
  <linearGradient id="paint0_linear" x1="0" y1="3.44141" x2="10.7867" y2="17.2637" gradientUnits="userSpaceOnUse">
    <stop stop-color="#80B622"/>
    <stop offset="1" stop-color="#3C87F8"/>
  </linearGradient>
  <clipPath id="clip0">
    <rect width="16" height="16" fill="white"/>
  </clipPath>
</defs>
</svg>

