import dayjs from 'dayjs'

const defaultState = () => ({
  item: null,
  items: [],
  conversation: {},
})

export const state = () => defaultState()

export const mutations = {
  SET_ITEM: (state, payload) => {
    state.item = payload
    state.item.isRead = true
  },
  SET_ITEMS: (state, payload) => {
    state.items = payload
  },
  UPDATE_ITEMS: (state, payload) => {
    state.items = [...state.items, ...payload]
  },
  SET_CONVERSATION: (state, { data, page }) => {
    if (page === 1) {
      state.conversation = data
    } else {
      state.conversation.count = data.count
      state.conversation.messages = [
        ...state.conversation.messages,
        ...data.messages,
      ]
    }
  },
  ADD_MESSAGE: (state, { threadId, message }) => {
    if (state.conversation.threadId === threadId) {
      if (state.conversation?.messages) {
        state.conversation.messages.unshift(message)
      } else {
        state.conversation.messages = [message]
      }
    } else {
      state.items.forEach((item) => {
        if (item.id === threadId) {
          item.isRead = false
        }
      })
    }
  },
  REMOVE_MESSAGE: (state, messageId) => {
    state.conversation.messages = state.conversation.messages.filter(
      (el) => el.id !== messageId
    )
  },
  UPDATE_READ_MESSAGE: (state, { threadId, messageId }) => {
    if (state.conversation.threadId === threadId) {
      state.conversation.messages.forEach((item) => {
        if (item.id === messageId) {
          item.readDate = dayjs.utc().format()
        }
      })
    }
  },
}

export const actions = {
  getItems({ commit }, { page, searchQuery }) {
    const perPage = process.env.NUXT_ENV_THREADS_PER_PAGE
    let url = `${process.env.NUXT_ENV_API_URL}/users/threads/${page}/${perPage}`

    if (searchQuery) {
      url += `/${searchQuery}`
    }

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        if (Array.isArray(data.threads)) {
          commit(page === 1 ? 'SET_ITEMS' : 'UPDATE_ITEMS', data.threads)

          return data
        }
      })
  },
  getConversation({ rootState, commit, dispatch }, { threadId, page }) {
    page = page || 1

    const perPage = process.env.NUXT_ENV_MESSAGES_PER_PAGE
    const url = `${process.env.NUXT_ENV_API_URL}/users/thread/${threadId}/${page}/${perPage}`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        data.messages?.forEach(async (message) => {
          if (
            !message.readDate &&
            message.authorId !== rootState.user.item.id
          ) {
            message.readDate = dayjs.utc().format()

            await dispatch('user/decreaseCountUnreadMessages', threadId, {
              root: true,
            })
          }
        })

        commit('SET_CONVERSATION', {
          data: { ...data, threadId },
          page,
        })
      })
  },
  checkConversation({ commit }, recipientId) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/user/thread/${recipientId}`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => data)
  },
  sendMessage({ dispatch }, { threadId, message }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/message`

    dispatch('loadingAllow', false, { root: true })

    return this.$axios
      .post(
        url,
        JSON.stringify({
          threadId,
          message,
        }),
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        dispatch('addMessage', { threadId, message: data })
        dispatch(
          'snackbar/success',
          { successMessage: 'message_sent' },
          { root: true }
        )
      })
      .catch((e) => {
        dispatch(
          'snackbar/error',
          { errorMessage: 'sending_error' },
          { root: true }
        )

        console.info(e)
      })
      .finally(() => dispatch('loadingAllow', true, { root: true }))
  },
  sendNewMessage({ dispatch }, { recipientId, message }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/thread`

    dispatch('loadingAllow', false, { root: true })

    return this.$axios
      .post(
        url,
        JSON.stringify({
          recipientId,
          message,
        }),
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        dispatch(
          'snackbar/success',
          { successMessage: 'message_sent' },
          { root: true }
        )

        return data
      })
      .catch((e) => {
        dispatch(
          'snackbar/error',
          { errorMessage: 'sending_error' },
          { root: true }
        )

        console.info(e)
      })
      .finally(() => dispatch('loadingAllow', true, { root: true }))
  },
  uploadFile({ commit, dispatch }, { threadId, file }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/message-file/${threadId}`
    const formData = new FormData()

    formData.append('file', file)

    return this.$axios
      .post(url, formData, {
        header: { 'Content-Type': 'multipart/form-data' },
      })
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        dispatch('addMessage', { threadId, message: data })
        dispatch(
          'snackbar/success',
          { successMessage: 'file_sent' },
          { root: true }
        )
      })
      .catch((e) => {
        dispatch(
          'snackbar/error',
          { errorMessage: 'sending_error' },
          { root: true }
        )

        console.info(e)
      })
  },
  removeMessage({ commit, dispatch }, messageId) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/message/${messageId}`

    dispatch('loadingAllow', false, { root: true })

    return this.$axios
      .delete(url)
      .then(() => {
        commit('REMOVE_MESSAGE', messageId)
        dispatch(
          'snackbar/success',
          { successMessage: 'message_deleted' },
          { root: true }
        )
      })
      .catch((e) => {
        dispatch(
          'snackbar/error',
          { errorMessage: 'deletion_error' },
          { root: true }
        )

        console.info(e)
      })
      .finally(() => dispatch('loadingAllow', true, { root: true }))
  },
  async addMessage(
    { rootState, state, commit, dispatch },
    { threadId, message }
  ) {
    if (
      rootState.user.item.id !== message.authorId &&
      state.item?.id === threadId
    ) {
      message.readDate = dayjs.utc().format()
    }

    if (state.item?.id !== threadId) {
      await dispatch('user/increaseCountUnreadMessages', threadId, {
        root: true,
      })
    }

    if (
      rootState.user.item.id === message.recipientId &&
      !state.items.map((thread) => thread.id).includes(threadId)
    ) {
      await dispatch('getItems', { page: 1 })
    }

    commit('ADD_MESSAGE', { threadId, message })
  },
}
