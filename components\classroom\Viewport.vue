<template>
  <div v-if="isOtherScreenAllowed" class="viewport-component" :style="styles">
    <div
      :class="[
        'user-name',
        {
          'user-name--br': isTopOffset && !isRightOffset,
          'user-name--tl': isRightOffset && !isTopOffset,
          'user-name--bl': isTopOffset && isRightOffset,
        },
      ]"
    >
      {{
        $t('classroom_user_screen', {
          username,
        })
      }}
    </div>
  </div>
</template>

<script>
import { isDevice } from '~/helpers/check_device'

export default {
  name: 'Viewport',
  props: {
    zoomAsset: {
      type: Object,
      required: true,
    },
    zoomOtherAsset: {
      type: Object,
      required: true,
    },
    viewportWidth: {
      type: Number,
      required: true,
    },
    viewportHeight: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      isDevice: isDevice(),
    }
  },
  computed: {
    role() {
      return this.$store.getters['classroom/role']
    },
    color() {
      return this.role === 'teacher'
        ? 'rgba(60, 135, 248, 0.4)'
        : 'rgba(127, 184, 2, 0.4)'
    },
    isOtherUserJoinedClassroom() {
      return this.$store.getters['classroom/isOtherUserJoinedClassroom']
    },
    otherScreenTop() {
      return (
        (this.zoomOtherAsset.asset.y - this.zoomAsset.asset.y) *
        this.zoomAsset.asset.zoomIndex
      )
    },
    otherScreenLeft() {
      return (
        (this.zoomOtherAsset.asset.x - this.zoomAsset.asset.x) *
        this.zoomAsset.asset.zoomIndex
      )
    },
    otherScreenWidth() {
      return (
        (this.zoomOtherAsset.asset.screen.width /
          this.zoomOtherAsset.asset.zoomIndex) *
        this.zoomAsset.asset.zoomIndex
      )
    },
    otherScreenHeight() {
      return (
        (this.zoomOtherAsset.asset.screen.height /
          this.zoomOtherAsset.asset.zoomIndex) *
        this.zoomAsset.asset.zoomIndex
      )
    },
    isOtherScreenAllowed() {
      return (
        !this.isDevice &&
        !!this.zoomOtherAsset?.asset?.screen &&
        this.isOtherUserJoinedClassroom &&
        this.zoomAsset.asset.screen.width >
          this.zoomOtherAsset.asset.screen.width
      )
    },
    styles() {
      return {
        top: `${this.otherScreenTop}px`,
        left: `${this.otherScreenLeft}px`,
        width: `${this.otherScreenWidth}px`,
        height: `${this.otherScreenHeight}px`,
        borderColor: this.color,
      }
    },
    username() {
      return this.zoomOtherAsset.asset.username
    },
    isTopOffset() {
      return this.otherScreenTop < 0
    },
    isLeftOffset() {
      return this.otherScreenLeft < 0
    },
    isBottomOffset() {
      return (
        this.viewportHeight + this.zoomAsset.asset.y <
        this.otherScreenHeight + this.zoomOtherAsset.asset.y
      )
    },
    isRightOffset() {
      return (
        this.viewportWidth + this.zoomAsset.asset.x <
        this.otherScreenWidth + this.zoomOtherAsset.asset.x
      )
    },
  },
}
</script>

<style scoped lang="scss">
.viewport-component {
  position: absolute;
  border-width: 4px;
  border-style: solid;
}

.user-name {
  position: absolute;
  top: 0;
  right: 0;
  padding: 4px 13px;
  line-height: 1;
  background: #ffffff;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  border: none;
  border-bottom-left-radius: 6px;
  z-index: 2;

  &--tl {
    top: 0;
    right: auto;
    left: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 6px;
  }

  &--br {
    top: auto;
    bottom: 0;
    right: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: 6px;
  }

  &--bl {
    top: auto;
    right: auto;
    bottom: 0;
    left: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 6px;
  }
}
</style>
