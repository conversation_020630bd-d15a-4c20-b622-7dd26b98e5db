<template>
  <div :class="['trusted-slider', { 'trusted-slider--dark': dark }]">
    <client-only>
      <VueSlickCarousel v-bind="sliderSettings">
        <div v-for="(item, index) in data" :key="index">
          <div class="slider-elem">
            <div class="slider-elem__img-wrap">
              <img
                :src="getSrcAvatar(item.profileImage)"
                class="slider-elem__img"
                alt
              />
            </div>
            <div class="slider-elem__content">
              <div class="slider-elem__title">{{ item.information }}</div>
              <div class="slider-elem__text">{{ item.description }}</div>
            </div>
          </div>
        </div>
      </VueSlickCarousel>
    </client-only>
  </div>
</template>

<script>
import VueSlickCarousel from 'vue-slick-carousel'
import 'vue-slick-carousel/dist/vue-slick-carousel.css'

export default {
  name: 'TestimonialsSlider',
  components: { VueSlickCarousel },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    dark: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      slider: null,
      sliderSettings: {
        centerMode: true,
        centerPadding: '210px',
        dots: false,
        focusOnSelect: true,
        infinite: true,
        speed: 800,
        slidesToShow: 1,
        slidesToScroll: 1,
        responsive: [
          {
            breakpoint: 1099,
            settings: {
              centerPadding: '170px',
            },
          },
          {
            breakpoint: 991,
            settings: {
              arrows: false,
              centerPadding: '80px',
            },
          },
          {
            breakpoint: 639,
            settings: {
              arrows: false,
              centerPadding: '60px',
            },
          },
          {
            breakpoint: 480,
            settings: {
              arrows: false,
              centerPadding: '45px',
            },
          },
        ],
      },
    }
  },
  methods: {
    getSrcAvatar(image, defaultImage = 'avatar.png') {
      return image || require(`~/assets/images/homepage/${defaultImage}`)
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.trusted-slider {
  max-width: 1640px;
  margin: 50px auto;
  background: none !important;
  position: relative !important;
  height: max-content;

  @media (max-width: 1170px) {
    margin: 30px auto 50px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    margin: 30px auto 0;
  }

  &::before {
    content: none !important;
  }

  .slick-track {
    display: flex !important;
    align-items: center;
  }

  .slick-slide {
    padding: 0 15px;
    transition: all 0.5s ease-in;

    @media only screen and (max-width: $xsm-and-down) {
      padding: 0 10px;
    }

    &:not(.slick-center) {
      transform: translate3d(0, 0, 0) scale(0.8) !important;
      opacity: 0.7;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        transform: translate3d(0, 0, 0) scale(0.9) !important;
        opacity: 0.6;
      }
    }

    .slider-elem {
      display: flex;
      width: 100%;
      max-width: 1000px;
      height: max-content;
      margin: 0 auto;
      padding: 40px;
      background: #fcc062;
      border-radius: 15px;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        padding: 35px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        padding: 25px;
      }

      @media only screen and (max-width: $xsm-and-down) {
        flex-direction: column;
        align-items: center;
      }

      &__title {
        margin-bottom: 20px;
        color: #fff;
        font-weight: bold;
        font-size: 25px;

        @media #{map-get($display-breakpoints, 'xs-only')} {
          margin-bottom: 12px;
          font-size: 22px;
        }

        @media only screen and (max-width: $xsm-and-down) {
          margin-bottom: 8px;
          text-align: center;
          font-size: 18px;
        }

        @media only screen and (max-width: $xxs-and-down) {
          font-size: 16px;
        }
      }

      &__text {
        color: white;
        font-weight: 500;
        font-size: 20px;
        line-height: 1.4;

        @media #{map-get($display-breakpoints, 'xs-only')} {
          font-size: 16px;
        }

        @media only screen and (max-width: $xsm-and-down) {
          font-size: 15px;
        }

        @media only screen and (max-width: $xxs-and-down) {
          font-size: 14px;
        }
      }

      &__content {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      &__img {
        height: 178px;
        width: 178px;
        object-fit: cover;
        border-radius: 50%;
        margin-right: 52px;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          margin-right: 35px;
        }

        @media #{map-get($display-breakpoints, 'xs-only')} {
          height: 100px;
          width: 100px;
          margin-right: 20px;
        }

        @media only screen and (max-width: $xsm-and-down) {
          height: 76px;
          width: 76px;
          margin-right: 0;
          margin-bottom: 14px;
        }
      }
    }
  }

  .slick-arrow {
    position: absolute;
    top: 50%;
    background-color: rgba(252, 192, 98, 0.95);
    transform: translateY(-50%);

    &.slick-next {
      right: 8%;
    }

    &.slick-prev {
      left: 8%;
    }
  }

  &--dark {
    .slick-slide {
      .slider-elem {
        background: var(--v-darkLight-base);

        &__title {
          color: #f9af48;
        }
      }
    }

    .slick-arrow {
      background-color: #000;
    }
  }
}
</style>
