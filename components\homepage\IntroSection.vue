<template>
  <section class="intro">
    <div class="intro-bg">
      <svg
        preserveAspectRatio="none"
        viewBox="0 0 1919 196"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g opacity=".1">
          <path
            d="M1919-862H0v983.943s117.939 52.907 271.192 70.772c153.254 17.865 339.247-41.614 429.776-70.772C965.497 36.741 1162.06 21.625 1250.02 28.496c186.81 14.594 270.52 58.404 417.11 93.447 155.65 37.207 251.87 0 251.87 0V-862z"
            fill="#C4C4C4"
          />
          <path
            d="M1919-862H0v983.943s117.939 52.907 271.192 70.772c153.254 17.865 339.247-41.614 429.776-70.772C965.497 36.741 1162.06 21.625 1250.02 28.496c186.81 14.594 270.52 58.404 417.11 93.447 155.65 37.207 251.87 0 251.87 0V-862z"
            fill="url(#b)"
          />
        </g>
        <g opacity=".2">
          <path
            d="M1-862h1919v984.873s-117.94 52.957-271.19 70.839c-153.26 17.882-339.25-41.653-429.78-70.839C954.503 37.591 757.939 22.46 669.985 29.337c-186.819 14.609-270.526 58.46-417.116 93.536-155.644 37.242-251.869 0-251.869 0V-862z"
            fill="#C4C4C4"
          />
          <path
            d="M1-862h1919v984.873s-117.94 52.957-271.19 70.839c-153.26 17.882-339.25-41.653-429.78-70.839C954.503 37.591 757.939 22.46 669.985 29.337c-186.819 14.609-270.526 58.46-417.116 93.536-155.644 37.242-251.869 0-251.869 0V-862z"
            fill="url(#c)"
          />
        </g>
        <path
          d="M0-862h1919V92.183s-117.94 51.307-271.19 68.632c-153.26 17.324-339.25-40.356-429.78-68.632C953.503 9.558 756.939-5.101 668.985 1.563 482.166 15.714 398.459 58.2 251.869 92.182 96.225 128.264 0 92.183 0 92.183V-862z"
          fill="#2D2D2D"
        />
        <path
          d="M0-862h1919V92.183s-117.94 51.307-271.19 68.632c-153.26 17.324-339.25-40.356-429.78-68.632C953.503 9.558 756.939-5.101 668.985 1.563 482.166 15.714 398.459 58.2 251.869 92.182 96.225 128.264 0 92.183 0 92.183V-862z"
          fill="#2D2D2D"
        />
        <defs>
          <linearGradient
            id="b"
            x1="1919"
            y1="-862"
            x2="678.457"
            y2="781.577"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#80B622" />
            <stop offset="1" stop-color="#3C87F8" />
          </linearGradient>
          <linearGradient
            id="c"
            x1="1"
            y1="-862"
            x2="1243.04"
            y2="782.002"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#80B622" />
            <stop offset="1" stop-color="#3C87F8" />
          </linearGradient>
        </defs>
      </svg>
    </div>
    <div class="intro-wrap">
      <v-container class="pa-0">
        <v-row>
          <v-col
            class="col-xl-10 offset-xl-1 d-flex align-center justify-space-between"
          >
            <div class="intro-content">
              <h1 class="text--gradient font-weight-bold">
                {{ $t('home_page.banner_title') }}
              </h1>
              <h2 class="font-weight-light">
                {{ $t('home_page.banner_subtitle') }}
              </h2>
              <div
                v-if="languageItems && languageItems.length"
                class="intro-content-bottom d-none d-md-flex"
              >
                <select-language :items="languageItems"></select-language>
              </div>
            </div>
            <div class="intro-img">
              <IntroImage />
            </div>
          </v-col>
          <v-col
            v-if="languageItems && languageItems.length"
            class="col-12 d-md-none"
          >
            <div class="intro-content-bottom">
              <select-language :items="languageItems"></select-language>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </section>
</template>

<script>
import IntroImage from '~/components/images/HomePageIntroImage.vue'
import SelectLanguage from '~/components/homepage/SelectLanguage.vue'

export default {
  name: 'IntroSection',
  components: { SelectLanguage, IntroImage },
  props: {
    languageItems: {
      type: Array,
      required: true,
    },
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.intro {
  --pb: 170px;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    --pb: 150px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    --pb: 120px;
  }

  @media only screen and (max-width: $xsm-and-down) {
    --pb: 100px;
  }

  position: relative;
  height: calc(100vh - 74px);
  padding-bottom: var(--pb);
  color: #fff;

  @media #{map-get($display-breakpoints, 'lg-and-down')} {
    .container {
      max-width: 80% !important;
    }
  }

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    .container {
      max-width: 90% !important;
    }
  }

  @media only screen and (min-width: $xsm-and-up) {
    min-height: 680px;
    max-height: 740px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-up')} {
    min-height: 780px;
    max-height: 875px;
  }

  @media only screen and (max-width: $xsm-and-down) {
    height: auto;
  }

  @media only screen and (max-width: $xxs-and-down) {
    .container {
      max-width: calc(100% - 30px) !important;
    }
  }

  &-bg {
    position: absolute;
    width: 100%;
    height: var(--pb);
    bottom: 0;
    left: 0;
    overflow: hidden;

    svg {
      position: absolute;
      width: 1920px;
      height: 100%;
      top: 0;
      left: 50%;
      transform: translateX(-50%);

      @media only screen and (min-width: $hd-and-up) {
        width: 100%;
        left: 0;
        transform: none;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        width: 1300px;
      }

      @media only screen and (max-width: $xsm-and-down) {
        width: 1000px;
      }
    }
  }

  &-wrap {
    position: relative;
    height: 100%;
    padding-top: 90px;
    background-color: var(--v-darkLight-base);
    overflow: hidden;

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      height: 100%;

      .col {
        height: 100%;
      }
    }

    .container,
    .row {
      height: 100%;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      padding-top: 50px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      padding: 60px 0 50px;
    }
  }

  &-content {
    max-width: 570px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: calc(100% - 340px);
    }

    @media only screen and (max-width: $xsm-and-down) {
      max-width: calc(100% - 160px);
    }

    @media only screen and (max-width: $xxs-and-down) {
      max-width: calc(100% - 135px);
    }

    h1 {
      font-size: 48px;
      line-height: 1.125;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 34px;
      }

      @media only screen and (max-width: $xsm-and-down) {
        font-size: 24px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        font-size: 22px;
      }
    }

    h2 {
      margin-top: 32px;
      font-size: 20px;
      line-height: 1.4;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 16px;
      }

      @media only screen and (max-width: $xsm-and-down) {
        font-size: 15px;
      }
    }

    &-bottom {
      margin-top: 56px;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        justify-content: center;
        margin-top: 35px;
      }

      @media only screen and (max-width: $xsm-and-down) {
        max-width: 360px;
        flex-direction: column;
        margin-left: auto;
        margin-right: auto;
      }
    }
  }

  &-select {
    width: 224px;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      width: 260px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      width: 100%;
      margin-bottom: 24px;
    }
  }

  &-img {
    display: flex;
    max-width: 516px;
    height: 100%;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: 340px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      position: relative;
      width: 256px;
      max-width: 256px;
      margin-right: -105px;
    }

    @media only screen and (max-width: $xxs-and-down) {
      width: 236px;
      max-width: 236px;
    }
  }
}
</style>
