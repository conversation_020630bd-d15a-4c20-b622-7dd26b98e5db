<template>
  <v-btn
    :class="['font-weight-medium', { gradient: outlined }]"
    width="100%"
    :color="outlined ? '' : 'primary'"
    :to="`/teacher-listing/1/language,${language.id}`"
  >
    <span :class="['d-flex', { 'text--gradient': outlined }]">
      <template v-if="locale === 'pl'">
        Znajdź więcej nauczycieli
        <div v-if="language.isoCode" class="flag-icon-ml elevation-2">
          <v-img
            :src="require(`~/assets/images/flags/${language.isoCode}.svg`)"
            width="24"
            height="18"
          ></v-img>
        </div>
      </template>
      <template v-else-if="locale === 'es'">
        Más profesores de
        <div v-if="language.isoCode" class="flag-icon-ml elevation-2">
          <v-img
            :src="require(`~/assets/images/flags/${language.isoCode}.svg`)"
            width="24"
            height="18"
          ></v-img>
        </div>
      </template>
      <template v-else>
        Find more
        <div
          v-if="language.isoCode"
          class="flag-icon-ml flag-icon-mr elevation-2"
        >
          <v-img
            :src="require(`~/assets/images/flags/${language.isoCode}.svg`)"
            width="24"
            height="18"
          ></v-img>
        </div>
        teachers
      </template>
    </span>
  </v-btn>
</template>

<script>
export default {
  name: 'FindMoreTeachersButton',
  props: {
    language: {
      type: Object,
      required: true,
    },
    outlined: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
  },
}
</script>

<style scoped lang="scss">
.flag-icon-ml {
  margin-left: 5px;
}

.flag-icon-mr {
  margin-right: 5px;
}
</style>
