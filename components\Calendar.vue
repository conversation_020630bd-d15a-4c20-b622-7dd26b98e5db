<template>
  <div class="calendar calendar--read-only">
    <div class="calendar-title text-center font-weight-bold mb-2 mb-lg-3">
      {{ $dayjs(currentDate).format('MMMM YYYY') }}
    </div>
    <v-calendar
      :weekdays="weekday"
      :locale="locale"
      :start="$dayjs(currentDate).format('YYYY-MM-DD')"
    >
      <template #day-label="{ date }">
        <calendar-date
          :date="date"
          :type="type"
          :item="getDate(date)"
          @click-date="$emit('click-date', $event)"
        ></calendar-date>
      </template>
    </v-calendar>
  </div>
</template>

<script>
import CalendarDate from '@/components/CalendarDate'

export default {
  name: 'Calendar',
  components: { CalendarDate },
  props: {
    currentDate: {
      type: Object,
      required: true,
    },
    items: {
      type: Array,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      weekday: [1, 2, 3, 4, 5, 6, 0],
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
  },
  methods: {
    getDate(date) {
      return this.items.find((item) => item.date === date)
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.calendar {
  padding: 26px 22px 38px;
  color: var(--v-greyBg-base);
  background-color: var(--v-darkLight-base);
  box-shadow: 0 30px 84px rgba(19, 10, 46, 0.08),
    0 8px 32px rgba(19, 10, 46, 0.07), 0 3px 14px rgba(19, 10, 46, 0.03),
    0 1px 3px rgba(19, 10, 46, 0.13);
  border-radius: 16px;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    padding: 24px 12px 32px;
  }

  &-title {
    font-size: 18px;
  }

  .theme--light.v-calendar-weekly {
    border: none !important;
    background-color: transparent !important;

    .v-calendar-weekly__head-weekday {
      font-weight: 700;
      color: var(--v-greyBg-base) !important;
      text-overflow: unset;
      text-transform: capitalize;
      border: inherit !important;

      &.v-outside {
        background-color: inherit !important;
      }
    }

    .v-calendar-weekly__day {
      border: inherit !important;
      background-color: inherit !important;

      &-label {
        @media only screen and (max-width: $mac-13-and-down) {
          margin-top: 8px;
        }
      }

      .v-btn {
        min-width: 38px !important;
        width: 38px !important;
        height: 38px !important;
        font-size: 14px !important;
        color: #fff !important;

        @media only screen and (max-width: $mac-13-and-down) {
          min-width: 32px !important;
          width: 32px !important;
          height: 32px !important;
          font-size: 12px !important;
        }
      }

      &.v-outside {
        opacity: 0.7;

        .v-btn {
          color: var(--v-greyBg-darken2) !important;
        }
      }

      &.v-present {
        .v-btn {
          font-weight: 600 !important;
          background: linear-gradient(
            126.15deg,
            rgba(128, 182, 34, 0.48) 0%,
            rgba(60, 135, 248, 0.48) 102.93%
          ) !important;
        }
      }
    }
  }

  &--read-only {
    .v-calendar-weekly__day > * {
      cursor: auto !important;
    }
  }
}
</style>
