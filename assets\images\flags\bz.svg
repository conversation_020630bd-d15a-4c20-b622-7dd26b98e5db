<svg xmlns="http://www.w3.org/2000/svg" height="480" width="640" viewBox="0 0 640 480">
  <defs>
    <clipPath id="a">
      <path fill-opacity=".67" d="M0 0h640v480H0z"/>
    </clipPath>
  </defs>
  <g clip-path="url(#a)">
    <path fill-rule="evenodd" fill="#c60000" d="M-40 0h720v480H-40z"/>
    <path fill-rule="evenodd" fill="#003bb2" d="M-40 68.57h720v342.86H-40z"/>
    <path d="M457.137 239.998c0 75.743-61.403 137.143-137.142 137.143-75.743 0-137.142-61.402-137.142-137.142 0-75.743 61.402-137.142 137.142-137.142 75.743 0 137.142 61.403 137.142 137.142z" fill-rule="evenodd" fill="#fff"/>
    <g stroke="#000" fill-rule="evenodd">
      <path d="M0 318.04c6.927 1.02 14.39 1.206 23.312 0 7.405 1.994 12.863 5.647 18.317 8.326 3.056 5.665 7.338 14 11.655 18.316 1.874 6.872 4.64 9.755 8.326 16.652.92 6.795 3.183 13.617 0 19.98-3.795 4.746-9.757 7.516-14.985 9.992-6.902 3.688-10.937 5.046-19.982 6.66-7.444 1.33-15.03 1.666-23.312 1.666-8.012-1.43-14.19-2.757-21.646-3.33-7.414-1.324-15.898-2.007-23.312-3.33-4.813-2.816-11.026-5.127-16.65-6.66-3.908-3.726-9.56-7.954-14.987-11.657-4.19-7.502-8.004-10.206-11.656-16.652-3.318-4.876-4.892-11.17-4.997-19.98 2.625-5.345 6.96-10.53 9.99-14.987 5.198-3.433 11.073-7.99 18.317-11.656 9.784-.346 13.604-3.214 23.312-3.33h24.977c5.65.565 8.44 5.583 13.32 9.99z" transform="matrix(.199 0 0 .1704 345.244 141.57)" stroke-width="3.75" fill="#030"/>
      <path d="M273.08 659.39c-6.66-38.298-16.652-133.21-16.652-174.84 1.665-31.64 121.56-139.87 124.89-151.53 3.33-11.656 64.94-99.91 74.93-126.55-24.976 18.316-66.605 98.243-74.93 106.57-8.326 8.326 18.316-83.257 9.99-111.56-8.325 33.303-21.646 118.22-31.637 126.55-4.994 4.996-91.582 104.9-101.57 101.57-9.99-3.33-36.632-19.98-31.636-29.972 4.995-9.99 71.6-71.6 74.93-93.248s8.327-73.266 14.987-78.26c6.662-4.997 16.653-29.974 24.978-49.956-21.646 9.99-24.977 39.964-33.302 41.63-8.326 1.664-28.308-33.304-28.308-33.304s13.32 38.298 14.986 46.624c1.666 8.326-6.66 63.275-11.656 69.936-4.995 6.66-41.628 53.284-48.29 54.95-6.66 1.665-24.976-76.597-24.976-91.583s13.32-73.266 0-76.597c-8.326 0-9.99 68.27-14.986 68.27s-16.652-68.27-16.652-68.27-6.66 31.638-3.33 53.284c-9.99 1.666-36.633-39.962-36.633-39.962s49.955 86.587 59.947 89.917c9.99 4.994 19.98 68.27 18.316 71.6s-49.954-16.65-59.945-23.312c-9.99-6.66-29.973-79.927-29.973-79.927s9.99 71.602-1.665 61.612c-11.656-9.99-29.972-18.317-39.963-33.303-9.99-14.987-4.995-76.598-4.995-76.598s-16.65 54.95-23.312 54.95c-6.66 0-28.307-73.266-28.307-73.266s3.33 79.927 13.32 84.922c9.99 4.995 159.85 104.9 161.52 114.9 1.666 9.99 19.983 48.288 29.974 59.944-1.666 21.647 6.66 161.52 9.99 194.82l29.973 9.99z" transform="matrix(.199 0 0 .1704 278.923 126.253)" stroke-width="1pt" fill="#520900"/>
      <path d="M-338.02 133.21c1.85 6.416 2.528 12.146 11.656 14.986 5.054 1.925 15.007 4.53 19.982 6.66 5.242 2.484 11.14 5.594 18.316 6.66 5.394 1.8 14.58 3.65 21.647 4.997 7.444 1.33 15.03 1.665 23.313 1.665 9.557-1.707 13.81-3.43 19.98-8.326 1.852-6.786 4.344-9.832 4.997-18.317.567-7.944 1.665-14.82 1.665-23.31-1.54-4.624-1.79-15.36-3.33-19.983-5.577-5.466-7.05-8.65-16.652-11.656-7.476-3.382-14.307-3.33-23.312-3.33-6.96 2.32-15.343 1.665-23.31 1.665h-23.312c-6.973 1.517-12.292 2.763-18.317 6.66-8.132 3.674-9.337 6.318-13.32 14.988-2.934 8.8-3.036 15.415 0 26.642z" transform="matrix(.199 0 0 .1704 343.254 135.897)" stroke-width="3.75" fill="#030"/>
      <path d="M-329.7 191.49c0-2.78-6.02 2.69-8.326 4.995-5.392 3.774-7.647 8.815-8.326 18.317-2.502 5.19 0 13.714 0 21.646 0 9.18.062 15.585 4.996 19.982 5.598 3.445 8.29 4.995 18.316 4.995h46.624c5.896-1.965 13.09-2.953 19.982-4.995 7.705-4.085 13.07-4.517 21.646-6.66 4.883-4.238 11.635-8.6 19.982-9.99 9.547-.456 15.257-3.79 21.647-8.327 8.217-2.054 15.636-6.365 21.648-9.99 4.837-3.29 8.31-7.416 11.656-13.322 3.396-7.924 4.482-12.894-1.665-21.646-12.144-4.415-6.11-9.19-14.988-13.32-4.062-2.454-13.524-5.674-18.316-8.327-8.293.615-14.192 2.68-23.312 3.33-4.71 2.356-14.368 1.666-21.647 1.666h-26.642c-7.968 0-16.35-.656-23.312 1.665-6.667 1.818-9.985 4.842-16.65 6.66-4.394 2.987-11.508 5.41-16.653 8.326l16.652-8.326c-4.393 2.987-11.507 5.41-23.312 13.322z" transform="matrix(.199 0 0 .1704 342.26 137.6)" stroke-width="3.75" fill="#030"/>
      <path d="M-186.5 49.95c3.314 0-5.2 3.98-8.326 6.66-4.01 7.063-4.474 13.422-6.66 19.98-2.566 5.773-5.727 13.067-6.66 19.983-3.14 7.494-4.88 10.226-4.997 19.982 0 7.677-.09 16.38 1.665 21.646 5.05 6.363 9.9 9.977 18.317 14.987 7.206 2.495 12.49 3.984 19.98 4.996h23.313c5.267 1.756 13.97 1.665 21.646 1.665 8.492-3.172 11.708-7.208 19.98-8.326 9.116-.828 10.688-4.642 14.987-11.656 3.998-5.168 5.674-11.013 6.66-18.316 0-8.4-.75-15.628-1.665-23.312-4.006-4.494-7.052-10.696-9.99-18.316-5.146-8.82-5.152-12.74-13.32-16.65-3.977-5.43-10.85-8.07-14.988-11.657-8.65-.618-15.218-1.694-21.646-4.995-7.678 0-16.38-.09-21.647 1.665-6.03 0-10.446.57-16.65 1.665z" transform="matrix(.199 0 0 .1453 343.918 141.036)" stroke-width="3.75" fill="#030"/>
      <path d="M-58.28 64.935c-.987 0-4.077 4.824-6.66 9.99-4.344 6.916-4.925 13.316-8.326 18.317-1.33 7.443-1.665 15.028-1.665 23.312.754 8.314 3.195 11.323 8.324 14.987 7.713 4.38 11.367 6.412 19.982 8.326 7.447.573 12.8 1.665 21.647 1.665 7.968 0 16.35.657 23.312-1.664 8.283 0 15.87-.336 23.312-1.665 3.823-3.24 12.04-6.14 16.65-8.324 5.483-6.642 8.107-13.245 11.657-18.317 0-9.823.078-15.565-4.995-23.313-3.622-6.388-7.962-9.32-13.323-14.986-6.718-2.336-12.38-7.195-19.982-8.325H-58.28z" transform="matrix(.199 0 0 .1704 342.59 135.33)" stroke-width="3.75" fill="#030"/>
      <path d="M76.596 76.59c-6.286 2.62-8.59 3.3-11.656 9.992-4.62 7.115-7.342 10.462-11.656 14.986-.99 6.664-3.643 13.88-6.66 18.317-.915 7.684-1.666 14.913-1.666 23.312 4.222 6.486 10.085 12.603 16.65 16.65 6.792 2.352 13.95 5.933 19.983 8.327 7.767 2.033 14.952 4.398 23.313 4.995 7.217 1.804 15.164 1.664 23.312 1.664 7.404-1.61 13.75-3.478 19.98-8.325 3.334-5.197 7.157-11.046 8.327-19.983 2.754-8.263.54-19.06-3.33-26.642-2.04-7.477-5.016-10.373-13.32-14.987-4.596-4.854-10.703-10.322-14.988-13.32-5.28-4.585-12.142-5.154-19.98-8.327-8.493-1.886-11.338-4.33-19.983-4.994l19.983 4.995c-8.488-1.885-11.333-4.33-28.304-6.66z" transform="matrix(.199 0 0 .1704 340.602 141.854)" stroke-width="3.75" fill="#030"/>
      <path d="M-51.62 146.53c-7.617 3.463-11.16 7.635-16.65 13.32 0 8.847 1.092 14.2 1.665 21.648 3.466 4.734 7.144 14.25 9.99 18.316 2.173 6.812 6.922 12.137 9.992 16.652 7.69 3.112 15.495 5.44 23.312 8.325 9.197 0 15.097 2.06 23.31 3.332 6.963 2.32 15.345 1.665 23.313 1.665 8.147 0 16.094-.14 23.312 1.665 7.703 1.37 13.396 4.36 21.647 4.995 4.74-.45 13.136-1.138 19.982-1.665 7.667-3.485 10.982-5.707 14.987-11.656 3.753-4.542 3.33-11.267 3.33-19.98 1.603-8.973 2.093-15.008-1.664-23.313-.108-8.96-1.817-13.172-3.33-21.647-3.006-6.01-5.647-8.65-11.657-11.656-5.465-3.808-12.864-5.998-18.317-9.99-7.433-.886-15.76-2.05-21.647-3.33-6.51-4.342-11.933-6.546-21.647-6.662-9.515-1.132-13.997-2.263-21.648 1.665-7.99.57-15.15 2.17-19.982 4.996-6.144.47-15.283.98-19.982 3.33-7.414 0-9.987 1.287-16.65 1.664l16.65-1.665c-7.414 0-9.987 1.287-18.316 9.99z" transform="matrix(.252 0 0 .132 337.902 146.95)" stroke-width="3.75" fill="#030"/>
      <path d="M-123.22 278.07c-4.3-.632-17.278-1.665-26.642-1.665-8.747 0-15.86.58-23.312 3.33-7.975 1.537-13.883 4.047-19.982 6.66-5.053 2.394-10.953 4.83-14.986 8.326-4.955 3.37-9.13 6.336-13.32 13.322-4.477 6.422-4.945 10.56-8.327 18.317v23.31c.588 7.634 1.64 14.23 4.996 21.648 5.022 4.017 11.977 5.282 18.317 6.66 6.03 1.31 13.875 2.775 21.647 3.33 9.24 0 14.16.466 21.646 3.33 9.747-.36 11.606-2.627 19.982-6.66 9.314-3.692 14.86-5.06 23.312-9.99 5.173-4.08 8.578-10.985 11.656-18.317 4.69-7.574 7.91-14.885 11.656-21.646 1.804-7.218 1.665-15.164 1.665-23.312-2.22-6.258-5.752-8.905-9.99-14.986-4.617-5.05-6.673-8.946-9.992-13.32l9.99 13.32c-4.615-5.05-6.67-8.946-18.315-11.656z" transform="matrix(.199 0 0 .1704 344.25 138.734)" stroke-width="3.75" fill="#030"/>
      <path d="M0 318.04c6.927 1.02 14.39 1.206 23.312 0 7.405 1.994 12.863 5.647 18.317 8.326 3.056 5.665 7.338 14 11.655 18.316 1.874 6.872 4.64 9.755 8.326 16.652.92 6.795 3.183 13.617 0 19.98-3.795 4.746-9.757 7.516-14.985 9.992-6.902 3.688-10.937 5.046-19.982 6.66-7.444 1.33-15.03 1.666-23.312 1.666-8.012-1.43-14.19-2.757-21.646-3.33-7.414-1.324-15.898-2.007-23.312-3.33-4.813-2.816-11.026-5.127-16.65-6.66-3.908-3.726-9.56-7.954-14.987-11.657-4.19-7.502-8.004-10.206-11.656-16.652-3.318-4.876-4.892-11.17-4.997-19.98 2.625-5.345 6.96-10.53 9.99-14.987 5.198-3.433 11.073-7.99 18.317-11.656 9.784-.346 13.604-3.214 23.312-3.33h24.977c5.65.565 8.44 5.583 13.32 9.99z" transform="matrix(.2554 0 0 .1425 350.637 136.542)" stroke-width="3.75" fill="#030"/>
    </g>
    <g fill-rule="evenodd" stroke="#000" stroke-width="3.711" fill="#006a00">
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.1647 -.0123 .0082 .1518 294.243 194.227)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.1647 -.0123 .0082 .1518 255.207 193.094)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.1647 -.0123 .0082 .1518 315.932 193.66)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.1602 -.0466 .031 .1476 288.976 212.412)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.1647 -.0123 .0082 .1518 330.39 202.72)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.1647 -.0123 .0082 .1518 314.006 204.422)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.1647 -.0123 .0082 .1518 238.82 193.094)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.1602 -.0466 .031 .1476 211.86 211.848)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.1647 -.0123 .0082 .1518 253.28 202.156)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(.1647 -.0123 .0082 .1518 236.893 203.853)"/>
      <path d="M200.16 613.18l4.71-7.064-12.952-2.355 9.42-9.418-12.952-4.71 12.952-9.42-12.952-7.064 14.13-5.887-7.065-11.773 14.13 2.355-5.887-14.13 16.484 7.065v-18.84l11.774 12.953 9.42-16.484 7.065 15.306 14.13-16.484 4.71 12.952 21.192-10.597-5.887 16.484 22.37-9.42-4.71 18.84 21.195-9.42-8.242 17.662 21.194 1.176-17.662 10.597 17.662 7.064-16.484 9.42 14.13 11.774c-45.136 20.802-79.674 27.474-131.87 9.42z" transform="matrix(-.3346 -.014 .019 -.1517 399.813 380.192)"/>
    </g>
    <path d="M356.34 196.48c0 22.512-18.995 6.66-38.298 6.66-19.302 0-36.632 17.517-39.962 0 0-9.19 25.655-23.312 44.957-23.312 14.308 1.666 36.634-4.196 33.303 16.652z" fill-rule="evenodd" transform="matrix(.273 0 0 .3008 278.558 210.98)" stroke="#000" stroke-width="2.5" fill="#510800"/>
    <path d="M356.34 196.48c0 22.512-18.995 6.66-38.298 6.66-19.302 0-36.632 17.517-39.962 0 0-9.19 25.655-23.312 44.957-23.312 14.308 1.666 36.634-4.196 33.303 16.652z" fill-rule="evenodd" transform="matrix(-.273 0 0 .3008 369.707 209.612)" stroke="#000" stroke-width="2.5" fill="#ffc600"/>
    <path d="M349.68 188.16c0 13.794-7.082 24.977-15.82 24.977-8.736 0-15.818-11.183-15.818-24.977s7.082-24.977 15.82-24.977c8.735 0 15.818 11.183 15.818 24.977z" fill-rule="evenodd" transform="matrix(.3368 0 0 .3286 277.827 205.293)" stroke="#000" stroke-width="2.5" fill="#520900"/>
    <g fill-rule="evenodd" stroke="#000" stroke-width="1pt" transform="matrix(-.6243 -.1173 -.3707 .6237 523.736 93.658)">
      <path d="M273.75 383.58c-2.737 4.56 42.976 12.952 57.694 22.37 14.72 9.42 14.718 10.01 21.194 14.72l1.765-2.945C350.87 414.193 315.91 391.37 292 383.58c-6.805-2.273-10.3-13.248-12.95-8.83l-5.3 8.83z" transform="matrix(-.7233 -.545 .52 -.758 322.39 677.92)" fill="#af8000"/>
      <path d="M319.08 369.45l-25.315-5.887s-5.887 2.354-6.475 11.185c-.59 8.83 4.71 14.13 4.71 14.13l27.08-9.42V369.45z" transform="matrix(.8444 -.2975 .2838 .885 -89.238 -64.18)" fill="gray"/>
      <rect ry="3.532" rx="3.532" transform="matrix(.8444 -.2975 .2838 .885 -19.207 -255.61)" height="19.428" width="7.064" y="534.62" x="175.71" fill="gray"/>
    </g>
    <path d="M244.77 87.42c7.322-5.948 11.157-3.72 17.65 3.1 9.322 9.815 41.317 58.855 46.312 63.019s4.24-6.418 10.344-1.388c3.032 3.727-10.437 17.026-13.737 14.57-6.412-5.022 3.914-5.376-.355-9.645-4.925-5.133-51.215-40.497-60.282-50.57-5.92-6.578-6.314-13.97.067-19.088z" fill-rule="evenodd" transform="matrix(.7148 .1604 .3327 .529 132.425 68.156)" stroke="#000" stroke-width="1.25" fill="#923d09"/>
    <g stroke="#000">
      <path d="M193.63 88.25c15.546 4.423 43.275 46.12 53.766 45.79 16.652-.48 83.233-77.56 72.564-88.252-7.4-8.246-67.94 41.192-71.852 44.126-.67-8.6-15.95-51.042-28.31-59.945-14.62-9.31-32.21-4.158-47.82-6.662-7.3-2.843-18.84-14.056-23.78-24.976 8.182-3.544 15.323-2.48 23.31-10.65 5.68-6.643 2.382-46.914.37-51.06-13.29-14.268 5.43-7.327-39.023-9.082-64.268-2.446-75.878 49.582-31.653 70.792-3.462 15.928-14.22 19.594-22.94 21.646-8.776.106-38.825-.763-55.545 12.49-33.403 24.55-8.925 30.903-15.352 35.8C.192 73.91-10.503 88.093-14.523 96.573c-10.472 4.996-99.485-4.995-116.5-1.664-16.42.965-56.467 15.818-73.15 26.64 3.565 14.143 241.51-7.602 249.27-2.496 5.946 3.345-2.705 51.237 2.01 54.95 1.66 9.062 116.54 5.828 125.6 5.828 7.558-18.86 15.39-81.558 20.926-91.583z" fill-rule="evenodd" transform="matrix(-.1285 0 0 .2044 282.003 186.258)" stroke-width="4.702" fill="#ffc600"/>
      <path d="M174.9-96.568c1.543 25.457-7.118 3.304-38.37 3.304C75.97-98.3 97.246 5.932 97.246-26.24 71.018-39.95 52.41-126.53 126.86-123.17c23.54 0 44.96 7.858 48.045 26.603z" fill-rule="evenodd" transform="matrix(-.1542 0 0 .1434 285.62 187.117)" stroke-width="5.125"/>
      <path d="M387.98 96.574c0 1.68-.172 7.957-.833 11.656-.445 3.74-1.54 8.077-2.498 10.823-.343 4.452-2.092 5.174-2.498 9.99-1.544 2.27-2.21 5.945-2.498 9.992 0 4.27.686 7.558.833 11.656.48 3.56 1.612 6.367 1.665 10.824 0 4.365-.005 7.55-1.665 9.99v5.83" transform="matrix(-.202 0 0 .2044 353.21 179.875)" stroke-width="3.75" fill="none"/>
      <path d="M383.81 76.592c.58 0 1.03 20.13 5.828 24.978 1.04 3.212 4.574 7.343 5.828 9.99 1.672 2.46 3.98 5.368 8.326 6.66h11.656c4.634 0 7.473.056 10.823-1.664 1.486-.8 2.532-1.846 3.33-3.33m12.489 2.494h.832c-2.327 0-1.092-.104 3.33 1.665 1.726 2.537 3.77 3.625 6.66 4.996 2.983 1.468 6.392 1.666 10.824 1.666 4.198-.354 6.74-1.686 9.16-3.33 4.042-1.31 4.814-2.936 5.827-7.494v-11.656c-.478-3.53-1.384-6.372-2.498-9.158-.42-1.258-.833-.705-.833-2.497m34.968 31.638c-.425 0-2.65 2.575-4.163 4.995-2.568.918-3.233 3.716-4.162 5.828-.67 2.675-1.666 2.86-1.666 6.66-1.678 3.71-2.205 7.008-2.498 10.824l2.497-10.823c-1.678 3.708-2.205 7.007-2.498 10.823m-36.63-94.911h-.833c2.25 0 1.1-.053-3.33.833-1.817 2.27-4.807 4.304-7.493 5.826-2.977 1.825-5.367 4.01-8.326 4.996l8.326-4.996c-2.977 1.825-5.367 4.01-8.326 4.996M402.13 51.615h.833c-2.248 0-1.105-.054 3.33.833 2.936 1.39 4.752 2.89 7.493 4.995 1.29 1.935 3.257 2.305 4.163 5.828 1.573.848 2.123 1.67 2.497 4.164m6.664-37.467h4.163c4.53 0 6.733.68 10.824.833 3.538-.424 4.382-1.737 7.493-2.497m-6.66 104.906c.14.66 1.652 4.256 2.498 5.828 1.517 2.512 3.538 4.914 4.163 8.326.97 2.905 1.527 6.23 1.666 9.99.76 3.503 1.264 6.62-.833 9.16-.898 3.29-2.57 5.69-4.162 9.157-1.11 0-.832-.277-.832.834m-29.15-41.635v.833c0-2.248.055-1.105-.832 3.33-1.147 4.013-2.15 7.388-4.163 9.99-1.098 3.295-2.326 4.505-2.498 9.16.662 1.543.833 3.262.833 5.827m2.5 5v4.163c.908 1.21.55 1.294 1.665 1.665M343.02 128.21v.832c0-2.327-.258-.936 4.163.833 3.572.638 7.937 1.037 10.823 1.665 2.672 1.312 5.265 3.48 7.493 4.995 1.993 1.728 4.818 3.48 7.493 4.996 3.004 1.503 4.325 2.824 5.828 5.83.908 1.21.55 1.292 1.666 1.664" transform="matrix(-.202 0 0 .2044 353.21 179.875)" stroke-width="3.75" fill="none"/>
      <path d="M423.78-16.655c.123-.184 2.467-1.914 4.996-2.498 1.805-1.354 4.812-.74 6.66 0" transform="matrix(-.3155 0 0 .2044 403.227 180.598)" stroke-width="3" fill="none"/>
      <path d="M445.42-19.153h.833c-2.248 0-1.106.054 3.33-.833 3.48-1.16 7.672-.832 11.656-.832 1.173.39.83.242.83 1.665M444.59-7.497v.833c0-2.327-.103-1.09 1.666 3.33.643 2.962 1.76 7.05.832 9.158-2.184 0-2.86-.274-4.163-.833" transform="matrix(-.2454 0 0 .2044 373.514 180.598)" stroke-width="3.402" fill="none"/>
      <path d="M371.33-24.98c0 3.218-4.1 5.827-9.158 5.827s-9.158-2.61-9.158-5.828 4.1-5.83 9.158-5.83 9.158 2.61 9.158 5.83z" fill-rule="evenodd" transform="matrix(-.2418 -.035 -.024 .115 350.644 199.07)" stroke-width="4.501"/>
      <path d="M371.33-24.98c0 3.218-4.1 5.827-9.158 5.827s-9.158-2.61-9.158-5.828 4.1-5.83 9.158-5.83 9.158 2.61 9.158 5.83z" fill-rule="evenodd" transform="matrix(-.175 -.023 -.0174 .0756 330.504 187.51)" stroke-width="6.528"/>
      <path d="M371.33-24.98c0 3.218-4.1 5.827-9.158 5.827s-9.158-2.61-9.158-5.828 4.1-5.83 9.158-5.83 9.158 2.61 9.158 5.83z" fill-rule="evenodd" transform="matrix(-.175 -.023 -.0174 .0756 325.192 186.998)" stroke-width="6.528"/>
      <path d="M412.95 113.64c0 2.07-1.864 3.747-4.163 3.747-2.3 0-4.163-1.678-4.163-3.747s1.864-3.747 4.163-3.747c2.3 0 4.163 1.678 4.163 3.747zm56.62 6.25c0 1.38-1.305 2.498-2.914 2.498-1.61 0-2.914-1.12-2.914-2.498s1.305-2.498 2.914-2.498c1.61 0 2.914 1.12 2.914 2.498z" fill-rule="evenodd" transform="matrix(-.202 0 0 .2044 353.21 179.875)" stroke-width="3.75"/>
      <path d="M447.09 31.634v.833c0-2.796.197-1.03-4.163 3.33-3.724 1.785-5.164 3.667-6.66 6.66-1.742 3.85-2.342 6.613-2.498 10.824v6.662M419.61 30.8c.577 0 1.477 2.274 2.498 4.996 1.856 2.19 3.037 5.35 3.33 9.16 0 4.517.78 6.452.833 10.823v3.33m78.269 69.931c0 .48-2.6 1.455-4.163 3.33-3.16.428-5.964.833-9.99.833-.61-.916-1.836-1.217-3.33-1.665" transform="matrix(-.202 0 0 .2044 353.21 179.875)" stroke-width="3.75" fill="none"/>
    </g>
    <g fill-rule="evenodd" stroke="#000" stroke-width="3.75" fill="#fff">
      <path d="M276.41 36.63l88.253 1.665V49.95s8.325 14.987 13.32 48.29c4.996 33.303 9.992 158.19 9.992 158.19l21.646 141.54s-13.32 21.647-23.31 19.982c-9.99-1.666-39.964-13.32-39.964-14.987 0-1.665-1.665-79.926-4.995-133.21-3.33-53.285-9.99-81.592-24.976-104.9-19.982 48.288-21.647 98.243-19.982 98.243s1.665 39.963 3.33 69.936c1.666 29.972-1.665 71.6-1.665 71.6l-58.28-3.33s1.665-36.633 3.33-64.94c1.666-28.307 6.745-41.625 8.327-73.266 1.748-34.963 4.202-53.32 6.66-109.9 3.238-45.74 14.687-75.258 17.9-103.24.87-7.133-.416-12.488.416-13.32z" transform="matrix(-.209 0 0 .1326 334.435 211.382)"/>
      <path d="M316.38 166.51s-8.326-13.32-11.656-36.633-3.33-28.307-3.33-28.307m-3.334 131.55l-9.99 39.963" transform="matrix(-.209 0 0 .1326 334.435 211.382)"/>
      <path d="M276.41 223.12l11.657 54.95" transform="matrix(-.209 0 0 .1326 321.91 211.602)"/>
      <path d="M387.98 417.95l-11.66-81.6M276.41 44.955l88.253 1.665M318.04 164.84c0-1.665 4.995-29.972 4.995-29.972M284.74 44.955l-8.326 84.922" transform="matrix(-.209 0 0 .1326 334.435 211.382)"/>
    </g>
    <path d="M549.21 538.58c0 136.98-111.05 248.03-248.03 248.03S53.15 675.56 53.15 538.58 164.2 290.55 301.18 290.55 549.21 401.6 549.21 538.58z" stroke="#006a00" stroke-width="6.25" fill="none" transform="matrix(.4628 0 0 .4634 181.152 -9.53)"/>
    <g stroke-width="1pt">
      <path d="M446.172 215.426c14.442 12.315-2.86 24.63-10.832 24.63-7.972 0-25.275-12.316-10.832-24.63-3.61 12.315 10.832 15.394 10.832 21.552 0-6.158 14.443-9.236 10.832-21.552z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.2038 0 0 .1738 377.57 140.305)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M310.32 112.045c13.822-13.01 24.133 5.57 23.25 13.502-.88 7.933-15.017 23.79-25.644 8.056 11.825 4.955 16.478-9.077 22.59-8.396-6.112-.68-7.57-15.394-20.195-13.162z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.0225 -.2028 .1725 .0192 228.17 172.006)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M347.045 361.822c-10.01 16.134-24.75.84-25.938-7.054-1.188-7.893 8.395-26.86 22.71-14.395-12.7-1.74-13.59 13.018-19.67 13.936 6.08-.92 11.274 12.922 22.898 7.512z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.0304 .2018 -.1716 .026 411.01 282.706)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M194.105 247.072c-13.647-13.193 4.388-24.403 12.345-23.905 7.957.498 24.46 13.87 9.277 25.258 4.37-12.066-9.852-16.04-9.47-22.187-.382 6.146-14.988 8.317-12.152 20.834z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.2034 -.0127 .0108 -.1734 257.893 326.33)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M435.467 184.24c16.876 8.673 2.853 24.63-4.908 26.458-7.76 1.83-27.42-6.188-16.18-21.49-.697 12.817 14.066 12.5 15.475 18.495-1.41-5.994 11.945-12.304 5.612-23.464z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.1984 -.0468 .0398 .1692 351.5 126.856)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M275.64 120.38c8.96-16.74 24.645-2.42 26.335 5.382 1.69 7.8-6.665 27.342-21.745 15.818 12.785.924 12.73-13.86 18.74-15.166-6.01 1.305-12.074-12.175-23.33-6.033z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.0432 -.1994 .1696 -.0368 216.853 203.426)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M383.86 347.56c-3.944 18.58-22.995 9.205-26.79 2.184-3.793-7.02-1.21-28.118 16.48-21.26-12.536 2.682-8.367 16.865-13.776 19.796 5.41-2.93 14.986 8.322 24.086-.72z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.097 .1795 -.1526 .0827 417.198 251.402)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M201.03 279.352c-17.135-8.152-3.607-24.532 4.093-26.598 7.7-2.067 27.597 5.343 16.83 20.982.304-12.83-14.442-12.06-16.034-18.01 1.59 5.948-11.563 12.666-4.89 23.626z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.1968 .0528 -.045 -.168 286.713 334.13)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M418.277 160.327c18.784 2.654 10.76 22.333 4.03 26.608-6.733 4.276-27.932 3.156-22.323-14.99 3.54 12.337 17.38 7.19 20.676 12.39-3.294-5.198 7.255-15.544-2.383-24.008z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.172 -.1093 .093 .1467 320.162 133.683)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M245.943 137.4c4.71-18.4 23.357-8.24 26.856-1.068 3.5 7.172.044 28.145-17.347 20.557 12.636-2.16 9.06-16.503 14.585-19.207-5.526 2.703-14.628-8.938-24.095-.283z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.0895 -.1833 .156 -.0763 208.648 232.092)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M410.195 327.94c1.517 18.934-19.414 15.395-25.052 9.75-5.638-5.642-9.178-26.6 9.732-25.085-11.25 6.154-3.21 18.555-7.558 22.91 4.347-4.355 16.735 3.692 22.878-7.574z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.144 .1443 -.1227 .123 414.726 226.253)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M213.82 306.27c-18.582-3.82-9.35-22.96-2.364-26.806 6.986-3.847 28.074-1.406 21.346 16.354-2.764-12.533-16.9-8.262-19.864-13.657 2.963 5.396-8.21 15.062.882 24.11z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.1786 .0983 -.0836 -.1523 310.086 338.99)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M393.347 138.187c18.82-2.372 16.233 18.724 10.857 24.618-5.377 5.894-26.128 10.38-25.468-8.603 6.646 10.975 18.657 2.374 23.198 6.527-4.54-4.153 2.93-16.907-8.587-22.543z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.1375 -.1507 .128 .1172 291.684 138.238)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M221.634 159.638c-.273-18.992 20.38-14.084 25.637-8.082 5.257 6.002 7.415 27.147-11.354 24.392 11.628-5.4 4.42-18.303 9.044-22.363-4.624 4.06-16.457-4.785-23.326 6.053z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.1344 -.1534 .1305 -.1146 210.445 260.81)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M428.034 301.192c5.935 18.04-15.24 19.545-22.048 15.393-6.808-4.153-15.186-23.684 3.548-26.677-9.483 8.637 1.25 18.79-1.95 24.048 3.2-5.26 17.134-.365 20.45-12.764z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.174 .1062 -.0903 .1484 408.483 201.303)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M234.75 330.176c-18.932 1.19-15.035-19.702-9.3-25.25 5.732-5.545 26.722-8.726 24.88 10.18-5.95-11.37-18.472-3.537-22.746-7.965 4.273 4.43-3.977 16.69 7.166 23.036z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.1466 .1418 -.1206 -.125 336.217 336.475)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M367.436 121.305c18-5.994 19.564 15.202 15.435 22.03-4.127 6.83-23.61 15.274-26.652-3.474 8.653 9.47 18.762-1.306 24.024 1.882-5.26-3.188-.413-17.153-12.806-20.437z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.1055 -.1746 .1485 .09 267.728 141.167)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M204.46 190.387c-6.63-17.796 14.47-20.122 21.435-16.238 6.964 3.884 16.094 23.074-2.51 26.793 9.14-8.998-1.98-18.725 1.014-24.105-2.994 5.38-17.107 1.03-19.94 13.55z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.178 -.0993 .0845 -.1518 227.874 289.44)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M440.556 278.107c7.952 17.244-12.913 21.157-20.15 17.81-7.237-3.35-17.786-21.795.484-26.908-8.436 9.662 3.385 18.522.805 24.112 2.58-5.59 16.98-2.32 18.86-15.015z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.185 .0856 -.0728 .1578 409.75 181.103)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M270.06 353.976c-17.433 7.492-20.774-13.498-17.235-20.65 3.54-7.153 22.24-17.21 26.85 1.213-9.418-8.707-18.584 2.885-24.096.152 5.51 2.733 1.854 17.057 14.48 19.284z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.0905 .183 -.1555 -.077 367.74 325.77)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M447.844 249.543c11.412 15.173-8.157 23.412-15.937 21.67-7.78-1.74-21.983-17.54-5.204-26.402-6.207 11.23 7.216 17.39 5.875 23.4 1.34-6.01 16.107-5.86 15.266-18.667z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.199 .0445 -.038 .1696 397.264 161.246)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M339.332 114.35c16.322-9.678 22.337 10.705 19.748 18.255-2.588 7.55-19.84 19.936-26.783 2.26 10.46 7.418 18.06-5.26 23.876-3.26-5.816-2-4.035-16.676-16.84-17.256z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(.0662 -.193 .1642 .0564 246.095 154.92)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M305.89 364.498c-15.547 10.883-23.08-8.99-21.066-16.712 2.013-7.724 18.282-21.378 26.536-4.276-10.99-6.608-17.61 6.607-23.562 5.053 5.95 1.554 5.28 16.324 18.093 15.935z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.0515 .1974 -.168 -.044 395.806 317.003)"/>
    </g>
    <g stroke-width="1pt">
      <path d="M196.065 216.915c-10.444-15.856 9.6-22.857 17.256-20.633 7.658 2.224 20.85 18.878 3.55 26.676 6.895-10.82-6.118-17.806-4.405-23.72-1.713 5.914-16.44 4.842-16.4 17.677z" fill-rule="evenodd" fill="#006a00"/>
      <path d="M336.61 432.28c35.433 124.02-53.15 106.3-53.15 141.73 0-35.433-88.582-17.717-53.15-141.73" stroke="#000" fill="none" transform="matrix(-.1957 -.057 .0484 -.167 241.046 308.198)"/>
    </g>
    <g stroke="#000">
      <path d="M193.63 88.25c15.546 4.423 43.275 46.12 53.766 45.79 16.652-.48 83.233-77.56 72.564-88.252-7.4-8.246-67.94 41.192-71.852 44.126-.67-8.6-15.95-51.042-28.31-59.945-14.62-9.31-32.21-4.158-47.82-6.662-7.3-2.843-18.84-14.056-23.78-24.976 8.182-3.544 15.323-2.48 23.31-10.65 5.68-6.643 2.382-46.914.37-51.06-13.29-14.268 5.43-7.327-39.023-9.082-64.268-2.446-75.878 49.582-31.653 70.792-3.462 15.928-14.22 19.594-22.94 21.646-8.776.106-38.825-.763-55.545 12.49-33.403 24.55-8.925 30.903-15.352 35.8C.192 73.91-10.503 88.093-14.523 96.573c-10.472 4.996-99.485-4.995-116.5-1.664-16.42.965-56.467 15.818-73.15 26.64 3.565 14.143 241.51-7.602 249.27-2.496 5.946 3.345-2.705 51.237 2.01 54.95 1.66 9.062 116.54 5.828 125.6 5.828 7.558-18.86 15.39-81.558 20.926-91.583z" fill-rule="evenodd" transform="matrix(.1285 0 0 .2044 363.593 187.474)" stroke-width="4.702" fill="#520900"/>
      <path d="M174.9-96.568c1.543 25.457-7.118 3.304-38.37 3.304C75.97-98.3 97.246 5.932 97.246-26.24 71.018-39.95 52.41-126.53 126.86-123.17c23.54 0 44.96 7.858 48.045 26.603z" fill-rule="evenodd" transform="matrix(.1542 0 0 .1434 359.98 188.333)" stroke-width="5.125"/>
      <path d="M387.98 96.574c0 1.68-.172 7.957-.833 11.656-.445 3.74-1.54 8.077-2.498 10.823-.343 4.452-2.092 5.174-2.498 9.99-1.544 2.27-2.21 5.945-2.498 9.992 0 4.27.686 7.558.833 11.656.48 3.56 1.612 6.367 1.665 10.824 0 4.365-.005 7.55-1.665 9.99v5.83" transform="matrix(.202 0 0 .2044 292.388 181.09)" stroke-width="3.75" fill="none"/>
      <path d="M383.81 76.592c.58 0 1.03 20.13 5.828 24.978 1.04 3.212 4.574 7.343 5.828 9.99 1.672 2.46 3.98 5.368 8.326 6.66h11.656c4.634 0 7.473.056 10.823-1.664 1.486-.8 2.532-1.846 3.33-3.33m12.489 2.494h.832c-2.327 0-1.092-.104 3.33 1.665 1.726 2.537 3.77 3.625 6.66 4.996 2.983 1.468 6.392 1.666 10.824 1.666 4.198-.354 6.74-1.686 9.16-3.33 4.042-1.31 4.814-2.936 5.827-7.494v-11.656c-.478-3.53-1.384-6.372-2.498-9.158-.42-1.258-.833-.705-.833-2.497m34.968 31.638c-.425 0-2.65 2.575-4.163 4.995-2.568.918-3.233 3.716-4.162 5.828-.67 2.675-1.666 2.86-1.666 6.66-1.678 3.71-2.205 7.008-2.498 10.824l2.497-10.823c-1.678 3.708-2.205 7.007-2.498 10.823m-36.63-94.911h-.833c2.25 0 1.1-.053-3.33.833-1.817 2.27-4.807 4.304-7.493 5.826-2.977 1.825-5.367 4.01-8.326 4.996l8.326-4.996c-2.977 1.825-5.367 4.01-8.326 4.996M402.13 51.615h.833c-2.248 0-1.105-.054 3.33.833 2.936 1.39 4.752 2.89 7.493 4.995 1.29 1.935 3.257 2.305 4.163 5.828 1.573.848 2.123 1.67 2.497 4.164m6.664-37.467h4.163c4.53 0 6.733.68 10.824.833 3.538-.424 4.382-1.737 7.493-2.497m-6.66 104.906c.14.66 1.652 4.256 2.498 5.828 1.517 2.512 3.538 4.914 4.163 8.326.97 2.905 1.527 6.23 1.666 9.99.76 3.503 1.264 6.62-.833 9.16-.898 3.29-2.57 5.69-4.162 9.157-1.11 0-.832-.277-.832.834m-29.15-41.635v.833c0-2.248.055-1.105-.832 3.33-1.147 4.013-2.15 7.388-4.163 9.99-1.098 3.295-2.326 4.505-2.498 9.16.662 1.543.833 3.262.833 5.827m2.5 5v4.163c.908 1.21.55 1.294 1.665 1.665M343.02 128.21v.832c0-2.327-.258-.936 4.163.833 3.572.638 7.937 1.037 10.823 1.665 2.672 1.312 5.265 3.48 7.493 4.995 1.993 1.728 4.818 3.48 7.493 4.996 3.004 1.503 4.325 2.824 5.828 5.83.908 1.21.55 1.292 1.666 1.664" transform="matrix(.202 0 0 .2044 292.388 181.09)" stroke-width="3.75" fill="none"/>
      <path d="M423.78-16.655c.123-.184 2.467-1.914 4.996-2.498 1.805-1.354 4.812-.74 6.66 0" transform="matrix(.3155 0 0 .2044 242.372 181.81)" stroke-width="3" fill="none"/>
      <path d="M445.42-19.153h.833c-2.248 0-1.106.054 3.33-.833 3.48-1.16 7.672-.832 11.656-.832 1.173.39.83.242.83 1.665M444.59-7.497v.833c0-2.327-.103-1.09 1.666 3.33.643 2.962 1.76 7.05.832 9.158-2.184 0-2.86-.274-4.163-.833" transform="matrix(.2454 0 0 .2044 272.086 181.81)" stroke-width="3.402" fill="none"/>
      <path d="M371.33-24.98c0 3.218-4.1 5.827-9.158 5.827s-9.158-2.61-9.158-5.828 4.1-5.83 9.158-5.83 9.158 2.61 9.158 5.83z" fill-rule="evenodd" transform="matrix(.2418 -.035 .024 .115 294.955 200.285)" stroke-width="4.501"/>
      <path d="M371.33-24.98c0 3.218-4.1 5.827-9.158 5.827s-9.158-2.61-9.158-5.828 4.1-5.83 9.158-5.83 9.158 2.61 9.158 5.83z" fill-rule="evenodd" transform="matrix(.175 -.023 .0174 .0756 315.095 188.724)" stroke-width="6.528"/>
      <path d="M371.33-24.98c0 3.218-4.1 5.827-9.158 5.827s-9.158-2.61-9.158-5.828 4.1-5.83 9.158-5.83 9.158 2.61 9.158 5.83z" fill-rule="evenodd" transform="matrix(.175 -.023 .0174 .0756 320.41 188.214)" stroke-width="6.528"/>
      <path d="M412.95 113.64c0 2.07-1.864 3.747-4.163 3.747-2.3 0-4.163-1.678-4.163-3.747s1.864-3.747 4.163-3.747c2.3 0 4.163 1.678 4.163 3.747zm56.62 6.25c0 1.38-1.305 2.498-2.914 2.498-1.61 0-2.914-1.12-2.914-2.498s1.305-2.498 2.914-2.498c1.61 0 2.914 1.12 2.914 2.498z" fill-rule="evenodd" transform="matrix(.202 0 0 .2044 292.388 181.09)" stroke-width="3.75"/>
      <path d="M447.09 31.634v.833c0-2.796.197-1.03-4.163 3.33-3.724 1.785-5.164 3.667-6.66 6.66-1.742 3.85-2.342 6.613-2.498 10.824v6.662M419.61 30.8c.577 0 1.477 2.274 2.498 4.996 1.856 2.19 3.037 5.35 3.33 9.16 0 4.517.78 6.452.833 10.823v3.33m78.269 69.931c0 .48-2.6 1.455-4.163 3.33-3.16.428-5.964.833-9.99.833-.61-.916-1.836-1.217-3.33-1.665" transform="matrix(.202 0 0 .2044 292.388 181.09)" stroke-width="3.75" fill="none"/>
    </g>
    <g fill-rule="evenodd" stroke="#000" stroke-width="3.75" fill="#fff">
      <path d="M276.41 36.63l88.253 1.665V49.95s8.325 14.987 13.32 48.29c4.996 33.303 9.992 158.19 9.992 158.19l21.646 141.54s-13.32 21.647-23.31 19.982c-9.99-1.666-39.964-13.32-39.964-14.987 0-1.665-1.665-79.926-4.995-133.21-3.33-53.285-9.99-81.592-24.976-104.9-19.982 48.288-21.647 98.243-19.982 98.243s1.665 39.963 3.33 69.936c1.666 29.972-1.665 71.6-1.665 71.6l-58.28-3.33s1.665-36.633 3.33-64.94c1.666-28.307 6.745-41.625 8.327-73.266 1.748-34.963 4.202-53.32 6.66-109.9 3.238-45.74 14.687-75.258 17.9-103.24.87-7.133-.416-12.488.416-13.32z" transform="matrix(.209 0 0 .1326 311.165 212.592)"/>
      <path d="M316.38 166.51s-8.326-13.32-11.656-36.633-3.33-28.307-3.33-28.307m-3.334 131.55l-9.99 39.963" transform="matrix(.209 0 0 .1326 311.165 212.592)"/>
      <path d="M276.41 223.12l11.657 54.95" transform="matrix(.209 0 0 .1326 323.69 212.812)"/>
      <path d="M387.98 417.95l-11.66-81.6M276.41 44.955l88.253 1.665M318.04 164.84c0-1.665 4.995-29.972 4.995-29.972M284.74 44.955l-8.326 84.922" transform="matrix(.209 0 0 .1326 311.165 212.592)"/>
    </g>
    <path d="M349.68 188.16c0 13.794-7.082 24.977-15.82 24.977-8.736 0-15.818-11.183-15.818-24.977s7.082-24.977 15.82-24.977c8.735 0 15.818 11.183 15.818 24.977z" fill-rule="evenodd" transform="matrix(.3368 0 0 .3286 143.1 205.98)" stroke="#000" stroke-width="2.5" fill="#ffc600"/>
    <g stroke-width="1pt">
      <g fill-rule="evenodd" stroke="#000">
        <path d="M212.6 892.91s53.15-17.716 70.867-17.716c17.716 0 17.716 17.716 19.135 18.212l-1.42 34.937H212.6V892.91z" transform="matrix(.2036 0 0 .241 240.01 90.375)" fill="#00cfe6"/>
        <path d="M265.75 892.91s0-17.716 17.717-17.716c17.716 0 35.433-.496 53.15 0v53.15H265.75V892.91z" transform="matrix(.2036 0 0 -.241 247.225 541.99)" fill="#00cfe6"/>
        <path d="M212.6 892.91s53.15-17.716 70.867-17.716c17.716 0 17.716 17.716 19.135 18.212l-1.42 34.937H212.6V892.91z" transform="matrix(-.2036 0 0 .241 413.137 90.428)" fill="#00cfe6"/>
        <path d="M265.75 892.91s0-17.716 17.717-17.716c17.716 0 35.433-.496 53.15 0v53.15H265.75V892.91z" transform="matrix(-.2036 0 0 -.241 405.924 542.038)" fill="#00cfe6"/>
        <path d="M249.45 875.2s0-17.717-17.716-17.717h53.15L249.45 875.2z" transform="matrix(-.2036 0 0 -.241 395.393 537.773)" fill="#fff"/>
        <path d="M159.45 892.91l106.3 53.15s-36.04 33.3-70.866 35.433c-17.717 0-35.433-35.433-35.433-35.433v-53.15z" transform="matrix(-.2375 0 0 .241 418.548 90.427)" fill="#00cfe6"/>
        <path d="M249.45 892.91c.71-8.858-2.214-35.434-19.93-35.434h55.363c17.717 0 35.434 17.717 35.434 35.434l-1.42 35.432H249.45V892.91z" transform="matrix(-.2036 0 0 .241 402.606 94.697)" fill="#fff"/>
        <path d="M249.45 875.2s0-17.717-17.716-17.717h53.15L249.45 875.2z" transform="matrix(.2036 0 0 -.241 257.756 537.725)" fill="#fff"/>
        <path d="M159.45 892.91l106.3 53.15s-36.04 33.3-70.866 35.433c-17.717 0-35.433-35.433-35.433-35.433v-53.15z" transform="matrix(.2375 0 0 .241 234.6 90.373)" fill="#00cfe6"/>
        <path d="M249.45 892.91c.71-8.858-2.214-35.434-19.93-35.434h55.363c17.717 0 35.434 17.717 35.434 35.434l-1.42 35.432H249.45V892.91z" transform="matrix(.2036 0 0 .241 250.543 94.643)" fill="#fff"/>
        <path d="M88.802 891.9c-.11 1.012 17.607-34.42 53.04-34.42s177.16 70.865 247.92 70.865c70.975 0 196.65-72.56 230.42-70.866 33.773 1.693 53.52 37.16 53.15 35.432-.374-1.728-17.717 53.15-17.717 53.15 0-17.716-17.717-35.433-35.433-35.433-17.717 0-106.3 70.865-230.42 70.865-123.91 0-212.49-70.866-230.21-70.866-17.717 0-35.433 17.715-35.433 35.432l-35.32-54.16z" transform="matrix(.2036 0 0 .241 247.202 90.428)" fill="#fff"/>
      </g>
      <path d="M362.457 313.76l-3.153-9.625 3.228-1.48.373 1.135-2.638 1.21.966 2.948 2.47-1.133.37 1.128-2.47 1.133 1.072 3.276 2.74-1.258.374 1.137-3.333 1.53zm-13.537 6.302l-3.153-9.625.59-.27 2.78 8.487 2.2-1.01.373 1.138-2.79 1.28zm-4.097 1.924l-3.153-9.625 3.012-1.38.372 1.136-2.42 1.11.975 2.98 2.094-.962.373 1.137-2.095.962 1.432 4.372-.59.272zm-36.27.124l2.08-9.925 1.215.356-.027 7.453c-.003.692-.008 1.212-.015 1.554.144-.34.365-.84.658-1.494l2.908-6.478 1.088.32-2.08 9.925-.778-.228 1.74-8.31-3.514 7.79-.73-.215.006-8.968-1.77 8.45-.78-.23zm-.356-10.882l.77.377-1.915 5.457c-.332.948-.663 1.67-.99 2.165-.33.493-.727.83-1.197 1.012-.465.18-.978.13-1.538-.146-.544-.267-.936-.635-1.175-1.112-.24-.474-.33-1.037-.278-1.685.056-.652.26-1.478.61-2.48l1.913-5.456.77.38-1.912 5.45c-.288.82-.454 1.448-.5 1.884-.04.432.018.81.17 1.133.158.322.4.565.73.727.566.278 1.04.27 1.427-.03.387-.3.788-1.04 1.204-2.226l1.91-5.45zm-15.467-6.582l.575.315-2.115 5.413c-.367.94-.71 1.662-1.025 2.163-.315.5-.67.852-1.066 1.06-.392.205-.797.19-1.216-.038a1.67 1.67 0 0 1-.822-1c-.14-.444-.155-.982-.042-1.613.114-.636.364-1.45.752-2.442l2.115-5.413.575.315-2.112 5.406c-.318.814-.518 1.435-.6 1.86-.08.42-.074.783.014 1.086.09.302.26.52.506.656.423.232.805.19 1.146-.124.342-.314.742-1.06 1.2-2.236l2.114-5.406zm-10.265 1.681l.583.195c-.158.484-.25.9-.275 1.256-.022.35.03.672.154.97.126.288.313.502.56.637.22.12.44.156.663.108a1.11 1.11 0 0 0 .598-.37c.18-.202.327-.45.44-.737.115-.296.175-.573.18-.833a1.6 1.6 0 0 0-.174-.77c-.078-.162-.276-.454-.596-.876-.317-.428-.52-.776-.61-1.047a2.84 2.84 0 0 1-.125-1.126c.035-.403.138-.822.306-1.25.184-.474.418-.88.703-1.22.286-.346.59-.56.912-.642.323-.083.63-.043.922.117.322.175.562.443.72.803.16.357.22.79.184 1.295a5.82 5.82 0 0 1-.363 1.594l-.586-.213c.202-.61.276-1.112.223-1.508-.052-.396-.245-.684-.58-.867-.347-.19-.655-.192-.923-.003-.265.186-.472.473-.625.862-.13.335-.182.643-.154.922.026.28.227.66.603 1.147.38.48.625.854.732 1.12.154.39.216.81.186 1.265-.03.45-.142.925-.338 1.428a4.75 4.75 0 0 1-.75 1.304c-.304.365-.63.604-.975.715a1.207 1.207 0 0 1-.978-.094 1.861 1.861 0 0 1-.846-.91c-.17-.393-.233-.877-.192-1.454.045-.58.186-1.186.423-1.82zm83.099.113c-.523-1.594-.733-2.937-.63-4.02.1-1.088.466-1.777 1.095-2.066.412-.19.852-.147 1.322.126s.916.75 1.34 1.433c.42.677.79 1.495 1.105 2.46.32.974.515 1.89.585 2.744.068.853.002 1.558-.203 2.11-.207.546-.504.907-.892 1.085-.42.193-.867.147-1.342-.14-.474-.283-.92-.766-1.338-1.444s-.767-1.442-1.044-2.287zm.616-.258c.38 1.16.823 2.007 1.33 2.545.51.534.982.7 1.42.5.444-.205.698-.71.763-1.517.068-.804-.1-1.827-.507-3.065-.257-.784-.542-1.44-.857-1.964-.314-.53-.642-.9-.985-1.11-.34-.215-.66-.256-.954-.12-.417.19-.675.667-.774 1.427-.1.755.09 1.858.564 3.305zm-8.91 9.846l-3.152-9.625 1.98-.908c.397-.182.728-.234.993-.154.264.078.53.305.796.686.268.382.487.836.66 1.366.225.683.31 1.305.26 1.87-.052.56-.272 1.002-.658 1.326.21.09.38.196.513.315.285.263.59.615.91 1.056l1.635 2.263-.743.34-1.246-1.73c-.363-.5-.65-.876-.862-1.132-.21-.254-.383-.423-.512-.504a.751.751 0 0 0-.35-.133c-.08 0-.193.034-.346.104l-.685.314 1.402 4.277-.59.27zm-1.17-5.648l1.27-.582c.27-.124.462-.282.576-.47.112-.192.164-.436.158-.735a3.285 3.285 0 0 0-.166-.922c-.15-.453-.348-.792-.6-1.015-.247-.224-.537-.26-.868-.108l-1.413.65 1.044 3.183zm-5.578 3.264c-.522-1.595-.734-2.937-.63-4.02.1-1.09.466-1.777 1.095-2.066.412-.19.853-.148 1.323.125s.916.75 1.34 1.433c.42.677.79 1.496 1.105 2.46.32.975.515 1.89.585 2.745.07.853 0 1.558-.203 2.11-.206.547-.504.908-.89 1.086-.422.193-.868.147-1.343-.14-.474-.283-.92-.766-1.338-1.445-.42-.678-.767-1.44-1.044-2.286zm.615-.26c.38 1.16.824 2.01 1.332 2.546.508.534.98.7 1.416.5.445-.204.7-.71.765-1.517.07-.804-.1-1.827-.506-3.065-.256-.784-.543-1.44-.857-1.964-.314-.53-.642-.9-.984-1.11-.34-.216-.66-.257-.954-.122-.42.192-.677.67-.776 1.428-.098.756.09 1.858.563 3.305zm-34.213 10.904l1.186-10.13 2.336.384c.477.078.846.244 1.108.5.265.25.452.6.564 1.055.115.452.145.91.09 1.375a3.34 3.34 0 0 1-.36 1.187 2.02 2.02 0 0 1-.762.818c.355.242.61.594.76 1.06.152.466.194.996.125 1.585a4.456 4.456 0 0 1-.34 1.296c-.17.383-.36.673-.566.863a1.623 1.623 0 0 1-.743.392c-.284.066-.625.068-1.022.003l-2.374-.39zm1.51-5.74l1.35.222c.364.06.63.063.798.014a.922.922 0 0 0 .53-.43c.137-.216.225-.502.266-.857.04-.336.025-.64-.045-.908-.07-.276-.19-.474-.363-.596-.173-.127-.484-.227-.93-.3l-1.246-.205-.36 3.06zm-.546 4.68l1.55.253c.267.045.456.06.568.046.196-.026.365-.092.508-.198.143-.11.27-.28.38-.51.11-.234.185-.514.223-.835.044-.38.023-.715-.063-1.01-.086-.302-.23-.525-.43-.67-.195-.15-.49-.256-.888-.32l-1.44-.237-.408 3.48zm6.362 2.297v-10.227h2.79c.558 0 .985.093 1.277.28.29.18.525.503.7.97.174.464.26.977.26 1.54 0 .726-.144 1.337-.433 1.836-.29.496-.734.812-1.338.948.22.172.388.34.502.508.243.363.473.817.69 1.36l1.094 2.785h-1.046l-.833-2.128a22.78 22.78 0 0 0-.6-1.41c-.158-.325-.3-.554-.425-.683a1.053 1.053 0 0 0-.377-.272c-.094-.034-.248-.05-.463-.05h-.965v4.543h-.832zm.832-5.713h1.79c.38 0 .677-.065.89-.19.216-.13.378-.333.49-.612a2.5 2.5 0 0 0 .167-.922c0-.482-.11-.88-.326-1.192-.215-.312-.556-.468-1.02-.468h-1.992v3.384zm6.453 5.441l.87-10.568.884-.186 4.04 9.538-.933.196-1.18-2.9-2.588.543-.225 3.194-.87.183zm1.168-4.51l2.1-.44-1.065-2.652a38.953 38.953 0 0 1-.75-1.997c.017.663 0 1.328-.047 1.993l-.237 3.097zm-40.427-5.503l3.66-9.37 1.63.893c.333.182.56.424.686.725.128.298.17.675.13 1.132a4.78 4.78 0 0 1-.31 1.328c-.157.4-.355.75-.594 1.048s-.503.5-.79.608c.196.31.288.702.272 1.18-.015.477-.13.99-.342 1.535-.17.437-.374.822-.61 1.155-.23.328-.45.56-.656.695a1.181 1.181 0 0 1-.66.204c-.23 0-.483-.076-.76-.227l-1.656-.907zm2.697-5.118l.94.514c.255.14.452.203.59.193.183-.015.355-.112.512-.29.16-.174.305-.427.433-.755.12-.31.193-.603.216-.874.024-.276-.012-.492-.107-.647-.094-.16-.297-.325-.61-.496l-.868-.475-1.107 2.83zm-1.69 4.326l1.082.592c.186.102.322.158.408.17a.715.715 0 0 0 .432-.074c.136-.07.277-.204.422-.398.147-.198.28-.447.395-.745a3.15 3.15 0 0 0 .23-.975c.02-.306-.025-.55-.134-.733-.104-.187-.295-.355-.572-.506l-1.005-.552-1.257 3.22z"/>
    </g>
    <path d="M212.6 432.28v106.3c0 70.866 88.583 106.3 88.583 106.3s88.583-35.433 88.583-106.3v-106.3h-177.17z" fill-rule="evenodd" transform="matrix(.4072 0 0 .3835 201.1 45.176)" stroke="#000" stroke-width="1pt" fill="#00daec"/>
    <path d="M212.6 432.28v106.3a83.104 83.104 0 0 0 3.806 24.914l84.777-42.63V432.28H212.6z" fill-rule="evenodd" transform="matrix(.4072 0 0 .3835 201.1 45.176)" stroke="#000" stroke-width="1pt" fill="#fff"/>
    <path d="M212.6 432.28v106.3a83.104 83.104 0 0 0 3.806 24.914l84.777-42.63V432.28H212.6z" fill-rule="evenodd" transform="matrix(-.4072 0 0 .3835 446.388 45.176)" stroke="#000" stroke-width="1pt" fill="#fff300"/>
    <g fill-rule="evenodd" transform="matrix(.4072 0 0 .3835 201.1 45.176)" stroke="#000" stroke-width="1.25">
      <path d="M244.77 87.42c7.322-5.948 11.157-3.72 17.65 3.1 9.322 9.815 41.317 58.855 46.312 63.019s4.24-6.418 10.344-1.388c3.032 3.727-10.437 17.026-13.737 14.57-6.412-5.022 3.914-5.376-.355-9.645-4.925-5.133-51.215-40.497-60.282-50.57-5.92-6.578-6.314-13.97.067-19.088z" transform="matrix(.6394 .2374 .1423 .7022 58.9 338.88)" fill="#923d09"/>
      <rect ry="1.293" rx="2.606" transform="matrix(.7556 .655 -.586 .8104 0 0)" height="66.045" width="5.212" y="181.35" x="495.52" fill="#af8000"/>
      <path transform="matrix(.8312 .556 -.641 .7675 0 0)" fill="#b0b0d4" d="M497.3 228.63h29.375v18.868H497.3z"/>
    </g>
    <g stroke="#000" fill-rule="evenodd" transform="matrix(.4072 0 0 .3835 201.1 45.176)">
      <path d="M385.112 528.567c4.885-2.105-27.333-35.603-33.785-51.842-6.452-16.24-6.108-16.717-8.617-24.322l-3.15 1.36c.805 4.93 15.87 43.87 30.735 64.15 4.198 5.82.63 16.77 5.36 14.73l9.457-4.076z" stroke-width="1pt" fill="#af8000"/>
      <path d="M341.548 453.55l-25.872 2.485s-4.83 4.107-2.573 12.665c2.255 8.558 8.965 11.892 8.965 11.892l22.668-17.557-3.188-9.486z" stroke-width="1pt" fill="gray"/>
      <rect ry="3.532" rx="3.532" transform="rotate(-18.58)" height="19.428" width="7.064" y="534.62" x="175.71" stroke-width="1pt" fill="gray"/>
      <path d="M239.61 381.52c0 7.316-1.58 13.246-3.532 13.246-1.95 0-3.533-5.93-3.533-13.246 0-7.316 1.582-13.246 3.533-13.246s3.532 5.93 3.532 13.246z" transform="matrix(.7447 -.716 .6347 .3935 -105.9 523.83)" stroke-width="1pt" fill="#cf0000"/>
      <path d="M239.61 381.52c0 7.316-1.58 13.246-3.532 13.246-1.95 0-3.533-5.93-3.533-13.246 0-7.316 1.582-13.246 3.533-13.246s3.532 5.93 3.532 13.246z" transform="matrix(-.978 .311 .0275 .7554 595.47 104.78)" stroke-width="1pt" fill="#cf0000"/>
      <path transform="matrix(.859 -.512 .4994 .8664 0 0)" stroke-width="1.153" fill="#b0b0d4" d="M15.073 596.25H93.94v17.796H15.074z"/>
    </g>
    <path d="M231.36 591.4l8.83-7.065s1.767 8.83 7.065 8.83 8.83-7.652 8.83-7.652 0 7.653 5.888 7.653 10.597-7.653 10.597-7.064c0 .588.588 7.064 5.298 7.064s10.597-6.476 10.597-6.476.59 7.654 5.887 7.065c5.3-.59 7.065-8.242 7.653-7.653.59.588 4.12 7.653 8.242 7.064s10.01-6.476 10.01-6.476 1.176 7.065 5.886 7.065 6.476-6.476 6.476-6.476 2.353 5.297 6.474 5.297 10.597-5.887 10.597-5.887" transform="matrix(.389 0 0 .3835 211.313 51.724)" stroke="#0072ff" stroke-width="1pt" fill="none"/>
    <path d="M231.95 591.4c3.14-1.374 5.69.196 9.418-7.654 0 4.12 2.945 9.42 8.243 9.42s8.832-7.653 8.832-7.653 0 7.653 5.887 7.653 10.596-7.653 10.596-7.064c0 .588.588 7.064 5.298 7.064s10.597-6.476 10.597-6.476.59 7.654 5.888 7.065c5.3-.59 7.065-8.242 7.653-7.653.59.588 4.122 7.653 8.243 7.064 2.943-.59 10.01-7.065 10.01-7.065" transform="matrix(.4072 0 0 .3835 212.128 55.788)" stroke="#0072ff" stroke-width="1pt" fill="none"/>
    <g fill-rule="evenodd">
      <path d="M305.54 350.62v18.84h14.717l-5.298-18.84h-9.42z" transform="matrix(.3216 0 0 .286 236.636 162.272)" stroke="#000" stroke-width="1pt" fill="#fff"/>
      <path d="M341.348 265.413c-.19.17-7.574 3.705-17.23 3.705s-13.445-1.01-14.39-1.684c-.948-.673-2.084-2.02-3.22-2.02-1.137 0-2.083.842-2.083.842s1.893.84 3.22 1.85c1.324 1.012 1.703 2.864 1.703 2.864l31.243.337s-.188-1.853-.188-2.863 1.136-2.525.946-3.03z"/>
      <path d="M314.083 253.458h.758v15.66h-.757zm10.225-2.022h.758v17.848h-.758zm10.035 3.032h.758v14.65h-.757z"/>
      <path stroke="#000" stroke-width="1.024" fill="#fff" d="M299.07 342.37h18.84v8.83h-18.84z" transform="matrix(.3216 0 0 .286 235.31 157.557)"/>
      <path stroke="#000" stroke-width="1.101" fill="#fff" d="M297.89 350.61h21.782v8.83H297.89z" transform="matrix(.3216 0 0 .286 235.31 157.557)"/>
      <path stroke="#000" stroke-width="1.159" fill="#fff" d="M296.71 358.86h24.137v8.83H296.71z" transform="matrix(.3216 0 0 .286 235.31 157.557)"/>
      <path stroke="#000" stroke-width="1.372" fill="#fff" d="M266.17 334.72h23.837v12.527H266.17z" transform="matrix(.3216 0 0 .286 235.31 157.557)"/>
      <path stroke="#000" stroke-width="1.475" fill="#fff" d="M264.68 346.41h27.56v12.527h-27.56z" transform="matrix(.3216 0 0 .286 235.31 157.557)"/>
      <path stroke="#000" stroke-width="1.553" fill="#fff" d="M263.19 358.1h30.54v12.527h-30.54z" transform="matrix(.3216 0 0 .286 235.31 157.557)"/>
      <path stroke="#000" stroke-width="1.312" fill="#fff" d="M260.21 368.28h36.5v15.307h-36.5z" transform="matrix(.3216 0 0 .286 235.31 157.557)"/>
      <path stroke="#000" stroke-width="1.017" fill="#fff" d="M238.25 342.37h15.763v10.414H238.25z" transform="matrix(.3216 0 0 .286 235.31 157.557)"/>
      <path stroke="#000" stroke-width="1.094" fill="#fff" d="M237.26 352.09h18.226v10.414H237.26z" transform="matrix(.3216 0 0 .286 235.31 157.557)"/>
      <path stroke="#000" stroke-width="1.151" fill="#fff" d="M236.28 361.81h20.197v10.414H236.28z" transform="matrix(.3216 0 0 .286 235.31 157.557)"/>
      <path stroke="#000" stroke-width=".973" fill="#fff" d="M234.31 370.27h24.137v12.725H234.31z" transform="matrix(.3216 0 0 .286 235.31 157.557)"/>
      <path d="M220 377.51h9.876l10.597-33.374L220 377.51z" stroke="#000" stroke-width="1.017" fill="#fff" transform="matrix(.3216 0 0 .286 235.31 157.557)"/>
      <path d="M314.254 253.63l.7.308-9.188 12.648-.7-.307z"/>
    </g>
    <path d="M231.36 591.4l8.83-7.065s1.767 8.83 7.065 8.83 8.83-7.652 8.83-7.652 0 7.653 5.888 7.653 10.597-7.653 10.597-7.064c0 .588.588 7.064 5.298 7.064s10.597-6.476 10.597-6.476.59 7.654 5.887 7.065c5.3-.59 7.065-8.242 7.653-7.653.59.588 4.12 7.653 8.242 7.064s10.01-6.476 10.01-6.476 1.176 7.065 5.886 7.065 6.476-6.476 6.476-6.476 2.353 5.297 6.474 5.297 7.065-7.064 7.065-7.064 1.176 6.476 4.12 6.476c2.943 0 6.476-6.477 6.476-6.477s1.177 8.242 4.12 7.653c2.944-.59 4.122-8.242 4.71-7.653.59.59 4.71 7.064 4.12 7.064" transform="matrix(.3847 0 0 .3835 207.988 46.983)" stroke="#0072ff" stroke-width="1pt" fill="none"/>
  </g>
</svg>
