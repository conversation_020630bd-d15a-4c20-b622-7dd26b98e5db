<template>
  <user-setting-template
    v-if="item"
    :title="$t('summary')"
    :submit-func="submitData"
  >
    <div class="mb-md-2">
      <v-row>
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-1">
              {{ $t('short_summary') }}
            </div>
            <div class="input-wrap-label">
              {{
                $t(
                  'provide_short_and_snappy_description_of_what_you_specialise_in_teaching'
                )
              }}
            </div>
            <v-textarea
              :value="item.shortSummary"
              class="l-textarea"
              no-resize
              height="78"
              counter="120"
              solo
              dense
              :rules="shortSummaryRules"
              @input="updateValue($event, 'shortSummary')"
            ></v-textarea>
          </div>
        </v-col>
      </v-row>
    </div>
    <div class="mb-md-2">
      <v-row>
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-1">
              {{ $t('longer_summary') }}
            </div>
            <div class="input-wrap-label">
              {{
                $t('provide_longer_introduction_of_yourself_and_your_teaching')
              }}
            </div>
            <v-textarea
              :value="item.longSummary"
              class="l-textarea"
              no-resize
              height="140"
              solo
              dense
              counter="500"
              :rules="longSummaryRules"
              @input="updateValue($event, 'longSummary')"
            ></v-textarea>
          </div>
        </v-col>
      </v-row>
    </div>
    <div>
      <v-row>
        <v-col class="col-12">
          <div class="input-wrap-title body-1 font-weight-medium mb-1">
            {{ $t('interesting_facts') }}
          </div>
          <div class="input-wrap-label">
            {{
              $t(
                'what_are_unique_facts_about_you_that_students_might_find_interesting'
              )
            }}
          </div>
        </v-col>
        <v-col class="col-12 col-sm-6 mb-2">
          <div class="input-wrap">
            <v-textarea
              :value="factsAbout[0]"
              class="l-textarea"
              no-resize
              height="80"
              solo
              dense
              hide-details
              :placeholder="$t('Interesting fact number 1')"
              @input="updateFact($event, 0)"
            ></v-textarea>
          </div>
        </v-col>
        <v-col class="col-12 col-sm-6 mb-2">
          <div class="input-wrap">
            <v-textarea
              :value="factsAbout[1]"
              class="l-textarea"
              no-resize
              height="80"
              solo
              dense
              hide-details
              :placeholder="$t('Interesting fact number 2')"
              @input="updateFact($event, 1)"
            ></v-textarea>
          </div>
        </v-col>
        <v-col class="col-12 col-sm-6">
          <div class="input-wrap">
            <v-textarea
              :value="factsAbout[2]"
              class="l-textarea"
              no-resize
              height="80"
              solo
              dense
              hide-details
              :placeholder="$t('Interesting fact number 3')"
              @input="updateFact($event, 2)"
            ></v-textarea>
          </div>
        </v-col>
      </v-row>
    </div>
  </user-setting-template>
</template>

<script>
import UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'

export default {
  name: 'SummaryInfo',
  components: { UserSettingTemplate },
  data() {
    return {
      shortSummaryRules: [(v) => v === null || v?.length <= 120],
      longSummaryRules: [(v) => v === null || v?.length <= 500],
    }
  },
  computed: {
    item() {
      return this.$store.state.settings.summaryItem
    },
    factsAbout() {
      return this.item.factsAbout || []
    },
  },
  beforeCreate() {
    this.$store.dispatch('settings/getSummary')
  },
  methods: {
    updateFact(value, index) {
      const facts = [...this.factsAbout]

      facts[index] = value

      this.updateValue(facts, 'factsAbout')
    },
    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_SUMMARY_ITEM', {
        [property]: value,
      })
    },
    submitData() {
      this.$store.dispatch('settings/updateSummary')
    },
  },
}
</script>
