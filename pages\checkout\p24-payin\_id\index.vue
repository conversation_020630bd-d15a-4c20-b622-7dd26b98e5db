<template>
  <v-col class="col-12 pa-0">
    <div :class="['p24-payin mx-auto', { 'p24-payin--error': isFailure }]">
      <v-container fluid class="py-0">
        <v-row>
          <v-col class="col-12">
            <template v-if="isFailure">
              <div class="text-center">
                <div
                  v-html="
                    $t(
                      'przelewy24_payment_failure_try_again_or_contact_langu_customer_service'
                    )
                  "
                ></div>
                <div v-if="username" class="mt-6">
                  <v-btn
                    class="gradient font-weight-medium"
                    :to="`/teacher/${username}`"
                  >
                    <div class="text--gradient">
                      {{ buttonText }}
                    </div>
                  </v-btn>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="text-center">
                <div v-html="$t('confirming_payment_p24')"></div>
                <p>
                  <a
                    class="text--gradient"
                    href="https://instagram.com/heylangu"
                    target="_blank"
                  >
                    https://instagram.com/heylangu
                  </a>
                </p>
                <div class="spinner mt-6">
                  <v-img
                    class="mx-auto"
                    :src="require('~/assets/images/ajax-loader.gif')"
                    width="48"
                    height="48"
                  ></v-img>
                </div>
              </div>
            </template>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </v-col>
</template>

<script>
const CALL_LIMIT = 100

export default {
  name: 'P24Payin',
  data() {
    return {
      called: 0,
      intervalId: null,
      username: null,
      isFailure: false,
    }
  },
  computed: {
    buttonText() {
      return `${process.env.NUXT_ENV_URL}/teacher/${this.username}`
    },
  },
  beforeMount() {
    this.username = window.localStorage.getItem('teacher-username')

    this.intervalId = window.setInterval(() => {
      this.called++

      if (this.called === CALL_LIMIT) {
        this.clearInterval()

        this.isFailure = true
      }

      this.$store.dispatch('loadingAllow', false)
      this.$store
        .dispatch('purchase/p24Paying', this.$route.params.id)
        .then(async (data) => {
          if (+data?.confirmed === 1) {
            this.clearInterval()
            this.clearLocalStorage()

            if (this.$store.getters['teacher_profile/isSelectedTrial']) {
              await this.$cookiz.set('confirmation_page_allowed', 1, {
                domain: process.env.NUXT_ENV_COOKIE_DOMAIN,
                path: '/',
              })

              return this.$router.push('/user/lessons/confirmation')
            } else {
              await this.$cookiz.set('thank_you_page_allowed', 1, {
                domain: process.env.NUXT_ENV_COOKIE_DOMAIN,
                path: '/',
              })

              return this.$router.push('/user/lessons/thank-you')
            }
          }
        })
        .catch((e) => {
          this.isFailure = true
        })
        .finally(() => {
          this.$store.dispatch('loadingAllow', true)
        })
    }, 5000)
  },
  beforeDestroy() {
    this.clearInterval()
    this.clearLocalStorage()
  },
  methods: {
    clearInterval() {
      clearInterval(this.intervalId)
    },
    clearLocalStorage() {
      window.localStorage.removeItem('teacher-username')
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.p24-payin {
  max-width: 620px;
  min-height: 410px;
  padding: 56px 0 80px;

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding: 38px 0 64px;
  }

  @media only screen and (max-width: $xxs-and-down) {
    padding: 28px 0 45px;
  }

  &--error {
    max-width: 800px;
  }

  a {
    color: var(--v-success-base) !important;
    text-decoration: none;
  }
}
</style>
