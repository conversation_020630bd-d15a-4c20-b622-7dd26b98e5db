@media #{map-get($display-breakpoints, 'sm-and-down')} {
  .container,
  .col-sm,
  .col-sm-auto,
  .col-sm-12,
  .col-sm-11,
  .col-sm-10,
  .col-sm-9,
  .col-sm-8,
  .col-sm-7,
  .col-sm-6,
  .col-sm-5,
  .col-sm-4,
  .col-sm-3,
  .col-sm-2,
  .col-sm-1,
  .col,
  .col-auto,
  .col-12,
  .col-11,
  .col-10,
  .col-9,
  .col-8,
  .col-7,
  .col-6,
  .col-5,
  .col-4,
  .col-3,
  .col-2,
  .col-1 {
    padding: 15px !important;
  }

  .row {
    margin: -15px !important;
  }

  .row.no-gutters {
    margin: 0 !important;
  }

  .row.no-gutters > .col,
  .row.no-gutters > [class*='col-'] {
    padding: 0 !important;
  }
}

.d-mac-13-none {
  @media only screen and (max-width: $mac-13-and-down) {
    display: none !important;
  }
}
.d-mac-13-flex {
  display: none !important;

  @media only screen and (max-width: $mac-13-and-down) {
    display: flex !important;
  }
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
}

.section-head {
  margin-bottom: 65px;
  line-height: 1.3;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    margin-bottom: 45px;
  }

  @media only screen and (max-width: $xsm-and-down) {
    margin-bottom: 40px;
    text-align: center;
  }

  &-title {
    display: inline-block;
    font-size: 32px;
    font-weight: 400;
    letter-spacing: 0.2px;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      font-size: 28px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      font-size: 22px;
    }
  }

  &-subtitle {
    max-width: 884px;
    margin-top: 4px;
    font-size: 32px;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      font-size: 26px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      margin: 8px auto 0;
      font-size: 18px;
      line-height: 1.67;
    }
  }

  &--decorated {
    position: relative;

    @media only screen and (min-width: $xsm-and-up) {
      padding-left: 34px;
    }

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: -6px;
      width: 8px;
      height: 55px;
      background-color: var(--v-orangeLight-base);
      border-radius: 4px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        height: 45px;
        top: -3px;
      }

      @media only screen and (max-width: $xsm-and-down) {
        display: none;
      }
    }
  }
}

.section-bg,
.section-bg .v-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.section-bg .v-image .v-image__image--cover {
  @media #{map-get($display-breakpoints, 'lg-and-up')} {
    background-size: 100% 100% !important;
  }
}

body {
  &.teacher-listing-page,
  &.teacher-profile-page,
  &.user-settings-page,
  &.user-register-page,
  &.user-lessons-page,
  &.user-messages-page,
  &.user-payments-page,
  &.faq-page {
    .theme--light.v-application {
      background-color: var(--v-greyBg-base);
    }
  }

  &.teacher-listing-page {
    .questions {
      margin: 110px 0 25px;

      @media #{map-get($display-breakpoints, 'md-and-up')} {
        width: calc(100% + 50px) !important;
        margin-left: -25px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        margin: 75px 0 25px;
      }

      .container {
        max-width: 100% !important;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding-left: 0 !important;
          padding-right: 0 !important;
        }
      }

      &-content {
        @media #{map-get($display-breakpoints, 'md-and-up')} {
          max-width: 900px;
          margin-left: 0;
        }
      }

      .section-head {
        margin-bottom: 40px;

        &--decorated {
          padding-left: 0;

          &::before {
            display: none;
          }
        }
      }
    }
    .v-radio {
      align-items: center !important;
    }
    .l-checkbox .v-input__slot {
      align-items: center !important;
    }
  }

  &.teacher-profile-page {
    .modal-is-opened {
      .login-sidebar {
        z-index: 1000101 !important;
      }
    }
  }

  &.business-page,
  &.education-page {
    overflow-x: hidden;
  }
}

.l-scroll {
  &::-webkit-scrollbar {
    width: 4px;
    border-radius: 8px;
  }

  &::-webkit-scrollbar-track {
    -webkit-box-shadow: none;
  }

  &::-webkit-scrollbar-thumb {
    margin-right: 5px;
    background-color: var(--v-orange-base);
    border-radius: 8px;
    outline: none;
  }

  &--grey {
    &::-webkit-scrollbar {
      background-color: #eaeaea;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #c0c0c0;
    }
  }

  &--dark-grey {
    &::-webkit-scrollbar {
      background-color: #eaeaea;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #706e6e;
    }
  }

  &--large {
    &::-webkit-scrollbar {
      width: 7px;
      border-radius: 16px;
    }

    &::-webkit-scrollbar-thumb {
      margin-right: 8px;
      border-radius: 16px;
    }
  }
}

.v-application {
  .select {
    &-menu {
      border-radius: 3px !important;
      box-shadow: 0 1px 4px rgb(0 0 0 / 30%) !important;

      .v-list {
        padding: 5px 0 !important;

        &-item {
          min-height: 29px !important;
          color: var(--v-dark-base) !important;
          font-size: 13px !important;
          text-decoration: none;
          line-height: 1.846;
        }
      }
    }

    &-list {
      max-width: 100% !important;
      border-radius: 20px !important;
      box-shadow: 0 8px 17px rgba(17, 46, 90, 0.1) !important;

      .v-list {
        padding: 5px 0 !important;

        &-item,
        &-item__title {
          color: var(--v-dark-base) !important;
          font-size: 14px !important;
          text-decoration: none;
          line-height: 1.2;
          letter-spacing: 0.1px;
        }

        &-item__title,
        &-item__subtitle {
          white-space: normal;
          overflow: unset;
          text-overflow: unset;
        }

        &-item {
          min-height: 40px !important;

          &__content {
            padding: 0 !important;
          }

          &--active {
            color: var(--v-orange-base) !important;

            &::before {
              background: var(--v-orange-base);
              opacity: 0.05 !important;
            }
          }

          &:hover::before {
            background: var(--v-orange-base);
            opacity: 0.05 !important;
          }

          .icon {
            flex-basis: 16px;
            width: 16px;
            height: 16px;
            margin-right: 8px;
            overflow: hidden;

            &-flag {
              flex-basis: 18px;
              width: 18px;
              height: 18px;
              border-radius: 50%;
            }
          }
        }
      }
    }
  }

  .teacher-profile-card-short-description,
  .teacher-profile-card-description {
    p {
      margin-bottom: 0 !important;
      margin: 0 !important;
    }
  }
}

.unselected {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.text--gradient {
  color: var(--v-success-base);
  background: linear-gradient(
    -75deg,
    var(--v-success-base),
    var(--v-primary-base)
  );
  background: -moz-linear-gradient(
    -75deg,
    var(--v-success-base),
    var(--v-primary-base)
  );
  background: -webkit-linear-gradient(
    -75deg,
    var(--v-success-base),
    var(--v-primary-base)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text--red-gradient {
  color: var(--v-redLight-base);
  background: linear-gradient(
    -75deg,
    var(--v-redLight-base) 0%,
    var(--v-orangeLight2-base) 100%
  );
  background: -moz-linear-gradient(
    -75deg,
    var(--v-redLight-base) 0%,
    var(--v-orangeLight2-base) 100%
  );
  background: -webkit-linear-gradient(
    -75deg,
    var(--v-redLight-base) 0%,
    var(--v-orangeLight2-base) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.alert-message {
  padding: 15px;
  font-size: 13px;
  border-radius: 3px;

  &--success {
    background-color: var(--v-greenDark-base);
  }

  &--error {
    background-color: var(--v-red-base);
  }
}

.v-app-bar.v-app-bar--fixed {
  z-index: 101 !important;
}

.v-toolbar__content,
.v-toolbar__extension {
  padding: 0 15px !important;
}

.v-text-field--outlined.v-input--is-focused fieldset,
.v-text-field--outlined.v-input--has-state fieldset {
  border: none !important;
}

.v-text-field--filled > .v-input__control > .v-input__slot {
  background-color: transparent !important;
}
.theme--dark.v-text-field > .v-input__control > .v-input__slot:before,
.v-text-field.v-input--is-focused > .v-input__control > .v-input__slot:after {
  display: none;
}

.l-checkbox {
  .v-input__slot {
    align-items: flex-start !important;
  }

  .v-label {
    line-height: 1.333 !important;
    color: var(--v-darkLight-base) !important;

    .label-icon {
      margin-right: 6px;

      &--time {
        width: 16px;
        height: 17px;

        svg {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .v-input--selection-controls__input {
    position: relative;
    width: 12px;
    height: 12px;
    margin-top: 5px;

    @media #{map-get($display-breakpoints, 'lg-and-up')} {
      margin-top: 2px;
    }

    &::before,
    &::after {
      content: '';
      position: absolute;
      width: 20px;
      height: 20px;
      top: -6px;
      left: 0;
      border-radius: 1px;
    }

    &::before {
      border: 1px solid var(--v-greyDark-base);
    }
  }

  .v-icon {
    display: none !important;
  }

  &.v-input--selection-controls {
    margin-top: 0;
    padding-top: 0;
  }

  &.theme--dark {
    .v-label {
      color: #fff;
    }

    .v-input--selection-controls__input {
      &::before {
        border: 1px solid #f2f2f2;
      }
    }
  }

  &.v-input--is-label-active {
    .v-label {
      color: var(--v-success-base) !important;
    }

    .v-input--selection-controls__input {
      &::before {
        display: none;
      }

      &::after {
        background-image: url('~assets/images/checkbox-marked.png');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
      }
    }
  }
}

.l-radio-button {
  align-items: flex-start !important;

  .v-input--selection-controls {
    margin-top: 0;
    padding-top: 0;
  }

  .v-input--selection-controls__input {
    position: relative;
    width: 16px;
    height: 16px;

    &::before,
    &::after {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      border-radius: 50%;
    }

    &::before {
      border: 1px solid var(--v-greyDark-base);
    }
  }

  .v-icon {
    display: none !important;
  }

  .v-label {
    font-size: 14px;
    font-weight: 400;
    color: var(--v-dark-base);
    letter-spacing: 0.3px !important;
    line-height: 1.333 !important;
  }

  .v-input--selection-controls__input {
    width: 16px;
    height: 16px;
  }

  .v-icon {
    display: none !important;
  }

  &.theme--dark {
    .v-label {
      color: #fff;
    }

    .v-input--selection-controls__input::before {
      border: 1px solid #f2f2f2;
    }
  }

  &.v-item--active {
    .v-label {
      color: var(--v-success-base) !important;
    }

    .v-input--selection-controls__input {
      &::before {
        display: none;
      }

      &::after {
        background-color: var(--v-success-base);
      }
    }

    .v-input--selection-controls__input::after {
      display: block;
    }
  }

  &--type-2 {
    .v-input--selection-controls__input {
      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }

      &::before {
        display: block !important;
        top: 0;
        left: 0;
        background: linear-gradient(
          126.15deg,
          var(--v-green-base) 0%,
          var(--v-primary-base) 102.93%
        );
        border: none;
      }

      &::after {
        top: 1px;
        left: 1px;
        width: calc(100% - 2px);
        height: calc(100% - 2px);
        background-color: #fff;
      }
    }

    &.v-item--active {
      .v-input--selection-controls__input {
        &::after {
          top: 1px;
          left: 1px;
          width: calc(100% - 2px);
          height: calc(100% - 2px);
          border: 3px solid #fff;
          background: linear-gradient(
            126.15deg,
            var(--v-green-base) 0%,
            var(--v-primary-base) 102.93%
          );
        }
      }
    }
  }

  &--active-gradient {
    &.v-item--active {
      label > div {
        color: var(--v-success-base);
        background: linear-gradient(
          -75deg,
          var(--v-success-base),
          var(--v-primary-base)
        );
        background: -moz-linear-gradient(
          -75deg,
          var(--v-success-base),
          var(--v-primary-base)
        );
        background: -webkit-linear-gradient(
          -75deg,
          var(--v-success-base),
          var(--v-primary-base)
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: 600;
      }
    }
  }
}

.l-textarea {
  border-radius: 20px !important;

  .v-input__control {
    & > .v-input__slot {
      padding: 12px !important;
      box-shadow: none !important;
      border: 1px solid #bebebe;
      font-size: 13px;
    }
  }

  &.v-input--is-focused {
    .v-input__control {
      & > .v-input__slot {
        position: relative;
        background-color: transparent !important;

        &::before {
          display: block !important;
          content: '';
          position: absolute;
          top: -1px;
          left: -1px;
          width: calc(100% + 2px);
          height: calc(100% + 2px);
          border-style: none;
          border-radius: 20px;
          padding: 1px;
          background: linear-gradient(
            126.15deg,
            var(--v-success-base) 0%,
            var(--v-primary-base) 102.93%
          );
          -webkit-mask: linear-gradient(#fff 0 0) content-box,
            linear-gradient(#fff 0 0);
          -webkit-mask-composite: destination-out;
          mask-composite: exclude;
          transform: none;
        }
      }
    }
  }
}

.l-select {
  margin-top: 0 !important;
  padding-top: 0 !important;

  .v-select__selections {
    flex-wrap: nowrap;
    padding-right: 24px;
    font-size: 16px !important;
    font-weight: 500 !important;
    line-height: 1.1;
    color: var(--v-greyDark-base) !important;

    .icon {
      width: 18px;
      height: 18px;
      margin-right: 12px;
      overflow: hidden;

      &-flag {
        border-radius: 50%;
      }
    }
  }

  .v-input__control > .v-input__slot:before {
    display: none;
  }

  .v-input__append-inner {
    position: absolute;
    right: 0;
    top: 50%;
    width: 24px;
    height: 12px;
    padding: 0 !important;
    color: var(--v-greyDark-base) !important;
    transform: translateY(-50%);
  }

  &.v-select--is-menu-active .v-input__append-inner {
    transform: translateY(-50%) rotate(180deg);
  }

  .v-input__slot::before {
    display: none !important;
  }

  &.v-input--is-readonly {
    opacity: 0.6;
  }
}

.l-file-input {
  &--input-hidden {
    .v-input__slot {
      display: none !important;
    }
  }
}

.l-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 2px;
  font-size: 12px;
  line-height: 1;
  vertical-align: middle;
  white-space: nowrap;
  text-align: center;
  border-radius: 10px;
  background-color: var(--v-dark-base);
  color: var(--v-orange-base);
  font-weight: 900;
}

.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}

.link-more {
  position: relative;
  display: inline-block;
  padding-right: 25px;
  font-weight: 500;
  text-decoration: none;
  color: var(--v-orange-base) !important;
  transition: all 0.2s;

  &::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    width: 17px;
    height: 12px;
    margin-top: -4px;
    background-image: url('~assets/images/arrow-right.svg');
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
  }

  &:hover {
    font-weight: 900;
  }
}

.form-message {
  position: relative;
  height: 36px;
  padding-top: 8px;
  font-size: 10px;
  line-height: 1.2;
  font-family: Inter, sans-serif;

  @media only screen and (max-width: $xsm-and-down) {
    height: 32px;
    padding-top: 4px;
  }

  &-wrap {
    display: flex;

    &--error {
      color: var(--v-error-base);
    }

    &--success {
      color: var(--v-success-base);
    }
  }

  &-icon {
    margin-right: 5px;
  }
}

.user-settings,
.user-register {
  .input-wrap {
    position: relative;
    flex: 0 0 100%;
    width: 100%;

    &-label {
      margin-bottom: 12px;
      font-size: 13px;
      line-height: 1.3;
      color: #898989;

      @media #{map-get($display-breakpoints, 'xs-only')} {
        margin-bottom: 8px;
      }
    }

    &-title {
      color: var(--v-darkLight-base);

      span {
        font-weight: 400 !important;
        color: var(--v-grey-base);
      }
    }

    &-notice {
      margin-top: 4px;
      font-size: 10px;
    }

    &-error {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
    }
  }
}

.user-register,
.user-settings-panel {
  .v-input {
    border-radius: 16px !important;
    font-size: 14px;

    &.v-input--is-focused {
      .v-input__control {
        .v-input__slot {
          &::before {
            border-radius: 16px !important;
          }
        }
      }
    }

    &.v-select {
      .v-input__slot {
        min-height: 44px !important;
        padding: 0 36px 0 12px !important;
      }

      fieldset {
        border: 1px solid #bebebe !important;
      }

      .v-input__append-inner {
        margin-top: 0 !important;
        position: absolute;
        right: -24px;
        top: 50%;
        width: 12px;
        height: 12px;
        padding: 0 !important;
        color: var(--v-greyDark-base) !important;
        transform: translateY(-50%) !important;
      }

      &--is-menu-active .v-input__append-inner {
        transform: translateY(-50%) rotate(180deg) !important;
      }

      &.v-input--is-focused {
        .v-input__control {
          .v-input__slot {
            position: relative;

            fieldset {
              border-color: transparent !important;
            }

            &::before {
              display: block !important;
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              border-style: none;
              border-radius: 16px;
              padding: 1px;
              background: linear-gradient(
                126.15deg,
                var(--v-success-base) 0%,
                var(--v-primary-base) 102.93%
              );
              -webkit-mask: linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
              -webkit-mask-composite: destination-out;
              mask-composite: exclude;
              transform: none;
            }
          }
        }
      }
    }
  }

  .select-list {
    width: 100%;
    border: 1px solid #bebebe !important;
    border-radius: 6px !important;
    box-shadow: none !important;
    background-color: #fff;

    .v-list {
      padding: 0 !important;

      &-item:hover .v-list-item__title {
        color: var(--v-orange-base) !important;
      }

      .icon {
        width: 24px;
        height: 24px;
        flex-basis: 24px;
        border-radius: 50%;
        box-shadow: 0 0 1px 0 #aaa;
      }
    }
  }
}

.v-application .v-dialog.time-picker {
  height: 100%;

  &:not(.v-dialog--fullscreen) {
    @media only screen and (min-height: 2000px) {
      max-height: 1820px;
    }
  }

  & > .v-card {
    height: 100%;
    padding: 70px 24px 92px !important;

    .dialog-content,
    .wrap,
    .time-picker-body {
      height: 100%;
    }

    .wrap {
      overflow-y: auto;
    }

    .time-picker-body {
      display: flex;
      flex-direction: column;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding: 50px 18px 74px !important;
    }

    @media only screen and (max-width: $xxs-and-down) {
      padding: 50px 10px 74px !important;
    }
  }

  .time-notice {
    color: #969696;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      padding-right: 16px;
    }
  }

  .time-picker {
    &-header {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 70px;
      display: flex;
      align-items: center;
      padding: 0 60px 0 24px;
      font-size: 20px;
      font-weight: 700;
      line-height: 1.1;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        height: 50px;
        padding-left: 18px;
        font-size: 18px;
      }
    }

    &-selects {
      display: flex;

      @media #{map-get($display-breakpoints, 'md-and-up')} {
        justify-content: space-between;
        align-items: center;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        flex-direction: column;
      }

      .selects {
        display: flex;
        align-items: center;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          flex-wrap: wrap;
        }

        & > div {
          margin-right: 24px;

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            margin-bottom: 16px;
          }

          @media only screen and (max-width: $xsm-and-down) {
            margin: 0 18px 12px 0;
          }

          @media only screen and (max-width: $xxs-and-down) {
            margin: 0 12px 12px 0;
          }

          &:last-child {
            margin-right: 0;
          }
        }

        &-course {
          width: 200px;

          @media only screen and (max-width: $xxs-and-down) {
            width: 100%;
          }
        }

        &-language {
          width: 190px;
        }

        &-lesson,
        &-duration {
          width: 140px;
        }

        &-lesson {
          &-value {
            font-size: 16px;
            font-weight: 500;
            color: var(--v-greyDark-base);

            .icon {
              flex-basis: 18px;
              margin-right: 12px;

              @media #{map-get($display-breakpoints, 'sm-and-down')} {
                margin-right: 8px;
              }

              &-flag {
                border-radius: 50%;
                overflow: hidden;
              }
            }
          }
        }

        .v-select {
          div[role='button'] {
            height: auto !important;
          }
        }
      }

      .selects-notice {
        color: #666;
        font-weight: 500;

        @media #{map-get($display-breakpoints, 'md-and-up')} {
          max-width: 235px;
          text-align: right;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          margin: 4px 0 12px;
          font-size: 14px !important;
          line-height: 1.2;
        }

        @media only screen and (max-width: $xsm-and-down) {
          font-size: 12px !important;
        }
      }
    }

    &-footer {
      display: flex;
      align-items: center;
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 92px;
      padding: 0 24px;

      @media #{map-get($display-breakpoints, 'md-and-up')} {
        justify-content: space-between;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        height: 74px;
        flex-direction: column;
        justify-content: center;
        padding: 0 18px;

        .v-btn.v-size--small {
          min-width: 292px !important;
        }
      }
    }
  }
}
.v-main {
  margin-top: 40px;
}
