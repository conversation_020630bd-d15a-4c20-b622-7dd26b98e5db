@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.user-register {
  margin-top: 10px;

  .row {
    margin: 0 -14px !important;
  }

  .col {
    padding: 0 14px !important;
  }

  &-wrap {
    padding: 44px;
    background-color: #fff;
    box-shadow: 0 8px 17px rgba(17, 46, 90, 0.1);
    border-radius: 20px;

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      max-width: 820px;
      margin: 0 auto;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin: 0 15px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin: 0 5px;
      padding: 25px 15px 35px;
    }
  }

  &-title {
    font-size: 24px;
    line-height: 1.333;

    @media only screen and (max-width: $xxs-and-down) {
      font-size: 20px;
    }
  }

  &-content {
    .checkbox-label {
      padding-top: 2px;
    }

    .l-checkbox {
      &.v-input--is-label-active .v-label {
        color: var(--v-darkLight-base) !important;
      }

      .v-input--selection-controls__input {
        margin-top: 5px;
      }
    }

    .time-zone-list {
      width: calc(100% + 16px);

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        width: calc(100% + 8px);
      }

      .item {
        position: relative;
        border-radius: 16px;

        &:not(.selected) {
          background: linear-gradient(
              126.15deg,
              rgba(128, 182, 34, 0.08) 0%,
              rgba(60, 135, 248, 0.08) 102.93%
          );
          cursor: pointer;
        }

        & > div {
          height: 50px;
          padding: 4px 16px;
          border-radius: inherit;

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            height: 42px;
          }

          @media #{map-get($display-breakpoints, 'xs-only')} {
            padding: 4px 10px;
            font-size: 12px !important;
          }
        }

        &:hover,
        &.selected {
          position: relative;
          height: 50px;

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            height: 42px;
          }

          & > div {
            background: linear-gradient(
                126.15deg,
                rgba(128, 182, 34, 0.28) 0%,
                rgba(60, 135, 248, 0.28) 102.93%
            );
          }
        }

        &.selected {
          top: -1px;

          & > div {
            height: inherit;
            margin-top: 1px;
          }

          &::before {
            display: block !important;
            content: '';
            position: absolute;
            top: 0;
            left: -1px;
            width: calc(100% + 2px);
            height: calc(100% + 2px);
            border-style: none;
            border-radius: 20px;
            padding: 1px;
            background: linear-gradient(
                126.15deg,
                var(--v-success-base) 0%,
                var(--v-primary-base) 102.93%
            );
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            transform: none;
          }
        }
      }
    }

    .alert-wrap {
      @media #{map-get($display-breakpoints, 'sm-and-up')} {
        height: 44px;
      }
    }
  }

  &-footer {
    margin-top: 90px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin-top: 60px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-top: 30px;
    }

    .v-btn:not(.v-btn--icon).v-size--default {
      min-width: 180px !important;

      @media only screen and (max-width: $xxs-and-down) {
        width: 100% !important;
      }
    }
  }

  button[type="submit"] svg {
    transform: rotate(-90deg);
  }
}
