@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.user-lessons {
  --sidebar-width: 345px;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    --sidebar-width: 325px;
  }

  @media only screen and (max-width: $mac-13-and-down) {
    --sidebar-width: 298px;
  }

  padding-bottom: 140px;

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding-bottom: 60px;
  }

  &-wrap {
    max-width: 1360px;
    padding-bottom: 25px;
  }

  &-header {
    @media only screen and (min-width: $mac-13-and-up) {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    @media only screen and (max-width: $mac-13-and-down) {
      flex-wrap: wrap;

      & > div {
        width: 100%;
      }
    }
  }

  &-title {
    position: relative;

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      margin-right: 24px;
    }

    @media only screen and (max-width: $mac-13-and-down) {
      margin-right: 0;
    }

    h1 {
      white-space: nowrap;
      font-size: 24px;
      line-height: 1.333;

      @media only screen and (max-width: $xxs-and-down) {
        font-size: 20px;
      }
    }
  }

  &-credits {
    position: absolute;
    left: 0;
    bottom: -20px;
    font-size: 13px;
    color: var(--v-dark-lighten3);
  }

  &-controls {
    justify-content: space-between;
    align-items: center;
    flex-grow: 1;


    @media only screen and (max-width: $mac-13-and-down) {
      margin-top: 18px;
    }

    @media only screen and (min-width: $mac-13-and-up) {
      max-width: 970px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      max-width: 100%;
      flex-wrap: wrap;
    }
  }

  &-search {
    width: 100%;

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      min-width: 240px;
      flex-basis: 380px;
    }
  }

  &-nav {
    min-width: 508px;
    margin-left: 18px;
    padding: 4px;
    background-color: #fff;
    box-shadow: 0 4px 14px rgba(217, 225, 236, 0.47);
    border-radius: 16px;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      min-width: 455px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: 100%;
      min-width: auto;
      margin: 12px 0 0;
    }

    & > a:not(:last-child) {
      margin-right: 4px;
    }

    .v-btn {
      &.nav-btn {
        flex-grow: 1;
        border-radius: 14px;
        background-color: transparent !important;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          width: 33.3333%;
          min-width: 70px !important;
          text-align: center;
        }

        @media only screen and (max-width: $xsm-and-down) {
          font-size: 13px !important;
          font-weight: 400 !important;
        }
      }

      &::before {
        background-color: transparent;
      }

      &:not(.v-btn--active) {
        color: var(--v-greyDark-base);
      }

      &--active,
      &--active:hover {
        &::before {
          background: linear-gradient(
              126.15deg,
              rgba(128, 182, 34, 0.16) 0%,
              rgba(60, 135, 248, 0.16) 102.93%
          );
          opacity: 1;
        }
      }
    }
  }

  &-body {
    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      display: flex;
    }
  }

  &-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: calc(100% - var(--sidebar-width));

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: 100%;
    }

    h1 {
      font-size: 32px;
    }

    .lessons-list > div:not(:last-child) {
      margin-bottom: 20px;

      @media #{map-get($display-breakpoints, 'xs-only')} {
        margin-bottom: 10px;
      }
    }
  }

  &-sidebar {
    width: var(--sidebar-width);

    &-helper {
      padding-left: 20px;

      @media only screen and (max-width: $mac-13-and-down) {
        padding-left: 16px;
      }

      & > div:not(:last-child) {
        margin-bottom: 10px;
      }
    }
  }

  &--student {
    .user-lessons-title {
      @media only screen and (max-width: $mac-13-and-down) {
        margin-bottom: 32px;
      }
    }
  }

  .lessons-list {
    &-empty {
      .steps {
        border-bottom: none !important;
      }
    }
  }
}
