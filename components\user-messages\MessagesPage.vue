<template>
  <v-col class="col-12 px-0">
    <div class="user-messages">
      <v-container fluid class="pa-0">
        <v-row no-gutters>
          <v-col class="col-12">
            <div class="user-messages-wrap mx-auto">
              <div
                class="user-messages-title font-weight-medium mb-3 d-sm-none"
              >
                {{ $t('my_messages') }} 📬
              </div>
              <div v-if="$vuetify.breakpoint.smAndUp" class="d-none d-sm-flex">
                <aside
                  v-if="$vuetify.breakpoint.smAndUp"
                  class="user-messages-sidebar d-none d-sm-block"
                >
                  <div class="user-messages-title font-weight-medium mb-2">
                    {{ $t('my_messages') }} 📬
                  </div>
                  <div class="user-messages-sidebar-sticky">
                    <div class="user-messages-sidebar-search mb-2">
                      <search-input
                        v-model.trim="searchQuery"
                        :disabled="!threadsNotEmpty && !searchQueryParam"
                        placeholder="search_for_recipient"
                        small
                        @submit="searchSubmitHandler"
                      ></search-input>
                    </div>
                    <div v-if="threadsNotEmpty" class="user-messages-tabs-nav">
                      <div ref="threadsList" class="py-1">
                        <div
                          v-for="(thread, idx) in threads"
                          :id="`thread-${thread.id}`"
                          :key="idx"
                          class="nav-item"
                        >
                          <v-btn
                            :class="[
                              'font-weight-regular',
                              { active: selectedThread.id === thread.id },
                            ]"
                            :dark="selectedThread.id === thread.id"
                            width="100%"
                            height="48"
                            @click="threadClickHandler(thread)"
                          >
                            <div class="nav-item-avatar mr-1">
                              <v-img
                                v-if="!thread.isRead"
                                :src="
                                  require('~/assets/images/envelop-icon-gradient.svg')
                                "
                                class="envelop"
                                width="22"
                                height="22"
                              ></v-img>
                              <v-avatar width="52" height="52">
                                <v-img
                                  :src="
                                    getSrcAvatar(
                                      !thread.userIsDeleted
                                        ? thread.avatars
                                        : {},
                                      'user_thumb_52x52'
                                    )
                                  "
                                  :srcset="
                                    getSrcSetAvatar(
                                      !thread.userIsDeleted
                                        ? thread.avatars
                                        : {},
                                      'user_thumb_52x52',
                                      'user_thumb_104x104'
                                    )
                                  "
                                  :options="{ rootMargin: '50%' }"
                                ></v-img>
                              </v-avatar>
                              <user-status
                                v-if="!thread.userIsDeleted"
                                :user-id="thread.userId"
                                :user-statuses="userStatuses"
                              ></user-status>
                            </div>
                            <template v-if="!thread.userIsDeleted">
                              {{ thread.firstName }} {{ thread.lastName }}
                            </template>
                            <template v-else>
                              {{ $t('deleted_user') }}
                            </template>
                          </v-btn>
                        </div>

                        <div
                          v-if="isMoreButtonShown"
                          class="user-messages-sidebar-show-more-btn"
                        >
                          <load-more-btn
                            large
                            :text-btn="$t('load_more_threads')"
                            :fetch-func="fetchThreads"
                          ></load-more-btn>
                        </div>
                      </div>
                    </div>
                  </div>
                </aside>
                <div class="user-messages-content">
                  <template v-if="threadsNotEmpty">
                    <conversation
                      :item="conversation"
                      :user-statuses="userStatuses"
                    ></conversation>
                  </template>
                  <template v-else>
                    <empty-content></empty-content>
                  </template>
                </div>
              </div>
              <div v-else class="d-sm-none">
                <template v-if="threadsNotEmpty">
                  <div class="user-messages-sidebar-search mb-2 mb-sm-3">
                    <search-input
                      v-model.trim="searchQuery"
                      :disabled="!threadsNotEmpty && !searchQueryParam"
                      placeholder="search_for_recipient"
                      small
                      @submit="searchSubmitHandler"
                    ></search-input>
                  </div>

                  <client-only>
                    <v-expansion-panels
                      v-model="tabActive"
                      accordion
                      flat
                      class="tabs-mobile"
                    >
                      <v-expansion-panel
                        v-for="(thread, idx) in threads"
                        :key="idx"
                      >
                        <v-expansion-panel-header
                          :ref="`panel-${idx}`"
                          disable-icon-rotate
                          @click="threadClickHandler(thread)"
                        >
                          <div class="nav-item-avatar mr-1">
                            <v-img
                              v-if="!thread.isRead"
                              :src="
                                require('~/assets/images/envelop-icon-gradient.svg')
                              "
                              class="envelop"
                              width="22"
                              height="22"
                            ></v-img>
                            <v-avatar width="48" height="48">
                              <v-img
                                :src="
                                  getSrcAvatar(
                                    !thread.userIsDeleted ? thread.avatars : {},
                                    'user_thumb_52x52'
                                  )
                                "
                                :srcset="
                                  getSrcSetAvatar(
                                    !thread.userIsDeleted ? thread.avatars : {},
                                    'user_thumb_52x52',
                                    'user_thumb_104x104'
                                  )
                                "
                                :options="{ rootMargin: '50%' }"
                              ></v-img>
                            </v-avatar>
                            <user-status
                              v-if="!thread.userIsDeleted"
                              :user-id="thread.userId"
                              :user-statuses="userStatuses"
                            ></user-status>
                          </div>
                          <template v-if="!thread.userIsDeleted">
                            {{ thread.firstName }} {{ thread.lastName }}
                          </template>
                          <template v-else>
                            {{ $t('deleted_user') }}
                          </template>
                          <template #actions>
                            <template v-if="tabActive === idx">
                              <v-icon color="dark">
                                {{ mdiMinus }}
                              </v-icon>
                            </template>
                            <template v-else>
                              <v-img
                                :src="
                                  require('~/assets/images/add-icon-gradient.svg')
                                "
                                width="24"
                                height="24"
                              ></v-img>
                            </template>
                          </template>
                        </v-expansion-panel-header>
                        <v-expansion-panel-content>
                          <conversation
                            :item="conversation"
                            :user-statuses="userStatuses"
                          ></conversation>
                        </v-expansion-panel-content>
                      </v-expansion-panel>
                    </v-expansion-panels>
                  </client-only>
                  <div
                    v-if="isMoreButtonShown"
                    class="user-messages-sidebar-show-more-btn mt-1"
                  >
                    <load-more-btn
                      large
                      :text-btn="$t('load_more_threads')"
                      :fetch-func="fetchThreads"
                    ></load-more-btn>
                  </div>
                </template>
                <template v-else>
                  <empty-content></empty-content>
                </template>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </v-col>
</template>

<script>
import { mdiMinus } from '@mdi/js'
import Avatars from '~/mixins/Avatars'
import StatusOnline from '~/mixins/StatusOnline'
import SearchInput from '~/components/form/SearchInput'
import UserStatus from '~/components/UserStatus'
import LoadMoreBtn from '~/components/LoadMoreBtn'
import Conversation from '~/components/user-messages/Conversation'
import EmptyContent from '~/components/user-messages/EmptyContent'

export default {
  name: 'MessagesPage',
  components: {
    SearchInput,
    UserStatus,
    LoadMoreBtn,
    Conversation,
    EmptyContent,
  },
  mixins: [Avatars, StatusOnline],
  props: {
    totalQuantity: {
      type: Number,
      required: true,
    },
    additionalUser: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mdiMinus,
      tabActive: null,
      searchQuery: '',
      threadsPage: 1,
    }
  },
  computed: {
    threads() {
      return this.$store.state.message.items
    },
    threadsNotEmpty() {
      return !!this.threads.length
    },
    selectedThread() {
      return this.$store.state.message.item
    },
    conversation() {
      return this.$store.state.message.conversation
    },
    totalPages() {
      return Math.ceil(
        this.totalQuantity / process.env.NUXT_ENV_THREADS_PER_PAGE
      )
    },
    isMoreButtonShown() {
      return this.totalPages > 1 && this.threadsPage < this.totalPages
    },
    searchQueryParam() {
      return this.$route.query?.search ?? ''
    },
    userId() {
      return this.$store.state.user.item?.id
    },
  },
  watch: {
    '$route.params.search': {
      handler() {
        this.threadsPage = 1
        this.searchQuery = this.searchQueryParam

        this.setArrStatusId()
      },
      deep: true,
    },
    'threads.length'() {
      this.setArrStatusId()

      this.tabActive = null
    },
    tabActive(newValue, oldValue) {
      if (newValue != null && this.$vuetify.breakpoint.xsOnly) {
        const el = this.$refs[`panel-${this.tabActive}`][0].$el

        if (el) {
          this.$nextTick(() => {
            window.setTimeout(() => {
              this.$vuetify.goTo(el, {
                duration: 0,
                offset: 10,
                easing: 'linear',
              })
            }, 400)
          })
        }
      }
    },
  },
  beforeMount() {
    this.searchQuery = this.searchQueryParam

    this.setArrStatusId()
    this.refreshStatusOnline()
  },
  methods: {
    setArrStatusId() {
      this.arrStatusId = this.threads
        .filter((item) => !item.userIsDeleted)
        .map((item) => item.userId)

      if (this.userId) {
        this.arrStatusId.push(this.userId)
      }

      if (this.additionalUser) {
        this.arrStatusId.push(this.conversation.userId)
      }
    },
    searchSubmitHandler() {
      this.$router.push({
        name: this.$route.name,
        query: this.searchQuery ? { search: this.searchQuery } : {},
      })
    },
    threadClickHandler(thread) {
      this.$store
        .dispatch('message/getConversation', {
          threadId: thread.id,
        })
        .then(() => {
          this.$store.commit('message/SET_ITEM', thread)
        })
    },
    async fetchThreads() {
      this.threadsPage++

      await this.$store.dispatch('loadingAllow', false)
      await this.$store
        .dispatch('message/getItems', { page: this.threadsPage })
        .then(() => {
          this.$nextTick(() => {
            const lastItem = this.threads[
              (this.threadsPage - 1) * process.env.NUXT_ENV_THREADS_PER_PAGE
            ]
            const lastEl = document.getElementById(`thread-${lastItem.id}`)

            if (lastEl) {
              this.$refs.threadsList.scrollTo({
                top: lastEl.offsetTop - 76,
                behavior: 'smooth',
              })
            }
          })
        })
      await this.$store.dispatch('loadingAllow', true)
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/user-messages.scss';
</style>
