<template>
  <user-setting-template
    v-if="preferences.length"
    :title="$t('teaching_preferences')"
    :submit-func="submitData"
  >
    <div class="mb-4 mb-md-7">
      <v-row>
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-1">
              {{ $t('specialities') }}
            </div>
            <div>
              <v-btn
                class="gradient font-weight-medium mt-2"
                @click="isShownSpecialitiesDialog = true"
              >
                <div class="mr-1">
                  <v-img
                    :src="require('~/assets/images/setting-icon-gradient.svg')"
                    width="24"
                    height="24"
                  ></v-img>
                </div>
                <div class="text--gradient">
                  {{ $t('manage_specialties') }}
                </div>
              </v-btn>
            </div>
          </div>
        </v-col>
      </v-row>
    </div>
    <div>
      <v-row>
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-2">
              {{ $t('which_levels_would_you_like_to_teach') }}
            </div>
            <div>
              <div
                v-for="(item, idx) in preferences"
                :key="idx"
                class="checkbox"
              >
                <v-checkbox
                  v-model="selectedPreferences"
                  :value="item"
                  class="l-checkbox"
                  :label="item.name"
                  hide-details
                  :ripple="false"
                ></v-checkbox>
              </div>
            </div>
          </div>
        </v-col>
      </v-row>
    </div>

    <speciality-dialog
      :is-shown-specialities-dialog="isShownSpecialitiesDialog"
      @close-dialog="isShownSpecialitiesDialog = false"
    ></speciality-dialog>
  </user-setting-template>
</template>

<script>
import UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'
import SpecialityDialog from '@/components/user-settings/SpecialityDialog'

export default {
  name: 'TeachingPreferencesInfo',
  components: { UserSettingTemplate, SpecialityDialog },
  data() {
    return {
      isShownSpecialitiesDialog: false,
    }
  },
  computed: {
    preferences() {
      return this.$store.state.settings.preferenceItems
    },
    selectedPreferences: {
      get() {
        return this.preferences.filter((item) => item.isSelected)
      },
      set(value) {
        return this.$store.commit(
          'settings/UPDATE_TEACHING_PREFERENCE_ITEMS',
          value
        )
      },
    },
  },
  async beforeCreate() {
    await Promise.all([
      this.$store.dispatch('settings/getTeachingPreferences'),
      this.$store.dispatch('settings/getSpecialities'),
    ])
  },
  methods: {
    submitData() {
      this.$store.dispatch('settings/updateTeachingPreferences')
    },
  },
}
</script>

<style scoped lang="scss">
.checkbox:not(:last-child) {
  margin-bottom: 20px;
}
</style>
