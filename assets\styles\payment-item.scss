@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.payment-item {
  position: relative;
  min-height: 128px;
  padding-left: 142px;

  @media only screen and (max-width: $mac-13-and-down) {
    min-height: 130px;
    padding-left: 85px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-up')} {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 6px 10px rgba(17, 46, 90, 0.08);
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding-left: 112px;
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    padding-left: 0;
  }

  &-date {
    justify-content: center;
    align-items: center;
    width: 142px;
    padding: 11px;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(
        126.15deg,
        rgba(128, 182, 34, 0.74) 0%,
        rgba(60, 135, 248, 0.74) 102.93%
    );
    border-radius: 16px;
    color: #fff;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      flex-direction: column;
      justify-content: space-between;
      box-shadow: 4px 5px 8px rgba(102, 102, 102, 0.25);
      z-index: 3;
    }

    @media only screen and (max-width: $mac-13-and-down) {
      width: 85px;
      padding: 14px 6px;
      font-size: 15px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: 112px;
      padding: 10px 4px;
      font-size: 13px;
    }
  }

  &-content {
    position: relative;
    display: flex;
    justify-content: space-between;
    width: 100%;
    border-radius: 0 10px 10px 0;
    overflow: hidden;
    padding: 16px 24px;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      min-height: 128px;
      margin-top: -25px;
      border-radius: 10px;
      background: #fff;
      box-shadow: 0 1px 5px rgba(130, 130, 130, 0.3);
      z-index: 2;
    }

    @media only screen and (max-width: $xsm-and-down) {
      flex-direction: column;
      padding: 12px 12px 4px;
    }

    .payment-info {
      font-size: 16px;
      color: var(--v-dark-lighten3);
      line-height: 1.1;

      @media #{map-get($display-breakpoints, 'sm-and-up')} {
        padding-bottom: 4px;
      }

      @media only screen and (max-width: $mac-13-and-down) {
        display: flex;
        align-items: flex-end;
        font-size: 14px;
      }

      & > div:not(:last-child) {
        margin-bottom: 3px;

        @media only screen and (max-width: $xsm-and-down) {
          margin-bottom: 5px;
        }
      }
    }
  }

  &-amount {
    display: flex;
    width: 350px;
    padding: 18px 18px 10px 0;

    @media only screen and (max-width: $mac-13-and-down) {
      padding: 12px 12px 8px 0;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      padding-top: 10px;
    }

    @media only screen and (min-width: $xsm-and-up) {
      flex-direction: column;
      align-items: flex-end;
      justify-content: space-between;
      flex-grow: 1;
    }

    @media only screen and (max-width: $xsm-and-down) {
      width: 100%;
      margin-top: 8px;
      padding: 8px 0 0;
      border-top: 1px solid rgba(0, 0, 0, 0.08);
    }
  }

  &-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;

    &.completed {
      background-color: var(--v-success-lighten5);
      color: var(--v-success-base);
    }

    &.pending {
      background-color: var(--v-warning-lighten5);
      color: var(--v-warning-base);
    }
  }
}