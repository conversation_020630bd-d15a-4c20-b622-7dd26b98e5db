<template>
  <default-layout>
    <h1 class="sr-only">Virtual classroom</h1>

    <v-main id="content" class="classroom-page-top">
      <nuxt />
    </v-main>
  </default-layout>
</template>

<script>
import DefaultLayout from '~/layouts/default'

export default {
  name: 'ClassroomLayout',
  components: { DefaultLayout },
  middleware({ store, redirect }) {
    if (!store.getters['user/isUserLogged']) {
      return redirect('/')
    }
  },
  head() {
    return {
      htmlAttrs: {
        class: 'classroom-page',
      },
      title: this.$t('classroom_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('classroom_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('classroom_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('classroom_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: 'body-classroom',
      },
      link: [
        {
          rel: 'stylesheet',
          href: 'https://cdn.plyr.io/3.6.2/plyr.css',
        },
      ],
    }
  },
  beforeDestroy() {
    // to trigger the websocket event 'user has left the classroom'
    this.$socket.disconnect()
    this.$socket.connect()
  },
}
</script>

<style lang="scss">
.body-classroom {
  position: relative;
  overflow: hidden;

  #content {
    background: url('~assets/images/bg.png') repeat;
  }

  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
  }

  #tidio-chat {
    display: none !important;
  }
  .classroom-page-top {
    margin-top: 0 !important;
  }
}
</style>
