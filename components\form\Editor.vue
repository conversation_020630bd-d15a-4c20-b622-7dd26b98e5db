<template>
  <div class="text-editor">
    <div v-if="editor" class="text-editor-buttons">
      <button
        :class="{ 'is-active': editor.isActive('bold') }"
        @click.stop.prevent="editor.chain().focus().toggleBold().run()"
      >
        <svg width="16" height="16" viewBox="0 0 16 16">
          <use
            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#editor-bold-icon`"
          ></use>
        </svg>
      </button>
      <button
        :class="{ 'is-active': editor.isActive('bulletList') }"
        @click.stop.prevent="editor.chain().focus().toggleBulletList().run()"
      >
        <svg width="16" height="16" viewBox="0 0 16 16">
          <use
            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#editor-list-icon`"
          ></use>
        </svg>
      </button>
    </div>
    <editor-content :editor="editor"></editor-content>
    <div v-if="counter" class="v-text-field__details">
      <div class="v-messages theme--light">
        <div class="v-messages__wrapper"></div>
      </div>
      <div
        :class="[
          'v-counter theme--light',
          { 'error--text': !isValid && isDirty },
        ]"
      >
        {{ text.length }} / {{ limit }}
      </div>
    </div>
  </div>
</template>

<script>
import { Editor, EditorContent } from '@tiptap/vue-2'
import StarterKit from '@tiptap/starter-kit'
import CharacterCount from '@tiptap/extension-character-count'
import Link from '@tiptap/extension-link'

export default {
  name: 'Editor',
  components: {
    EditorContent,
  },
  props: {
    value: {
      type: String,
      required: true,
    },
    counter: {
      type: Boolean,
      default: false,
    },
    autoLink: {
      type: Boolean,
      default: false,
    },
    limit: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      editor: null,
      text: '',
      isValid: true,
      editorEl: null,
      keysPressed: {},
      isDirty: false,
    }
  },
  watch: {
    value(value) {
      const isSame = this.editor.getHTML() === value

      if (isSame) {
        return
      }

      this.editor.commands.setContent(value, false)
    },
  },
  mounted() {
    this.editor = new Editor({
      content: this.value,
      extensions: [
        StarterKit,
        CharacterCount.configure({
          limit: this.limit,
        }),
        Link.configure({
          autolink: true,
        }),
      ],
    })

    this.editor.on('create', ({ editor }) => {
      this.text = editor.getText()

      this.$nextTick(() => {
        this.editorEl = document.getElementsByClassName('ProseMirror')[0]

        if (this.editorEl) {
          this.editorEl.addEventListener('keydown', this.keydownHandler)
          this.editorEl.addEventListener('keyup', this.keyupHandler)
        }
      })

      this.validation()
    })

    this.editor.on('update', ({ editor }) => {
      this.isDirty = true
      this.text = editor.getText()

      this.validation()
      this.$emit('update', this.text ? editor.getHTML() : '')
    })
  },
  beforeDestroy() {
    if (this.editorEl) {
      this.editorEl.removeEventListener('keydown', this.keydownHandler)
      this.editorEl.removeEventListener('keyup', this.keyupHandler)
    }

    this.editor.destroy()
  },
  methods: {
    keydownHandler(e) {
      this.keysPressed[e.keyCode] = true

      if (
        (e.ctrlKey ||
          this.keysPressed[17] ||
          this.keysPressed[91] ||
          this.keysPressed[93] ||
          this.keysPressed[224]) &&
        this.keysPressed[13]
      ) {
        e.preventDefault()

        this.$emit('submit')

        this.keysPressed = {}
      } else if (e.keyCode === 13 && !e.shiftKey) {
        e.preventDefault()
        this.editor.commands.enter()
      }
    },
    keyupHandler(e) {
      delete this.keysPressed[e.keyCode]
    },
    validation() {
      const strLength = this.text.trim().length

      this.isValid = !!strLength

      if (!!strLength && this.limit) {
        this.isValid = strLength <= this.limit
      }

      this.$emit('validation', this.isValid)
    },
  },
}
</script>

<style lang="scss">
.text-editor {
  position: relative;

  &-buttons {
    position: absolute;
    right: 18px;
    top: 8px;
    z-index: 2;

    button {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      width: 24px;
      height: 24px;
      margin-left: 8px;
      border-radius: 2px;
      border: 1px solid transparent;

      &.is-active {
        background-color: var(--v-greyBg-base);
        border-color: var(--v-greyLight-base);
      }
    }
  }

  .ProseMirror {
    min-height: 280px;
    margin-bottom: 4px;
    padding: 40px 12px 12px;
    border: 1px solid #bebebe;
    font-size: 13px;
    border-radius: 16px;
    line-height: 1.23;

    & > * {
      position: relative;
    }

    p {
      margin-bottom: 0;
    }

    ul {
      padding-left: 28px;

      & > li {
        p {
          margin-bottom: 0;
        }
      }
    }

    strong {
      font-weight: 700 !important;
    }

    &:focus,
    &:focus-visible {
      outline: none !important;
    }

    &-focused {
      &::before {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        width: calc(100% + 2px);
        height: calc(100% + 2px);
        border-style: none;
        border-radius: 16px;
        padding: 1px;
        background: linear-gradient(
          126.15deg,
          var(--v-success-base) 0%,
          var(--v-primary-base) 102.93%
        );
        -webkit-mask: linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: destination-out;
        mask-composite: exclude;
        transform: none;
      }
    }
  }

  .v-text-field__details {
    padding: 0 14px;
  }
}
</style>
