<template>
  <v-expansion-panels v-model="value" accordion multiple :flat="flat">
    <v-expansion-panel
      v-for="i in items"
      :key="i.id"
      :class="flat ? 'mb-2 mb-sm-3' : ''"
    >
      <v-expansion-panel-header
        :id="i.selectorId || null"
        v-slot="{ open }"
        disable-icon-rotate
        hide-actions
      >
        <div
          :class="[
            'font-weight-medium',
            open ? 'orange--text' : 'darkLight--text',
          ]"
        >
          {{ i.title }}
        </div>
        <div
          class="v-expansion-panel-header__icon v-expansion-panel-header__icon--disable-rotate"
        >
          <v-icon class="ml-auto" :color="open ? 'orange' : 'greyLight'">
            {{ open ? mdiMinus : mdiPlus }}
          </v-icon>
        </div>
        <a
          v-if="link"
          :href="`/faq#faq${i.id}`"
          class="d-block"
          @click="changeURL($event, `faq${i.id}`, open)"
        ></a>
      </v-expansion-panel-header>
      <v-expansion-panel-content>
        <div v-html="i.description"></div>
      </v-expansion-panel-content>
    </v-expansion-panel>
  </v-expansion-panels>
</template>

<script>
import { mdiPlus, mdiMinus } from '@mdi/js'

export default {
  name: 'LExpansionPanels',
  props: {
    items: {
      type: Array,
      required: true,
    },
    panels: {
      type: Array,
      default: () => [],
    },
    flat: {
      type: Boolean,
      default: false,
    },
    link: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mdiPlus,
      mdiMinus,
      value: undefined,
    }
  },
  mounted() {
    this.value = [...this.panels]
  },
  methods: {
    changeURL(e, hash, isOpened) {
      e.preventDefault()

      const currentHash = window.location.hash

      if (!isOpened) {
        window.location.hash = hash

        return
      }

      if (currentHash === '#' + hash) {
        history.replaceState({}, document.title, '/faq')
      }
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';

.v-expansion-panels {
  .v-expansion-panel {
    font-size: 16px;

    &-header {
      opacity: 0.7;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        min-height: 58px !important;
        padding: 16px !important;
        font-size: 16px !important;
      }

      a {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }

    &-content {
      line-height: 1.5;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 14px;
        line-height: 1.4;
      }

      &__wrap {
        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding: 0 16px 16px;
        }
      }
    }

    &::before {
      box-shadow: 0 4px 10px rgba(71, 68, 68, 0.1) !important;
    }
  }

  &--flat {
    .v-expansion-panel {
      border-radius: 8px !important;
    }
  }
}
</style>
