<template>
  <section class="thinking">
    <v-container class="py-0">
      <v-row>
        <v-col class="col-12 py-0">
          <div class="section-head section-head--decorated">
            <h3
              class="section-head-title"
              style="color: #262626; -webkit-text-fill-color: #262626"
            >
              {{ $t('home_page.thinking_section_title') }}
            </h3>
          </div>
        </v-col>
      </v-row>
      <v-row>
        <v-col class="col-12 col-md-7 order-md-2 py-0">
          <div class="thinking-images">
            <div
              v-for="i in 5"
              :key="i"
              :class="[`thinking-images-item thinking-images-item-t${i}`]"
            >
              <div class="thinking-images-item-decor">
                <v-img
                  :src="require('~/assets/images/homepage/decoration-4.svg')"
                  :options="{ rootMargin: '50%' }"
                  contain
                ></v-img>
              </div>
              <v-avatar>
                <v-img
                  :src="require(`~/assets/images/homepage/teacher-${i}.jpeg`)"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </v-avatar>
            </div>
          </div>
        </v-col>
        <v-col class="col-12 col-md-5 order-md-1 py-0">
          <div class="thinking-content">
            <div class="thinking-text">
              {{ $t('home_page.thinking_text') }}
            </div>
            <div class="thinking-bottom">
              {{ $t('home_page.thinking_highline') }}
            </div>
          </div>
        </v-col>
      </v-row>
      <v-row>
        <v-col class="col-12 py-0">
          <div class="thinking-button">
            <v-btn
              href="https://heylangu-teachers.com"
              target="_blank"
              large
              color="primary"
              >{{ $t('apply_now') }}</v-btn
            >
          </div>
        </v-col>
      </v-row>
    </v-container>
  </section>
</template>

<script>
export default {
  name: 'ThinkingSection',
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.thinking {
  position: relative;
  padding: 90px 0 48px;
  overflow: hidden;

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding: 30px 0;
  }

  &::before {
    content: '';
    position: absolute;
    top: 50px;
    right: 0;
    width: 117px;
    height: 256px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: bottom center;
    background-image: url('~assets/images/homepage/thinking-bg.svg');

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      top: 112px;
      right: 0;
      width: 95px;
      height: 210px;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      top: auto;
      left: 0;
      bottom: 100px;
      right: auto;
      width: 95px;
      height: 210px;
      transform: scaleX(-1);
    }
  }

  .section-head {
    margin-bottom: 105px;
    max-width: 715px;

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      max-width: 660px;
      margin-bottom: 42px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin-bottom: 80px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      margin-bottom: 30px;
    }
  }

  &-content {
    padding-bottom: 80px;
    font-size: 24px;
    line-height: 1.4;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      padding-bottom: 100px;
      font-size: 22px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding: 30px 0 0;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      font-size: 20px;
    }
  }

  &-images {
    position: relative;
    height: 100%;

    @media #{map-get($display-breakpoints, 'xl-only')} {
      left: -88px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      min-height: 320px;
    }

    &-item {
      position: absolute;

      &-decor {
        position: absolute;
      }

      &-t1 {
        top: -122px;
        left: 30%;

        @media #{map-get($display-breakpoints, 'lg-and-down')} {
          top: -110px;
          left: 40%;
        }

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          top: -20px;
          left: 22%;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          top: 0;
          left: 65px;
        }

        @media only screen and (max-width: $xsm-and-down) {
          top: 0;
          left: 0;
        }

        .v-avatar {
          width: 99px !important;
          height: 99px !important;

          @media #{map-get($display-breakpoints, 'lg-and-down')} {
            width: 80px !important;
            height: 80px !important;
          }

          @media only screen and (max-width: $xsm-and-down) {
            width: 89px !important;
            height: 89px !important;
          }
        }

        .thinking-images-item-decor {
          left: -4px;
          bottom: -13px;
          width: 137px;
          height: 107px;

          @media #{map-get($display-breakpoints, 'lg-and-down')} {
            left: -2px;
            bottom: -12px;
            width: 105px;
            height: 86px;
          }

          @media only screen and (max-width: $xsm-and-down) {
            left: -2px;
            bottom: -12px;
            width: 118px;
            height: 97px;
            transform: scaleX(-1);
          }
        }
      }

      &-t2 {
        right: 11%;
        top: -127px;

        @media #{map-get($display-breakpoints, 'lg-and-down')} {
          top: -124px;
        }

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          top: -40px;
          right: 0;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          top: 10px;
          right: 45px;
        }

        @media only screen and (max-width: $xsm-and-down) {
          right: -20px;
          top: 20px;
        }

        .v-avatar {
          width: 150px !important;
          height: 150px !important;

          @media #{map-get($display-breakpoints, 'lg-and-down')} {
            width: 120px !important;
            height: 120px !important;
          }

          @media only screen and (max-width: $xsm-and-down) {
            width: 85px !important;
            height: 85px !important;
          }
        }

        .thinking-images-item-decor {
          left: -19px;
          bottom: 20px;
          width: 200px;
          height: 163px;

          @media #{map-get($display-breakpoints, 'lg-and-down')} {
            left: -15px;
            bottom: 16px;
            width: 160px;
            height: 130px;
          }

          @media only screen and (max-width: $xsm-and-down) {
            left: -15px;
            bottom: 0;
            width: 110px;
            height: 96px;
          }
        }
      }

      &-t3 {
        bottom: 32px;
        left: 8%;

        @media #{map-get($display-breakpoints, 'lg-and-down')} {
          bottom: 53px;
          left: 21%;
        }

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          bottom: 0;
          left: 0;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          bottom: 60px;
          left: 80px;
        }

        @media only screen and (max-width: $xsm-and-down) {
          bottom: 60px;
          left: 20px;
        }

        .v-avatar {
          width: 144px !important;
          height: 144px !important;

          @media #{map-get($display-breakpoints, 'lg-and-down')} {
            width: 115px !important;
            height: 115px !important;
          }

          @media only screen and (max-width: $xsm-and-down) {
            width: 80px !important;
            height: 80px !important;
          }
        }

        .thinking-images-item-decor {
          left: 10px;
          bottom: -20px;
          width: 191px;
          height: 156px;

          @media #{map-get($display-breakpoints, 'lg-and-down')} {
            width: 153px;
            height: 125px;
          }

          @media only screen and (max-width: $xsm-and-down) {
            left: -10px;
            bottom: 10px;
            width: 106px;
            height: 87px;
          }
        }
      }

      &-t4 {
        left: 50%;
        bottom: 56px;

        @media #{map-get($display-breakpoints, 'lg-and-down')} {
          left: 57%;
          bottom: 107px;
        }

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          left: 48%;
          bottom: 50px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          left: 45%;
          bottom: 40%;
        }

        @media only screen and (max-width: $xsm-and-down) {
          left: 40%;
          bottom: 44%;
        }

        .v-avatar {
          width: 125px !important;
          height: 125px !important;

          @media #{map-get($display-breakpoints, 'lg-and-down')} {
            width: 85px !important;
            height: 85px !important;
          }

          @media only screen and (max-width: $xsm-and-down) {
            width: 103px !important;
            height: 103px !important;
          }
        }

        .thinking-images-item-decor {
          left: -9px;
          bottom: 12px;
          width: 162px;
          height: 141px;

          @media #{map-get($display-breakpoints, 'lg-and-down')} {
            left: -4px;
            bottom: 8px;
            width: 110px;
            height: 96px;
          }

          @media only screen and (max-width: $xsm-and-down) {
            left: 0;
            bottom: -14px;
            width: 136px;
            height: 111px;
          }
        }
      }

      &-t5 {
        right: 3%;
        bottom: -13px;

        @media #{map-get($display-breakpoints, 'lg-and-down')} {
          right: 2%;
          bottom: 47px;
        }

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          right: 0;
          bottom: 0;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          right: 115px;
          bottom: 80px;
        }

        @media only screen and (max-width: $xsm-and-down) {
          right: 25px;
          bottom: 15px;
        }

        .v-avatar {
          width: 111px !important;
          height: 111px !important;

          @media #{map-get($display-breakpoints, 'lg-and-down')} {
            width: 89px !important;
            height: 89px !important;
          }
        }

        .thinking-images-item-decor {
          left: 0;
          bottom: -16px;
          width: 147px;
          height: 120px;

          @media #{map-get($display-breakpoints, 'lg-and-down')} {
            left: 0;
            bottom: -16px;
            width: 118px;
            height: 96px;
          }

          @media only screen and (max-width: $xsm-and-down) {
            left: -2px;
          }
        }
      }
    }
  }

  &-bottom {
    margin-top: 60px;
    font-weight: 600;

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      margin-top: 40px;
    }
  }

  &-button {
    display: flex;
    justify-content: center;
    margin-top: 90px;

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      margin-top: 75px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      margin-top: 60px;
    }

    .v-btn {
      min-width: 150px !important;

      @media only screen and (max-width: $xxs-and-down) {
        .v-btn {
          min-width: 100% !important;
          width: 100% !important;
        }
      }
    }
  }
}
</style>
